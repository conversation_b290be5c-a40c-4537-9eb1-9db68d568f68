"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.kycService = exports.KYCService = void 0;
const mongoose_1 = require("mongoose");
const models_1 = require("../models");
const UserKYC_1 = require("../models/UserKYC");
const types_1 = require("../types");
const AuditLog_1 = require("../models/AuditLog");
const pdfkit_1 = __importDefault(require("pdfkit"));
const https_1 = __importDefault(require("https"));
const http_1 = __importDefault(require("http"));
class KYCService {
    constructor() {
    }
    handleError(error, operation) {
        console.error(`KYC Service Error in ${operation}:`, error);
        return new Error(`${operation} failed: ${error.message || error}`);
    }
    async getUserKYC(userId) {
        try {
            let kyc = await models_1.UserKYC.findOne({ userId }).populate('userId', 'firstName lastName email phone dateOfBirth');
            if (!kyc) {
                kyc = new models_1.UserKYC({
                    userId,
                    status: types_1.KYCStatus.NOT_STARTED,
                    country: 'IN',
                    riskLevel: 'low',
                    personalInfo: {},
                    address: {},
                    documents: [],
                    completedSteps: []
                });
                await kyc.save();
                await models_1.User.findByIdAndUpdate(userId, {
                    kycStatus: types_1.KYCStatus.NOT_STARTED,
                    updatedAt: new Date()
                });
            }
            else {
                let needsUpdate = false;
                if (!kyc.completedSteps.includes('signature')) {
                    const hasSignature = kyc.documents.some(doc => doc.type === UserKYC_1.DocumentType.DIGITAL_SIGNATURE);
                    const hasSelfie = kyc.documents.some(doc => doc.type === UserKYC_1.DocumentType.SELFIE);
                    if (hasSignature && hasSelfie) {
                        kyc.completedSteps.push('signature');
                        needsUpdate = true;
                        console.log('✅ Updating existing KYC: Signature step marked as completed');
                    }
                }
                if (needsUpdate) {
                    kyc.updatedAt = new Date();
                    await kyc.save();
                }
            }
            return kyc;
        }
        catch (error) {
            throw this.handleError(error, 'getUserKYC');
        }
    }
    async getUserDetailsForKYC(userId) {
        try {
            const user = await models_1.User.findById(userId).select('firstName lastName email phone dateOfBirth');
            return user;
        }
        catch (error) {
            throw this.handleError(error, 'getUserDetailsForKYC');
        }
    }
    async updatePersonalInfo(userId, personalInfo) {
        try {
            const kyc = await this.getUserKYC(userId);
            kyc.personalInfo = {
                ...kyc.personalInfo,
                ...personalInfo
            };
            if (personalInfo.nationality) {
                kyc.country = personalInfo.nationality;
            }
            if (!kyc.completedSteps.includes('personal')) {
                kyc.completedSteps.push('personal');
            }
            kyc.updatedAt = new Date();
            await kyc.save();
            await models_1.User.findByIdAndUpdate(userId, {
                kycStatus: types_1.KYCStatus.PENDING,
                updatedAt: new Date()
            });
            await this.logAudit(userId, AuditLog_1.AuditAction.UPDATE, AuditLog_1.AuditEntity.USER_KYC, kyc._id.toString(), {
                step: 'personal',
                data: personalInfo
            });
            return kyc;
        }
        catch (error) {
            throw this.handleError(error, 'updatePersonalInfo');
        }
    }
    async updateAddress(userId, address) {
        try {
            const kyc = await this.getUserKYC(userId);
            kyc.address = {
                ...kyc.address,
                ...address
            };
            if (!kyc.completedSteps.includes('address')) {
                kyc.completedSteps.push('address');
            }
            kyc.updatedAt = new Date();
            await kyc.save();
            await this.logAudit(userId, AuditLog_1.AuditAction.UPDATE, AuditLog_1.AuditEntity.USER_KYC, kyc._id.toString(), {
                step: 'address',
                data: address
            });
            return kyc;
        }
        catch (error) {
            throw this.handleError(error, 'updateAddress');
        }
    }
    async uploadDocument(userId, documentData) {
        try {
            const kyc = await this.getUserKYC(userId);
            const document = {
                type: documentData.type,
                category: documentData.category,
                documentUrl: documentData.fileUrl,
                documentNumber: documentData.documentNumber,
                expiryDate: documentData.expiryDate,
                status: types_1.KYCStatus.PENDING,
                uploadedAt: new Date()
            };
            kyc.documents.push(document);
            if (!kyc.completedSteps.includes('documents') && kyc.documents.length >= 2) {
                kyc.completedSteps.push('documents');
            }
            if (!kyc.completedSteps.includes('signature')) {
                const hasSignature = kyc.documents.some(doc => doc.type === UserKYC_1.DocumentType.DIGITAL_SIGNATURE);
                const hasSelfie = kyc.documents.some(doc => doc.type === UserKYC_1.DocumentType.SELFIE);
                if (hasSignature && hasSelfie) {
                    kyc.completedSteps.push('signature');
                    console.log('✅ Signature step marked as completed - both signature and selfie uploaded');
                }
            }
            kyc.updatedAt = new Date();
            await kyc.save();
            await this.logAudit(userId, AuditLog_1.AuditAction.CREATE, AuditLog_1.AuditEntity.USER_KYC, kyc._id.toString(), {
                documentType: documentData.type,
                fileUrl: documentData.fileUrl
            });
            return kyc;
        }
        catch (error) {
            throw this.handleError(error, 'uploadDocument');
        }
    }
    async submitKYC(userId) {
        try {
            const kyc = await this.getUserKYC(userId);
            const requiredSteps = ['personal', 'address', 'documents'];
            const missingSteps = requiredSteps.filter(step => !kyc.completedSteps.includes(step));
            if (missingSteps.length > 0) {
                throw new Error(`Missing required steps: ${missingSteps.join(', ')}`);
            }
            kyc.status = types_1.KYCStatus.UNDER_REVIEW;
            kyc.submittedAt = new Date();
            kyc.updatedAt = new Date();
            if (!kyc.completedSteps.includes('submitted')) {
                kyc.completedSteps.push('submitted');
            }
            await kyc.save();
            await models_1.User.findByIdAndUpdate(userId, {
                kycStatus: 'under_review',
                updatedAt: new Date()
            });
            console.log('✅ Updated user kycStatus to under_review for user:', userId);
            await this.logAudit(userId, AuditLog_1.AuditAction.UPDATE, AuditLog_1.AuditEntity.USER_KYC, kyc._id.toString(), {
                action: 'submitted_for_verification'
            });
            return kyc;
        }
        catch (error) {
            throw this.handleError(error, 'submitKYC');
        }
    }
    isImageUrl(url) {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg'];
        const urlLower = url.toLowerCase();
        return imageExtensions.some(ext => urlLower.includes(ext)) || urlLower.includes('image');
    }
    async downloadImage(url) {
        return new Promise((resolve, reject) => {
            const protocol = url.startsWith('https:') ? https_1.default : http_1.default;
            protocol.get(url, (response) => {
                if (response.statusCode !== 200) {
                    reject(new Error(`Failed to download image: ${response.statusCode}`));
                    return;
                }
                const chunks = [];
                response.on('data', (chunk) => chunks.push(chunk));
                response.on('end', () => resolve(Buffer.concat(chunks)));
                response.on('error', reject);
            }).on('error', reject);
        });
    }
    async generateKYCPDF(user, kyc) {
        return new Promise(async (resolve, reject) => {
            try {
                const doc = new pdfkit_1.default({ margin: 50, size: 'A4' });
                const chunks = [];
                doc.on('data', (chunk) => chunks.push(chunk));
                doc.on('end', () => resolve(Buffer.concat(chunks)));
                doc.fontSize(28).fillColor('#0ea5e9').text('KYC VERIFICATION REPORT', { align: 'center' });
                doc.moveDown(0.5);
                doc.fontSize(14).fillColor('#64748b').text('SGM Investment Platform', { align: 'center' });
                doc.fontSize(10).fillColor('#64748b').text(`Generated on: ${new Date().toLocaleString()}`, { align: 'center' });
                doc.moveDown(2);
                doc.fontSize(18).fillColor('#1e293b').text('PERSONAL INFORMATION', { underline: true });
                doc.moveDown(0.5);
                doc.fontSize(12).fillColor('#334155');
                const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Not provided';
                const personalData = [
                    ['Full Name:', fullName],
                    ['Email:', user.email || 'Not provided'],
                    ['Phone:', user.phone || 'Not provided'],
                    ['Date of Birth:', user.dateOfBirth ? new Date(user.dateOfBirth).toLocaleDateString() : 'Not provided'],
                    ['Gender:', kyc.personalInfo?.gender || 'Not provided']
                ];
                personalData.forEach(([label, value]) => {
                    doc.text(`${label} ${value}`, { continued: false });
                });
                doc.moveDown();
                if (kyc.personalInfo && Object.keys(kyc.personalInfo).length > 0) {
                    doc.fontSize(16).fillColor('#1e293b').text('ADDITIONAL DETAILS', { underline: true });
                    doc.moveDown(0.5);
                    doc.fontSize(12).fillColor('#334155');
                    const additionalData = [
                        ['Nationality:', kyc.personalInfo.nationality || 'Not provided'],
                        ['Place of Birth:', kyc.personalInfo.placeOfBirth || 'Not provided'],
                        ['Gender:', kyc.personalInfo.gender || 'Not provided'],
                        ['Marital Status:', kyc.personalInfo.maritalStatus || 'Not provided']
                    ];
                    additionalData.forEach(([label, value]) => {
                        doc.text(`${label} ${value}`);
                    });
                    doc.moveDown();
                }
                if (kyc.address && Object.keys(kyc.address).length > 0) {
                    doc.fontSize(16).fillColor('#1e293b').text('ADDRESS INFORMATION', { underline: true });
                    doc.moveDown(0.5);
                    doc.fontSize(12).fillColor('#334155');
                    const addressData = [
                        ['Street:', kyc.address.street || 'Not provided'],
                        ['City:', kyc.address.city || 'Not provided'],
                        ['State:', kyc.address.state || 'Not provided'],
                        ['Postal Code:', kyc.address.postalCode || 'Not provided'],
                        ['Country:', kyc.address.country || 'Not provided']
                    ];
                    addressData.forEach(([label, value]) => {
                        doc.text(`${label} ${value}`);
                    });
                    doc.moveDown();
                }
                doc.fontSize(16).fillColor('#1e293b').text('VERIFICATION STATUS', { underline: true });
                doc.moveDown(0.5);
                doc.fontSize(12).fillColor('#334155');
                const statusData = [
                    ['Status:', kyc.status.toUpperCase()],
                    ['Submitted:', kyc.createdAt.toLocaleDateString()],
                    ['Last Updated:', kyc.updatedAt.toLocaleDateString()]
                ];
                if (kyc.verifiedAt) {
                    statusData.push(['Verified:', kyc.verifiedAt.toLocaleDateString()]);
                }
                // Add verifier information if available
                if (kyc.verifiedBy) {
                    const verifier = kyc.verifiedBy; // Populated verifier
                    if (verifier.firstName && verifier.lastName) {
                        statusData.push(['Verified By:', `${verifier.firstName} ${verifier.lastName}`]);
                    } else if (verifier.email) {
                        statusData.push(['Verified By:', verifier.email]);
                    }
                }
                statusData.forEach(([label, value]) => {
                    doc.text(`${label} ${value}`);
                });
                if (kyc.status === types_1.KYCStatus.APPROVED && kyc.userId) {
                    doc.moveDown(0.5);
                    doc.fontSize(14).fillColor('#059669').text('VERIFIED DETAILS', { underline: true });
                    doc.moveDown(0.3);
                    doc.fontSize(12).fillColor('#334155');
                    const user = kyc.userId;
                    const verifiedData = [
                        ['Name:', `${user.firstName} ${user.lastName}`],
                        ['Email:', user.email],
                        ['Phone:', user.phone || 'Not provided']
                    ];
                    verifiedData.forEach(([label, value]) => {
                        doc.text(`${label} ${value}`);
                    });
                }
                doc.moveDown();
                doc.fontSize(18).fillColor('#1e293b').text('UPLOADED DOCUMENTS', { underline: true });
                doc.moveDown(0.5);
                doc.fontSize(12).fillColor('#334155');
                if (kyc.documents && kyc.documents.length > 0) {
                    for (let i = 0; i < kyc.documents.length; i++) {
                        const document = kyc.documents[i];
                        if (doc.y > 650) {
                            doc.addPage();
                        }
                        doc.fontSize(14).fillColor('#1e293b').text(`${i + 1}. ${document.type.toUpperCase().replace('_', ' ')} (${document.category.toUpperCase()})`);
                        doc.fontSize(12).fillColor('#334155');
                        doc.text(`   Status: ${document.status.toUpperCase()}`);
                        doc.text(`   Uploaded: ${document.uploadedAt.toLocaleDateString()}`);
                        if (document.documentNumber) {
                            doc.text(`   Document Number: ${document.documentNumber}`);
                        }
                        if (document.expiryDate) {
                            doc.text(`   Expiry Date: ${new Date(document.expiryDate).toLocaleDateString()}`);
                        }
                        if (document.verifiedAt) {
                            doc.text(`   Verified: ${document.verifiedAt.toLocaleDateString()}`);
                        }
                        if (document.rejectionReason) {
                            doc.fillColor('#dc2626').text(`   Rejection Reason: ${document.rejectionReason}`);
                            doc.fillColor('#334155');
                        }
                        if (document.documentUrl && this.isImageUrl(document.documentUrl)) {
                            try {
                                const imageBuffer = await this.downloadImage(document.documentUrl);
                                const imageWidth = 200;
                                const imageHeight = 150;
                                if (doc.y + imageHeight > 750) {
                                    doc.addPage();
                                }
                                doc.moveDown(0.5);
                                doc.image(imageBuffer, doc.x, doc.y, {
                                    width: imageWidth,
                                    height: imageHeight,
                                    fit: [imageWidth, imageHeight]
                                });
                                doc.y += imageHeight + 10;
                            }
                            catch (error) {
                                console.error(`Failed to embed image for document ${i + 1}:`, error);
                                doc.text(`   [Image could not be embedded: ${document.documentUrl.split('/').pop()}]`);
                            }
                        }
                        else {
                            doc.text(`   File: ${document.documentUrl.split('/').pop() || 'Document'}`);
                        }
                        doc.moveDown(1);
                    }
                }
                else {
                    doc.text('No documents uploaded');
                }
                const selfieDoc = kyc.documents.find(doc => doc.documentUrl && doc.documentUrl.includes('selfie'));
                const signatureDoc = kyc.documents.find(doc => doc.documentUrl && (doc.documentUrl.includes('signature') ||
                    doc.documentUrl.includes('sign')));
                if (selfieDoc || signatureDoc) {
                    doc.addPage();
                    doc.fontSize(18).fillColor('#1e293b').text('VERIFICATION IMAGES', { underline: true });
                    doc.moveDown(0.5);
                    if (selfieDoc && this.isImageUrl(selfieDoc.documentUrl)) {
                        try {
                            doc.fontSize(14).fillColor('#1e293b').text('SELFIE VERIFICATION');
                            doc.moveDown(0.5);
                            const selfieBuffer = await this.downloadImage(selfieDoc.documentUrl);
                            doc.image(selfieBuffer, doc.x, doc.y, {
                                width: 150,
                                height: 150,
                                fit: [150, 150]
                            });
                            doc.y += 160;
                        }
                        catch (error) {
                            console.error('Failed to embed selfie:', error);
                            doc.text('Selfie image could not be embedded');
                        }
                    }
                    if (signatureDoc && this.isImageUrl(signatureDoc.documentUrl)) {
                        try {
                            doc.fontSize(14).fillColor('#1e293b').text('DIGITAL SIGNATURE');
                            doc.moveDown(0.5);
                            const signatureBuffer = await this.downloadImage(signatureDoc.documentUrl);
                            doc.image(signatureBuffer, doc.x, doc.y, {
                                width: 200,
                                height: 100,
                                fit: [200, 100]
                            });
                            doc.y += 110;
                        }
                        catch (error) {
                            console.error('Failed to embed signature:', error);
                            doc.text('Digital signature could not be embedded');
                        }
                    }
                }
                doc.moveDown(2);
                doc.fontSize(10).fillColor('#64748b');
                doc.text(`Generated on: ${new Date().toLocaleString()}`, { align: 'center' });
                doc.text('This is an official KYC verification report from SGM', { align: 'center' });
                doc.text('For verification purposes only - Not for external distribution', { align: 'center' });
                doc.end();
            }
            catch (error) {
                reject(error);
            }
        });
    }
    async getPendingVerifications(options = {}) {
        try {
            const { page = 1, limit = 10, country } = options;
            const skip = (page - 1) * limit;
            const filter = { status: types_1.KYCStatus.PENDING };
            if (country)
                filter.country = country;
            return await models_1.UserKYC.find(filter)
                .populate('userId', 'firstName lastName email')
                .sort({ createdAt: 1 })
                .skip(skip)
                .limit(limit);
        }
        catch (error) {
            throw this.handleError(error, 'getPendingVerifications');
        }
    }
    async getKYCById(kycId) {
        try {
            const kyc = await models_1.UserKYC.findById(kycId)
                .populate('userId', 'firstName lastName email phone dateOfBirth fullName')
                .populate('verifiedBy', 'firstName lastName email');
            return kyc;
        }
        catch (error) {
            throw this.handleError(error, 'getKYCById');
        }
    }
    async approveKYC(userId, verifiedBy, notes) {
        try {
            const kyc = await this.getUserKYC(userId);
            kyc.status = types_1.KYCStatus.APPROVED;
            kyc.verifiedAt = new Date();
            kyc.verifiedBy = verifiedBy;
            kyc.updatedAt = new Date();
            if (notes) {
                kyc.adminNotes = notes;
            }
            await kyc.save();
            await models_1.User.findByIdAndUpdate(userId, {
                kycStatus: 'approved',
                updatedAt: new Date()
            });
            console.log('✅ KYC approved and user status updated for user:', userId);
            await this.logAudit(userId, AuditLog_1.AuditAction.UPDATE, AuditLog_1.AuditEntity.USER_KYC, kyc._id.toString(), {
                action: 'kyc_approved',
                notes
            });
            return kyc;
        }
        catch (error) {
            throw this.handleError(error, 'approveKYC');
        }
    }
    async rejectKYC(userId, rejectionReason, verifiedBy, notes) {
        try {
            const kyc = await this.getUserKYC(userId);
            kyc.status = types_1.KYCStatus.REJECTED;
            kyc.overallRejectionReason = rejectionReason;
            kyc.verifiedAt = new Date();
            kyc.verifiedBy = verifiedBy;
            kyc.updatedAt = new Date();
            if (notes) {
                kyc.adminNotes = notes;
            }
            await kyc.save();
            await models_1.User.findByIdAndUpdate(userId, {
                kycStatus: 'rejected',
                updatedAt: new Date()
            });
            console.log('✅ KYC rejected and user status updated for user:', userId);
            await this.logAudit(userId, AuditLog_1.AuditAction.UPDATE, AuditLog_1.AuditEntity.USER_KYC, kyc._id.toString(), {
                action: 'kyc_rejected',
                rejectionReason,
                notes
            });
            return kyc;
        }
        catch (error) {
            throw this.handleError(error, 'rejectKYC');
        }
    }
    async verifyDocument(userId, documentIndex, status, rejectionReason) {
        try {
            const kyc = await this.getUserKYC(userId);
            if (documentIndex < 0 || documentIndex >= kyc.documents.length) {
                throw new Error('Document not found');
            }
            const document = kyc.documents[documentIndex];
            document.status = status;
            if (rejectionReason) {
                document.rejectionReason = rejectionReason;
            }
            if (status === types_1.KYCStatus.APPROVED) {
                document.verifiedAt = new Date();
            }
            await kyc.save();
            await this.logAudit(userId, AuditLog_1.AuditAction.UPDATE, AuditLog_1.AuditEntity.USER_KYC, kyc._id.toString(), {
                action: 'document_verification',
                documentIndex,
                status,
                rejectionReason
            });
            return kyc;
        }
        catch (error) {
            throw this.handleError(error, 'verifyDocument');
        }
    }
    async getKYCStats() {
        try {
            const stats = await models_1.UserKYC.aggregate([
                {
                    $group: {
                        _id: '$status',
                        count: { $sum: 1 }
                    }
                }
            ]);
            const totalUsers = await models_1.UserKYC.countDocuments();
            const pendingCount = stats.find(s => s._id === types_1.KYCStatus.PENDING)?.count || 0;
            const approvedCount = stats.find(s => s._id === types_1.KYCStatus.APPROVED)?.count || 0;
            const rejectedCount = stats.find(s => s._id === types_1.KYCStatus.REJECTED)?.count || 0;
            return {
                total: totalUsers,
                pending: pendingCount,
                approved: approvedCount,
                rejected: rejectedCount,
                underReview: stats.find(s => s._id === types_1.KYCStatus.UNDER_REVIEW)?.count || 0
            };
        }
        catch (error) {
            throw this.handleError(error, 'getKYCStats');
        }
    }
    async logAudit(userId, action, entity, entityId, details) {
        try {
            await models_1.AuditLog.create({
                userId: new mongoose_1.Types.ObjectId(userId),
                action,
                entity,
                entityId: new mongoose_1.Types.ObjectId(entityId),
                changes: details
            });
        }
        catch (error) {
            console.error('Failed to log audit:', error);
        }
    }
}
exports.KYCService = KYCService;
exports.kycService = new KYCService();
//# sourceMappingURL=KYCService.js.map