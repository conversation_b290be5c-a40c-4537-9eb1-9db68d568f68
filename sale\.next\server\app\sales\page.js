(()=>{var e={};e.id=117,e.ids=[117],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8273:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>C});var t=a(40969),r=a(73356),l=a(88251),i=a(66949),d=a(46411),n=a(57387),o=a(76650),c=a(2223),m=a(21857),x=a(79201),u=a(83427),h=a(78182),p=a(14493),g=a(77060),y=a(51603),v=a(31435),j=a(91798),b=a(71727),N=a(82583),f=a(8713),w=a(92386),A=a(53168),k=a(21764),T=a(99206);let P=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"paid":return"bg-sky-100 text-sky-800";default:return"bg-gray-100 text-gray-800"}},q=e=>{switch(e){case"pending":return(0,t.jsx)(m.A,{className:"w-4 h-4"});case"approved":return(0,t.jsx)(x.A,{className:"w-4 h-4"});case"paid":return(0,t.jsx)(u.A,{className:"w-4 h-4"});default:return(0,t.jsx)(h.A,{className:"w-4 h-4"})}};function C(){let[e,s]=(0,r.useState)(""),[a,h]=(0,r.useState)("all"),[C,D]=(0,r.useState)(1),{data:S,isLoading:$,error:L,refetch:M}=(0,T.aT)({page:C,limit:20,status:"all"!==a?a:void 0}),{data:R,isLoading:_}=(0,T.nK)({page:1,limit:100}),E=S?.data?.commissions||[],U=S?.data?.pagination;return(R?.data?.customers,$)?(0,t.jsx)(l.A,{title:"Sales & Commissions",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)(p.A,{className:"w-8 h-8 animate-spin text-sky-500"})})})}):L?(0,t.jsx)(l.A,{title:"Sales & Commissions",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(g.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Failed to load sales data"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Please try refreshing the page"}),(0,t.jsx)(d.$,{onClick:()=>M(),className:"bg-sky-500 hover:bg-sky-600",children:"Try Again"})]})})})}):(0,t.jsx)(l.A,{title:"Sales & Commissions",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Sales & Commissions"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Track your sales performance and commission earnings"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(d.$,{variant:"outline",className:"border-green-300 text-green-600 hover:bg-green-50",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 mr-2"}),"Export"]}),(0,t.jsxs)(d.$,{className:"bg-sky-500 hover:bg-sky-600 text-white",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"New Sale"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,t.jsx)(i.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-sky-100 rounded-lg",children:(0,t.jsx)(u.A,{className:"w-6 h-6 text-sky-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Commissions"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,k.vv)(E.reduce((e,s)=>e+(s.commissionAmount||0),0))})]})]})})}),(0,t.jsx)(i.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)(x.A,{className:"w-6 h-6 text-green-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Paid Commissions"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,k.vv)(E.filter(e=>"paid"===e.status).reduce((e,s)=>e+(s.commissionAmount||0),0))})]})]})})}),(0,t.jsx)(i.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,t.jsx)(m.A,{className:"w-6 h-6 text-yellow-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,k.vv)(E.filter(e=>"pending"===e.status).reduce((e,s)=>e+(s.commissionAmount||0),0))})]})]})})}),(0,t.jsx)(i.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,t.jsx)(j.A,{className:"w-6 h-6 text-purple-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"This Month"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:E.filter(e=>{let s=new Date(e.dealClosedDate||e.createdAt),a=new Date;return s.getMonth()===a.getMonth()&&s.getFullYear()===a.getFullYear()}).length})]})]})})})]}),(0,t.jsx)(i.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(b.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(n.p,{placeholder:"Search by customer, property, or commission ID...",value:e,onChange:e=>s(e.target.value),className:"pl-10 border-gray-300 focus:border-sky-500 focus:ring-sky-500"})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(c.l6,{value:a,onValueChange:h,children:[(0,t.jsx)(c.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,t.jsx)(c.yv,{placeholder:"Status"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"all",children:"All Status"}),(0,t.jsx)(c.eb,{value:"pending",children:"Pending"}),(0,t.jsx)(c.eb,{value:"approved",children:"Approved"}),(0,t.jsx)(c.eb,{value:"paid",children:"Paid"})]})]}),(0,t.jsxs)(d.$,{variant:"outline",className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:[(0,t.jsx)(N.A,{className:"w-4 h-4 mr-2"}),"More Filters"]})]})]})})}),(0,t.jsxs)(i.Zp,{className:"border-gray-200 shadow-sm",children:[(0,t.jsx)(i.aR,{className:"bg-white border-b border-gray-200",children:(0,t.jsxs)(i.ZB,{className:"text-gray-900",children:["Commissions (",E.length,")",U&&(0,t.jsxs)("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["Page ",U.currentPage," of ",U.totalPages,"(",U.totalItems," total)"]})]})}),(0,t.jsxs)(i.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"space-y-0",children:E.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-100 hover:bg-sky-50 transition-colors",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:q(e.status)}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:(0,k.vv)(e.commissionAmount)}),(0,t.jsx)(o.E,{className:`text-xs ${P(e.status)}`,children:e.status.toUpperCase()})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600",children:[(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"w-3 h-3 mr-1 text-green-500"}),"Deal: ",(0,k.vv)(e.dealAmount)]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(j.A,{className:"w-3 h-3 mr-1 text-sky-500"}),"Rate: ",e.commissionRate,"%"]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"w-3 h-3 mr-1 text-yellow-500"}),(0,k.Yq)(e.dealClosedDate||e.createdAt)]})]}),e.notes&&(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1 truncate",children:e.notes})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,t.jsx)(d.$,{size:"sm",variant:"outline",className:"border-sky-300 text-sky-600 hover:bg-sky-50",children:(0,t.jsx)(w.A,{className:"w-3 h-3"})}),(0,t.jsx)(d.$,{size:"sm",variant:"outline",className:"border-gray-300 text-gray-600 hover:bg-gray-50",children:(0,t.jsx)(A.A,{className:"w-3 h-3"})})]})]},e.id))}),0===E.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(u.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500",children:"No commissions found"}),(0,t.jsx)(d.$,{className:"mt-4 bg-sky-500 hover:bg-sky-600 text-white",onClick:()=>M(),children:"Refresh"})]}),U&&U.totalPages>1&&(0,t.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(U.currentPage-1)*U.itemsPerPage+1," to"," ",Math.min(U.currentPage*U.itemsPerPage,U.totalItems)," of"," ",U.totalItems," results"]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>D(U.currentPage-1),disabled:1===U.currentPage,className:"border-gray-300",children:"Previous"}),(0,t.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>D(U.currentPage+1),disabled:U.currentPage===U.totalPages,className:"border-gray-300",children:"Next"})]})]})]})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27607:(e,s,a)=>{Promise.resolve().then(a.bind(a,28863))},28863:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\sales\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\sales\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31435:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33873:e=>{"use strict";e.exports=require("path")},48471:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=a(10557),r=a(68490),l=a(13172),i=a.n(l),d=a(68835),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);a.d(s,n);let o={children:["",{children:["sales",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,28863)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\sales\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\sales\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/sales/page",pathname:"/sales",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},51603:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},53168:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77060:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},78182:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},79201:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},79551:e=>{"use strict";e.exports=require("url")},80751:(e,s,a)=>{Promise.resolve().then(a.bind(a,8273))},82583:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},92386:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},99206:(e,s,a)=>{"use strict";a.d(s,{$V:()=>d,FO:()=>o,Hu:()=>l,OT:()=>T,Pb:()=>r,WD:()=>v,Zu:()=>b,aT:()=>y,cm:()=>j,nK:()=>u,pv:()=>k,ro:()=>N});let t=a(53412).q.injectEndpoints({endpoints:e=>({getDashboardStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getDashboardActivities:e.query({query:e=>({url:"/dashboard/activities",params:e}),providesTags:["Dashboard"]}),getSalesStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getLeads:e.query({query:e=>({url:"/sales/leads",params:e}),providesTags:["Lead"]}),getLeadById:e.query({query:e=>`/sales/leads/${e}`,providesTags:(e,s,a)=>[{type:"Lead",id:a}]}),createLead:e.mutation({query:e=>({url:"/sales/leads",method:"POST",body:e}),invalidatesTags:["Lead","Dashboard"]}),updateLead:e.mutation({query:({id:e,data:s})=>({url:`/sales/leads/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:a})=>[{type:"Lead",id:a},"Lead","Dashboard"]}),deleteLead:e.mutation({query:e=>({url:`/sales/leads/${e}`,method:"DELETE"}),invalidatesTags:["Lead","Dashboard"]}),assignLead:e.mutation({query:({leadId:e,salesRepId:s})=>({url:`/sales/leads/${e}/assign`,method:"POST",body:{salesRepId:s}}),invalidatesTags:["Lead"]}),getCustomers:e.query({query:e=>({url:"/sales/customers",params:e}),providesTags:["Customer"]}),getCustomerById:e.query({query:e=>`/sales/customers/${e}`,providesTags:(e,s,a)=>[{type:"Customer",id:a}]}),createCustomer:e.mutation({query:e=>({url:"/sales/customers",method:"POST",body:e}),invalidatesTags:["Customer","Dashboard"]}),updateCustomer:e.mutation({query:({id:e,data:s})=>({url:`/sales/customers/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:a})=>[{type:"Customer",id:a},"Customer"]}),getCommissions:e.query({query:e=>({url:"/sales/commissions",params:e}),providesTags:["Commission"]}),getSalesTargets:e.query({query:()=>"/sales/targets",providesTags:["Report"]}),createSalesTarget:e.mutation({query:e=>({url:"/sales/targets",method:"POST",body:e}),invalidatesTags:["Report","Dashboard"]}),updateSalesTarget:e.mutation({query:({id:e,data:s})=>({url:`/sales/targets/${e}`,method:"PUT",body:s}),invalidatesTags:["Report","Dashboard"]}),getFollowUps:e.query({query:e=>({url:"/follow-ups",params:e}),providesTags:["Task"]}),createFollowUp:e.mutation({query:e=>({url:"/follow-ups",method:"POST",body:e}),invalidatesTags:["Task","Dashboard"]}),updateFollowUp:e.mutation({query:({id:e,data:s})=>({url:`/follow-ups/${e}`,method:"PUT",body:s}),invalidatesTags:["Task","Dashboard"]}),deleteFollowUp:e.mutation({query:e=>({url:`/follow-ups/${e}`,method:"DELETE"}),invalidatesTags:["Task","Dashboard"]})})}),{useGetDashboardStatsQuery:r,useGetDashboardActivitiesQuery:l,useGetSalesStatsQuery:i,useGetLeadsQuery:d,useGetLeadByIdQuery:n,useCreateLeadMutation:o,useUpdateLeadMutation:c,useDeleteLeadMutation:m,useAssignLeadMutation:x,useGetCustomersQuery:u,useGetCustomerByIdQuery:h,useCreateCustomerMutation:p,useUpdateCustomerMutation:g,useGetCommissionsQuery:y,useGetSalesTargetsQuery:v,useCreateSalesTargetMutation:j,useUpdateSalesTargetMutation:b,useGetFollowUpsQuery:N,useCreateFollowUpMutation:f,useUpdateFollowUpMutation:w,useDeleteFollowUpMutation:A}=t,{useGetCustomersQuery:k,useGetCommissionsQuery:T}=t}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[755,598,544,29,796,286,447,512],()=>a(48471));module.exports=t})();