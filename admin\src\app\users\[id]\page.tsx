'use client'

import React, { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { useGetUserDetailsQuery } from '@/store/api/adminUserApi'
import { useUpdateKYCStatusMutation, useDownloadKYCPDFMutation } from '@/store/api/adminKycApi'
import {
  useGetUserInvestmentsQuery,
  useGetUserTransactionsQuery,
  useGetUserActivityQuery,
  useGetUserStatsQuery,
  useGetUserPortfolioPerformanceQuery
} from '@/store/api/userDetailsApi'
import {
  ArrowLeft,
  Edit,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Shield,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  FileText,
  Download,
  Eye,
  Wallet,
  TrendingUp,
  Activity,
  CreditCard,
  Building,
  Globe,
  Users,
  Star,
  Award,
  Briefcase
} from 'lucide-react'

export default function UserDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const userId = params?.id as string
  
  const { data: userResponse, isLoading: userLoading, error: userError, refetch: refetchUser } = useGetUserDetailsQuery(userId)
  const [updateKYCStatus, { isLoading: isUpdatingKYC }] = useUpdateKYCStatusMutation()
  const [downloadKYCPDFMutation, { isLoading: isDownloadingPDF }] = useDownloadKYCPDFMutation()

  // User details APIs
  const { data: investmentsResponse, isLoading: investmentsLoading } = useGetUserInvestmentsQuery({
    userId,
    page: 1,
    limit: 10
  })
  const { data: transactionsResponse, isLoading: transactionsLoading } = useGetUserTransactionsQuery({
    userId,
    page: 1,
    limit: 10
  })
  const { data: activityResponse, isLoading: activityLoading } = useGetUserActivityQuery({
    userId,
    page: 1,
    limit: 20
  })
  const { data: statsResponse, isLoading: statsLoading } = useGetUserStatsQuery(userId)
  const { data: portfolioResponse, isLoading: portfolioLoading } = useGetUserPortfolioPerformanceQuery({
    userId,
    period: '1Y'
  })

  // State hooks - must be at the top level before any conditional returns
  const [selectedTab, setSelectedTab] = useState('overview')
  const [showKYCModal, setShowKYCModal] = useState(false)
  const [kycAction, setKycAction] = useState<'approve' | 'reject' | ''>('')
  const [rejectionReason, setRejectionReason] = useState('')
  const [reviewNotes, setReviewNotes] = useState('')

  const user = userResponse?.user
  const kyc = userResponse?.kyc
  const wallet = userResponse?.wallet
  const investments = investmentsResponse?.data?.investments || []
  const transactions = transactionsResponse?.data?.transactions || []
  const activities = activityResponse?.data?.activities || []
  const userStats = statsResponse?.data
  const portfolioData = portfolioResponse?.data

  // Loading states
  const isLoading = userLoading

  // Error handling
  if (userError) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-2">Error Loading User</h2>
            <p className="text-gray-600">Failed to load user details. Please try again.</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  // Loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading user details...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  // User not found
  if (!user) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">User Not Found</h2>
            <p className="text-gray-600">The requested user could not be found.</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  // KYC action handlers
  const handleKYCAction = (action: 'approve' | 'reject') => {
    setKycAction(action)
    setShowKYCModal(true)
    setRejectionReason('')
    setReviewNotes('')
  }

  const handleKYCSubmit = async () => {
    if (!kyc?._id) return

    try {
      const data = {
        status: kycAction === 'approve' ? 'approved' as const : 'rejected' as const,
        ...(kycAction === 'reject' && rejectionReason && { rejectionReason }),
        ...(reviewNotes && { notes: reviewNotes }),
        level: 'basic' as const
      }

      console.log('Updating KYC status:', { id: kyc._id, data })
      await updateKYCStatus({ id: kyc._id, data }).unwrap()

      toast.success(`KYC ${kycAction === 'approve' ? 'approved' : 'rejected'} successfully`)
      setShowKYCModal(false)
      setKycAction('')
      setRejectionReason('')
      setReviewNotes('')
      refetchUser()
    } catch (error: any) {
      console.error('KYC update error:', error)
      toast.error(error?.data?.message || `Failed to ${kycAction} KYC`)
    }
  }

  const downloadKYCPDF = async () => {
    if (!kyc?._id) return

    try {
      const blob = await downloadKYCPDFMutation(kyc._id).unwrap()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `KYC_${user?.firstName}_${user?.lastName}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      toast.success('KYC PDF downloaded successfully')
    } catch (error) {
      console.error('PDF download error:', error)
      toast.error('Failed to download KYC PDF')
    }
  }

  if (userLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading user details...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (userError || !user) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">User not found</h3>
            <p className="text-gray-600 mb-4">The user you're looking for doesn't exist or has been removed.</p>
            <Button onClick={() => router.push('/users')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Users
            </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'inactive': return 'bg-gray-100 text-gray-800'
      case 'suspended': return 'bg-red-100 text-red-800'
      case 'pending_verification': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getKYCStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      case 'under_review': return 'bg-blue-100 text-blue-800'
      case 'not_started': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getKYCStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4" />
      case 'pending': return <Clock className="h-4 w-4" />
      case 'rejected': return <XCircle className="h-4 w-4" />
      case 'under_review': return <Activity className="h-4 w-4" />
      case 'not_started': return <AlertCircle className="h-4 w-4" />
      default: return <AlertCircle className="h-4 w-4" />
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'ADMIN': return <Badge className="bg-purple-100 text-purple-800">Admin</Badge>
      case 'SUBADMIN': return <Badge className="bg-blue-100 text-blue-800">Sub Admin</Badge>
      case 'SALES': return <Badge className="bg-orange-100 text-orange-800">Sales</Badge>
      case 'USER': return <Badge className="bg-gray-100 text-gray-800">User</Badge>
      default: return <Badge variant="secondary">{role}</Badge>
    }
  }



  return (
    <DashboardLayout>
      <div className="h-full overflow-auto">
        <div className="max-w-7xl mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg p-6 text-white">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div className="flex items-center gap-4">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => router.back()}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
                <Avatar className="h-16 w-16 border-4 border-white/30">
                  <AvatarImage src="" />
                  <AvatarFallback className="bg-white/20 text-white text-xl font-bold">
                    {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h1 className="text-3xl font-bold mb-2">
                    {user.firstName} {user.lastName}
                  </h1>
                  <div className="flex items-center gap-3">
                    <Badge className={getStatusColor(user.status)}>
                      {user.status?.replace('_', ' ') || 'Unknown'}
                    </Badge>
                    {getRoleBadge(user.role)}
                    <Badge className={getKYCStatusColor(user.kyc?.status || 'not_submitted')}>
                      {getKYCStatusIcon(user.kyc?.status || 'not_submitted')}
                      <span className="ml-1">{user.kyc?.status?.replace('_', ' ') || 'Not Submitted'}</span>
                    </Badge>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Button
                  onClick={() => router.push(`/users/${userId}/edit`)}
                  className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit User
                </Button>
                <Button
                  variant="secondary"
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </Button>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Investments</p>
                    <p className="text-2xl font-bold text-gray-900">
                      ₹{(userStats?.totalInvestments || 0).toLocaleString()}
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Wallet Balance</p>
                    <p className="text-2xl font-bold text-gray-900">
                      ₹{(user.wallet?.balance || 0).toLocaleString()}
                    </p>
                  </div>
                  <Wallet className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Returns</p>
                    <p className="text-2xl font-bold text-gray-900">
                      ₹{(userStats?.totalReturns || 0).toLocaleString()}
                    </p>
                  </div>
                  <Award className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Member Since</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {new Date(user.createdAt).toLocaleDateString('en-US', { 
                        month: 'short', 
                        year: 'numeric' 
                      })}
                    </p>
                  </div>
                  <Calendar className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Tabs */}
          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="kyc">KYC Details</TabsTrigger>
              <TabsTrigger value="investments">Investments</TabsTrigger>
              <TabsTrigger value="transactions">Transactions</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Personal Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-600">First Name</p>
                        <p className="font-medium">{user.firstName}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Last Name</p>
                        <p className="font-medium">{user.lastName}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Email</p>
                        <p className="font-medium flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          {user.email}
                          {user.status === 'active' && <CheckCircle className="h-4 w-4 text-green-600" />}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Phone</p>
                        <p className="font-medium flex items-center gap-2">
                          <Phone className="h-4 w-4" />
                          {user.phone || 'Not provided'}
                          {user.phone && <CheckCircle className="h-4 w-4 text-green-600" />}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Date of Birth</p>
                        <p className="font-medium">
                          {'Not provided'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Referral Code</p>
                        <p className="font-medium">{'Not generated'}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Wallet className="h-5 w-5" />
                      Wallet Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4">
                      <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <span className="text-sm text-gray-600">Current Balance</span>
                        <span className="font-semibold text-blue-600">₹{(user.wallet?.balance || 0).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <span className="text-sm text-gray-600">Total Invested</span>
                        <span className="font-semibold text-green-600">₹{(userStats?.totalInvestments || 0).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                        <span className="text-sm text-gray-600">Total Returns</span>
                        <span className="font-semibold text-purple-600">₹{(userStats?.totalReturns || 0).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                        <span className="text-sm text-gray-600">Portfolio Value</span>
                        <span className="font-semibold text-orange-600">₹{(userStats?.portfolioValue || 0).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                        <span className="text-sm text-gray-600">ROI</span>
                        <span className="font-semibold text-red-600">{(userStats?.roi || 0).toFixed(2)}%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Portfolio Summary */}
                {userStats && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5" />
                        Portfolio Summary
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                          <span className="text-sm text-gray-600">Total Investments</span>
                          <span className="font-semibold text-blue-600">{userStats.totalInvestments}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                          <span className="text-sm text-gray-600">Active Investments</span>
                          <span className="font-semibold text-green-600">{userStats.activeInvestments}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                          <span className="text-sm text-gray-600">Portfolio Value</span>
                          <span className="font-semibold text-purple-600">₹{userStats.portfolioValue.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                          <span className="text-sm text-gray-600">Overall ROI</span>
                          <span className={`font-semibold ${userStats.roi >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {userStats.roi}%
                          </span>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 gap-4">
                        <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                          <span className="text-sm text-gray-600">Member Since</span>
                          <span className="font-semibold">{new Date(userStats.joinDate).toLocaleDateString()}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                          <span className="text-sm text-gray-600">Last Activity</span>
                          <span className="font-semibold">{new Date(userStats.lastActivity).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>

            {/* KYC Details Tab */}
            <TabsContent value="kyc" className="space-y-6">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading KYC details...</span>
                </div>
              ) : !kyc ? (
                <Card>
                  <CardContent className="p-12 text-center">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No KYC Submission</h3>
                    <p className="text-gray-600">This user hasn't submitted KYC documents yet.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-6">
                  {/* KYC Status Card */}
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-2">
                          <Shield className="h-5 w-5" />
                          KYC Status & Actions
                        </CardTitle>
                        <div className="flex items-center gap-2">
                          <Badge className={getKYCStatusColor(kyc.status)}>
                            {getKYCStatusIcon(kyc.status)}
                            <span className="ml-1">{kyc.status?.replace('_', ' ') || 'Unknown'}</span>
                          </Badge>
                          <Badge variant="outline">{kyc.level} Level</Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div>
                          <p className="text-sm text-gray-600">Submitted At</p>
                          <p className="font-medium">
                            {kyc.submittedAt ? new Date(kyc.submittedAt).toLocaleString() : 'Not submitted'}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Reviewed At</p>
                          <p className="font-medium">
                            {kyc.reviewedAt ? new Date(kyc.reviewedAt).toLocaleString() : 'Not reviewed'}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Reviewed By</p>
                          <p className="font-medium">
                            {kyc.reviewedBy ? `${kyc.reviewedBy.firstName} ${kyc.reviewedBy.lastName}` : 'Not reviewed'}
                          </p>
                        </div>
                      </div>

                      {kyc.rejectionReason && (
                        <div className="p-4 bg-red-50 border border-red-200 rounded-lg mb-6">
                          <h4 className="font-medium text-red-800 mb-2">Rejection Reason</h4>
                          <p className="text-red-700">{kyc.rejectionReason}</p>
                        </div>
                      )}

                      <div className="flex items-center gap-3">
                        {(kyc.status === 'pending' || kyc.status === 'under_review') && (
                          <>
                            <Button
                              onClick={() => handleKYCAction('approve')}
                              disabled={isUpdatingKYC}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CheckCircle className="h-4 w-4 mr-2" />
                              Approve KYC
                            </Button>
                            <Button
                              onClick={() => handleKYCAction('reject')}
                              disabled={isUpdatingKYC}
                              variant="destructive"
                            >
                              <XCircle className="h-4 w-4 mr-2" />
                              Reject KYC
                            </Button>
                          </>
                        )}
                        <Button
                          onClick={downloadKYCPDF}
                          variant="outline"
                          className="ml-auto"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download PDF
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Personal Information */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <User className="h-5 w-5" />
                          Personal Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-600">Nationality</p>
                            <p className="font-medium">{kyc.personalInfo.nationality || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Place of Birth</p>
                            <p className="font-medium">{kyc.personalInfo.placeOfBirth || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Gender</p>
                            <p className="font-medium">{kyc.personalInfo.gender || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Marital Status</p>
                            <p className="font-medium">{kyc.personalInfo.maritalStatus || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Occupation</p>
                            <p className="font-medium">{(kyc.personalInfo as any).occupation || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Employer</p>
                            <p className="font-medium">{(kyc.personalInfo as any).employerName || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Annual Income</p>
                            <p className="font-medium">
                              {(kyc.personalInfo as any).annualIncome ? `₹${(kyc.personalInfo as any).annualIncome.toLocaleString()}` : 'Not provided'}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Source of Funds</p>
                            <p className="font-medium">{(kyc.personalInfo as any).sourceOfFunds || 'Not provided'}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <MapPin className="h-5 w-5" />
                          Address Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-3">
                          <div>
                            <p className="text-sm text-gray-600">Street Address</p>
                            <p className="font-medium">{kyc.address.street || 'Not provided'}</p>
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-600">City</p>
                              <p className="font-medium">{kyc.address.city || 'Not provided'}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">State</p>
                              <p className="font-medium">{kyc.address.state || 'Not provided'}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Postal Code</p>
                              <p className="font-medium">{kyc.address.postalCode || 'Not provided'}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Country</p>
                              <p className="font-medium">{kyc.address.country || 'Not provided'}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Address Type</p>
                              <p className="font-medium">{kyc.address.addressType || 'Not provided'}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Residence Since</p>
                              <p className="font-medium">
                                {kyc.address.residenceSince ? new Date(kyc.address.residenceSince).toLocaleDateString() : 'Not provided'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Identity & Bank Information */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <CreditCard className="h-5 w-5" />
                          Identity Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-4">
                          <div>
                            <p className="text-sm text-gray-600">Aadhar Number</p>
                            <p className="font-medium">{kyc.identityInfo?.aadharNumber || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">PAN Number</p>
                            <p className="font-medium">{kyc.identityInfo?.panNumber || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Passport Number</p>
                            <p className="font-medium">{kyc.identityInfo?.passportNumber || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Driving License</p>
                            <p className="font-medium">{kyc.identityInfo?.drivingLicenseNumber || 'Not provided'}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Building className="h-5 w-5" />
                          Bank Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-4">
                          <div>
                            <p className="text-sm text-gray-600">Account Number</p>
                            <p className="font-medium">{kyc.bankInfo?.accountNumber || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">IFSC Code</p>
                            <p className="font-medium">{kyc.bankInfo?.ifscCode || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Bank Name</p>
                            <p className="font-medium">{kyc.bankInfo?.bankName || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Account Type</p>
                            <p className="font-medium">{kyc.bankInfo?.accountType || 'Not provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Account Holder Name</p>
                            <p className="font-medium">{kyc.bankInfo?.accountHolderName || 'Not provided'}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Documents */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Uploaded Documents
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {kyc.documents && kyc.documents.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {kyc.documents.map((doc) => (
                            <div key={doc._id} className="p-4 border border-gray-200 rounded-lg">
                              <div className="flex items-start justify-between mb-2">
                                <div>
                                  <h4 className="font-medium text-gray-900">{doc.type}</h4>
                                  <p className="text-sm text-gray-600">{doc.subType}</p>
                                </div>
                                <Badge className={
                                  doc.status === 'verified' ? 'bg-green-100 text-green-800' :
                                  doc.status === 'rejected' ? 'bg-red-100 text-red-800' :
                                  'bg-yellow-100 text-yellow-800'
                                }>
                                  {doc.status}
                                </Badge>
                              </div>

                              {doc.documentNumber && (
                                <p className="text-sm text-gray-600 mb-2">
                                  <strong>Number:</strong> {doc.documentNumber}
                                </p>
                              )}

                              <div className="flex items-center gap-2">
                                <Button size="sm" variant="outline">
                                  <Eye className="h-4 w-4 mr-2" />
                                  View
                                </Button>
                                <Button size="sm" variant="outline">
                                  <Download className="h-4 w-4 mr-2" />
                                  Download
                                </Button>
                              </div>

                              {doc.rejectionReason && (
                                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                                  <strong>Rejected:</strong> {doc.rejectionReason}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8">
                          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">No documents uploaded</h3>
                          <p className="text-gray-600">User hasn't uploaded any KYC documents yet.</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              )}
            </TabsContent>

            {/* Other tabs placeholder */}
            <TabsContent value="investments">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Investment Portfolio
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {investments.length > 0 ? (
                    <div className="space-y-4">
                      {investments.map((investment) => (
                        <div key={investment._id} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h4 className="font-semibold">{investment.propertyName}</h4>
                              <p className="text-sm text-muted-foreground">{investment.location}</p>
                            </div>
                            <Badge variant={investment.status === 'active' ? 'default' : 'secondary'}>
                              {investment.status}
                            </Badge>
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <p className="text-muted-foreground">Stocks Owned</p>
                              <p className="font-medium">{investment.stocksOwned}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Total Investment</p>
                              <p className="font-medium">₹{investment.totalInvestment.toLocaleString()}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Current Value</p>
                              <p className="font-medium">₹{investment.currentValue.toLocaleString()}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">ROI</p>
                              <p className={`font-medium ${investment.roi >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {investment.roi}%
                              </p>
                            </div>
                          </div>
                          <div className="mt-3 pt-3 border-t">
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">Purchase Date:</span>
                              <span>{new Date(investment.purchaseDate).toLocaleDateString()}</span>
                            </div>
                            <div className="flex justify-between text-sm mt-1">
                              <span className="text-muted-foreground">Returns Received:</span>
                              <span className="font-medium">₹{investment.returns.toLocaleString()}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Building className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No investments found</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="transactions">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Transaction History
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {transactions.length > 0 ? (
                    <div className="space-y-4">
                      {transactions.map((transaction) => (
                        <div key={transaction._id} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h4 className="font-semibold capitalize">{transaction.type}</h4>
                              <p className="text-sm text-muted-foreground">{transaction.description}</p>
                              {transaction.propertyName && (
                                <p className="text-sm text-blue-600">{transaction.propertyName}</p>
                              )}
                            </div>
                            <div className="text-right">
                              <p className={`font-semibold ${
                                transaction.type === 'deposit' || transaction.type === 'return'
                                  ? 'text-green-600'
                                  : 'text-red-600'
                              }`}>
                                {transaction.type === 'deposit' || transaction.type === 'return' ? '+' : '-'}
                                ₹{transaction.amount.toLocaleString()}
                              </p>
                              <Badge variant={
                                transaction.status === 'completed' ? 'default' :
                                transaction.status === 'pending' ? 'secondary' :
                                transaction.status === 'failed' ? 'destructive' : 'outline'
                              }>
                                {transaction.status}
                              </Badge>
                            </div>
                          </div>
                          <div className="flex justify-between items-center text-sm text-muted-foreground">
                            <span>{new Date(transaction.createdAt).toLocaleDateString()}</span>
                            {transaction.paymentMethod && (
                              <span className="capitalize">{transaction.paymentMethod}</span>
                            )}
                            {transaction.reference && (
                              <span className="font-mono text-xs">Ref: {transaction.reference}</span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No transactions found</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="activity">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    User Activity Log
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {activities.length > 0 ? (
                    <div className="space-y-4">
                      {activities.map((activity) => (
                        <div key={activity._id} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h4 className="font-semibold capitalize">{activity.action}</h4>
                              <p className="text-sm text-muted-foreground">{activity.description}</p>
                            </div>
                            <Badge variant="outline" className="capitalize">
                              {activity.entity}
                            </Badge>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground mt-3">
                            <div>
                              <span className="font-medium">Date:</span>
                              <p>{new Date(activity.createdAt).toLocaleString()}</p>
                            </div>
                            {activity.ipAddress && (
                              <div>
                                <span className="font-medium">IP Address:</span>
                                <p className="font-mono">{activity.ipAddress}</p>
                              </div>
                            )}
                            {activity.location && (
                              <div>
                                <span className="font-medium">Location:</span>
                                <p>{activity.location}</p>
                              </div>
                            )}
                          </div>
                          {activity.metadata && Object.keys(activity.metadata).length > 0 && (
                            <div className="mt-3 pt-3 border-t">
                              <span className="text-sm font-medium text-muted-foreground">Additional Details:</span>
                              <pre className="text-xs bg-gray-50 p-2 rounded mt-1 overflow-x-auto">
                                {JSON.stringify(activity.metadata, null, 2)}
                              </pre>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No activity found</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* KYC Action Modal */}
      <Dialog open={showKYCModal} onOpenChange={setShowKYCModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {kycAction === 'approve' ? 'Approve KYC' : 'Reject KYC'}
            </DialogTitle>
            <DialogDescription>
              {kycAction === 'approve'
                ? 'Are you sure you want to approve this KYC application?'
                : 'Please provide a reason for rejecting this KYC application.'
              }
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {kycAction === 'reject' && (
              <div>
                <Label htmlFor="rejectionReason">Rejection Reason *</Label>
                <Textarea
                  id="rejectionReason"
                  placeholder="Enter the reason for rejection..."
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  className="mt-1"
                />
              </div>
            )}

            <div>
              <Label htmlFor="reviewNotes">Review Notes (Optional)</Label>
              <Textarea
                id="reviewNotes"
                placeholder="Add any additional notes..."
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                className="mt-1"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowKYCModal(false)}
              disabled={isUpdatingKYC}
            >
              Cancel
            </Button>
            <Button
              onClick={handleKYCSubmit}
              disabled={isUpdatingKYC || (kycAction === 'reject' && !rejectionReason.trim())}
              className={kycAction === 'approve' ? 'bg-green-600 hover:bg-green-700' : ''}
              variant={kycAction === 'reject' ? 'destructive' : 'default'}
            >
              {isUpdatingKYC ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  {kycAction === 'approve' ? (
                    <CheckCircle className="h-4 w-4 mr-2" />
                  ) : (
                    <XCircle className="h-4 w-4 mr-2" />
                  )}
                  {kycAction === 'approve' ? 'Approve' : 'Reject'}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  )
}
