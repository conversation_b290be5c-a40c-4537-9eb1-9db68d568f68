(()=>{var e={};e.id=4520,e.ids=[4520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4942:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(62544);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},5568:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9184:(e,t,s)=>{Promise.resolve().then(s.bind(s,36570))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22324:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},26806:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36570:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\login\\page.tsx","default")},40002:(e,t,s)=>{"use strict";s.d(t,{B3:()=>n,Lv:()=>u,Ng:()=>i,_L:()=>r,ac:()=>f,ge:()=>a,nd:()=>d,pv:()=>p,tl:()=>c,uU:()=>o});let{useLoginMutation:r,useRegisterMutation:a,useLogoutMutation:i,useRefreshTokenMutation:l,useVerifyEmailMutation:o,useResendVerificationEmailMutation:n,useForgotPasswordMutation:d,useVerifyPasswordResetOTPMutation:c,useResetPasswordMutation:u,useChangePasswordMutation:m,useVerifyPhoneMutation:h,useSendPhoneOTPMutation:x,useSendEmailOTPMutation:f,useVerifyEmailOTPMutation:p,useGetCurrentUserQuery:g,useGetUserProfileQuery:y,useCheckAuthStatusQuery:v}=s(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({login:e.mutation({query:e=>({url:"/auth/login",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),register:e.mutation({query:e=>({url:"/auth/register",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),logout:e.mutation({query:()=>({url:"/auth/logout",method:"POST"}),invalidatesTags:["Auth","User"]}),refreshToken:e.mutation({query:e=>({url:"/auth/refresh",method:"POST",body:e})}),verifyEmail:e.mutation({query:e=>({url:"/auth/verify-email",method:"POST",body:e}),invalidatesTags:["User"]}),resendVerificationEmail:e.mutation({query:e=>({url:"/auth/send-verification",method:"POST",body:e})}),forgotPassword:e.mutation({query:e=>({url:"/auth/forgot-password",method:"POST",body:e})}),verifyPasswordResetOTP:e.mutation({query:e=>({url:"/auth/verify-reset-otp",method:"POST",body:e})}),resetPassword:e.mutation({query:e=>({url:"/auth/reset-password",method:"POST",body:e})}),changePassword:e.mutation({query:e=>({url:"/auth/change-password",method:"POST",body:e}),invalidatesTags:["User"]}),verifyPhone:e.mutation({query:e=>({url:"/auth/verify-phone",method:"POST",body:e}),invalidatesTags:["User"]}),sendPhoneOTP:e.mutation({query:e=>({url:"/auth/send-phone-otp",method:"POST",body:e})}),sendEmailOTP:e.mutation({query:e=>({url:"/auth/send-otp",method:"POST",body:e})}),verifyEmailOTP:e.mutation({query:e=>({url:"/auth/verify-otp",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),getCurrentUser:e.query({query:()=>"/auth/me",providesTags:["User"]}),checkAuthStatus:e.query({query:()=>"/auth/status",providesTags:["Auth"]}),getUserProfile:e.query({query:()=>"/auth/profile",providesTags:["User","Auth"]})})})},46344:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>T});var r=s(40969),a=s(73356),i=s(12011),l=s(27092),o=s.n(l),n=s(58121),d=s(66782),c=s(65406),u=s(26806),m=s(85538),h=s(22324),x=s(5568),f=s(5825),p=s(75694),g=s(46411),y=s(66949),v=s(40002),b=s(92462),j=s(61631),w=s(77038),N=s(1507);let P=c.Ik({email:c.Yj().email("Please enter a valid email address"),password:c.Yj().min(6,"Password must be at least 6 characters"),rememberMe:c.zM().optional(),referralCode:c.Yj().optional()});function k(){let e=(0,i.useRouter)(),t=(0,i.useSearchParams)(),s=(0,b.jL)();(0,b.GV)(j.Kc);let[l,c]=(0,a.useState)(!1),[k,{isLoading:T}]=(0,v._L)(),{getNextStepForUser:A}=(0,w.YK)(),{register:q,handleSubmit:S,formState:{errors:C},setError:M,setValue:O}=(0,n.mN)({resolver:(0,d.u)(P),defaultValues:{email:"",password:"",rememberMe:!1,referralCode:""}}),U=async r=>{try{let a=await k(r).unwrap();if(a.success&&a.data){s((0,j.LA)({user:a.data.user,token:a.data.token,refreshToken:a.data.refreshToken}));let r=A(a.data.user);if("kyc"===r)N.toast.info("Please complete your KYC verification to unlock all features."),e.push("/kyc");else if("email_verification"===r)N.toast.warning("Please verify your email to activate your account."),e.push("/dashboard?verification=required");else{let s=t.get("redirect")||document.cookie.split("; ").find(e=>e.startsWith("redirectTo="))?.split("=")[1]||localStorage.getItem("redirectTo")||"/dashboard";document.cookie="redirectTo=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;",localStorage.removeItem("redirectTo"),N.toast.success("Login successful! Welcome back."),setTimeout(()=>{console.log("\uD83D\uDD04 User: Redirecting after login success to:",s),e.push(decodeURIComponent(s))},500)}}}catch(e){console.error("Login error:",e),401===e.status?(M("email",{message:"Invalid email or password"}),M("password",{message:"Invalid email or password"})):e.data?.message?N.toast.error(e.data.message):N.toast.error("Login failed. Please try again.")}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8 animate-fade-in",children:[(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsx)(p.A,{size:"lg",variant:"full"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-black mb-2",children:"Welcome Back"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Continue your investment journey"}),(0,r.jsx)("div",{className:"w-24 h-1 bg-sky-500 mx-auto mt-4 rounded-full"})]}),(0,r.jsxs)(y.Zp,{className:"shadow-2xl border-0 backdrop-blur-sm bg-white/90 relative overflow-hidden animate-scale-in",children:[(0,r.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500"}),(0,r.jsxs)(y.aR,{className:"space-y-1 relative",children:[(0,r.jsx)(y.ZB,{className:"text-2xl font-bold text-center bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent",children:"Sign In"}),(0,r.jsx)(y.BT,{className:"text-center text-gray-600",children:"Enter your credentials to access your account"})]}),(0,r.jsxs)(y.Wu,{children:[(0,r.jsxs)("form",{onSubmit:S(U),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...q("email"),type:"email",id:"email",placeholder:"Enter your email",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),C.email&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:C.email.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...q("password"),type:l?"text":"password",id:"password",placeholder:"Enter your password",className:"w-full pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)("button",{type:"button",onClick:()=>c(!l),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:l?(0,r.jsx)(h.A,{className:"h-4 w-4"}):(0,r.jsx)(x.A,{className:"h-4 w-4"})})]}),C.password&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:C.password.message})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{...q("rememberMe"),type:"checkbox",id:"rememberMe",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"rememberMe",className:"ml-2 text-sm text-gray-700",children:"Remember me"})]}),(0,r.jsx)(o(),{href:"/forgot-password",className:"text-sm text-blue-600 hover:text-blue-500",children:"Forgot password?"})]}),(0,r.jsx)(g.$,{type:"submit",disabled:T,className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors",children:T?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Signing in..."]}):"Sign In"})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Don't have an account?"})})]})}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsx)(o(),{href:"/register",className:"text-blue-600 hover:text-blue-500 font-medium",children:"Create a new account"})})]})]}),(0,r.jsx)("div",{className:"mt-8 text-center text-sm text-gray-600",children:(0,r.jsxs)("p",{children:["By signing in, you agree to our"," ",(0,r.jsx)(o(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",(0,r.jsx)(o(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})})]})})}function T(){return(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),children:(0,r.jsx)(k,{})})}},61345:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(10557),a=s(68490),i=s(13172),l=s.n(i),o=s(68835),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(t,n);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36570)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\login\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72736:(e,t,s)=>{Promise.resolve().then(s.bind(s,46344))},75694:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(40969);s(73356);let a=({size:e="md",variant:t="full",className:s=""})=>{let a={sm:"h-8 w-8",md:"h-12 w-12",lg:"h-16 w-16",xl:"h-24 w-24"},i={sm:"text-lg",md:"text-2xl",lg:"text-3xl",xl:"text-4xl"},l=()=>(0,r.jsx)("div",{className:`${a[e]} ${s} relative`,children:(0,r.jsxs)("svg",{viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"w-full h-full",children:[(0,r.jsx)("circle",{cx:"50",cy:"50",r:"48",fill:"#0ea5e9",stroke:"#ffffff",strokeWidth:"2"}),(0,r.jsxs)("g",{transform:"translate(20, 25)",children:[(0,r.jsx)("rect",{x:"15",y:"20",width:"30",height:"35",fill:"#ffffff",rx:"2"}),(0,r.jsx)("rect",{x:"20",y:"25",width:"6",height:"6",fill:"#0ea5e9"}),(0,r.jsx)("rect",{x:"34",y:"25",width:"6",height:"6",fill:"#0ea5e9"}),(0,r.jsx)("rect",{x:"20",y:"35",width:"6",height:"6",fill:"#0ea5e9"}),(0,r.jsx)("rect",{x:"34",y:"35",width:"6",height:"6",fill:"#0ea5e9"}),(0,r.jsx)("rect",{x:"27",y:"45",width:"6",height:"10",fill:"#fbbf24"}),(0,r.jsx)("rect",{x:"5",y:"30",width:"8",height:"25",fill:"#ffffff",rx:"1"}),(0,r.jsx)("rect",{x:"47",y:"35",width:"8",height:"20",fill:"#ffffff",rx:"1"}),(0,r.jsx)("path",{d:"M10 15 L20 5 L30 10 L40 2 L50 8",stroke:"#fbbf24",strokeWidth:"3",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("polygon",{points:"45,2 50,8 45,8",fill:"#fbbf24"})]}),(0,r.jsx)("text",{x:"50",y:"75",textAnchor:"middle",fill:"#ffffff",fontSize:"12",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:"SGM"})]})});switch(t){case"icon":return(0,r.jsx)(l,{});case"text":return(0,r.jsx)(()=>(0,r.jsxs)("div",{className:`${s} flex items-center`,children:[(0,r.jsx)("span",{className:`${i[e]} font-bold sgm-accent-text`,children:"SGM"}),(0,r.jsx)("span",{className:`${i[e]} font-light sgm-primary-text ml-1`,children:"Investments"})]}),{});default:return(0,r.jsx)(()=>(0,r.jsxs)("div",{className:`${s} flex items-center space-x-3`,children:[(0,r.jsx)(l,{}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:`${i[e]} font-bold sgm-accent-text leading-none`,children:"SGM"}),(0,r.jsx)("span",{className:"text-sm sgm-primary-text leading-none",children:"Investments"})]})]}),{})}}},79551:e=>{"use strict";e.exports=require("url")},85538:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[755,3777,2544,7092,3035,2487],()=>s(61345));module.exports=r})();