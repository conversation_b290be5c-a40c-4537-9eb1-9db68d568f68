(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{2855:(e,t,s)=>{"use strict";s.d(t,{SettingsProvider:()=>n,YK:()=>d});var a=s(9605),i=s(9585);let{useGetPublicSettingsQuery:r}=s(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getPublicSettings:e.query({query:()=>"/settings/public",providesTags:["Settings"]})})}),o=(0,i.createContext)(void 0),n=e=>{let{children:t}=e,[s,n]=(0,i.useState)({}),{data:l,isLoading:d,error:c,refetch:u}=r(void 0,{skip:!1,refetchOnMountOrArgChange:!0,refetchOnFocus:!1,refetchOnReconnect:!0});return(0,i.useEffect)(()=>{var e;(null==l?void 0:l.success)&&(null==(e=l.data)?void 0:e.settings)&&n(e=>{var t;return{...e,...null==(t=l.data)?void 0:t.settings}})},[l]),(0,i.useEffect)(()=>{c&&console.warn("Failed to load settings from server, using defaults:",c)},[c]),(0,i.useEffect)(()=>{let e=setInterval(()=>{u()},3e5);return()=>clearInterval(e)},[u]),(0,a.jsx)(o.Provider,{value:{settings:s,isLoading:d,error:c,isEmailVerificationEnabled:()=>{var e;return null==(e=s.EMAIL_VERIFICATION_ENABLED)||e},isPhoneVerificationEnabled:()=>{var e;return null!=(e=s.PHONE_VERIFICATION_ENABLED)&&e},isKYCRequired:()=>{var e;return null==(e=s.KYC_REQUIRED_FOR_ACTIVATION)||e},isKYCPriorityOverEmail:()=>{var e;return null==(e=s.KYC_PRIORITY_OVER_EMAIL)||e},allowKYCForInactiveUsers:()=>{var e;return null==(e=s.ALLOW_KYC_FOR_INACTIVE_USERS)||e},getMinInvestmentAmount:()=>{var e;return null!=(e=s.MIN_INVESTMENT_AMOUNT)?e:1e3},getMaxInvestmentAmount:()=>{var e;return null!=(e=s.MAX_INVESTMENT_AMOUNT)?e:1e6},isFeatureEnabled:e=>{var t;return null==(t=s["FEATURE_".concat(e.toUpperCase(),"_ENABLED")])||t}},children:t})},l=()=>{let e=(0,i.useContext)(o);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e},d=()=>{let{isEmailVerificationEnabled:e,isKYCRequired:t,isKYCPriorityOverEmail:s,allowKYCForInactiveUsers:a}=l();return{emailVerificationEnabled:e(),kycRequired:t(),kycPriorityOverEmail:s(),allowKYCForInactive:a(),shouldShowEmailFirst:()=>e()&&!s(),shouldShowKYCFirst:()=>t()&&s(),getNextStepForUser:a=>{if(!a)return null;let i=t()&&["not_started","not_submitted","incomplete","rejected"].includes(a.kycStatus||"not_started"),r=e()&&!a.emailVerified;return"pending"===a.kycStatus||"under_review"===a.kycStatus?r?"email_verification":null:i&&s()?"kyc":r&&!s()?"email_verification":i?"kyc":r?"email_verification":null}}}},3815:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>b,H$:()=>k,Kc:()=>y,LA:()=>l,PK:()=>r,ri:()=>m,xu:()=>x});var a=s(7895);let i=(0,a.zD)("auth/login",async(e,t)=>{let{rejectWithValue:s}=t;try{console.log("\uD83D\uDD10 User backend login attempt:",e.email);let t=await fetch("".concat("http://localhost:5000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)}),s=await t.json();if(!t.ok)throw Error(s.message||"Login failed");console.log("✅ User backend login successful:",s);let{user:a,accessToken:i,refreshToken:r}=s.data||s;return localStorage.setItem("userProfile",JSON.stringify(a)),console.log("✅ User login successful, backend set HttpOnly cookies:",{user:a.email,role:a.role,note:"Tokens stored in HttpOnly cookies (not accessible from JS)"}),{user:a,accessToken:i,refreshToken:r}}catch(e){return console.error("❌ User backend login failed:",e.message),s(e.message||"Login failed - please check your credentials")}}),r=(0,a.zD)("auth/checkAuth",async(e,t)=>{let{rejectWithValue:s}=t;try{if(console.log("\uD83D\uDD0D User: Checking authentication status..."),!(document.cookie.includes("accessToken=")||document.cookie.includes("token=")))throw console.log("❌ User: No auth cookies found"),Error("No authentication token found");return console.log("✅ User: Authentication cookies found, user data will be fetched by RTK Query"),{authenticated:!0}}catch(e){return console.error("❌ User: Authentication check failed:",e.message),s(e.message||"Authentication check failed")}}),o=(0,a.zD)("auth/register",async(e,t)=>{let{rejectWithValue:s}=t;try{console.log("\uD83D\uDCDD User backend registration attempt:",e.email);let t=await fetch("http://localhost:5000/api",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)}),s=await t.json();if(!t.ok)throw Error(s.message||"Registration failed");console.log("✅ User backend registration successful:",s);let{user:a,accessToken:i,refreshToken:r}=s.data||s;return localStorage.setItem("userProfile",JSON.stringify(a)),{user:a,accessToken:i,refreshToken:r}}catch(e){return console.error("❌ User backend registration failed:",e.message),s(e.message||"Registration failed")}}),n=(0,a.Z0)({name:"auth",initialState:{user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null},reducers:{setCredentials:(e,t)=>{let{user:s,token:a,refreshToken:i}=t.payload;e.user=s,e.token=a,e.refreshToken=i||null,e.isAuthenticated=!0,e.error=null},setUser:(e,t)=>{e.user=t.payload},clearUser:e=>{e.user=null},setToken:(e,t)=>{e.token=t.payload},setLoading:(e,t)=>{e.isLoading=t.payload},setError:(e,t)=>{e.error=t.payload},logout:e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.error=null},clearError:e=>{e.error=null},updateUserProfile:(e,t)=>{e.user&&(e.user={...e.user,...t.payload})}},extraReducers:e=>{e.addCase(r.pending,e=>{e.isLoading=!0,e.error=null}).addCase(r.fulfilled,(e,t)=>{e.isLoading=!1,e.isAuthenticated=t.payload.authenticated,e.error=null}).addCase(r.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null}).addCase(i.pending,e=>{e.isLoading=!0,e.error=null}).addCase(i.fulfilled,(e,t)=>{e.isLoading=!1,e.user=t.payload.user,e.token=t.payload.accessToken,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null}).addCase(i.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null}).addCase(o.pending,e=>{e.isLoading=!0,e.error=null}).addCase(o.fulfilled,(e,t)=>{e.isLoading=!1,e.user=t.payload.user,e.token=t.payload.accessToken,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null}).addCase(o.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null})}}),{setCredentials:l,setUser:d,clearUser:c,setToken:u,setLoading:h,setError:f,logout:m,clearError:g,updateUserProfile:p}=n.actions,x=e=>e.auth.user,y=e=>e.auth.isAuthenticated,k=e=>e.auth.isLoading,b=n.reducer},5935:(e,t,s)=>{"use strict";var a=s(5383);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},6793:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(5050).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},6965:(e,t,s)=>{"use strict";s.d(t,{q:()=>d});var a=s(6597),i=s(2713),r=s(3815),o=s(7578);let n=(0,a.cw)({baseUrl:"http://localhost:5000/api",credentials:"include",prepareHeaders:(e,t)=>{let{getState:s}=t,a=s().auth.token,i=o.s.getAuthToken(),r=a||i;return r&&e.set("authorization","Bearer ".concat(r)),e.set("Content-Type","application/json"),e}}),l=async(e,t,s)=>{let a=await n(e,t,s);if(a.error&&401===a.error.status){let o=await n({url:"/auth/refresh",method:"POST"},t,s);if(o.data){var i;let l=o.data;l.success&&(null==(i=l.data)?void 0:i.user)?(t.dispatch((0,r.LA)({user:l.data.user,token:"cookie-based",refreshToken:"cookie-based"})),a=await n(e,t,s)):t.dispatch((0,r.ri)())}else t.dispatch((0,r.ri)())}return a},d=(0,i.xP)({reducerPath:"baseApi",baseQuery:l,tagTypes:["User","Property","Investment","Transaction","Wallet","Referral","SupportTicket","Notification","Dashboard","Activity","Wishlist","Featured","KYC","Lead","PropertyOwner","Auth","Settings","FAQ","FAQCategories","StockHoldings","StockCertificate","StockTransaction"],endpoints:()=>({})})},7499:(e,t,s)=>{Promise.resolve().then(s.bind(s,8072))},7578:(e,t,s)=>{"use strict";s.d(t,{s:()=>a});class a{static getCookie(e){if("undefined"==typeof document)return null;let t="; ".concat(document.cookie).split("; ".concat(e,"="));if(2===t.length){var s;let e=null==(s=t.pop())?void 0:s.split(";").shift();return e?decodeURIComponent(e):null}return null}static setCookie(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("undefined"==typeof document)return;let a="".concat(e,"=").concat(encodeURIComponent(t));s.expires&&("string"==typeof s.expires?a+="; expires=".concat(s.expires):a+="; expires=".concat(s.expires.toUTCString())),s.maxAge&&(a+="; max-age=".concat(s.maxAge)),s.path?a+="; path=".concat(s.path):a+="; path=/",s.domain&&(a+="; domain=".concat(s.domain)),s.secure&&(a+="; secure"),s.sameSite&&(a+="; samesite=".concat(s.sameSite)),document.cookie=a}static deleteCookie(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.setCookie(e,"",{...t,expires:new Date(0)})}static hasCookie(e){return null!==this.getCookie(e)}static getAllCookies(){if("undefined"==typeof document)return{};let e={};return document.cookie.split(";").forEach(t=>{let[s,a]=t.trim().split("=");s&&a&&(e[s]=decodeURIComponent(a))}),e}static getAuthToken(){return this.getCookie("accessToken")||this.getCookie("token")}static getRefreshToken(){return this.getCookie("refreshToken")}static isAuthenticated(){return this.hasCookie("accessToken")||this.hasCookie("token")}static clearAuthCookies(){this.deleteCookie("accessToken"),this.deleteCookie("refreshToken"),this.deleteCookie("token")}static getRedirectUrl(){return this.getCookie("redirectTo")}static setRedirectUrl(e){this.setCookie("redirectTo",e,{maxAge:300,sameSite:"lax"})}static clearRedirectUrl(){this.deleteCookie("redirectTo")}static parseCookieString(e){let t={};return e.split(";").forEach(e=>{let[s,a]=e.trim().split("=");s&&a&&(t[s]=decodeURIComponent(a))}),t}static setSessionCookie(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.setCookie(e,t,{...s,expires:void 0,maxAge:void 0})}static setPersistentCookie(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:7,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=new Date;i.setDate(i.getDate()+s),this.setCookie(e,t,{...a,expires:i})}}},8072:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(9605),i=s(9585),r=s(5935),o=s(9311),n=s(3815),l=s(2855),d=s(6793),c=s(8120);function u(){let e=(0,r.useRouter)(),t=(0,o.GV)(n.Kc),s=(0,o.GV)(n.xu),{getNextStepForUser:u}=(0,l.YK)();return(0,i.useEffect)(()=>{if(t&&s){let t=u(s);"kyc"===t?(console.log("\uD83D\uDD04 Home: Redirecting to KYC for user with status:",s.kycStatus),e.push("/kyc")):"email_verification"===t?(console.log("\uD83D\uDD04 Home: Redirecting to dashboard with email verification required"),e.push("/dashboard?verification=required")):(console.log("\uD83D\uDD04 Home: Redirecting to dashboard"),e.push("/dashboard"))}else!1===t&&e.push("/login")},[t,s,e,u]),(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center max-w-lg mx-auto px-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 border border-gray-100",children:[(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsx)("div",{className:"bg-blue-50 rounded-full p-6 shadow-md border border-blue-100",children:(0,a.jsx)(c.A,{size:"lg",variant:"full"})})}),(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-3",children:"SGM"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-8 font-medium",children:"Premium Real Estate Investment Platform"}),(0,a.jsxs)("div",{className:"space-y-6 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,a.jsx)("div",{className:"absolute inset-0 h-8 w-8 animate-ping rounded-full bg-blue-600 opacity-20"})]}),(0,a.jsx)("p",{className:"text-gray-700 font-medium text-lg",children:"Loading your portal..."})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full animate-pulse",style:{width:"60%"}})}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Preparing your investment dashboard..."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center mb-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-xl p-4 border border-blue-100",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFD7️"}),(0,a.jsx)("p",{className:"text-xs text-gray-700 font-semibold",children:"Properties"})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-xl p-4 border border-green-100",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCCA"}),(0,a.jsx)("p",{className:"text-xs text-gray-700 font-semibold",children:"Analytics"})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 rounded-xl p-4 border border-yellow-100",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCB0"}),(0,a.jsx)("p",{className:"text-xs text-gray-700 font-semibold",children:"Investments"})]})]}),(0,a.jsxs)("div",{className:"flex justify-center items-center space-x-4 text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{children:"Secure"})]}),(0,a.jsx)("div",{className:"w-1 h-1 bg-gray-300 rounded-full"}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,a.jsx)("span",{children:"Reliable"})]}),(0,a.jsx)("div",{className:"w-1 h-1 bg-gray-300 rounded-full"}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),(0,a.jsx)("span",{children:"Professional"})]})]})]}),(0,a.jsx)("p",{className:"mt-6 text-xs text-gray-400",children:"\xa9 2025 SGM. All rights reserved."})]})})}},8120:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(9605);s(9585);let i=e=>{let{size:t="md",variant:s="full",className:i=""}=e,r={sm:"h-8 w-8",md:"h-12 w-12",lg:"h-16 w-16",xl:"h-24 w-24"},o={sm:"text-lg",md:"text-2xl",lg:"text-3xl",xl:"text-4xl"},n=()=>(0,a.jsx)("div",{className:"".concat(r[t]," ").concat(i," relative"),children:(0,a.jsxs)("svg",{viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"w-full h-full",children:[(0,a.jsx)("circle",{cx:"50",cy:"50",r:"48",fill:"#0ea5e9",stroke:"#ffffff",strokeWidth:"2"}),(0,a.jsxs)("g",{transform:"translate(20, 25)",children:[(0,a.jsx)("rect",{x:"15",y:"20",width:"30",height:"35",fill:"#ffffff",rx:"2"}),(0,a.jsx)("rect",{x:"20",y:"25",width:"6",height:"6",fill:"#0ea5e9"}),(0,a.jsx)("rect",{x:"34",y:"25",width:"6",height:"6",fill:"#0ea5e9"}),(0,a.jsx)("rect",{x:"20",y:"35",width:"6",height:"6",fill:"#0ea5e9"}),(0,a.jsx)("rect",{x:"34",y:"35",width:"6",height:"6",fill:"#0ea5e9"}),(0,a.jsx)("rect",{x:"27",y:"45",width:"6",height:"10",fill:"#fbbf24"}),(0,a.jsx)("rect",{x:"5",y:"30",width:"8",height:"25",fill:"#ffffff",rx:"1"}),(0,a.jsx)("rect",{x:"47",y:"35",width:"8",height:"20",fill:"#ffffff",rx:"1"}),(0,a.jsx)("path",{d:"M10 15 L20 5 L30 10 L40 2 L50 8",stroke:"#fbbf24",strokeWidth:"3",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("polygon",{points:"45,2 50,8 45,8",fill:"#fbbf24"})]}),(0,a.jsx)("text",{x:"50",y:"75",textAnchor:"middle",fill:"#ffffff",fontSize:"12",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:"SGM"})]})});switch(s){case"icon":return(0,a.jsx)(n,{});case"text":return(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"".concat(i," flex items-center"),children:[(0,a.jsx)("span",{className:"".concat(o[t]," font-bold sgm-accent-text"),children:"SGM"}),(0,a.jsx)("span",{className:"".concat(o[t]," font-light sgm-primary-text ml-1"),children:"Investments"})]}),{});default:return(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"".concat(i," flex items-center space-x-3"),children:[(0,a.jsx)(n,{}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"".concat(o[t]," font-bold sgm-accent-text leading-none"),children:"SGM"}),(0,a.jsx)("span",{className:"text-sm sgm-primary-text leading-none",children:"Investments"})]})]}),{})}}},9311:(e,t,s)=>{"use strict";s.d(t,{M_:()=>V,jL:()=>Y,GV:()=>q});var a=s(7895),i=s(6597),r=s(9559),o=s(6965),n=s(3815);let l={sidebarCollapsed:!1,sidebarMobileOpen:!1,theme:"light",loading:{global:!1,page:!1,component:{}},modals:{investmentModal:!1,withdrawalModal:!1,addMoneyModal:!1,profileModal:!1,supportModal:!1},notifications:{show:!1,unreadCount:0},searchQuery:"",filters:{}},d=(0,a.Z0)({name:"ui",initialState:l,reducers:{toggleSidebar:e=>{e.sidebarCollapsed=!e.sidebarCollapsed},setSidebarCollapsed:(e,t)=>{e.sidebarCollapsed=t.payload},toggleMobileSidebar:e=>{e.sidebarMobileOpen=!e.sidebarMobileOpen},setMobileSidebarOpen:(e,t)=>{e.sidebarMobileOpen=t.payload},setTheme:(e,t)=>{e.theme=t.payload},toggleTheme:e=>{e.theme="light"===e.theme?"dark":"light"},setGlobalLoading:(e,t)=>{e.loading.global=t.payload},setPageLoading:(e,t)=>{e.loading.page=t.payload},setComponentLoading:(e,t)=>{let{component:s,loading:a}=t.payload;e.loading.component[s]=a},openModal:(e,t)=>{e.modals[t.payload]=!0},closeModal:(e,t)=>{e.modals[t.payload]=!1},closeAllModals:e=>{Object.keys(e.modals).forEach(t=>{e.modals[t]=!1})},toggleNotifications:e=>{e.notifications.show=!e.notifications.show},setNotificationsOpen:(e,t)=>{e.notifications.show=t.payload},setUnreadCount:(e,t)=>{e.notifications.unreadCount=t.payload},setSearchQuery:(e,t)=>{e.searchQuery=t.payload},setFilter:(e,t)=>{let{key:s,value:a}=t.payload;e.filters[s]=a},clearFilters:e=>{e.filters={}},resetUI:e=>({...l,theme:e.theme})}}),{toggleSidebar:c,setSidebarCollapsed:u,toggleMobileSidebar:h,setMobileSidebarOpen:f,setTheme:m,toggleTheme:g,setGlobalLoading:p,setPageLoading:x,setComponentLoading:y,openModal:k,closeModal:b,closeAllModals:v,toggleNotifications:C,setNotificationsOpen:j,setUnreadCount:N,setSearchQuery:w,setFilter:A,clearFilters:S,resetUI:T}=d.actions,E=d.reducer,R=(0,a.Z0)({name:"notifications",initialState:{notifications:[],unreadCount:0,loading:!1,error:null},reducers:{setNotifications:(e,t)=>{e.notifications=t.payload,e.unreadCount=t.payload.filter(e=>!e.read).length},addNotification:(e,t)=>{e.notifications.unshift(t.payload),t.payload.read||(e.unreadCount+=1)},markAsRead:(e,t)=>{let s=e.notifications.find(e=>e.id===t.payload);s&&!s.read&&(s.read=!0,e.unreadCount=Math.max(0,e.unreadCount-1))},markAllAsRead:e=>{e.notifications.forEach(e=>{e.read=!0}),e.unreadCount=0},removeNotification:(e,t)=>{let s=e.notifications.findIndex(e=>e.id===t.payload);-1!==s&&(e.notifications[s].read||(e.unreadCount=Math.max(0,e.unreadCount-1)),e.notifications.splice(s,1))},clearAllNotifications:e=>{e.notifications=[],e.unreadCount=0},setLoading:(e,t)=>{e.loading=t.payload},setError:(e,t)=>{e.error=t.payload},receiveNotification:(e,t)=>{if(e.notifications.unshift(t.payload),t.payload.read||(e.unreadCount+=1),e.notifications.length>50){let t=e.notifications.splice(50).filter(e=>!e.read).length;e.unreadCount=Math.max(0,e.unreadCount-t)}}}}),{setNotifications:P,addNotification:I,markAsRead:L,markAllAsRead:M,removeNotification:U,clearAllNotifications:O,setLoading:_,setError:D,receiveNotification:F}=R.actions,K=R.reducer,V=(0,a.U1)({reducer:{[o.q.reducerPath]:o.q.reducer,auth:n.Ay,ui:E,notifications:K},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE","persist/PAUSE","persist/PURGE","persist/REGISTER"]}}).concat(o.q.middleware),devTools:!1});(0,i.$k)(V.dispatch);let Y=()=>(0,r.wA)(),q=r.d4}},e=>{var t=t=>e(e.s=t);e.O(0,[2094,390,110,7358],()=>t(7499)),_N_E=e.O()}]);