(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7754],{1470:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1480:(e,s,t)=>{Promise.resolve().then(t.bind(t,7123))},5263:(e,s,t)=>{"use strict";t.d(s,{EF:()=>r,U0:()=>a,e6:()=>l});let{useGetUserWishlistQuery:r,useAddToWishlistMutation:a,useRemoveFromWishlistMutation:l,useCheckWishlistStatusQuery:i,useSetPriceAlertMutation:d,useRemovePriceAlertMutation:c,useGetPopularPropertiesQuery:n,useGetWishlistStatsQuery:o,useClearWishlistMutation:p,useExportWishlistMutation:u}=t(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getUserWishlist:e.query({query:e=>{let{page:s=1,limit:t=12}=e;return{url:"/wishlist",params:{page:s,limit:t}}},providesTags:[{type:"Wishlist",id:"LIST"}],keepUnusedDataFor:300}),addToWishlist:e.mutation({query:e=>({url:"/wishlist",method:"POST",body:{propertyId:e}}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),removeFromWishlist:e.mutation({query:e=>({url:"/wishlist/".concat(e),method:"DELETE"}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),checkWishlistStatus:e.query({query:e=>"/wishlist/check/".concat(e),providesTags:(e,s,t)=>[{type:"Wishlist",id:"check-".concat(t)}],keepUnusedDataFor:600}),setPriceAlert:e.mutation({query:e=>{let{propertyId:s,...t}=e;return{url:"/wishlist/".concat(s,"/price-alert"),method:"POST",body:t}},invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),removePriceAlert:e.mutation({query:e=>({url:"/wishlist/".concat(e,"/price-alert"),method:"DELETE"}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),getPopularProperties:e.query({query:e=>{let{limit:s=10}=e;return{url:"/wishlist/popular",params:{limit:s}}},providesTags:[{type:"Wishlist",id:"POPULAR"}],keepUnusedDataFor:1800}),getWishlistStats:e.query({query:()=>"/wishlist/stats",providesTags:[{type:"Wishlist",id:"STATS"}],keepUnusedDataFor:600}),clearWishlist:e.mutation({query:()=>({url:"/wishlist/clear",method:"DELETE"}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),exportWishlist:e.mutation({query:e=>({url:"/wishlist/export",method:"POST",body:e})})})})},5768:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},6751:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},7123:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(9605),a=t(9585),l=t(5935),i=t(3005),d=t(8063),c=t(2933),n=t(5768),o=t(8437),p=t(9405);let u=(0,t(5050).A)("SlidersHorizontal",[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]]);var y=t(8049),h=t(5833),m=t(3559),x=t(6751),v=t(1470),g=t(470),j=t(6994),b=t(7661),k=t(5263);function N(){var e;let s=(0,l.useRouter)(),[t,N]=(0,a.useState)(""),[f,w]=(0,a.useState)("all"),[P,T]=(0,a.useState)("all"),[q,A]=(0,a.useState)({min:"",max:""}),[S,L]=(0,a.useState)("featured"),[C,E]=(0,a.useState)("grid"),[F,I]=(0,a.useState)(!1),{data:D,isLoading:M}=(0,b.zq)({search:t||void 0,type:"all"!==f?f:void 0,location:"all"!==P?P:void 0,minPrice:q.min?parseInt(q.min):void 0,maxPrice:q.max?parseInt(q.max):void 0,sortBy:"featured"===S?"featured":S,sortOrder:"desc",limit:20}),[W]=(0,k.U0)(),[U]=(0,k.e6)(),O=Array.isArray(null==D?void 0:D.data)?D.data:(null==D||null==(e=D.data)?void 0:e.data)||[],z=async(e,s)=>{try{s?await U(e).unwrap():await W(e).unwrap()}catch(e){console.error("Wishlist error:",e)}},R=(0,a.useMemo)(()=>O.filter(e=>!t||e.name.toLowerCase().includes(t.toLowerCase())||e.location.toLowerCase().includes(t.toLowerCase())||e.developer.toLowerCase().includes(t.toLowerCase())),[O,t]);return M?(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(e=>(0,r.jsx)("div",{className:"h-80 bg-gray-200 rounded-lg"},e))})]})})}):(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Browse Properties"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Discover investment opportunities in real estate"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(c.$,{variant:"grid"===C?"default":"outline",size:"sm",onClick:()=>E("grid"),children:(0,r.jsx)(n.A,{className:"h-4 w-4"})}),(0,r.jsx)(c.$,{variant:"list"===C?"default":"outline",size:"sm",onClick:()=>E("list"),children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})]})]}),(0,r.jsx)(d.Zp,{children:(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search properties by name, location, or developer...",value:t,onChange:e=>N(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("select",{value:f,onChange:e=>w(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[{value:"all",label:"All Types"},{value:"residential",label:"Residential"},{value:"commercial",label:"Commercial"},{value:"mixed",label:"Mixed Use"}].map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,r.jsx)("select",{value:P,onChange:e=>T(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[{value:"all",label:"All Locations"},{value:"mumbai",label:"Mumbai"},{value:"delhi",label:"Delhi"},{value:"bangalore",label:"Bangalore"},{value:"pune",label:"Pune"},{value:"hyderabad",label:"Hyderabad"}].map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,r.jsx)("select",{value:S,onChange:e=>L(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[{value:"featured",label:"Featured First"},{value:"pricePerStock",label:"Price: Low to High"},{value:"-pricePerStock",label:"Price: High to Low"},{value:"roi",label:"Returns: High to Low"},{value:"availableStocks",label:"Availability"},{value:"createdAt",label:"Newest First"}].map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)(c.$,{variant:"outline",onClick:()=>I(!F),className:"flex items-center space-x-2",children:[(0,r.jsx)(u,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Advanced Filters"})]})]}),F&&(0,r.jsx)("div",{className:"border-t pt-4 space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Min Price per Stock"}),(0,r.jsx)("input",{type:"number",placeholder:"₹0",value:q.min,onChange:e=>A(s=>({...s,min:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Price per Stock"}),(0,r.jsx)("input",{type:"number",placeholder:"₹1,00,000",value:q.max,onChange:e=>A(s=>({...s,max:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg"})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsx)(c.$,{variant:"outline",onClick:()=>{N(""),w("all"),T("all"),A({min:"",max:""}),L("featured")},className:"w-full",children:"Clear Filters"})})]})})]})})}),(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("p",{className:"text-gray-600",children:["Showing ",R.length," properties"]})}),R.length>0?(0,r.jsx)("div",{className:"grid"===C?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:R.map(e=>{var t,a,l,i;return(0,r.jsxs)(d.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,r.jsxs)("div",{className:"relative",children:[e.images&&e.images.length>0?(0,r.jsx)("img",{src:e.images[0].url,alt:e.name,className:"w-full h-48 object-cover rounded-t-lg"}):(0,r.jsx)("div",{className:"w-full h-48 bg-gray-200 rounded-t-lg flex items-center justify-center",children:(0,r.jsx)(y.A,{className:"h-12 w-12 text-gray-400"})}),e.featured&&(0,r.jsxs)("div",{className:"absolute top-3 left-3 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center",children:[(0,r.jsx)(h.A,{className:"h-3 w-3 mr-1"}),"Featured"]}),(0,r.jsx)("button",{onClick:()=>z(e._id,!1),className:"absolute top-3 right-3 p-2 bg-white rounded-full shadow-md hover:bg-gray-50",children:(0,r.jsx)(m.A,{className:"h-4 w-4 text-gray-600"})})]}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center text-gray-600 text-sm mt-1",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"string"==typeof e.location?e.location:(null==(t=e.location)?void 0:t.address)||"Location not specified"]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Stock Price"}),(0,r.jsx)("div",{className:"font-semibold",children:(null==(a=e.stockInfo)?void 0:a.stockPrice)?(0,j.vv)(e.stockInfo.stockPrice):"Not Available"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Expected Returns"}),(0,r.jsx)("div",{className:"font-semibold text-green-600",children:(null==(l=e.stockInfo)?void 0:l.expectedROI)?(0,j.Ee)(e.stockInfo.expectedROI):e.expectedReturns?(0,j.Ee)(e.expectedReturns):"TBD"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Available"}),(0,r.jsxs)("div",{className:"font-semibold",children:[(null==(i=e.stockInfo)?void 0:i.availableStocks)||0," stocks"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Type"}),(0,r.jsx)("div",{className:"font-semibold capitalize",children:e.propertyType||"Property"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,r.jsxs)(c.$,{variant:"outline",size:"sm",className:"flex items-center space-x-2",onClick:()=>s.push("/properties/".concat(e._id)),children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"View Details"})]}),(0,r.jsxs)(c.$,{size:"sm",className:"flex items-center space-x-2",onClick:()=>s.push("/properties/".concat(e._id)),children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Invest Now"})]})]})]})})]},e._id)})}):(0,r.jsx)(d.Zp,{children:(0,r.jsxs)(d.Wu,{className:"text-center py-12",children:[(0,r.jsx)(y.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No properties found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Try adjusting your search criteria or filters to find more properties."}),(0,r.jsx)(c.$,{onClick:()=>{N(""),w("all"),T("all"),A({min:"",max:""})},children:"Clear All Filters"})]})})]})})}},7661:(e,s,t)=>{"use strict";t.d(s,{$k:()=>c,Zj:()=>l,m9:()=>h,zq:()=>r});let{useGetPropertiesQuery:r,useGetFeaturedPropertiesQuery:a,useGetPropertyByIdQuery:l,useGetPropertyAnalyticsQuery:i,useGetSimilarPropertiesQuery:d,useCalculateInvestmentQuery:c,useGetPropertyReviewsQuery:n,useAddPropertyReviewMutation:o,useGetPropertyLocationsQuery:p,useGetPropertyTypesQuery:u,useSearchPropertiesQuery:y,usePurchasePropertyStocksMutation:h}=t(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getProperties:e.query({query:e=>({url:"/properties",params:{page:e.page||1,limit:e.limit||12,...e.search&&{search:e.search},...e.type&&{type:e.type},...e.status&&{status:e.status},...void 0!==e.featured&&{featured:e.featured},...e.minPrice&&{minPrice:e.minPrice},...e.maxPrice&&{maxPrice:e.maxPrice},...e.location&&{location:e.location},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),providesTags:e=>{var s;return(null==e||null==(s=e.data)?void 0:s.data)?[...e.data.data.map(e=>{let{_id:s}=e;return{type:"Property",id:s}}),{type:"Property",id:"LIST"}]:[{type:"Property",id:"LIST"}]},keepUnusedDataFor:600}),getFeaturedProperties:e.query({query:e=>{let{limit:s=6}=e;return{url:"/properties/featured",params:{limit:s}}},providesTags:[{type:"Property",id:"FEATURED"}],keepUnusedDataFor:900}),getPropertyById:e.query({query:e=>"/properties/".concat(e),providesTags:(e,s,t)=>[{type:"Property",id:t}],keepUnusedDataFor:1800}),getPropertyAnalytics:e.query({query:e=>"/properties/".concat(e,"/analytics"),providesTags:(e,s,t)=>[{type:"Property",id:"".concat(t,"-analytics")}],keepUnusedDataFor:300}),getSimilarProperties:e.query({query:e=>{let{propertyId:s,limit:t=4}=e;return{url:"/properties/".concat(s,"/similar"),params:{limit:t}}},providesTags:(e,s,t)=>{let{propertyId:r}=t;return[{type:"Property",id:"".concat(r,"-similar")}]},keepUnusedDataFor:1200}),calculateInvestment:e.query({query:e=>{let{propertyId:s,stockQuantity:t,investmentPeriod:r}=e;return{url:"/properties/".concat(s,"/calculate"),params:{stockQuantity:t,investmentPeriod:r}}},keepUnusedDataFor:0}),getPropertyReviews:e.query({query:e=>{let{propertyId:s,page:t=1,limit:r=10,rating:a}=e;return{url:"/properties/".concat(s,"/reviews"),params:{page:t,limit:r,...a&&{rating:a}}}},providesTags:(e,s,t)=>{let{propertyId:r}=t;return[{type:"Property",id:"".concat(r,"-reviews")}]},keepUnusedDataFor:600}),addPropertyReview:e.mutation({query:e=>{let{propertyId:s,...t}=e;return{url:"/properties/".concat(s,"/reviews"),method:"POST",body:t}},invalidatesTags:(e,s,t)=>{let{propertyId:r}=t;return[{type:"Property",id:"".concat(r,"-reviews")},{type:"Property",id:r}]}}),getPropertyLocations:e.query({query:()=>"/properties/locations",providesTags:[{type:"Property",id:"LOCATIONS"}],keepUnusedDataFor:3600}),getPropertyTypes:e.query({query:()=>"/properties/types",providesTags:[{type:"Property",id:"TYPES"}],keepUnusedDataFor:3600}),searchProperties:e.query({query:e=>{let{query:s,limit:t=10}=e;return{url:"/properties/search",params:{q:s,limit:t}}},keepUnusedDataFor:0}),purchasePropertyStocks:e.mutation({query:e=>({url:"/transactions/stock-purchase",method:"POST",body:e}),invalidatesTags:(e,s,t)=>{let{propertyId:r}=t;return[{type:"Property",id:r},{type:"Property",id:"LIST"},{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}})})})},8049:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},8437:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[2094,5315,7436,7693,1147,7627,3005,390,110,7358],()=>s(1480)),_N_E=e.O()}]);