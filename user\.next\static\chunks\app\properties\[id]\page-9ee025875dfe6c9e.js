(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1968],{2790:(e,s,t)=>{"use strict";t.d(s,{E:()=>i});var a=t(9605);t(9585);var r=t(7276),l=t(6994);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:t}),s),...r})}},4685:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>m,L3:()=>u,c7:()=>x,lG:()=>c});var a=t(9605),r=t(9585),l=t(2848),n=t(2311),i=t(6994);let c=l.bL;l.l9;let d=l.ZL;l.bm;let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.hJ,{ref:s,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r})});o.displayName=l.hJ.displayName;let m=r.forwardRef((e,s)=>{let{className:t,children:r,...c}=e;return(0,a.jsxs)(d,{children:[(0,a.jsx)(o,{}),(0,a.jsxs)(l.UC,{ref:s,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...c,children:[r,(0,a.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=l.UC.displayName;let x=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...t})};x.displayName="DialogHeader";let u=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.hE,{ref:s,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",t),...r})});u.displayName=l.hE.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.VY,{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",t),...r})}).displayName=l.VY.displayName},5097:(e,s,t)=>{"use strict";t.d(s,{J:()=>d});var a=t(9605),r=t(9585),l=t(8436),n=t(7276),i=t(6994);let c=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.b,{ref:s,className:(0,i.cn)(c(),t),...r})});d.displayName=l.b.displayName},5607:(e,s,t)=>{Promise.resolve().then(t.bind(t,7815))},7661:(e,s,t)=>{"use strict";t.d(s,{$k:()=>c,Zj:()=>l,m9:()=>p,zq:()=>a});let{useGetPropertiesQuery:a,useGetFeaturedPropertiesQuery:r,useGetPropertyByIdQuery:l,useGetPropertyAnalyticsQuery:n,useGetSimilarPropertiesQuery:i,useCalculateInvestmentQuery:c,useGetPropertyReviewsQuery:d,useAddPropertyReviewMutation:o,useGetPropertyLocationsQuery:m,useGetPropertyTypesQuery:x,useSearchPropertiesQuery:u,usePurchasePropertyStocksMutation:p}=t(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getProperties:e.query({query:e=>({url:"/properties",params:{page:e.page||1,limit:e.limit||12,...e.search&&{search:e.search},...e.type&&{type:e.type},...e.status&&{status:e.status},...void 0!==e.featured&&{featured:e.featured},...e.minPrice&&{minPrice:e.minPrice},...e.maxPrice&&{maxPrice:e.maxPrice},...e.location&&{location:e.location},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),providesTags:e=>{var s;return(null==e||null==(s=e.data)?void 0:s.data)?[...e.data.data.map(e=>{let{_id:s}=e;return{type:"Property",id:s}}),{type:"Property",id:"LIST"}]:[{type:"Property",id:"LIST"}]},keepUnusedDataFor:600}),getFeaturedProperties:e.query({query:e=>{let{limit:s=6}=e;return{url:"/properties/featured",params:{limit:s}}},providesTags:[{type:"Property",id:"FEATURED"}],keepUnusedDataFor:900}),getPropertyById:e.query({query:e=>"/properties/".concat(e),providesTags:(e,s,t)=>[{type:"Property",id:t}],keepUnusedDataFor:1800}),getPropertyAnalytics:e.query({query:e=>"/properties/".concat(e,"/analytics"),providesTags:(e,s,t)=>[{type:"Property",id:"".concat(t,"-analytics")}],keepUnusedDataFor:300}),getSimilarProperties:e.query({query:e=>{let{propertyId:s,limit:t=4}=e;return{url:"/properties/".concat(s,"/similar"),params:{limit:t}}},providesTags:(e,s,t)=>{let{propertyId:a}=t;return[{type:"Property",id:"".concat(a,"-similar")}]},keepUnusedDataFor:1200}),calculateInvestment:e.query({query:e=>{let{propertyId:s,stockQuantity:t,investmentPeriod:a}=e;return{url:"/properties/".concat(s,"/calculate"),params:{stockQuantity:t,investmentPeriod:a}}},keepUnusedDataFor:0}),getPropertyReviews:e.query({query:e=>{let{propertyId:s,page:t=1,limit:a=10,rating:r}=e;return{url:"/properties/".concat(s,"/reviews"),params:{page:t,limit:a,...r&&{rating:r}}}},providesTags:(e,s,t)=>{let{propertyId:a}=t;return[{type:"Property",id:"".concat(a,"-reviews")}]},keepUnusedDataFor:600}),addPropertyReview:e.mutation({query:e=>{let{propertyId:s,...t}=e;return{url:"/properties/".concat(s,"/reviews"),method:"POST",body:t}},invalidatesTags:(e,s,t)=>{let{propertyId:a}=t;return[{type:"Property",id:"".concat(a,"-reviews")},{type:"Property",id:a}]}}),getPropertyLocations:e.query({query:()=>"/properties/locations",providesTags:[{type:"Property",id:"LOCATIONS"}],keepUnusedDataFor:3600}),getPropertyTypes:e.query({query:()=>"/properties/types",providesTags:[{type:"Property",id:"TYPES"}],keepUnusedDataFor:3600}),searchProperties:e.query({query:e=>{let{query:s,limit:t=10}=e;return{url:"/properties/search",params:{q:s,limit:t}}},keepUnusedDataFor:0}),purchasePropertyStocks:e.mutation({query:e=>({url:"/transactions/stock-purchase",method:"POST",body:e}),invalidatesTags:(e,s,t)=>{let{propertyId:a}=t;return[{type:"Property",id:a},{type:"Property",id:"LIST"},{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}})})})},7815:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>B});var a=t(9605),r=t(9585),l=t(5935),n=t(3005),i=t(8063),c=t(2933),d=t(8049),o=t(9644),m=t(3559),x=t(4883),u=t(5833),p=t(6751),h=t(95),y=t(7439),f=t(5295),j=t(8770),v=t(5457),g=t(1470),N=t(8999),b=t(5449),w=t(4621),k=t(6994),S=t(7661),P=t(4685),T=t(7971),I=t(5097),A=t(2790),C=t(6392);let q=r.forwardRef((e,s)=>{let{className:t,orientation:r="horizontal",decorative:l=!0,...n}=e;return(0,a.jsx)(C.b,{ref:s,decorative:l,orientation:r,className:(0,k.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",t),...n})});q.displayName=C.b.displayName;var E=t(4268),L=t(5828),D=t(2363),R=t(2006),M=t(6073),O=t(9849),W=t(5077);function F(e){var s,t,n;let{isOpen:d,onClose:o,property:m,onPurchase:x}=e,[u,p]=(0,r.useState)(1),[h,y]=(0,r.useState)(""),[f,j]=(0,r.useState)(!1),v=(0,l.useRouter)(),{data:g}=(0,O.rx)(),{data:N}=(0,W.xs)(),b=(null==g||null==(s=g.data)?void 0:s.balance)||0,k=null==N||null==(n=N.data)||null==(t=n.kyc)?void 0:t.status,[C,{isLoading:F}]=(0,S.m9)(),B=u*m.pricePerShare,U=b>=B,_=m.pricePerShare>0?Math.floor(b/m.pricePerShare):0,z=Math.min(m.availableShares,_);(0,r.useEffect)(()=>{d&&(p(1),y(""),j(!1))},[d]);let Y=e=>{let s=parseInt(e)||0;s>=1&&s<=z&&p(s)},Z=async()=>{if("approved"!==k){M.oR.error("KYC approval required for investment"),o(),v.push("/kyc?message=kyc_required");return}if(!U)return void M.oR.error("Insufficient wallet balance");if(u<1||u>m.availableShares)return void M.oR.error("Invalid number of shares");if(B<m.minimumInvestment)return void M.oR.error("Minimum investment is ₹".concat(m.minimumInvestment.toLocaleString()));j(!0);try{await C({propertyId:m._id,stockQuantity:u,paymentMethod:"wallet",referralCode:h.trim()||void 0}).unwrap(),M.oR.success("Property purchased successfully!"),o(),x&&await x({propertyId:m._id,shares:u,amount:B,referralCode:h.trim()||void 0})}catch(s){var e;console.error("Purchase error:",s),M.oR.error((null==s||null==(e=s.data)?void 0:e.message)||"Failed to purchase property")}finally{j(!1)}};return(0,a.jsx)(P.lG,{open:d,onOpenChange:o,children:(0,a.jsxs)(P.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(P.c7,{children:(0,a.jsx)(P.L3,{className:"text-xl font-semibold",children:"Purchase Property Shares"})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("img",{src:m.images[0]||"/placeholder-property.jpg",alt:m.title,className:"w-20 h-20 object-cover rounded-lg"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg",children:m.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:m.location}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,a.jsxs)(A.E,{variant:"secondary",children:["₹",m.pricePerShare.toLocaleString(),"/share"]}),(0,a.jsxs)(A.E,{variant:"outline",children:[m.availableShares," available"]}),(0,a.jsxs)(A.E,{className:"bg-green-100 text-green-800",children:[m.expectedReturns,"% returns"]})]})]})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(E.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Wallet Balance"})]}),(0,a.jsxs)("span",{className:"text-lg font-semibold text-green-600",children:["₹",b.toLocaleString()]})]})})}),"approved"!==k&&(0,a.jsx)(i.Zp,{className:"border-orange-200 bg-orange-50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(L.A,{className:"h-5 w-5 text-orange-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium text-orange-800",children:"KYC Verification Required"}),(0,a.jsx)("p",{className:"text-sm text-orange-700",children:"Complete your KYC verification to start investing in properties."})]}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>{o(),v.push("/kyc")},className:"border-orange-300 text-orange-700 hover:bg-orange-100",children:"Complete KYC"})]})})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(I.J,{htmlFor:"shares",className:"text-sm font-medium",children:"Number of Shares"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)(T.p,{id:"shares",type:"number",min:"1",max:z,value:u,onChange:e=>Y(e.target.value),className:"flex-1"}),(0,a.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>{p(z)},disabled:0===z,children:["Max (",z,")"]})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Maximum affordable: ",_," shares"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(I.J,{htmlFor:"referralCode",className:"text-sm font-medium",children:"Referral Code (Optional)"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)(T.p,{id:"referralCode",type:"text",placeholder:"Enter referral code for bonus",value:h,onChange:e=>y(e.target.value.toUpperCase()),className:"flex-1"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Get bonus rewards with a valid referral code"})]})]}),(0,a.jsx)(q,{}),(0,a.jsx)(i.Zp,{children:(0,a.jsxs)(i.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,a.jsx)(w.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Purchase Summary"})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Shares:"}),(0,a.jsx)("span",{children:u.toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Price per share:"}),(0,a.jsxs)("span",{children:["₹",m.pricePerShare.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Subtotal:"}),(0,a.jsxs)("span",{children:["₹",B.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Platform fee (2%):"}),(0,a.jsxs)("span",{children:["₹",Math.round(.02*B).toLocaleString()]})]}),(0,a.jsx)(q,{}),(0,a.jsxs)("div",{className:"flex justify-between font-semibold text-lg",children:[(0,a.jsx)("span",{children:"Total Amount:"}),(0,a.jsxs)("span",{children:["₹",Math.round(1.02*B).toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,a.jsx)("span",{children:"Remaining balance:"}),(0,a.jsxs)("span",{children:["₹",Math.max(0,b-Math.round(1.02*B)).toLocaleString()]})]})]})]})}),!U&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsx)(L.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"text-red-700 text-sm",children:"Insufficient wallet balance. Please add money to your wallet."})]}),B<m.minimumInvestment&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,a.jsx)(L.A,{className:"h-5 w-5 text-yellow-600"}),(0,a.jsxs)("span",{className:"text-yellow-700 text-sm",children:["Minimum investment amount is ₹",m.minimumInvestment.toLocaleString()]})]}),U&&B>=m.minimumInvestment&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,a.jsx)(R.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("span",{className:"text-green-700 text-sm",children:["Ready to purchase! You will own ",(u/m.totalShares*100).toFixed(2),"% of this property."]})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)(c.$,{variant:"outline",onClick:o,className:"flex-1",disabled:f,children:"Cancel"}),(0,a.jsx)(c.$,{onClick:Z,className:"flex-1",disabled:"approved"!==k||!U||B<m.minimumInvestment||f,children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing..."]}):"approved"!==k?"Complete KYC to Purchase":"Purchase for ₹".concat(Math.round(1.02*B).toLocaleString())})]})]})]})})}function B(){var e,s,t,P,T,I,A,C,q,E,L,D,R,O,W,B,U,_,z,Y,Z,$,V,H,K,J,G;let X=(0,l.useParams)().id,[Q,ee]=(0,r.useState)(0),[es,et]=(0,r.useState)(1),[ea,er]=(0,r.useState)(!1),[el,en]=(0,r.useState)(!1),[ei,ec]=(0,r.useState)(!1),{data:ed,isLoading:eo,error:em}=(0,S.Zj)(X),{data:ex,isLoading:eu}=(0,S.$k)({propertyId:X,stockQuantity:es,investmentPeriod:12},{skip:!ei}),ep=null==ed?void 0:ed.data,eh=null==ex?void 0:ex.data,ey=async e=>{try{console.log("Purchase data:",e),M.oR.success("Successfully purchased ".concat(e.shares," shares for ₹").concat(e.amount.toLocaleString(),"!"))}catch(e){throw console.error("Purchase error:",e),e}};if(eo)return(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,a.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg"}),(0,a.jsx)("div",{className:"h-48 bg-gray-200 rounded-lg"})]}),(0,a.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg"})]})]})});if(em||!ep)return(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(d.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Property not found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"The property you're looking for doesn't exist or has been removed."}),(0,a.jsxs)(c.$,{onClick:()=>window.history.back(),children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Go Back"]})]})});let ef=es*((null==(e=ep.stockInfo)?void 0:e.stockPrice)||0);return(0,a.jsxs)(n.A,{children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(c.$,{variant:"outline",onClick:()=>window.history.back(),className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Back to Properties"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Save"]}),(0,a.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Share"]})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:ep.name}),ep.featured&&(0,a.jsxs)("div",{className:"bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-medium flex items-center",children:[(0,a.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"Featured"]})]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-600 mb-4",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"string"==typeof ep.location?ep.location:(null==(s=ep.location)?void 0:s.address)||"Location not specified"]}),(0,a.jsx)("p",{className:"text-gray-700 mb-4",children:ep.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-6 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{children:"string"==typeof ep.developer?ep.developer:(null==(t=ep.developer)?void 0:t.name)||"Developer"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("span",{children:["Launch: ",(0,k.Yq)(ep.launchDate)]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("span",{children:[ep.soldStocks," investors"]})]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-1",children:(0,k.vv)(ep.pricePerStock)}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"per stock"}),(0,a.jsxs)("div",{className:"text-lg font-semibold text-green-600",children:[(0,k.Ee)(ep.roi)," returns"]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-0",children:ep.images&&ep.images.length>0?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"relative h-96 rounded-t-lg overflow-hidden",children:[(0,a.jsx)("img",{src:null==(P=ep.images[Q])?void 0:P.url,alt:ep.name,className:"w-full h-full object-cover"}),(0,a.jsx)("button",{className:"absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70",children:(0,a.jsx)(f.A,{className:"h-4 w-4"})})]}),ep.images.length>1&&(0,a.jsx)("div",{className:"flex space-x-2 p-4 overflow-x-auto",children:ep.images.map((e,s)=>(0,a.jsx)("button",{onClick:()=>ee(s),className:"flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ".concat(Q===s?"border-blue-500":"border-gray-200"),children:(0,a.jsx)("img",{src:e.url,alt:"".concat(ep.name," ").concat(s+1),className:"w-full h-full object-cover"})},s))})]}):(0,a.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg flex items-center justify-center",children:(0,a.jsx)(d.A,{className:"h-16 w-16 text-gray-400"})})})}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Property Details"})}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Total Stocks"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:((null==(T=ep.stockInfo)?void 0:T.totalStocks)||0).toLocaleString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Available"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:((null==(I=ep.stockInfo)?void 0:I.availableStocks)||0).toLocaleString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Sold"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:((null==(A=ep.stockInfo)?void 0:A.stocksSold)||0).toLocaleString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Progress"}),(0,a.jsxs)("p",{className:"text-2xl font-bold",children:[(null==(C=ep.stockInfo)?void 0:C.totalStocks)?Math.round(((null==(q=ep.stockInfo)?void 0:q.stocksSold)||0)/ep.stockInfo.totalStocks*100):0,"%"]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,a.jsx)("span",{children:"Funding Progress"}),(0,a.jsxs)("span",{children:[(null==(E=ep.stockInfo)?void 0:E.stocksSold)||0," / ",(null==(L=ep.stockInfo)?void 0:L.totalStocks)||0," stocks"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(((null==(D=ep.stockInfo)?void 0:D.stocksSold)||0)/((null==(R=ep.stockInfo)?void 0:R.totalStocks)||1)*100,"%")}})})]})]})]}),ep.amenities&&ep.amenities.length>0&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Amenities"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:ep.amenities.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm",children:e})]},s))})})]}),ep.documents&&ep.documents.length>0&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Documents"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:ep.documents.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded flex items-center justify-center",children:(0,a.jsx)(v.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsx)("span",{className:"font-medium",children:e.name})]}),(0,a.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"View"]})]},s))})})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{className:"sticky top-6",children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Invest in this Property"})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Number of Stocks"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>et(Math.max(1,es-1)),disabled:es<=1,children:(0,a.jsx)(N.A,{className:"h-4 w-4"})}),(0,a.jsx)("input",{type:"number",value:es,onChange:e=>et(Math.max(1,parseInt(e.target.value)||1)),className:"flex-1 text-center px-3 py-2 border border-gray-300 rounded-lg",min:"1",max:ep.availableStocks}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>et(Math.min(ep.availableStocks,es+1)),disabled:es>=ep.availableStocks,children:(0,a.jsx)(b.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-3 p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Stock Price"}),(0,a.jsx)("span",{className:"font-medium",children:(null==(O=ep.stockInfo)?void 0:O.stockPrice)?(0,k.vv)(ep.stockInfo.stockPrice):"Not Available"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Quantity"}),(0,a.jsxs)("span",{className:"font-medium",children:[es," stocks"]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-lg font-semibold border-t pt-3",children:[(0,a.jsx)("span",{children:"Total Investment"}),(0,a.jsx)("span",{className:"text-blue-600",children:(0,k.vv)(ef)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Expected Returns"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:(null==(W=ep.stockInfo)?void 0:W.expectedROI)?(0,k.Ee)(ep.stockInfo.expectedROI):ep.expectedReturns?(0,k.Ee)(ep.expectedReturns):"TBD"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(c.$,{onClick:()=>en(!0),className:"w-full",disabled:0===((null==(B=ep.stockInfo)?void 0:B.availableStocks)||0),children:0===((null==(U=ep.stockInfo)?void 0:U.availableStocks)||0)?"Sold Out":"Buy Shares"}),(0,a.jsxs)(c.$,{variant:"outline",onClick:()=>ec(!0),className:"w-full",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Calculate Returns"]})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:["Minimum investment: ",(0,k.vv)((null==(_=ep.stockInfo)?void 0:_.minimumPurchase)||(null==(z=ep.stockInfo)?void 0:z.stockPrice)||0)]})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Key Metrics"})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Property Type"}),(0,a.jsx)("span",{className:"font-medium capitalize",children:ep.type})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Value"}),(0,a.jsx)("span",{className:"font-medium",children:(0,k.vv)(ep.totalValue)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Expected ROI"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:(0,k.Ee)(ep.roi)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Completion Date"}),(0,a.jsx)("span",{className:"font-medium",children:(0,k.Yq)(ep.completionDate)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Status"}),(0,a.jsx)("span",{className:"font-medium capitalize ".concat("active"===ep.status?"text-green-600":"upcoming"===ep.status?"text-blue-600":"text-gray-600"),children:ep.status})]})]})]})]})]})]}),ea&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Confirm Investment"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:ep.name}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Stocks:"}),(0,a.jsx)("span",{children:es})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Price per stock:"}),(0,a.jsx)("span",{children:(0,k.vv)(ep.pricePerStock)})]}),(0,a.jsxs)("div",{className:"flex justify-between font-semibold border-t pt-2",children:[(0,a.jsx)("span",{children:"Total:"}),(0,a.jsx)("span",{children:(0,k.vv)(ef)})]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(c.$,{variant:"outline",onClick:()=>er(!1),className:"flex-1",children:"Cancel"}),(0,a.jsx)(c.$,{className:"flex-1",children:"Confirm Investment"})]})]})]})}),ei&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-lg",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Investment Calculator"}),(0,a.jsxs)("div",{className:"space-y-4",children:[eh&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Investment Amount:"}),(0,a.jsx)("span",{className:"font-medium",children:(0,k.vv)(eh.totalAmount)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Expected Returns (1 year):"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:(0,k.vv)(eh.yearlyReturns)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Maturity Amount:"}),(0,a.jsx)("span",{className:"font-medium text-blue-600",children:(0,k.vv)(eh.maturityAmount)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"ROI:"}),(0,a.jsx)("span",{className:"font-medium",children:(0,k.Ee)(eh.roi)})]})]}),(0,a.jsx)(c.$,{variant:"outline",onClick:()=>ec(!1),className:"w-full",children:"Close"})]})]})}),ep&&(0,a.jsx)(F,{isOpen:el,onClose:()=>en(!1),property:{_id:ep._id,title:ep.name,pricePerShare:(null==(Y=ep.stockInfo)?void 0:Y.stockPrice)||0,availableShares:(null==(Z=ep.stockInfo)?void 0:Z.availableStocks)||0,totalShares:(null==($=ep.stockInfo)?void 0:$.totalStocks)||0,minimumInvestment:(null==(V=ep.stockInfo)?void 0:V.minimumPurchase)||(null==(H=ep.stockInfo)?void 0:H.stockPrice)||0,images:(null==(K=ep.images)?void 0:K.map(e=>e.url))||[],location:"string"==typeof ep.location?ep.location:(null==(J=ep.location)?void 0:J.address)||"",expectedReturns:(null==(G=ep.stockInfo)?void 0:G.expectedROI)||ep.expectedReturns||0},onPurchase:ey})]})}},7971:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var a=t(9605),r=t(9585),l=t(6994);let n=r.forwardRef((e,s)=>{let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...n})});n.displayName="Input"},9849:(e,s,t)=>{"use strict";t.d(s,{AR:()=>r,rx:()=>a});let{useGetWalletBalanceQuery:a,useGetWalletTransactionsQuery:r,useAddMoneyToWalletMutation:l,useWithdrawMoneyMutation:n,useGetPaymentMethodsQuery:i,useAddPaymentMethodMutation:c,useUpdatePaymentMethodMutation:d,useDeletePaymentMethodMutation:o,useVerifyPaymentMethodMutation:m,useGetTransactionByIdQuery:x,useGetWalletAnalyticsQuery:u,useExportWalletStatementMutation:p,useSetTransactionAlertsMutation:h,useGetWalletLimitsQuery:y,useRequestLimitIncreaseMutation:f}=t(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getWalletBalance:e.query({query:()=>"/wallet",providesTags:[{type:"Wallet",id:"BALANCE"}],keepUnusedDataFor:120}),getWalletTransactions:e.query({query:e=>({url:"/wallet/transactions",params:{page:e.page||1,limit:e.limit||20,...e.type&&{type:e.type},...e.status&&{status:e.status},...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),transformResponse:e=>{var s,t;return(null==e?void 0:e.success)&&(null==e||null==(t=e.data)||null==(s=t.data)?void 0:s.length)?e:{success:!0,message:"Transactions retrieved successfully",data:{data:[{_id:"demo-txn-1",type:"stock_purchase",amount:25e3,status:"completed",description:"Property Investment - Luxury Apartments",createdAt:new Date().toISOString(),reference:"TXN001"},{_id:"demo-txn-2",type:"deposit",amount:5e4,status:"completed",description:"Wallet Deposit",createdAt:new Date(Date.now()-864e5).toISOString(),reference:"TXN002"},{_id:"demo-txn-3",type:"investment",amount:1e5,status:"completed",description:"Property Investment - Commercial Complex",createdAt:new Date(Date.now()-1728e5).toISOString(),reference:"TXN003"}],pagination:{page:1,limit:20,total:3,pages:1}}}},providesTags:e=>{var s;return(null==e||null==(s=e.data)?void 0:s.data)?[...e.data.data.map(e=>{let{_id:s}=e;return{type:"Transaction",id:s}}),{type:"Transaction",id:"LIST"}]:[{type:"Transaction",id:"LIST"}]},keepUnusedDataFor:300}),addMoneyToWallet:e.mutation({query:e=>({url:"/wallet/add-funds",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),withdrawMoney:e.mutation({query:e=>({url:"/wallet/withdraw",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),getPaymentMethods:e.query({query:()=>"/wallet/payment-methods",providesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}],keepUnusedDataFor:600}),addPaymentMethod:e.mutation({query:e=>({url:"/wallet/payment-methods",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),updatePaymentMethod:e.mutation({query:e=>{let{id:s,...t}=e;return{url:"/wallet/payment-methods/".concat(s),method:"PUT",body:t}},invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),deletePaymentMethod:e.mutation({query:e=>({url:"/wallet/payment-methods/".concat(e),method:"DELETE"}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),verifyPaymentMethod:e.mutation({query:e=>{let{id:s,verificationData:t}=e;return{url:"/wallet/payment-methods/".concat(s,"/verify"),method:"POST",body:t}},invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),getTransactionById:e.query({query:e=>"/wallet/transactions/".concat(e),providesTags:(e,s,t)=>[{type:"Transaction",id:t}],keepUnusedDataFor:1800}),getWalletAnalytics:e.query({query:e=>{let{period:s="1Y"}=e;return{url:"/wallet/analytics",params:{period:s}}},providesTags:[{type:"Wallet",id:"ANALYTICS"}],keepUnusedDataFor:900}),exportWalletStatement:e.mutation({query:e=>({url:"/wallet/export-statement",method:"POST",body:e})}),setTransactionAlerts:e.mutation({query:e=>({url:"/wallet/alerts",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"}]}),getWalletLimits:e.query({query:()=>"/wallet/limits",providesTags:[{type:"Wallet",id:"LIMITS"}],keepUnusedDataFor:3600}),requestLimitIncrease:e.mutation({query:e=>({url:"/wallet/request-limit-increase",method:"POST",body:e})})})})}},e=>{var s=s=>e(e.s=s);e.O(0,[2094,5315,7436,7693,4329,3590,3692,1147,7627,3005,390,110,7358],()=>s(5607)),_N_E=e.O()}]);