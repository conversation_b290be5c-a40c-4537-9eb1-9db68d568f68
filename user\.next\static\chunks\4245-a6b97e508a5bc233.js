"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4245],{2747:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(5050).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},3089:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(5050).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4281:(e,t,n)=>{n.d(t,{X:()=>l});var r=n(9585),o=n(6921);function l(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},5080:(e,t,n)=>{n.d(t,{UC:()=>nd,YJ:()=>nh,In:()=>nc,q7:()=>ng,VF:()=>nw,p4:()=>nv,JU:()=>nm,ZL:()=>nf,bL:()=>na,wn:()=>nx,PP:()=>ny,wv:()=>nb,l9:()=>ns,WT:()=>nu,LM:()=>np});var r=n(9585),o=n(3220);function l(e,[t,n]){return Math.min(n,Math.max(t,e))}var i=n(4761),a=n(4321),s=n(4455),u=n(8972),c=n(153),f=n(7271),d=n(5790),p=n(6452),h=n(7421);let m=["top","right","bottom","left"],g=Math.min,v=Math.max,w=Math.round,y=Math.floor,x=e=>({x:e,y:e}),b={left:"right",right:"left",bottom:"top",top:"bottom"},S={start:"end",end:"start"};function C(e,t){return"function"==typeof e?e(t):e}function R(e){return e.split("-")[0]}function A(e){return e.split("-")[1]}function T(e){return"x"===e?"y":"x"}function P(e){return"y"===e?"height":"width"}let k=new Set(["top","bottom"]);function E(e){return k.has(R(e))?"y":"x"}function j(e){return e.replace(/start|end/g,e=>S[e])}let L=["left","right"],N=["right","left"],D=["top","bottom"],M=["bottom","top"];function H(e){return e.replace(/left|right|bottom|top/g,e=>b[e])}function O(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function I(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function B(e,t,n){let r,{reference:o,floating:l}=e,i=E(t),a=T(E(t)),s=P(a),u=R(t),c="y"===i,f=o.x+o.width/2-l.width/2,d=o.y+o.height/2-l.height/2,p=o[s]/2-l[s]/2;switch(u){case"top":r={x:f,y:o.y-l.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-l.width,y:d};break;default:r={x:o.x,y:o.y}}switch(A(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let F=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=B(u,r,s),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:l,fn:m}=a[n],{x:g,y:v,data:w,reset:y}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:i,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=v?v:f,p={...p,[l]:{...p[l],...w}},y&&h<=50&&(h++,"object"==typeof y&&(y.placement&&(d=y.placement),y.rects&&(u=!0===y.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:c,y:f}=B(u,d,s)),n=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:p}};async function V(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:i,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=C(t,e),h=O(p),m=a[d?"floating"===f?"reference":"floating":f],g=I(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(m)))||n?m:m.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),v="floating"===f?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,w=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),y=await (null==l.isElement?void 0:l.isElement(w))&&await (null==l.getScale?void 0:l.getScale(w))||{x:1,y:1},x=I(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:w,strategy:s}):v);return{top:(g.top-x.top+h.top)/y.y,bottom:(x.bottom-g.bottom+h.bottom)/y.y,left:(g.left-x.left+h.left)/y.x,right:(x.right-g.right+h.right)/y.x}}function W(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function _(e){return m.some(t=>e[t]>=0)}let z=new Set(["left","top"]);async function G(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=R(n),a=A(n),s="y"===E(n),u=z.has(i)?-1:1,c=l&&s?-1:1,f=C(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),s?{x:p*c,y:d*u}:{x:d*u,y:p*c}}function K(){return"undefined"!=typeof window}function U(e){return Y(e)?(e.nodeName||"").toLowerCase():"#document"}function q(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function X(e){var t;return null==(t=(Y(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Y(e){return!!K()&&(e instanceof Node||e instanceof q(e).Node)}function Z(e){return!!K()&&(e instanceof Element||e instanceof q(e).Element)}function $(e){return!!K()&&(e instanceof HTMLElement||e instanceof q(e).HTMLElement)}function J(e){return!!K()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof q(e).ShadowRoot)}let Q=new Set(["inline","contents"]);function ee(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ef(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Q.has(o)}let et=new Set(["table","td","th"]),en=[":popover-open",":modal"];function er(e){return en.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eo=["transform","translate","scale","rotate","perspective"],el=["transform","translate","scale","rotate","perspective","filter"],ei=["paint","layout","strict","content"];function ea(e){let t=es(),n=Z(e)?ef(e):e;return eo.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||el.some(e=>(n.willChange||"").includes(e))||ei.some(e=>(n.contain||"").includes(e))}function es(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eu=new Set(["html","body","#document"]);function ec(e){return eu.has(U(e))}function ef(e){return q(e).getComputedStyle(e)}function ed(e){return Z(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ep(e){if("html"===U(e))return e;let t=e.assignedSlot||e.parentNode||J(e)&&e.host||X(e);return J(t)?t.host:t}function eh(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ep(t);return ec(n)?t.ownerDocument?t.ownerDocument.body:t.body:$(n)&&ee(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),i=q(o);if(l){let e=em(i);return t.concat(i,i.visualViewport||[],ee(o)?o:[],e&&n?eh(e):[])}return t.concat(o,eh(o,[],n))}function em(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eg(e){let t=ef(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=$(e),l=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=w(n)!==l||w(r)!==i;return a&&(n=l,r=i),{width:n,height:r,$:a}}function ev(e){return Z(e)?e:e.contextElement}function ew(e){let t=ev(e);if(!$(t))return x(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=eg(t),i=(l?w(n.width):n.width)/r,a=(l?w(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),a&&Number.isFinite(a)||(a=1),{x:i,y:a}}let ey=x(0);function ex(e){let t=q(e);return es()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ey}function eb(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=ev(e),a=x(1);t&&(r?Z(r)&&(a=ew(r)):a=ew(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===q(i))&&o)?ex(i):x(0),u=(l.left+s.x)/a.x,c=(l.top+s.y)/a.y,f=l.width/a.x,d=l.height/a.y;if(i){let e=q(i),t=r&&Z(r)?q(r):r,n=e,o=em(n);for(;o&&r&&t!==n;){let e=ew(o),t=o.getBoundingClientRect(),r=ef(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,f*=e.x,d*=e.y,u+=l,c+=i,o=em(n=q(o))}}return I({width:f,height:d,x:u,y:c})}function eS(e,t){let n=ed(e).scrollLeft;return t?t.left+n:eb(X(e)).left+n}function eC(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eS(e,r)),y:r.top+t.scrollTop}}let eR=new Set(["absolute","fixed"]);function eA(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=q(e),r=X(e),o=n.visualViewport,l=r.clientWidth,i=r.clientHeight,a=0,s=0;if(o){l=o.width,i=o.height;let e=es();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:l,height:i,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=X(e),n=ed(e),r=e.ownerDocument.body,o=v(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=v(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),i=-n.scrollLeft+eS(e),a=-n.scrollTop;return"rtl"===ef(r).direction&&(i+=v(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:i,y:a}}(X(e));else if(Z(t))r=function(e,t){let n=eb(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=$(e)?ew(e):x(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:r*l.y}}(t,n);else{let n=ex(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return I(r)}function eT(e){return"static"===ef(e).position}function eP(e,t){if(!$(e)||"fixed"===ef(e).position)return null;if(t)return t(e);let n=e.offsetParent;return X(e)===n&&(n=n.ownerDocument.body),n}function ek(e,t){var n;let r=q(e);if(er(e))return r;if(!$(e)){let t=ep(e);for(;t&&!ec(t);){if(Z(t)&&!eT(t))return t;t=ep(t)}return r}let o=eP(e,t);for(;o&&(n=o,et.has(U(n)))&&eT(o);)o=eP(o,t);return o&&ec(o)&&eT(o)&&!ea(o)?r:o||function(e){let t=ep(e);for(;$(t)&&!ec(t);){if(ea(t))return t;if(er(t))break;t=ep(t)}return null}(e)||r}let eE=async function(e){let t=this.getOffsetParent||ek,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=$(t),o=X(t),l="fixed"===n,i=eb(e,!0,l,t),a={scrollLeft:0,scrollTop:0},s=x(0);if(r||!r&&!l)if(("body"!==U(t)||ee(o))&&(a=ed(t)),r){let e=eb(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eS(o));l&&!r&&o&&(s.x=eS(o));let u=!o||r||l?x(0):eC(o,a);return{x:i.left+a.scrollLeft-s.x-u.x,y:i.top+a.scrollTop-s.y-u.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ej={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,i=X(r),a=!!t&&er(t.floating);if(r===i||a&&l)return n;let s={scrollLeft:0,scrollTop:0},u=x(1),c=x(0),f=$(r);if((f||!f&&!l)&&(("body"!==U(r)||ee(i))&&(s=ed(r)),$(r))){let e=eb(r);u=ew(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let d=!i||f||l?x(0):eC(i,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+d.x,y:n.y*u.y-s.scrollTop*u.y+c.y+d.y}},getDocumentElement:X,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?er(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eh(e,[],!1).filter(e=>Z(e)&&"body"!==U(e)),o=null,l="fixed"===ef(e).position,i=l?ep(e):e;for(;Z(i)&&!ec(i);){let t=ef(i),n=ea(i);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&eR.has(o.position)||ee(i)&&!n&&function e(t,n){let r=ep(t);return!(r===n||!Z(r)||ec(r))&&("fixed"===ef(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=ep(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],i=l[0],a=l.reduce((e,n)=>{let r=eA(t,n,o);return e.top=v(r.top,e.top),e.right=g(r.right,e.right),e.bottom=g(r.bottom,e.bottom),e.left=v(r.left,e.left),e},eA(t,i,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:ek,getElementRects:eE,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eg(e);return{width:t,height:n}},getScale:ew,isElement:Z,isRTL:function(e){return"rtl"===ef(e).direction}};function eL(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eN=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:i,elements:a,middlewareData:s}=t,{element:u,padding:c=0}=C(e,t)||{};if(null==u)return{};let f=O(c),d={x:n,y:r},p=T(E(o)),h=P(p),m=await i.getDimensions(u),w="y"===p,y=w?"clientHeight":"clientWidth",x=l.reference[h]+l.reference[p]-d[p]-l.floating[h],b=d[p]-l.reference[p],S=await (null==i.getOffsetParent?void 0:i.getOffsetParent(u)),R=S?S[y]:0;R&&await (null==i.isElement?void 0:i.isElement(S))||(R=a.floating[y]||l.floating[h]);let k=R/2-m[h]/2-1,j=g(f[w?"top":"left"],k),L=g(f[w?"bottom":"right"],k),N=R-m[h]-L,D=R/2-m[h]/2+(x/2-b/2),M=v(j,g(D,N)),H=!s.arrow&&null!=A(o)&&D!==M&&l.reference[h]/2-(D<j?j:L)-m[h]/2<0,I=H?D<j?D-j:D-N:0;return{[p]:d[p]+I,data:{[p]:M,centerOffset:D-M-I,...H&&{alignmentOffset:I}},reset:H}}}),eD=(e,t,n)=>{let r=new Map,o={platform:ej,...n},l={...o.platform,_c:r};return F(e,t,{...o,platform:l})};var eM="undefined"!=typeof document?r.useLayoutEffect:function(){};function eH(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eH(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eH(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eO(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eI(e,t){let n=eO(e);return Math.round(t*n)/n}function eB(e){let t=r.useRef(e);return eM(()=>{t.current=e}),t}let eF=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eN({element:n.current,padding:r}).fn(t):{}:n?eN({element:n,padding:r}).fn(t):{}}}),eV=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:i,middlewareData:a}=t,s=await G(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:l+s.y,data:{...s,placement:i}}}}}(e),options:[e,t]}),eW=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:i=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=C(e,t),u={x:n,y:r},c=await V(t,s),f=E(R(o)),d=T(f),p=u[d],h=u[f];if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=p+c[e],r=p-c[t];p=v(n,g(p,r))}if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=h+c[e],r=h-c[t];h=v(n,g(h,r))}let m=a.fn({...t,[d]:p,[f]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[d]:l,[f]:i}}}}}}(e),options:[e,t]}),e_=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=C(e,t),c={x:n,y:r},f=E(o),d=T(f),p=c[d],h=c[f],m=C(a,t),g="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===d?"height":"width",t=l.reference[d]-l.floating[e]+g.mainAxis,n=l.reference[d]+l.reference[e]-g.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var v,w;let e="y"===d?"width":"height",t=z.has(R(o)),n=l.reference[f]-l.floating[e]+(t&&(null==(v=i.offset)?void 0:v[f])||0)+(t?0:g.crossAxis),r=l.reference[f]+l.reference[e]+(t?0:(null==(w=i.offset)?void 0:w[f])||0)-(t?g.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}}(e),options:[e,t]}),ez=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,i;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:f,elements:d}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:w=!0,...y}=C(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let x=R(a),b=E(c),S=R(c)===c,k=await (null==f.isRTL?void 0:f.isRTL(d.floating)),O=m||(S||!w?[H(c)]:function(e){let t=H(e);return[j(e),t,j(t)]}(c)),I="none"!==v;!m&&I&&O.push(...function(e,t,n,r){let o=A(e),l=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?N:L;return t?L:N;case"left":case"right":return t?D:M;default:return[]}}(R(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(j)))),l}(c,w,v,k));let B=[c,...O],F=await V(t,y),W=[],_=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&W.push(F[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=A(e),o=T(E(e)),l=P(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=H(i)),[i,H(i)]}(a,u,k);W.push(F[e[0]],F[e[1]])}if(_=[..._,{placement:a,overflows:W}],!W.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=B[e];if(t&&("alignment"!==h||b===E(t)||_.every(e=>e.overflows[0]>0&&E(e.placement)===b)))return{data:{index:e,overflows:_},reset:{placement:t}};let n=null==(l=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(g){case"bestFit":{let e=null==(i=_.filter(e=>{if(I){let t=E(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eG=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:i,rects:a,platform:s,elements:u}=t,{apply:c=()=>{},...f}=C(e,t),d=await V(t,f),p=R(i),h=A(i),m="y"===E(i),{width:w,height:y}=a.floating;"top"===p||"bottom"===p?(o=p,l=h===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(l=p,o="end"===h?"top":"bottom");let x=y-d.top-d.bottom,b=w-d.left-d.right,S=g(y-d[o],x),T=g(w-d[l],b),P=!t.middlewareData.shift,k=S,j=T;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(j=b),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=x),P&&!h){let e=v(d.left,0),t=v(d.right,0),n=v(d.top,0),r=v(d.bottom,0);m?j=w-2*(0!==e||0!==t?e+t:v(d.left,d.right)):k=y-2*(0!==n||0!==r?n+r:v(d.top,d.bottom))}await c({...t,availableWidth:j,availableHeight:k});let L=await s.getDimensions(u.floating);return w!==L.width||y!==L.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eK=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=C(e,t);switch(r){case"referenceHidden":{let e=W(await V(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:_(e)}}}case"escaped":{let e=W(await V(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:_(e)}}}default:return{}}}}}(e),options:[e,t]}),eU=(e,t)=>({...eF(e),options:[e,t]});var eq=n(7905),eX=n(9605),eY=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,eX.jsx)(eq.sG.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eX.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eY.displayName="Arrow";var eZ=n(7090),e$=n(6921),eJ=n(4281),eQ="Popper",[e0,e1]=(0,u.A)(eQ),[e2,e5]=e0(eQ),e9=e=>{let{__scopePopper:t,children:n}=e,[o,l]=r.useState(null);return(0,eX.jsx)(e2,{scope:t,anchor:o,onAnchorChange:l,children:n})};e9.displayName=eQ;var e6="PopperAnchor",e8=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...l}=e,i=e5(e6,n),a=r.useRef(null),u=(0,s.s)(t,a);return r.useEffect(()=>{i.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eX.jsx)(eq.sG.div,{...l,ref:u})});e8.displayName=e6;var e4="PopperContent",[e3,e7]=e0(e4),te=r.forwardRef((e,t)=>{var n,l,i,a,u,c,f,d;let{__scopePopper:p,side:h="bottom",sideOffset:m=0,align:w="center",alignOffset:x=0,arrowPadding:b=0,avoidCollisions:S=!0,collisionBoundary:C=[],collisionPadding:R=0,sticky:A="partial",hideWhenDetached:T=!1,updatePositionStrategy:P="optimized",onPlaced:k,...E}=e,j=e5(e4,p),[L,N]=r.useState(null),D=(0,s.s)(t,e=>N(e)),[M,H]=r.useState(null),O=(0,eJ.X)(M),I=null!=(f=null==O?void 0:O.width)?f:0,B=null!=(d=null==O?void 0:O.height)?d:0,F="number"==typeof R?R:{top:0,right:0,bottom:0,left:0,...R},V=Array.isArray(C)?C:[C],W=V.length>0,_={padding:F,boundary:V.filter(to),altBoundary:W},{refs:z,floatingStyles:G,placement:K,isPositioned:U,middlewareData:q}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:l=[],platform:i,elements:{reference:a,floating:s}={},transform:u=!0,whileElementsMounted:c,open:f}=e,[d,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=r.useState(l);eH(h,l)||m(l);let[g,v]=r.useState(null),[w,y]=r.useState(null),x=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=r.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),S=a||g,C=s||w,R=r.useRef(null),A=r.useRef(null),T=r.useRef(d),P=null!=c,k=eB(c),E=eB(i),j=eB(f),L=r.useCallback(()=>{if(!R.current||!A.current)return;let e={placement:t,strategy:n,middleware:h};E.current&&(e.platform=E.current),eD(R.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==j.current};N.current&&!eH(T.current,t)&&(T.current=t,o.flushSync(()=>{p(t)}))})},[h,t,n,E,j]);eM(()=>{!1===f&&T.current.isPositioned&&(T.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[f]);let N=r.useRef(!1);eM(()=>(N.current=!0,()=>{N.current=!1}),[]),eM(()=>{if(S&&(R.current=S),C&&(A.current=C),S&&C){if(k.current)return k.current(S,C,L);L()}},[S,C,L,k,P]);let D=r.useMemo(()=>({reference:R,floating:A,setReference:x,setFloating:b}),[x,b]),M=r.useMemo(()=>({reference:S,floating:C}),[S,C]),H=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=eI(M.floating,d.x),r=eI(M.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eO(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,M.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:L,refs:D,elements:M,floatingStyles:H}),[d,L,D,M,H])}({strategy:"fixed",placement:h+("center"!==w?"-"+w:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=ev(e),f=l||i?[...c?eh(c):[],...eh(t)]:[];f.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)});let d=c&&s?function(e,t){let n,r=null,o=X(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function i(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),l();let u=e.getBoundingClientRect(),{left:c,top:f,width:d,height:p}=u;if(a||t(),!d||!p)return;let h=y(f),m=y(o.clientWidth-(c+d)),w={rootMargin:-h+"px "+-m+"px "+-y(o.clientHeight-(f+p))+"px "+-y(c)+"px",threshold:v(0,g(1,s))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==s){if(!x)return i();r?i(!1,r):n=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==r||eL(u,e.getBoundingClientRect())||i(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),l}(c,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!u&&h.observe(c),h.observe(t));let m=u?eb(e):null;return u&&function t(){let r=eb(e);m&&!eL(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{l&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=h)||e.disconnect(),h=null,u&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===P})},elements:{reference:j.anchor},middleware:[eV({mainAxis:m+B,alignmentAxis:x}),S&&eW({mainAxis:!0,crossAxis:!1,limiter:"partial"===A?e_():void 0,..._}),S&&ez({..._}),eG({..._,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:l,height:i}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),M&&eU({element:M,padding:b}),tl({arrowWidth:I,arrowHeight:B}),T&&eK({strategy:"referenceHidden",..._})]}),[Y,Z]=ti(K),$=(0,eZ.c)(k);(0,e$.N)(()=>{U&&(null==$||$())},[U,$]);let J=null==(n=q.arrow)?void 0:n.x,Q=null==(l=q.arrow)?void 0:l.y,ee=(null==(i=q.arrow)?void 0:i.centerOffset)!==0,[et,en]=r.useState();return(0,e$.N)(()=>{L&&en(window.getComputedStyle(L).zIndex)},[L]),(0,eX.jsx)("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:U?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:et,"--radix-popper-transform-origin":[null==(a=q.transformOrigin)?void 0:a.x,null==(u=q.transformOrigin)?void 0:u.y].join(" "),...(null==(c=q.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eX.jsx)(e3,{scope:p,placedSide:Y,onArrowChange:H,arrowX:J,arrowY:Q,shouldHideArrow:ee,children:(0,eX.jsx)(eq.sG.div,{"data-side":Y,"data-align":Z,...E,ref:D,style:{...E.style,animation:U?void 0:"none"}})})})});te.displayName=e4;var tt="PopperArrow",tn={top:"bottom",right:"left",bottom:"top",left:"right"},tr=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e7(tt,n),l=tn[o.placedSide];return(0,eX.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eX.jsx)(eY,{...r,ref:t,style:{...r.style,display:"block"}})})});function to(e){return null!==e}tr.displayName=tt;var tl=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,l,i;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,h]=ti(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!=(l=null==(r=u.arrow)?void 0:r.x)?l:0)+f/2,v=(null!=(i=null==(o=u.arrow)?void 0:o.y)?i:0)+d/2,w="",y="";return"bottom"===p?(w=c?m:"".concat(g,"px"),y="".concat(-d,"px")):"top"===p?(w=c?m:"".concat(g,"px"),y="".concat(s.floating.height+d,"px")):"right"===p?(w="".concat(-d,"px"),y=c?m:"".concat(v,"px")):"left"===p&&(w="".concat(s.floating.width+d,"px"),y=c?m:"".concat(v,"px")),{data:{x:w,y}}}});function ti(e){let[t,n="center"]=e.split("-");return[t,n]}var ta=n(8036),ts=n(8130),tu=n(9728),tc=n(8069),tf=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,eX.jsx)(eq.sG.span,{...e,ref:t,style:{...tf,...e.style}})).displayName="VisuallyHidden";var td=n(3383),tp=n(4229),th=[" ","Enter","ArrowUp","ArrowDown"],tm=[" ","Enter"],tg="Select",[tv,tw,ty]=(0,a.N)(tg),[tx,tb]=(0,u.A)(tg,[ty,e1]),tS=e1(),[tC,tR]=tx(tg),[tA,tT]=tx(tg),tP=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:l,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:f,name:d,autoComplete:p,disabled:m,required:g,form:v}=e,w=tS(t),[y,x]=r.useState(null),[b,S]=r.useState(null),[C,R]=r.useState(!1),A=(0,c.jH)(f),[T,P]=(0,tu.i)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:tg}),[k,E]=(0,tu.i)({prop:a,defaultProp:s,onChange:u,caller:tg}),j=r.useRef(null),L=!y||v||!!y.closest("form"),[N,D]=r.useState(new Set),M=Array.from(N).map(e=>e.props.value).join(";");return(0,eX.jsx)(e9,{...w,children:(0,eX.jsxs)(tC,{required:g,scope:t,trigger:y,onTriggerChange:x,valueNode:b,onValueNodeChange:S,valueNodeHasChildren:C,onValueNodeHasChildrenChange:R,contentId:(0,h.B)(),value:k,onValueChange:E,open:T,onOpenChange:P,dir:A,triggerPointerDownPosRef:j,disabled:m,children:[(0,eX.jsx)(tv.Provider,{scope:t,children:(0,eX.jsx)(tA,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{D(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),L?(0,eX.jsxs)(nr,{"aria-hidden":!0,required:g,tabIndex:-1,name:d,autoComplete:p,value:k,onChange:e=>E(e.target.value),disabled:m,form:v,children:[void 0===k?(0,eX.jsx)("option",{value:""}):null,Array.from(N)]},M):null]})})};tP.displayName=tg;var tk="SelectTrigger",tE=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...l}=e,a=tS(n),u=tR(tk,n),c=u.disabled||o,f=(0,s.s)(t,u.onTriggerChange),d=tw(n),p=r.useRef("touch"),[h,m,g]=nl(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=ni(t,e,n);void 0!==r&&u.onValueChange(r.value)}),v=e=>{c||(u.onOpenChange(!0),g()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,eX.jsx)(e8,{asChild:!0,...a,children:(0,eX.jsx)(eq.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":no(u.value)?"":void 0,...l,ref:f,onClick:(0,i.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&v(e)}),onPointerDown:(0,i.m)(l.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(v(e),e.preventDefault())}),onKeyDown:(0,i.m)(l.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&th.includes(e.key)&&(v(),e.preventDefault())})})})});tE.displayName=tk;var tj="SelectValue",tL=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:i="",...a}=e,u=tR(tj,n),{onValueNodeHasChildrenChange:c}=u,f=void 0!==l,d=(0,s.s)(t,u.onValueNodeChange);return(0,e$.N)(()=>{c(f)},[c,f]),(0,eX.jsx)(eq.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:no(u.value)?(0,eX.jsx)(eX.Fragment,{children:i}):l})});tL.displayName=tj;var tN=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,eX.jsx)(eq.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tN.displayName="SelectIcon";var tD=e=>(0,eX.jsx)(ta.Z,{asChild:!0,...e});tD.displayName="SelectPortal";var tM="SelectContent",tH=r.forwardRef((e,t)=>{let n=tR(tM,e.__scopeSelect),[l,i]=r.useState();return((0,e$.N)(()=>{i(new DocumentFragment)},[]),n.open)?(0,eX.jsx)(tF,{...e,ref:t}):l?o.createPortal((0,eX.jsx)(tO,{scope:e.__scopeSelect,children:(0,eX.jsx)(tv.Slot,{scope:e.__scopeSelect,children:(0,eX.jsx)("div",{children:e.children})})}),l):null});tH.displayName=tM;var[tO,tI]=tx(tM),tB=(0,ts.TL)("SelectContent.RemoveScroll"),tF=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:a,onPointerDownOutside:u,side:c,sideOffset:h,align:m,alignOffset:g,arrowPadding:v,collisionBoundary:w,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S,...C}=e,R=tR(tM,n),[A,T]=r.useState(null),[P,k]=r.useState(null),E=(0,s.s)(t,e=>T(e)),[j,L]=r.useState(null),[N,D]=r.useState(null),M=tw(n),[H,O]=r.useState(!1),I=r.useRef(!1);r.useEffect(()=>{if(A)return(0,td.Eq)(A)},[A]),(0,d.Oh)();let B=r.useCallback(e=>{let[t,...n]=M().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&P&&(P.scrollTop=0),n===r&&P&&(P.scrollTop=P.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[M,P]),F=r.useCallback(()=>B([j,A]),[B,j,A]);r.useEffect(()=>{H&&F()},[H,F]);let{onOpenChange:V,triggerPointerDownPosRef:W}=R;r.useEffect(()=>{if(A){let e={x:0,y:0},t=t=>{var n,r,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=W.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(l=null==(r=W.current)?void 0:r.y)?l:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():A.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),W.current=null};return null!==W.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[A,V,W]),r.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[_,z]=nl(e=>{let t=M().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=ni(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),G=r.useCallback((e,t,n)=>{let r=!I.current&&!n;(void 0!==R.value&&R.value===t||r)&&(L(e),r&&(I.current=!0))},[R.value]),K=r.useCallback(()=>null==A?void 0:A.focus(),[A]),U=r.useCallback((e,t,n)=>{let r=!I.current&&!n;(void 0!==R.value&&R.value===t||r)&&D(e)},[R.value]),q="popper"===o?tW:tV,X=q===tW?{side:c,sideOffset:h,align:m,alignOffset:g,arrowPadding:v,collisionBoundary:w,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,eX.jsx)(tO,{scope:n,content:A,viewport:P,onViewportChange:k,itemRefCallback:G,selectedItem:j,onItemLeave:K,itemTextRefCallback:U,focusSelectedItem:F,selectedItemText:N,position:o,isPositioned:H,searchRef:_,children:(0,eX.jsx)(tp.A,{as:tB,allowPinchZoom:!0,children:(0,eX.jsx)(p.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.m)(l,e=>{var t;null==(t=R.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,eX.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,eX.jsx)(q,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...C,...X,onPlaced:()=>O(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,i.m)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=M().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});tF.displayName="SelectContentImpl";var tV=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...i}=e,a=tR(tM,n),u=tI(tM,n),[c,f]=r.useState(null),[d,p]=r.useState(null),h=(0,s.s)(t,e=>p(e)),m=tw(n),g=r.useRef(!1),v=r.useRef(!0),{viewport:w,selectedItem:y,selectedItemText:x,focusSelectedItem:b}=u,S=r.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&d&&w&&y&&x){let e=a.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=x.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,i=n.left-o,a=e.left-i,s=e.width+a,u=Math.max(s,t.width),f=l(i,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.left=f+"px"}else{let o=t.right-r.right,i=window.innerWidth-n.right-o,a=window.innerWidth-e.right-i,s=e.width+a,u=Math.max(s,t.width),f=l(i,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.right=f+"px"}let i=m(),s=window.innerHeight-20,u=w.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),h=parseInt(f.paddingTop,10),v=parseInt(f.borderBottomWidth,10),b=p+h+u+parseInt(f.paddingBottom,10)+v,S=Math.min(5*y.offsetHeight,b),C=window.getComputedStyle(w),R=parseInt(C.paddingTop,10),A=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,P=y.offsetHeight/2,k=p+h+(y.offsetTop+P);if(k<=T){let e=i.length>0&&y===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-T,P+(e?A:0)+(d.clientHeight-w.offsetTop-w.offsetHeight)+v);c.style.height=k+t+"px"}else{let e=i.length>0&&y===i[0].ref.current;c.style.top="0px";let t=Math.max(T,p+w.offsetTop+(e?R:0)+P);c.style.height=t+(b-k)+"px",w.scrollTop=k-T+w.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=S+"px",c.style.maxHeight=s+"px",null==o||o(),requestAnimationFrame(()=>g.current=!0)}},[m,a.trigger,a.valueNode,c,d,w,y,x,a.dir,o]);(0,e$.N)(()=>S(),[S]);let[C,R]=r.useState();(0,e$.N)(()=>{d&&R(window.getComputedStyle(d).zIndex)},[d]);let A=r.useCallback(e=>{e&&!0===v.current&&(S(),null==b||b(),v.current=!1)},[S,b]);return(0,eX.jsx)(t_,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:g,onScrollButtonChange:A,children:(0,eX.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,eX.jsx)(eq.sG.div,{...i,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});tV.displayName="SelectItemAlignedPosition";var tW=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,i=tS(n);return(0,eX.jsx)(te,{...i,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tW.displayName="SelectPopperPosition";var[t_,tz]=tx(tM,{}),tG="SelectViewport",tK=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...l}=e,a=tI(tG,n),u=tz(tG,n),c=(0,s.s)(t,a.onViewportChange),f=r.useRef(0);return(0,eX.jsxs)(eX.Fragment,{children:[(0,eX.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,eX.jsx)(tv.Slot,{scope:n,children:(0,eX.jsx)(eq.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,i.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if((null==r?void 0:r.current)&&n){let e=Math.abs(f.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,i=Math.min(r,l),a=l-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}f.current=t.scrollTop})})})]})});tK.displayName=tG;var tU="SelectGroup",[tq,tX]=tx(tU),tY=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,eX.jsx)(tq,{scope:n,id:o,children:(0,eX.jsx)(eq.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});tY.displayName=tU;var tZ="SelectLabel",t$=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tX(tZ,n);return(0,eX.jsx)(eq.sG.div,{id:o.id,...r,ref:t})});t$.displayName=tZ;var tJ="SelectItem",[tQ,t0]=tx(tJ),t1=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:l=!1,textValue:a,...u}=e,c=tR(tJ,n),f=tI(tJ,n),d=c.value===o,[p,m]=r.useState(null!=a?a:""),[g,v]=r.useState(!1),w=(0,s.s)(t,e=>{var t;return null==(t=f.itemRefCallback)?void 0:t.call(f,e,o,l)}),y=(0,h.B)(),x=r.useRef("touch"),b=()=>{l||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,eX.jsx)(tQ,{scope:n,value:o,disabled:l,textId:y,isSelected:d,onItemTextChange:r.useCallback(e=>{m(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,eX.jsx)(tv.ItemSlot,{scope:n,value:o,disabled:l,textValue:p,children:(0,eX.jsx)(eq.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":g?"":void 0,"aria-selected":d&&g,"data-state":d?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...u,ref:w,onFocus:(0,i.m)(u.onFocus,()=>v(!0)),onBlur:(0,i.m)(u.onBlur,()=>v(!1)),onClick:(0,i.m)(u.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:(0,i.m)(u.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:(0,i.m)(u.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,i.m)(u.onPointerMove,e=>{if(x.current=e.pointerType,l){var t;null==(t=f.onItemLeave)||t.call(f)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=f.onItemLeave)||t.call(f)}}),onKeyDown:(0,i.m)(u.onKeyDown,e=>{var t;((null==(t=f.searchRef)?void 0:t.current)===""||" "!==e.key)&&(tm.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});t1.displayName=tJ;var t2="SelectItemText",t5=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:l,style:i,...a}=e,u=tR(t2,n),c=tI(t2,n),f=t0(t2,n),d=tT(t2,n),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e),f.onItemTextChange,e=>{var t;return null==(t=c.itemTextRefCallback)?void 0:t.call(c,e,f.value,f.disabled)}),g=null==p?void 0:p.textContent,v=r.useMemo(()=>(0,eX.jsx)("option",{value:f.value,disabled:f.disabled,children:g},f.value),[f.disabled,f.value,g]),{onNativeOptionAdd:w,onNativeOptionRemove:y}=d;return(0,e$.N)(()=>(w(v),()=>y(v)),[w,y,v]),(0,eX.jsxs)(eX.Fragment,{children:[(0,eX.jsx)(eq.sG.span,{id:f.textId,...a,ref:m}),f.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});t5.displayName=t2;var t9="SelectItemIndicator",t6=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return t0(t9,n).isSelected?(0,eX.jsx)(eq.sG.span,{"aria-hidden":!0,...r,ref:t}):null});t6.displayName=t9;var t8="SelectScrollUpButton",t4=r.forwardRef((e,t)=>{let n=tI(t8,e.__scopeSelect),o=tz(t8,e.__scopeSelect),[l,i]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,e$.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,eX.jsx)(ne,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});t4.displayName=t8;var t3="SelectScrollDownButton",t7=r.forwardRef((e,t)=>{let n=tI(t3,e.__scopeSelect),o=tz(t3,e.__scopeSelect),[l,i]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,e$.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,eX.jsx)(ne,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});t7.displayName=t3;var ne=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...l}=e,a=tI("SelectScrollButton",n),s=r.useRef(null),u=tw(n),c=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,e$.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,eX.jsx)(eq.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,i.m)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,i.m)(l.onPointerMove,()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,i.m)(l.onPointerLeave,()=>{c()})})}),nt=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,eX.jsx)(eq.sG.div,{"aria-hidden":!0,...r,ref:t})});nt.displayName="SelectSeparator";var nn="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tS(n),l=tR(nn,n),i=tI(nn,n);return l.open&&"popper"===i.position?(0,eX.jsx)(tr,{...o,...r,ref:t}):null}).displayName=nn;var nr=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,...l}=e,i=r.useRef(null),a=(0,s.s)(t,i),u=(0,tc.Z)(o);return r.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==o&&t){let n=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(n)}},[u,o]),(0,eX.jsx)(eq.sG.select,{...l,style:{...tf,...l.style},ref:a,defaultValue:o})});function no(e){return""===e||void 0===e}function nl(e){let t=(0,eZ.c)(e),n=r.useRef(""),o=r.useRef(0),l=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),i=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,l,i]}function ni(e,t,n){var r,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=e,o=Math.max(i,0),r.map((e,t)=>r[(o+t)%r.length]));1===l.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return s!==n?s:void 0}nr.displayName="SelectBubbleInput";var na=tP,ns=tE,nu=tL,nc=tN,nf=tD,nd=tH,np=tK,nh=tY,nm=t$,ng=t1,nv=t5,nw=t6,ny=t4,nx=t7,nb=nt},6054:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(5050).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},8069:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(9585);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}}]);