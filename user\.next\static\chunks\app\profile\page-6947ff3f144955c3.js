(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{1470:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1716:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},2006:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4926:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},5457:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},5828:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5968:(e,s,a)=>{Promise.resolve().then(a.bind(a,9992))},7530:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},7971:(e,s,a)=>{"use strict";a.d(s,{p:()=>i});var t=a(9605),l=a(9585),r=a(6994);let i=l.forwardRef((e,s)=>{let{className:a,type:l,...i}=e;return(0,t.jsx)("input",{type:l,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...i})});i.displayName="Input"},8424:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},9992:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>P});var t=a(9605),l=a(9585),r=a(6762),i=a.n(r),c=a(3005),n=a(8063),d=a(2933),o=a(7971),m=a(498),x=a(360),h=a(3760),u=a(2006),p=a(5457),f=a(4926),j=a(5050);let g=(0,j.A)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);var y=a(8424),b=a(7530),v=a(1470);let N=(0,j.A)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]);var w=a(5828),k=a(1716),A=a(6994),C=a(5215),S=a(6845);function P(){var e,s,a,r,j;let[P,M]=(0,l.useState)("profile"),[V,Y]=(0,l.useState)(!1),[E,Z]=(0,l.useState)(!1),[I,R]=(0,l.useState)(!1),{data:z,isLoading:q,refetch:B}=(0,C.M7)(),[K,{isLoading:_}]=(0,C.ik)(),O=(null==z?void 0:z.data)||{firstName:"",lastName:"",email:"",phone:"",dateOfBirth:"",address:{street:"",city:"",state:"",country:"",zipCode:""},profileImage:"",emailVerified:!1,phoneVerified:!1,kycStatus:"not_submitted",createdAt:""},[L,$]=(0,l.useState)({firstName:O.firstName||"",lastName:O.lastName||"",email:O.email||"",phone:O.phone||"",dateOfBirth:O.dateOfBirth||"",address:O.address||{street:"",city:"",state:"",country:"",zipCode:""}}),[W,H]=(0,l.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[U,F]=(0,l.useState)({emailNotifications:!0,smsNotifications:!0,pushNotifications:!0,marketingEmails:!1,investmentUpdates:!0,securityAlerts:!0}),D=async()=>{try{await K(L).unwrap(),S.toast.success("Profile updated successfully!"),Y(!1),B()}catch(e){console.error("Profile update error:",e),S.toast.error("Failed to update profile")}},T=async()=>{if(W.newPassword!==W.confirmPassword)return void S.toast.error("New passwords do not match");try{S.toast.success("Password changed successfully!"),H({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){console.error("Password change error:",e),S.toast.error("Failed to change password")}},G=e=>{switch(e){case"approved":return"text-green-600 bg-green-100";case"pending":return"text-yellow-600 bg-yellow-100";case"rejected":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},J=[{id:"profile",label:"Profile Information",icon:m.A},{id:"security",label:"Security",icon:x.A},{id:"notifications",label:"Notifications",icon:h.A},{id:"kyc",label:"KYC Verification",icon:u.A}];return q?(0,t.jsx)(c.A,{children:(0,t.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,t.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg"}),(0,t.jsx)("div",{className:"lg:col-span-3 h-96 bg-gray-200 rounded-lg"})]})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/30",children:(0,t.jsx)(c.A,{children:(0,t.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Profile Settings"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage your account settings and preferences"})]}),(0,t.jsxs)(d.$,{variant:"outline",className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Export Data"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(n.Zp,{className:"shadow-lg border-0 bg-gradient-to-br from-white to-blue-50/30 animate-scale-in",children:(0,t.jsxs)(n.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsxs)("div",{className:"relative inline-block",children:[O.profileImage?(0,t.jsx)("img",{src:O.profileImage,alt:"Profile",className:"w-20 h-20 rounded-full mx-auto object-cover"}):(0,t.jsx)("div",{className:"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto",children:(0,A.IM)(O.firstName,O.lastName)}),(0,t.jsx)("button",{className:"absolute bottom-0 right-0 bg-blue-600 text-white p-1 rounded-full hover:bg-blue-700",children:(0,t.jsx)(f.A,{className:"h-3 w-3"})})]}),(0,t.jsxs)("h3",{className:"font-semibold mt-3",children:[O.firstName," ",O.lastName]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:O.email}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 mt-2",children:[O.emailVerified&&(0,t.jsx)(u.A,{className:"h-4 w-4 text-green-600"}),O.phoneVerified&&(0,t.jsx)(u.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(G(O.kycStatus)),children:["KYC ",O.kycStatus.replace("_"," ")]})]})]}),(0,t.jsx)("nav",{className:"space-y-2",children:J.map(e=>{let s=e.icon;return(0,t.jsxs)("button",{onClick:()=>M(e.id),className:"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ".concat(P===e.id?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-100"),children:[(0,t.jsx)(s,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:e.label})]},e.id)})})]})}),(0,t.jsxs)("div",{className:"lg:col-span-3",children:["profile"===P&&(0,t.jsxs)(n.Zp,{className:"shadow-lg border-0 bg-gradient-to-br from-white to-indigo-50/30 animate-slide-in-right",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(n.ZB,{children:"Profile Information"}),(0,t.jsxs)(d.$,{variant:V?"default":"outline",onClick:()=>V?D():Y(!0),disabled:_,className:"flex items-center space-x-2",children:[V?(0,t.jsx)(g,{className:"h-4 w-4"}):(0,t.jsx)(y.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:V?"Save Changes":"Edit Profile"})]})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Personal Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name"}),(0,t.jsx)(o.p,{value:L.firstName,onChange:e=>$(s=>({...s,firstName:e.target.value})),disabled:!V})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name"}),(0,t.jsx)(o.p,{value:L.lastName,onChange:e=>$(s=>({...s,lastName:e.target.value})),disabled:!V})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.p,{value:L.email,onChange:e=>$(s=>({...s,email:e.target.value})),disabled:!V}),O.emailVerified&&(0,t.jsx)(u.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-600"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.p,{value:L.phone,onChange:e=>$(s=>({...s,phone:e.target.value})),disabled:!V}),O.phoneVerified&&(0,t.jsx)(u.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-600"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth"}),(0,t.jsx)(o.p,{type:"date",value:L.dateOfBirth,onChange:e=>$(s=>({...s,dateOfBirth:e.target.value})),disabled:!V})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Address Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Street Address"}),(0,t.jsx)(o.p,{value:(null==(e=L.address)?void 0:e.street)||"",onChange:e=>$(s=>({...s,address:{...s.address,street:e.target.value}})),disabled:!V})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"City"}),(0,t.jsx)(o.p,{value:(null==(s=L.address)?void 0:s.city)||"",onChange:e=>$(s=>({...s,address:{...s.address,city:e.target.value}})),disabled:!V})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"State"}),(0,t.jsx)(o.p,{value:(null==(a=L.address)?void 0:a.state)||"",onChange:e=>$(s=>({...s,address:{...s.address,state:e.target.value}})),disabled:!V})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Country"}),(0,t.jsx)(o.p,{value:(null==(r=L.address)?void 0:r.country)||"",onChange:e=>$(s=>({...s,address:{...s.address,country:e.target.value}})),disabled:!V})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"ZIP Code"}),(0,t.jsx)(o.p,{value:(null==(j=L.address)?void 0:j.zipCode)||"",onChange:e=>$(s=>({...s,address:{...s.address,zipCode:e.target.value}})),disabled:!V})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Account Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Member Since"}),(0,t.jsx)(o.p,{value:(0,A.Yq)(O.createdAt),disabled:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Account Status"}),(0,t.jsx)(o.p,{value:"Active",disabled:!0})]})]})]})]})]}),"security"===P&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Security Settings"})}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Change Password"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Current Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.p,{type:E?"text":"password",value:W.currentPassword,onChange:e=>H(s=>({...s,currentPassword:e.target.value})),placeholder:"Enter current password"}),(0,t.jsx)("button",{type:"button",onClick:()=>Z(!E),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:E?(0,t.jsx)(b.A,{className:"h-4 w-4"}):(0,t.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.p,{type:I?"text":"password",value:W.newPassword,onChange:e=>H(s=>({...s,newPassword:e.target.value})),placeholder:"Enter new password"}),(0,t.jsx)("button",{type:"button",onClick:()=>R(!I),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:I?(0,t.jsx)(b.A,{className:"h-4 w-4"}):(0,t.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),(0,t.jsx)(o.p,{type:"password",value:W.confirmPassword,onChange:e=>H(s=>({...s,confirmPassword:e.target.value})),placeholder:"Confirm new password"})]}),(0,t.jsxs)(d.$,{onClick:T,className:"flex items-center space-x-2",children:[(0,t.jsx)(N,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Update Password"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Two-Factor Authentication"}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:"SMS Authentication"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Receive verification codes via SMS"})]}),(0,t.jsx)(d.$,{variant:"outline",children:"Enable"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Active Sessions"}),(0,t.jsx)("div",{className:"space-y-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:"Current Session"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Chrome on Windows • Mumbai, India"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Last active: Now"})]}),(0,t.jsx)("span",{className:"text-green-600 text-sm font-medium",children:"Active"})]})})]})]})]}),"notifications"===P&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Notification Preferences"})}),(0,t.jsx)(n.Wu,{className:"space-y-6",children:Object.entries(U).map(e=>{let[s,a]=e;return(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium capitalize",children:s.replace(/([A-Z])/g," $1").trim()}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["emailNotifications"===s&&"Receive notifications via email","smsNotifications"===s&&"Receive notifications via SMS","pushNotifications"===s&&"Receive push notifications","marketingEmails"===s&&"Receive marketing and promotional emails","investmentUpdates"===s&&"Get updates about your investments","securityAlerts"===s&&"Receive security and account alerts"]})]}),(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",checked:a,onChange:e=>F(a=>({...a,[s]:e.target.checked})),className:"sr-only peer"}),(0,t.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]},s)})})]}),"kyc"===P&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"KYC Verification"})}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("div",{className:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ".concat(G(O.kycStatus)),children:["approved"===O.kycStatus&&(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"pending"===O.kycStatus&&(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"KYC ",O.kycStatus.replace("_"," ")]})}),"not_submitted"===O.kycStatus&&(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Complete Your KYC Verification"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Verify your identity to unlock all features and increase your investment limits."}),(0,t.jsx)(i(),{href:"/kyc",children:(0,t.jsxs)(d.$,{className:"flex items-center space-x-2",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Start KYC Process"})]})})]}),"pending"===O.kycStatus&&(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-2",children:"KYC Under Review"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Your documents are being reviewed. This process typically takes 1-2 business days."}),(0,t.jsx)(i(),{href:"/kyc",children:(0,t.jsxs)(d.$,{variant:"outline",className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"View KYC Status"})]})})]}),"approved"===O.kycStatus&&(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-2 text-green-600",children:"KYC Verified"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Your identity has been successfully verified. You can now access all platform features."})]})]})]})]})]})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2094,5315,7436,7693,1147,7627,3005,390,110,7358],()=>s(5968)),_N_E=e.O()}]);