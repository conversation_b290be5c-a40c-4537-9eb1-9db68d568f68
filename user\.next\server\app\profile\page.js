(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={2029:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>S});var a=t(40969),r=t(73356),i=t(27092),l=t.n(i),n=t(37020),d=t(66949),c=t(46411),o=t(57387),m=t(49610),x=t(63980),u=t(71708),h=t(1110),p=t(56525),f=t(28966),j=t(99024);let g=(0,j.A)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);var b=t(34132),y=t(22324),v=t(5568);let N=(0,j.A)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]);var w=t(44356),k=t(16716),A=t(21764),C=t(31825),P=t(1507);function S(){let[e,s]=(0,r.useState)("profile"),[t,i]=(0,r.useState)(!1),[j,S]=(0,r.useState)(!1),[M,R]=(0,r.useState)(!1),{data:_,isLoading:q,refetch:B}=(0,C.M7)(),[E,{isLoading:V}]=(0,C.ik)(),Y=_?.data||{firstName:"",lastName:"",email:"",phone:"",dateOfBirth:"",address:{street:"",city:"",state:"",country:"",zipCode:""},profileImage:"",emailVerified:!1,phoneVerified:!1,kycStatus:"not_submitted",createdAt:""},[I,Z]=(0,r.useState)({firstName:Y.firstName||"",lastName:Y.lastName||"",email:Y.email||"",phone:Y.phone||"",dateOfBirth:Y.dateOfBirth||"",address:Y.address||{street:"",city:"",state:"",country:"",zipCode:""}}),[z,K]=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[$,U]=(0,r.useState)({emailNotifications:!0,smsNotifications:!0,pushNotifications:!0,marketingEmails:!1,investmentUpdates:!0,securityAlerts:!0}),D=async()=>{try{await E(I).unwrap(),P.toast.success("Profile updated successfully!"),i(!1),B()}catch(e){console.error("Profile update error:",e),P.toast.error("Failed to update profile")}},O=async()=>{if(z.newPassword!==z.confirmPassword)return void P.toast.error("New passwords do not match");try{P.toast.success("Password changed successfully!"),K({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){console.error("Password change error:",e),P.toast.error("Failed to change password")}},T=e=>{switch(e){case"approved":return"text-green-600 bg-green-100";case"pending":return"text-yellow-600 bg-yellow-100";case"rejected":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},G=[{id:"profile",label:"Profile Information",icon:m.A},{id:"security",label:"Security",icon:x.A},{id:"notifications",label:"Notifications",icon:u.A},{id:"kyc",label:"KYC Verification",icon:h.A}];return q?(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg"}),(0,a.jsx)("div",{className:"lg:col-span-3 h-96 bg-gray-200 rounded-lg"})]})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/30",children:(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Profile Settings"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage your account settings and preferences"})]}),(0,a.jsxs)(c.$,{variant:"outline",className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Export Data"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(d.Zp,{className:"shadow-lg border-0 bg-gradient-to-br from-white to-blue-50/30 animate-scale-in",children:(0,a.jsxs)(d.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsxs)("div",{className:"relative inline-block",children:[Y.profileImage?(0,a.jsx)("img",{src:Y.profileImage,alt:"Profile",className:"w-20 h-20 rounded-full mx-auto object-cover"}):(0,a.jsx)("div",{className:"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto",children:(0,A.IM)(Y.firstName,Y.lastName)}),(0,a.jsx)("button",{className:"absolute bottom-0 right-0 bg-blue-600 text-white p-1 rounded-full hover:bg-blue-700",children:(0,a.jsx)(f.A,{className:"h-3 w-3"})})]}),(0,a.jsxs)("h3",{className:"font-semibold mt-3",children:[Y.firstName," ",Y.lastName]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:Y.email}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 mt-2",children:[Y.emailVerified&&(0,a.jsx)(h.A,{className:"h-4 w-4 text-green-600"}),Y.phoneVerified&&(0,a.jsx)(h.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsxs)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${T(Y.kycStatus)}`,children:["KYC ",Y.kycStatus.replace("_"," ")]})]})]}),(0,a.jsx)("nav",{className:"space-y-2",children:G.map(t=>{let r=t.icon;return(0,a.jsxs)("button",{onClick:()=>s(t.id),className:`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${e===t.id?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-100"}`,children:[(0,a.jsx)(r,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:t.label})]},t.id)})})]})}),(0,a.jsxs)("div",{className:"lg:col-span-3",children:["profile"===e&&(0,a.jsxs)(d.Zp,{className:"shadow-lg border-0 bg-gradient-to-br from-white to-indigo-50/30 animate-slide-in-right",children:[(0,a.jsx)(d.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(d.ZB,{children:"Profile Information"}),(0,a.jsxs)(c.$,{variant:t?"default":"outline",onClick:()=>t?D():i(!0),disabled:V,className:"flex items-center space-x-2",children:[t?(0,a.jsx)(g,{className:"h-4 w-4"}):(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:t?"Save Changes":"Edit Profile"})]})]})}),(0,a.jsxs)(d.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Personal Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name"}),(0,a.jsx)(o.p,{value:I.firstName,onChange:e=>Z(s=>({...s,firstName:e.target.value})),disabled:!t})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name"}),(0,a.jsx)(o.p,{value:I.lastName,onChange:e=>Z(s=>({...s,lastName:e.target.value})),disabled:!t})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{value:I.email,onChange:e=>Z(s=>({...s,email:e.target.value})),disabled:!t}),Y.emailVerified&&(0,a.jsx)(h.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-600"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{value:I.phone,onChange:e=>Z(s=>({...s,phone:e.target.value})),disabled:!t}),Y.phoneVerified&&(0,a.jsx)(h.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-600"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth"}),(0,a.jsx)(o.p,{type:"date",value:I.dateOfBirth,onChange:e=>Z(s=>({...s,dateOfBirth:e.target.value})),disabled:!t})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Address Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Street Address"}),(0,a.jsx)(o.p,{value:I.address?.street||"",onChange:e=>Z(s=>({...s,address:{...s.address,street:e.target.value}})),disabled:!t})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"City"}),(0,a.jsx)(o.p,{value:I.address?.city||"",onChange:e=>Z(s=>({...s,address:{...s.address,city:e.target.value}})),disabled:!t})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"State"}),(0,a.jsx)(o.p,{value:I.address?.state||"",onChange:e=>Z(s=>({...s,address:{...s.address,state:e.target.value}})),disabled:!t})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Country"}),(0,a.jsx)(o.p,{value:I.address?.country||"",onChange:e=>Z(s=>({...s,address:{...s.address,country:e.target.value}})),disabled:!t})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"ZIP Code"}),(0,a.jsx)(o.p,{value:I.address?.zipCode||"",onChange:e=>Z(s=>({...s,address:{...s.address,zipCode:e.target.value}})),disabled:!t})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Account Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Member Since"}),(0,a.jsx)(o.p,{value:(0,A.Yq)(Y.createdAt),disabled:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Account Status"}),(0,a.jsx)(o.p,{value:"Active",disabled:!0})]})]})]})]})]}),"security"===e&&(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Security Settings"})}),(0,a.jsxs)(d.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Change Password"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Current Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{type:j?"text":"password",value:z.currentPassword,onChange:e=>K(s=>({...s,currentPassword:e.target.value})),placeholder:"Enter current password"}),(0,a.jsx)("button",{type:"button",onClick:()=>S(!j),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:j?(0,a.jsx)(y.A,{className:"h-4 w-4"}):(0,a.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{type:M?"text":"password",value:z.newPassword,onChange:e=>K(s=>({...s,newPassword:e.target.value})),placeholder:"Enter new password"}),(0,a.jsx)("button",{type:"button",onClick:()=>R(!M),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:M?(0,a.jsx)(y.A,{className:"h-4 w-4"}):(0,a.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),(0,a.jsx)(o.p,{type:"password",value:z.confirmPassword,onChange:e=>K(s=>({...s,confirmPassword:e.target.value})),placeholder:"Confirm new password"})]}),(0,a.jsxs)(c.$,{onClick:O,className:"flex items-center space-x-2",children:[(0,a.jsx)(N,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Update Password"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Two-Factor Authentication"}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"SMS Authentication"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Receive verification codes via SMS"})]}),(0,a.jsx)(c.$,{variant:"outline",children:"Enable"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Active Sessions"}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Current Session"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Chrome on Windows • Mumbai, India"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Last active: Now"})]}),(0,a.jsx)("span",{className:"text-green-600 text-sm font-medium",children:"Active"})]})})]})]})]}),"notifications"===e&&(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Notification Preferences"})}),(0,a.jsx)(d.Wu,{className:"space-y-6",children:Object.entries($).map(([e,s])=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium capitalize",children:e.replace(/([A-Z])/g," $1").trim()}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["emailNotifications"===e&&"Receive notifications via email","smsNotifications"===e&&"Receive notifications via SMS","pushNotifications"===e&&"Receive push notifications","marketingEmails"===e&&"Receive marketing and promotional emails","investmentUpdates"===e&&"Get updates about your investments","securityAlerts"===e&&"Receive security and account alerts"]})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:s,onChange:s=>U(t=>({...t,[e]:s.target.checked})),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]},e))})]}),"kyc"===e&&(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"KYC Verification"})}),(0,a.jsxs)(d.Wu,{className:"space-y-6",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${T(Y.kycStatus)}`,children:["approved"===Y.kycStatus&&(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"pending"===Y.kycStatus&&(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"KYC ",Y.kycStatus.replace("_"," ")]})}),"not_submitted"===Y.kycStatus&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Complete Your KYC Verification"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Verify your identity to unlock all features and increase your investment limits."}),(0,a.jsx)(l(),{href:"/kyc",children:(0,a.jsxs)(c.$,{className:"flex items-center space-x-2",children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Start KYC Process"})]})})]}),"pending"===Y.kycStatus&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"KYC Under Review"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Your documents are being reviewed. This process typically takes 1-2 business days."}),(0,a.jsx)(l(),{href:"/kyc",children:(0,a.jsxs)(c.$,{variant:"outline",className:"flex items-center space-x-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"View KYC Status"})]})})]}),"approved"===Y.kycStatus&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-2 text-green-600",children:"KYC Verified"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Your identity has been successfully verified. You can now access all platform features."})]})]})]})]})]})]})})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5568:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22324:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},28966:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34132:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},44356:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},56525:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},57387:(e,s,t)=>{"use strict";t.d(s,{p:()=>l});var a=t(40969),r=t(73356),i=t(21764);let l=r.forwardRef(({className:e,type:s,...t},r)=>(0,a.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));l.displayName="Input"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65762:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\profile\\page.tsx","default")},66232:(e,s,t)=>{Promise.resolve().then(t.bind(t,2029))},72856:(e,s,t)=>{Promise.resolve().then(t.bind(t,65762))},79551:e=>{"use strict";e.exports=require("url")},81849:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=t(10557),r=t(68490),i=t(13172),l=t.n(i),n=t(68835),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,65762)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\profile\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[755,3777,2544,7092,7555,2487,3427],()=>t(81849));module.exports=a})();