{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format currency\nexport function formatCurrency(amount: number, currency = \"₹\"): string {\n  return `${currency}${amount.toLocaleString('en-IN', { \n    minimumFractionDigits: 2, \n    maximumFractionDigits: 2 \n  })}`\n}\n\n// Format date\nexport function formatDate(date: string | Date, format = \"dd/MM/yyyy\"): string {\n  const d = new Date(date)\n  \n  if (format === \"dd/MM/yyyy\") {\n    return d.toLocaleDateString('en-GB')\n  }\n  \n  if (format === \"relative\") {\n    const now = new Date()\n    const diffInMs = now.getTime() - d.getTime()\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n    \n    if (diffInDays === 0) return \"Today\"\n    if (diffInDays === 1) return \"Yesterday\"\n    if (diffInDays < 7) return `${diffInDays} days ago`\n    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`\n    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`\n    return `${Math.floor(diffInDays / 365)} years ago`\n  }\n  \n  return d.toLocaleDateString()\n}\n\n// Truncate text\nexport function truncateText(text: string, length = 50): string {\n  if (text.length <= length) return text\n  return text.substring(0, length) + \"...\"\n}\n\n// Generate initials\nexport function getInitials(firstName?: string, lastName?: string, email?: string): string {\n  if (firstName && lastName) {\n    return `${firstName[0]}${lastName[0]}`.toUpperCase()\n  }\n  \n  if (firstName) {\n    return firstName.substring(0, 2).toUpperCase()\n  }\n  \n  if (email) {\n    return email.substring(0, 2).toUpperCase()\n  }\n  \n  return \"U\"\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Sleep function\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n// Generate random ID\nexport function generateId(prefix = \"\"): string {\n  const timestamp = Date.now().toString(36)\n  const random = Math.random().toString(36).substring(2, 8)\n  return `${prefix}${timestamp}${random}`.toUpperCase()\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// Validate phone\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\n// Format phone number\nexport function formatPhone(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '')\n  \n  if (cleaned.length === 10) {\n    return `+91 ${cleaned.substring(0, 5)} ${cleaned.substring(5)}`\n  }\n  \n  if (cleaned.length === 12 && cleaned.startsWith('91')) {\n    return `+${cleaned.substring(0, 2)} ${cleaned.substring(2, 7)} ${cleaned.substring(7)}`\n  }\n  \n  return phone\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0\n  return Math.round((value / total) * 100)\n}\n\n// Get status color\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    active: \"text-green-600 bg-green-100\",\n    inactive: \"text-gray-600 bg-gray-100\",\n    pending: \"text-yellow-600 bg-yellow-100\",\n    suspended: \"text-red-600 bg-red-100\",\n    completed: \"text-green-600 bg-green-100\",\n    failed: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    new: \"text-blue-600 bg-blue-100\",\n    contacted: \"text-purple-600 bg-purple-100\",\n    qualified: \"text-indigo-600 bg-indigo-100\",\n    converted: \"text-green-600 bg-green-100\",\n    lost: \"text-red-600 bg-red-100\",\n  }\n  \n  return statusColors[status.toLowerCase()] || \"text-gray-600 bg-gray-100\"\n}\n\n// Copy to clipboard\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error)\n    return false\n  }\n}\n\n// Download file\nexport function downloadFile(data: any, filename: string, type = 'application/json'): void {\n  const blob = new Blob([typeof data === 'string' ? data : JSON.stringify(data, null, 2)], { type })\n  const url = URL.createObjectURL(blob)\n  const link = document.createElement('a')\n  link.href = url\n  link.download = filename\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  URL.revokeObjectURL(url)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Get file extension\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)\n}\n\n// Check if mobile device\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// Scroll to element\nexport function scrollToElement(elementId: string, offset = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({ top, behavior: 'smooth' })\n  }\n}\n\n// Local storage helpers\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null\n    \n    try {\n      const item = localStorage.getItem(key)\n      return item ? JSON.parse(item) : defaultValue || null\n    } catch (error) {\n      console.error(`Error getting item from localStorage:`, error)\n      return defaultValue || null\n    }\n  },\n  \n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.setItem(key, JSON.stringify(value))\n    } catch (error) {\n      console.error(`Error setting item in localStorage:`, error)\n    }\n  },\n  \n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.removeItem(key)\n    } catch (error) {\n      console.error(`Error removing item from localStorage:`, error)\n    }\n  },\n  \n  clear: (): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.clear()\n    } catch (error) {\n      console.error(`Error clearing localStorage:`, error)\n    }\n  }\n}\n\n// Format time\nexport function formatTime(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAW,GAAG;IAC3D,OAAO,GAAG,WAAW,OAAO,cAAc,CAAC,SAAS;QAClD,uBAAuB;QACvB,uBAAuB;IACzB,IAAI;AACN;AAGO,SAAS,WAAW,IAAmB,EAAE,SAAS,YAAY;IACnE,MAAM,IAAI,IAAI,KAAK;IAEnB,IAAI,WAAW,cAAc;QAC3B,OAAO,EAAE,kBAAkB,CAAC;IAC9B;IAEA,IAAI,WAAW,YAAY;QACzB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,EAAE,OAAO;QAC1C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,SAAS,CAAC;QACnD,IAAI,aAAa,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;QACrE,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,WAAW,CAAC;QACxE,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,UAAU,CAAC;IACpD;IAEA,OAAO,EAAE,kBAAkB;AAC7B;AAGO,SAAS,aAAa,IAAY,EAAE,SAAS,EAAE;IACpD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,UAAU;AACrC;AAGO,SAAS,YAAY,SAAkB,EAAE,QAAiB,EAAE,KAAc;IAC/E,IAAI,aAAa,UAAU;QACzB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW;IACpD;IAEA,IAAI,WAAW;QACb,OAAO,UAAU,SAAS,CAAC,GAAG,GAAG,WAAW;IAC9C;IAEA,IAAI,OAAO;QACT,OAAO,MAAM,SAAS,CAAC,GAAG,GAAG,WAAW;IAC1C;IAEA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,WAAW,SAAS,EAAE;IACpC,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,OAAO,GAAG,SAAS,YAAY,QAAQ,CAAC,WAAW;AACrD;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,YAAY,KAAa;IACvC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,CAAC,IAAI,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACjE;IAEA,IAAI,QAAQ,MAAM,KAAK,MAAM,QAAQ,UAAU,CAAC,OAAO;QACrD,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACzF;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;QACR,WAAW;QACX,KAAK;QACL,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;IACR;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,SAAS,aAAa,IAAS,EAAE,QAAgB,EAAE,OAAO,kBAAkB;IACjF,MAAM,OAAO,IAAI,KAAK;QAAC,OAAO,SAAS,WAAW,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM;KAAG,EAAE;QAAE;IAAK;IAChG,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAGO,SAAS;IACd,wCAAmC,OAAO;;AAE5C;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAS,CAAC;IAC3D,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YAAE;YAAK,UAAU;QAAS;IAC5C;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,wCAAmC,OAAO,gBAAgB;;IAS5D;IAEA,KAAK,CAAI,KAAa;QACpB,wCAAmC;;IAOrC;IAEA,QAAQ,CAAC;QACP,wCAAmC;;IAOrC;IAEA,OAAO;QACL,wCAAmC;;IAOrC;AACF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"card-base text-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6BACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"btn-primary\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600\",\n        outline: \"btn-outline\",\n        secondary: \"btn-secondary\",\n        ghost: \"hover:bg-primary-100 text-primary\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,mQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6WAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,6QAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,6QAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,6QAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,6QAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = \"Avatar\"\n\nconst AvatarImage = React.forwardRef<\n  HTMLImageElement,\n  React.ImgHTMLAttributes<HTMLImageElement>\n>(({ className, ...props }, ref) => (\n  <img\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = \"AvatarImage\"\n\nconst AvatarFallback = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = \"AvatarFallback\"\n\nconst AvatarInitials = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & { name: string }\n>(({ className, name, ...props }, ref) => {\n  const initials = name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"flex h-full w-full items-center justify-center rounded-full bg-sky-500 text-white text-sm font-medium\",\n        className\n      )}\n      {...props}\n    >\n      {initials}\n    </div>\n  )\n})\nAvatarInitials.displayName = \"AvatarInitials\"\n\nexport { Avatar, AvatarImage, AvatarFallback, AvatarInitials }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAEA,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,+BAAiB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAChC,MAAM,WAAW,KACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IAEZ,qBACE,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yGACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,+QAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,+QAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gLACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC;;0BACC,6WAAC;;;;;0BACD,6WAAC,+QAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6WAAC,+QAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6WAAC,gRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6WAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,+QAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 699, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,8QAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,8QAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/logo.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'full' | 'icon' | 'text'\n  className?: string\n}\n\nconst Logo: React.FC<LogoProps> = ({ \n  size = 'md', \n  variant = 'full', \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'h-6 w-6',\n    md: 'h-8 w-8', \n    lg: 'h-10 w-10',\n    xl: 'h-12 w-12'\n  }\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl', \n    xl: 'text-3xl'\n  }\n\n  const LogoIcon = () => (\n    <div className={`${sizeClasses[size]} bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center shadow-lg ${className}`}>\n      <div className=\"relative\">\n        {/* S */}\n        <div className=\"absolute -left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5 ml-1\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n        </div>\n        \n        {/* G */}\n        <div className=\"absolute left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-4 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"absolute right-0 top-2 w-1 h-2 bg-white rounded-full\"></div>\n          <div className=\"absolute right-0 top-3 w-1.5 h-1 bg-white rounded-full\"></div>\n        </div>\n        \n        {/* M */}\n        <div className=\"absolute left-4 top-0\">\n          <div className=\"w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-0.5 top-1 w-1 h-1 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1 top-0 w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1.5 top-0 w-1 h-5 bg-white rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const LogoText = () => (\n    <span className={`font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent ${textSizeClasses[size]} ${className}`}>\n      SGM\n    </span>\n  )\n\n  if (variant === 'icon') {\n    return <LogoIcon />\n  }\n\n  if (variant === 'text') {\n    return <LogoText />\n  }\n\n  return (\n    <div className={`flex items-center gap-3 ${className}`}>\n      <LogoIcon />\n      <LogoText />\n    </div>\n  )\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;;AAQA,MAAM,OAA4B,CAAC,EACjC,OAAO,IAAI,EACX,UAAU,MAAM,EAChB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,WAAW,kBACf,6WAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,mGAAmG,EAAE,WAAW;sBACnJ,cAAA,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,WAAW,kBACf,6WAAC;YAAK,WAAW,CAAC,mFAAmF,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;sBAAE;;;;;;IAK/I,IAAI,YAAY,QAAQ;QACtB,qBAAO,6WAAC;;;;;IACV;IAEA,IAAI,YAAY,QAAQ;QACtB,qBAAO,6WAAC;;;;;IACV;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,6WAAC;;;;;0BACD,6WAAC;;;;;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 938, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAppSelector } from '@/store'\nimport { selectUser } from '@/store/slices/authSlice'\nimport { UserRole } from '@/types'\nimport { cn } from '@/lib/utils'\nimport Logo from '@/components/ui/logo'\nimport {\n  LayoutDashboard,\n  Users,\n  Building,\n  TrendingUp,\n  Target,\n  DollarSign,\n  Headphones,\n  Settings,\n  Plus,\n  UserPlus,\n  UserCheck,\n  BarChart3,\n  Shield,\n  Bell,\n  PieChart,\n  CheckSquare,\n  CalendarDays,\n  UserCog,\n  Award,\n  Minus,\n  Receipt,\n  CreditCard\n} from 'lucide-react'\n\ninterface SidebarProps {\n  collapsed?: boolean\n  onToggle?: () => void\n}\n\nexport default function Sidebar({ collapsed = false, onToggle }: SidebarProps) {\n  const pathname = usePathname()\n  const user = useAppSelector((state) => selectUser(state as any))\n\n  console.log('Sidebar rendering...', { collapsed, pathname, user })\n\n  const menuSections = [\n    {\n      title: \"Dashboard\",\n      items: [\n        {\n          id: \"main-dashboard\",\n          label: \"Overview\",\n          icon: LayoutDashboard,\n          href: \"/dashboard\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES, UserRole.USER]\n        }\n        \n      ]\n    },\n    {\n      title: \"User Management\",\n      items: [\n        {\n          id: \"users-overview\",\n          label: \"All Users\",\n          icon: Users,\n          href: \"/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-management-comprehensive\",\n          label: \"User Management\",\n          icon: Users,\n          href: \"/user-management\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-user\",\n          label: \"Add User\",\n          icon: UserPlus,\n          href: \"/users/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"role-management\",\n          label: \"Role Management\",\n          icon: UserCheck,\n          href: \"/users/roles\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Property Management\",\n      items: [\n        {\n          id: \"properties-overview\",\n          label: \"All Properties\",\n          icon: Building,\n          href: \"/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-property\",\n          label: \"Add Property\",\n          icon: Plus,\n          href: \"/properties/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-owners\",\n          label: \"Property Owners\",\n          icon: UserCheck,\n          href: \"/property-owners\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Stock Investments\",\n      items: [\n        {\n          id: \"stocks-overview\",\n          label: \"All Stocks\",\n          icon: TrendingUp,\n          href: \"/stocks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"create-stock\",\n          label: \"Create Stock\",\n          icon: Plus,\n          href: \"/stocks/create\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Lead Management\",\n      items: [\n        {\n          id: \"leads-overview\",\n          label: \"Lead Management\",\n          icon: Target,\n          href: \"/leads\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES]\n        },\n        {\n          id: \"sales-analytics\",\n          label: \"Sales Analytics\",\n          icon: BarChart3,\n          href: \"/sales-analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Sales Management\",\n      items: [\n        {\n          id: \"sales-team\",\n          label: \"Sales Team\",\n          icon: UserCog,\n          href: \"/sales-team\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-tasks\",\n          label: \"Sales Tasks\",\n          icon: CheckSquare,\n          href: \"/sales-tasks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-calendar\",\n          label: \"Sales Calendar\",\n          icon: CalendarDays,\n          href: \"/sales-calendar\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-targets\",\n          label: \"Sales Targets\",\n          icon: Award,\n          href: \"/sales-targets\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"commissions\",\n          label: \"Commissions\",\n          icon: DollarSign,\n          href: \"/commissions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Financial Management\",\n      items: [\n        {\n          id: \"finance-overview\",\n          label: \"Financial Management\",\n          icon: DollarSign,\n          href: \"/finance\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-funds\",\n          label: \"Add Funds\",\n          icon: Plus,\n          href: \"/add-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"deduct-funds\",\n          label: \"Deduct Funds\",\n          icon: Minus,\n          href: \"/deduct-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"admin-transactions\",\n          label: \"All Transactions\",\n          icon: Receipt,\n          href: \"/admin-transactions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"withdrawal-requests\",\n          label: \"Withdrawal Requests\",\n          icon: CreditCard,\n          href: \"/withdrawal-requests\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Support Management\",\n      items: [\n        {\n          id: \"support-dashboard\",\n          label: \"Support Management\",\n          icon: Headphones,\n          href: \"/support\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"System & Settings\",\n      items: [\n        {\n          id: \"system-settings\",\n          label: \"Settings Management\",\n          icon: Settings,\n          href: \"/settings\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Reports & Analytics\",\n      items: [\n        {\n          id: \"analytics-dashboard\",\n          label: \"Analytics Dashboard\",\n          icon: BarChart3,\n          href: \"/analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-reports\",\n          label: \"User Reports\",\n          icon: Users,\n          href: \"/reports/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-reports\",\n          label: \"Property Reports\",\n          icon: Building,\n          href: \"/reports/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"financial-reports\",\n          label: \"Financial Reports\",\n          icon: DollarSign,\n          href: \"/reports/financial\",\n          roles: [UserRole.ADMIN]\n        },\n       \n      ]\n    }\n  ]\n\n  // Filter menu sections based on user role\n  const userRole = user?.role as UserRole || UserRole.USER\n  const filteredSections = menuSections.map(section => ({\n    ...section,\n    items: section.items.filter(item => item.roles.includes(userRole))\n  })).filter(section => section.items.length > 0)\n\n  // Debug logging\n  console.log('User:', user)\n  console.log('User Role:', user?.role)\n  console.log('UserRole enum:', userRole)\n  console.log('Menu Sections:', menuSections.length)\n  console.log('Filtered Sections:', filteredSections.length)\n\n  // Use filtered sections based on user role\n  const sectionsToShow = filteredSections\n\n  const isActive = (href: string) => {\n    if (!pathname) return false\n    if (href === '/dashboard') {\n      return pathname === '/dashboard'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <div className={cn(\n      \"h-screen bg-white border-r border-sky-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg\",\n      collapsed ? \"w-20\" : \"w-72\"\n    )}>\n      {/* Logo */}\n      <div className=\"flex items-center space-x-3 p-4 border-b border-sky-200 flex-shrink-0 bg-gradient-to-r from-sky-50 to-sky-100\">\n        <Logo size={collapsed ? \"lg\" : \"xl\"} variant={collapsed ? \"icon\" : \"full\"} />\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-300 scrollbar-track-sky-100\">\n        <div className=\"space-y-2\">\n          {sectionsToShow.length === 0 ? (\n            <div className=\"text-center text-red-500 p-4\">\n              <p>No menu items found!</p>\n              <p>User: {user?.firstName || 'Not logged in'}</p>\n              <p>Role: {user?.role || 'No role'}</p>\n            </div>\n          ) : (\n            sectionsToShow.map((section) => (\n              <div key={section.title} className=\"space-y-1 mb-6\">\n                {!collapsed && (\n                  <h3 className=\"px-3 py-2 text-xs font-semibold text-sky-600 uppercase tracking-wider\">\n                    {section.title}\n                  </h3>\n                )}\n\n                {section.items.map((item) => (\n                  <Link\n                    key={item.id}\n                    href={item.href || '#'}\n                    className={cn(\n                      \"flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200\",\n                      isActive(item.href || '')\n                        ? \"bg-sky-600 text-white shadow-md\"\n                        : \"text-gray-700 hover:text-sky-600 hover:bg-sky-50\"\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                    {!collapsed && (\n                      <span className=\"flex-1 truncate\">{item.label}</span>\n                    )}\n                  </Link>\n                ))}\n              </div>\n            ))\n          )}\n        </div>\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-sky-200 bg-gradient-to-r from-sky-50 to-sky-100\">\n        {!collapsed && (\n          <div className=\"text-xs text-sky-600 text-center font-medium\">\n            © 2025 SGM. All rights reserved.\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;AAwCe,SAAS,QAAQ,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAgB;IAC3E,MAAM,WAAW,CAAA,GAAA,iQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,QAAU,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;IAElD,QAAQ,GAAG,CAAC,wBAAwB;QAAE;QAAW;QAAU;IAAK;IAEhE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,gTAAA,CAAA,kBAAe;oBACrB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,IAAI;qBAAC;gBAC3E;aAED;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,oSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,oSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,0RAAA,CAAA,SAAM;oBACZ,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBAC5D;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,gSAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,+SAAA,CAAA,cAAW;oBACjB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,0SAAA,CAAA,eAAY;oBAClB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,4RAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aAED;QACH;KACD;IAED,0CAA0C;IAC1C,MAAM,WAAW,MAAM,QAAoB,qHAAA,CAAA,WAAQ,CAAC,IAAI;IACxD,MAAM,mBAAmB,aAAa,GAAG,CAAC,CAAA,UAAW,CAAC;YACpD,GAAG,OAAO;YACV,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;QAC1D,CAAC,GAAG,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,CAAC,MAAM,GAAG;IAE7C,gBAAgB;IAChB,QAAQ,GAAG,CAAC,SAAS;IACrB,QAAQ,GAAG,CAAC,cAAc,MAAM;IAChC,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,kBAAkB,aAAa,MAAM;IACjD,QAAQ,GAAG,CAAC,sBAAsB,iBAAiB,MAAM;IAEzD,2CAA2C;IAC3C,MAAM,iBAAiB;IAEvB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,6GACA,YAAY,SAAS;;0BAGrB,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,gIAAA,CAAA,UAAI;oBAAC,MAAM,YAAY,OAAO;oBAAM,SAAS,YAAY,SAAS;;;;;;;;;;;0BAIrE,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;8BACZ,eAAe,MAAM,KAAK,kBACzB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;0CAAE;;;;;;0CACH,6WAAC;;oCAAE;oCAAO,MAAM,aAAa;;;;;;;0CAC7B,6WAAC;;oCAAE;oCAAO,MAAM,QAAQ;;;;;;;;;;;;+BAG1B,eAAe,GAAG,CAAC,CAAC,wBAClB,6WAAC;4BAAwB,WAAU;;gCAChC,CAAC,2BACA,6WAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAIjB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6WAAC,2RAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI,IAAI;wCACnB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,SAAS,KAAK,IAAI,IAAI,MAClB,oCACA;;0DAGN,6WAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,CAAC,2BACA,6WAAC;gDAAK,WAAU;0DAAmB,KAAK,KAAK;;;;;;;uCAX1C,KAAK,EAAE;;;;;;2BATR,QAAQ,KAAK;;;;;;;;;;;;;;;0BA+B/B,6WAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,6WAAC;oBAAI,WAAU;8BAA+C;;;;;;;;;;;;;;;;;AAOxE", "debugId": null}}, {"offset": {"line": 1473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector, useAppDispatch } from '@/store'\nimport { selectUser, logoutAsync } from '@/store/slices/authSlice'\nimport { Button } from '@/components/ui/button'\nimport Sidebar from './Sidebar'\nimport { Menu, Bell, Search, LogOut, User, Settings, HelpCircle, ChevronDown } from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n  const [userMenuOpen, setUserMenuOpen] = useState(false)\n  const router = useRouter()\n  const dispatch = useAppDispatch()\n  const user = useAppSelector(selectUser)\n\n  const handleLogout = async () => {\n    await dispatch(logoutAsync())\n    router.push('/login')\n  }\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed)\n  }\n\n  return (\n    <div className=\"h-screen bg-white overflow-hidden\">\n      <div className=\"flex h-full\">\n        {/* Sidebar */}\n        <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col h-full overflow-hidden\">\n          {/* Header */}\n          <header className=\"bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg border-b border-sky-700 flex-shrink-0 z-40\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex justify-between items-center py-4\">\n                {/* Left side */}\n                <div className=\"flex items-center space-x-4\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={toggleSidebar}\n                    className=\"lg:hidden hover:bg-white/20 text-white\"\n                  >\n                    <Menu className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Search Bar */}\n                  <div className=\"hidden md:flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2.5 min-w-[350px] border border-white/30\">\n                    <Search className=\"h-4 w-4 text-white/70\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search users, properties, transactions...\"\n                      className=\"bg-transparent border-none outline-none flex-1 text-sm placeholder:text-white/70 text-white\"\n                    />\n                    <kbd className=\"hidden lg:inline-flex items-center px-2 py-1 text-xs font-medium text-sky-600 bg-white rounded border border-white/30\">\n                      ⌘K\n                    </kbd>\n                  </div>\n                </div>\n\n                {/* Right side */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Quick Actions */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"hover:bg-white/20 text-white\">\n                    <HelpCircle className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Notifications */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"relative hover:bg-white/20 text-white\">\n                    <Bell className=\"h-5 w-5\" />\n                    <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-yellow-500 text-black text-xs rounded-full flex items-center justify-center font-semibold\">\n                      5\n                    </span>\n                  </Button>\n\n                  {/* User Menu */}\n                  <div className=\"relative\">\n                    <Button\n                      variant=\"ghost\"\n                      onClick={() => setUserMenuOpen(!userMenuOpen)}\n                      className=\"flex items-center space-x-3 px-3 py-2 hover:bg-white/20 rounded-lg text-white\"\n                    >\n                      <div className=\"hidden sm:block text-right\">\n                        <p className=\"text-sm font-medium text-white\">\n                          {user?.firstName} {user?.lastName}\n                        </p>\n                        <div className=\"flex items-center justify-end space-x-1\">\n                          <div className=\"bg-yellow-500 px-2 py-0.5 rounded-full\">\n                            <span className=\"text-xs font-medium text-black capitalize\">\n                              {user?.role}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"h-10 w-10 bg-white/20 rounded-full flex items-center justify-center shadow-sm border border-white/30\">\n                        <User className=\"h-5 w-5 text-white\" />\n                      </div>\n\n                      <ChevronDown className=\"h-4 w-4 text-white/70\" />\n                    </Button>\n\n                    {/* User Dropdown Menu */}\n                    {userMenuOpen && (\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sky-200 py-2 z-50\">\n                        <div className=\"px-4 py-2 border-b border-sky-100\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {user?.firstName} {user?.lastName}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                        </div>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <User className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Profile\n                        </Button>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <Settings className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Settings\n                        </Button>\n\n                        <div className=\"border-t border-sky-100 mt-2 pt-2\">\n                          <Button\n                            variant=\"ghost\"\n                            className=\"w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                            onClick={handleLogout}\n                          >\n                            <LogOut className=\"h-4 w-4 mr-3\" />\n                            Logout\n                          </Button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Main Content Area */}\n          <main className=\"flex-1 overflow-y-auto bg-gray-50\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAce,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,mIAAA,CAAA,aAAU;IAEtC,MAAM,eAAe;QACnB,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC,uIAAA,CAAA,UAAO;oBAAC,WAAW;oBAAkB,UAAU;;;;;;8BAGhD,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAO,WAAU;sCAChB,cAAA,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDAEb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAIlB,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,0RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6WAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6WAAC;4DAAI,WAAU;sEAAwH;;;;;;;;;;;;;;;;;;sDAO3I,6WAAC;4CAAI,WAAU;;8DAEb,6WAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;8DAC5C,cAAA,6WAAC,kTAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAIxB,6WAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;;sEAC5C,6WAAC,sRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6WAAC;4DAAK,WAAU;sEAAgI;;;;;;;;;;;;8DAMlJ,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;;8EAEV,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,6WAAC;4EAAI,WAAU;sFACb,cAAA,6WAAC;gFAAI,WAAU;0FACb,cAAA,6WAAC;oFAAK,WAAU;8FACb,MAAM;;;;;;;;;;;;;;;;;;;;;;8EAMf,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGlB,6WAAC,wSAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;wDAIxB,8BACC,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,6WAAC;4EAAE,WAAU;sFAAyB,MAAM;;;;;;;;;;;;8EAG9C,6WAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,6WAAC,sRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIhD,6WAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,6WAAC,8RAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIpD,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,WAAU;wEACV,SAAS;;0FAET,6WAAC,8RAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAarD,6WAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1880, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/adminKycApi.ts"], "sourcesContent": ["import { base<PERSON><PERSON>, <PERSON>piR<PERSON>po<PERSON>, BaseQueryParams, createQueryParams } from './baseApi'\n\n// KYC Document Interface\nexport interface KYCDocument {\n  _id: string\n  type: 'identity' | 'address' | 'income' | 'bank' | 'photo' | 'signature' | 'other'\n  subType: string\n  documentNumber?: string\n  issuedBy?: string\n  issuedDate?: string\n  expiryDate?: string\n  status: 'pending' | 'verified' | 'rejected'\n  rejectionReason?: string\n  fileUrl?: string\n  uploadedAt: string\n  verifiedAt?: string\n}\n\n// KYC Status Interface\nexport interface KYCStatus {\n  _id: string\n  userId: {\n    _id: string\n    firstName: string\n    lastName: string\n    email: string\n    phone?: string\n    profileImage?: string\n  }\n  status: 'not_started' | 'pending' | 'approved' | 'rejected' | 'under_review'\n  level: 'basic' | 'advanced' | 'premium'\n  submittedAt?: string\n  reviewedAt?: string\n  reviewedBy?: {\n    _id: string\n    firstName: string\n    lastName: string\n  }\n  rejectionReason?: string\n  documents: KYCDocument[]\n  personalInfo: {\n    nationality?: string\n    placeOfBirth?: string\n    gender?: string\n    maritalStatus?: string\n  }\n  address: {\n    street?: string\n    city?: string\n    state?: string\n    postalCode?: string\n    country?: string\n    addressType?: 'permanent' | 'current' | 'mailing'\n    residenceSince?: string\n  }\n  identityInfo: {\n    aadharNumber?: string\n    panNumber?: string\n    passportNumber?: string\n    drivingLicenseNumber?: string\n  }\n  bankInfo: {\n    accountNumber?: string\n    ifscCode?: string\n    bankName?: string\n    accountType?: string\n    accountHolderName?: string\n  }\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface KYCQueryParams extends BaseQueryParams {\n  status?: string\n  level?: string\n  userId?: string\n}\n\nexport interface KYCApprovalRequest {\n  status: 'approved' | 'rejected'\n  rejectionReason?: string\n  level?: 'basic' | 'advanced' | 'premium'\n  notes?: string\n}\n\nexport const adminKycApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // Get all KYC submissions for admin review\n    getAllKYCSubmissions: builder.query<ApiResponse<{\n      kycs: KYCStatus[]\n      total: number\n      page: number\n      limit: number\n      totalPages: number\n    }>, KYCQueryParams>({\n      query: (params = {}) => ({\n        url: '/admin/kyc',\n        params: createQueryParams(params)\n      }),\n      providesTags: ['KYC'],\n    }),\n\n    // Get KYC by ID for detailed review\n    getKYCById: builder.query<ApiResponse<KYCStatus>, string>({\n      query: (id) => `/admin/kyc/${id}`,\n      providesTags: (_, __, id) => [{ type: 'KYC', id }],\n    }),\n\n    // Get KYC by User ID\n    getKYCByUserId: builder.query<ApiResponse<KYCStatus>, string>({\n      query: (userId) => `/kyc/user/${userId}`,\n      providesTags: (_, __, userId) => [{ type: 'KYC', id: userId }],\n    }),\n\n    // Approve or reject KYC\n    updateKYCStatus: builder.mutation<ApiResponse<KYCStatus>, { id: string; data: KYCApprovalRequest }>({\n      query: ({ id, data }) => ({\n        url: `/kyc/${id}/status`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (_, __, { id }) => [{ type: 'KYC', id }, 'KYC', 'User'],\n    }),\n\n    // Get KYC statistics for admin dashboard\n    getKYCStats: builder.query<ApiResponse<{\n      totalSubmissions: number\n      pendingReview: number\n      approved: number\n      rejected: number\n      underReview: number\n      basicLevel: number\n      advancedLevel: number\n      premiumLevel: number\n      recentSubmissions: Array<{\n        _id: string\n        userId: {\n          firstName: string\n          lastName: string\n          email: string\n        }\n        status: string\n        submittedAt: string\n      }>\n    }>, void>({\n      query: () => '/admin/kyc/stats',\n      providesTags: ['KYC'],\n    }),\n\n    // Bulk approve/reject KYCs\n    bulkUpdateKYCStatus: builder.mutation<ApiResponse<{ updated: number }>, {\n      ids: string[]\n      data: KYCApprovalRequest\n    }>({\n      query: ({ ids, data }) => ({\n        url: '/admin/kyc/bulk-update',\n        method: 'PUT',\n        body: { ids, ...data },\n      }),\n      invalidatesTags: ['KYC', 'User'],\n    }),\n\n    // Get KYC document by ID\n    getKYCDocument: builder.query<ApiResponse<KYCDocument>, string>({\n      query: (documentId) => `/admin/kyc/document/${documentId}`,\n      providesTags: (_, __, id) => [{ type: 'KYC', id }],\n    }),\n\n    // Verify/reject specific document\n    updateDocumentStatus: builder.mutation<ApiResponse<KYCDocument>, {\n      documentId: string\n      status: 'verified' | 'rejected'\n      rejectionReason?: string\n    }>({\n      query: ({ documentId, ...data }) => ({\n        url: `/admin/kyc/document/${documentId}/status`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: ['KYC'],\n    }),\n\n    // Get KYC history/audit trail\n    getKYCHistory: builder.query<ApiResponse<Array<{\n      action: string\n      status: string\n      timestamp: string\n      details?: string\n      performedBy: {\n        _id: string\n        firstName: string\n        lastName: string\n      }\n    }>>, string>({\n      query: (kycId) => `/admin/kyc/${kycId}/history`,\n      providesTags: (_, __, id) => [{ type: 'KYC', id }],\n    }),\n\n    // Download KYC PDF\n    downloadKYCPDF: builder.mutation<Blob, string>({\n      query: (kycId) => ({\n        url: `/kyc/${kycId}/download-pdf`,\n        method: 'GET',\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Export KYC data\n    exportKYCData: builder.mutation<ApiResponse<{ downloadUrl: string }>, {\n      format: 'csv' | 'excel' | 'pdf'\n      filters?: KYCQueryParams\n    }>({\n      query: (data) => ({\n        url: '/admin/kyc/export',\n        method: 'POST',\n        body: data,\n      }),\n    }),\n  }),\n})\n\nexport const {\n  useGetAllKYCSubmissionsQuery,\n  useGetKYCByIdQuery,\n  useGetKYCByUserIdQuery,\n  useUpdateKYCStatusMutation,\n  useDownloadKYCPDFMutation,\n  useGetKYCStatsQuery,\n  useBulkUpdateKYCStatusMutation,\n  useGetKYCDocumentQuery,\n  useUpdateDocumentStatusMutation,\n  useGetKYCHistoryQuery,\n  useExportKYCDataMutation,\n} = adminKycApi\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAqFO,MAAM,cAAc,8HAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACjD,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,2CAA2C;YAC3C,sBAAsB,QAAQ,KAAK,CAMf;gBAClB,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE;oBAC5B,CAAC;gBACD,cAAc;oBAAC;iBAAM;YACvB;YAEA,oCAAoC;YACpC,YAAY,QAAQ,KAAK,CAAiC;gBACxD,OAAO,CAAC,KAAO,CAAC,WAAW,EAAE,IAAI;gBACjC,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;qBAAE;YACpD;YAEA,qBAAqB;YACrB,gBAAgB,QAAQ,KAAK,CAAiC;gBAC5D,OAAO,CAAC,SAAW,CAAC,UAAU,EAAE,QAAQ;gBACxC,cAAc,CAAC,GAAG,IAAI,SAAW;wBAAC;4BAAE,MAAM;4BAAO,IAAI;wBAAO;qBAAE;YAChE;YAEA,wBAAwB;YACxB,iBAAiB,QAAQ,QAAQ,CAAmE;gBAClG,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC;wBACxB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;wBAAG;wBAAO;qBAAO;YAC1E;YAEA,yCAAyC;YACzC,aAAa,QAAQ,KAAK,CAmBhB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAM;YACvB;YAEA,2BAA2B;YAC3B,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;wBACzB,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAK,GAAG,IAAI;wBAAC;oBACvB,CAAC;gBACD,iBAAiB;oBAAC;oBAAO;iBAAO;YAClC;YAEA,yBAAyB;YACzB,gBAAgB,QAAQ,KAAK,CAAmC;gBAC9D,OAAO,CAAC,aAAe,CAAC,oBAAoB,EAAE,YAAY;gBAC1D,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;qBAAE;YACpD;YAEA,kCAAkC;YAClC,sBAAsB,QAAQ,QAAQ,CAInC;gBACD,OAAO,CAAC,EAAE,UAAU,EAAE,GAAG,MAAM,GAAK,CAAC;wBACnC,KAAK,CAAC,oBAAoB,EAAE,WAAW,OAAO,CAAC;wBAC/C,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAM;YAC1B;YAEA,8BAA8B;YAC9B,eAAe,QAAQ,KAAK,CAUf;gBACX,OAAO,CAAC,QAAU,CAAC,WAAW,EAAE,MAAM,QAAQ,CAAC;gBAC/C,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;qBAAE;YACpD;YAEA,mBAAmB;YACnB,gBAAgB,QAAQ,QAAQ,CAAe;gBAC7C,OAAO,CAAC,QAAU,CAAC;wBACjB,KAAK,CAAC,KAAK,EAAE,MAAM,aAAa,CAAC;wBACjC,QAAQ;wBACR,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,kBAAkB;YAClB,eAAe,QAAQ,QAAQ,CAG5B;gBACD,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,4BAA4B,EAC5B,kBAAkB,EAClB,sBAAsB,EACtB,0BAA0B,EAC1B,yBAAyB,EACzB,mBAAmB,EACnB,8BAA8B,EAC9B,sBAAsB,EACtB,+BAA+B,EAC/B,qBAAqB,EACrB,wBAAwB,EACzB,GAAG", "debugId": null}}, {"offset": {"line": 2023, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/userDetailsApi.ts"], "sourcesContent": ["import { baseApi } from './baseApi'\n\n// Types for user-specific data\nexport interface UserInvestment {\n  _id: string\n  propertyId: string\n  propertyName: string\n  location: string\n  stocksOwned: number\n  stockPrice: number\n  totalInvestment: number\n  currentValue: number\n  returns: number\n  roi: number\n  purchaseDate: string\n  status: 'active' | 'sold'\n  expectedAnnualReturn: number\n  propertyImage?: string\n}\n\nexport interface UserTransaction {\n  _id: string\n  type: 'deposit' | 'withdrawal' | 'investment' | 'return' | 'refund'\n  amount: number\n  status: 'pending' | 'completed' | 'failed' | 'cancelled'\n  paymentMethod?: string\n  description: string\n  createdAt: string\n  updatedAt: string\n  propertyId?: string\n  propertyName?: string\n  reference?: string\n}\n\nexport interface UserActivity {\n  _id: string\n  action: string\n  entity: string\n  entityId?: string\n  description: string\n  ipAddress?: string\n  userAgent?: string\n  location?: string\n  createdAt: string\n  metadata?: any\n}\n\nexport interface UserStats {\n  totalInvestments: number\n  totalReturns: number\n  activeInvestments: number\n  totalTransactions: number\n  walletBalance: number\n  portfolioValue: number\n  roi: number\n  joinDate: string\n  lastActivity: string\n}\n\n// API endpoints for user details\nexport const userDetailsApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // Get user investments\n    getUserInvestments: builder.query<{\n      success: boolean\n      data: {\n        investments: UserInvestment[]\n        pagination: {\n          currentPage: number\n          totalPages: number\n          totalItems: number\n          itemsPerPage: number\n        }\n      }\n    }, {\n      userId: string\n      page?: number\n      limit?: number\n      status?: string\n      sortBy?: string\n      sortOrder?: 'asc' | 'desc'\n    }>({\n      query: ({ userId, ...params }) => ({\n        url: `/admin/users/${userId}/investments`,\n        params,\n      }),\n      providesTags: (result, error, { userId }) => [\n        { type: 'User', id: userId },\n        'Investment'\n      ],\n    }),\n\n    // Get user transactions\n    getUserTransactions: builder.query<{\n      success: boolean\n      data: {\n        transactions: UserTransaction[]\n        pagination: {\n          currentPage: number\n          totalPages: number\n          totalItems: number\n          itemsPerPage: number\n        }\n      }\n    }, {\n      userId: string\n      page?: number\n      limit?: number\n      type?: string\n      status?: string\n      startDate?: string\n      endDate?: string\n      sortBy?: string\n      sortOrder?: 'asc' | 'desc'\n    }>({\n      query: ({ userId, ...params }) => ({\n        url: `/admin/users/${userId}/transactions`,\n        params,\n      }),\n      providesTags: (result, error, { userId }) => [\n        { type: 'User', id: userId },\n        'Transaction'\n      ],\n    }),\n\n    // Get user activity log\n    getUserActivity: builder.query<{\n      success: boolean\n      data: {\n        activities: UserActivity[]\n        pagination: {\n          currentPage: number\n          totalPages: number\n          totalItems: number\n          itemsPerPage: number\n        }\n      }\n    }, {\n      userId: string\n      page?: number\n      limit?: number\n      action?: string\n      entity?: string\n      startDate?: string\n      endDate?: string\n      sortBy?: string\n      sortOrder?: 'asc' | 'desc'\n    }>({\n      query: ({ userId, ...params }) => ({\n        url: `/admin/users/${userId}/activity`,\n        params,\n      }),\n      providesTags: (result, error, { userId }) => [\n        { type: 'User', id: userId },\n        'Activity'\n      ],\n    }),\n\n    // Get user statistics\n    getUserStats: builder.query<{\n      success: boolean\n      data: UserStats\n    }, string>({\n      query: (userId) => `/admin/users/${userId}/stats`,\n      providesTags: (result, error, userId) => [\n        { type: 'User', id: userId },\n        'UserStats'\n      ],\n    }),\n\n    // Get user portfolio performance\n    getUserPortfolioPerformance: builder.query<{\n      success: boolean\n      data: {\n        chartData: Array<{\n          date: string\n          value: number\n          invested: number\n          returns: number\n        }>\n        summary: {\n          totalInvested: number\n          currentValue: number\n          totalReturns: number\n          roi: number\n          bestPerforming: UserInvestment\n          worstPerforming: UserInvestment\n        }\n      }\n    }, {\n      userId: string\n      period?: '1M' | '3M' | '6M' | '1Y' | 'ALL'\n    }>({\n      query: ({ userId, period = '1Y' }) => ({\n        url: `/admin/users/${userId}/portfolio-performance`,\n        params: { period },\n      }),\n      providesTags: (result, error, { userId }) => [\n        { type: 'User', id: userId },\n        'Portfolio'\n      ],\n    }),\n  }),\n})\n\nexport const {\n  useGetUserInvestmentsQuery,\n  useGetUserTransactionsQuery,\n  useGetUserActivityQuery,\n  useGetUserStatsQuery,\n  useGetUserPortfolioPerformanceQuery,\n} = userDetailsApi\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AA4DO,MAAM,iBAAiB,8HAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACpD,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,uBAAuB;YACvB,oBAAoB,QAAQ,KAAK,CAkB9B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,QAAQ,GAAK,CAAC;wBACjC,KAAK,CAAC,aAAa,EAAE,OAAO,YAAY,CAAC;wBACzC;oBACF,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC3C;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,KAAK,CAqB/B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,QAAQ,GAAK,CAAC;wBACjC,KAAK,CAAC,aAAa,EAAE,OAAO,aAAa,CAAC;wBAC1C;oBACF,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC3C;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,wBAAwB;YACxB,iBAAiB,QAAQ,KAAK,CAqB3B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,QAAQ,GAAK,CAAC;wBACjC,KAAK,CAAC,aAAa,EAAE,OAAO,SAAS,CAAC;wBACtC;oBACF,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC3C;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,sBAAsB;YACtB,cAAc,QAAQ,KAAK,CAGhB;gBACT,OAAO,CAAC,SAAW,CAAC,aAAa,EAAE,OAAO,MAAM,CAAC;gBACjD,cAAc,CAAC,QAAQ,OAAO,SAAW;wBACvC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,iCAAiC;YACjC,6BAA6B,QAAQ,KAAK,CAqBvC;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,SAAS,IAAI,EAAE,GAAK,CAAC;wBACrC,KAAK,CAAC,aAAa,EAAE,OAAO,sBAAsB,CAAC;wBACnD,QAAQ;4BAAE;wBAAO;oBACnB,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC3C;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,0BAA0B,EAC1B,2BAA2B,EAC3B,uBAAuB,EACvB,oBAAoB,EACpB,mCAAmC,EACpC,GAAG", "debugId": null}}, {"offset": {"line": 2114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/app/users/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport { Separator } from '@/components/ui/separator'\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Label } from '@/components/ui/label'\nimport { toast } from 'sonner'\nimport DashboardLayout from '@/components/layout/DashboardLayout'\nimport { useGetUserDetailsQuery } from '@/store/api/adminUserApi'\nimport { useUpdateKYCStatusMutation, useDownloadKYCPDFMutation } from '@/store/api/adminKycApi'\nimport {\n  useGetUserInvestmentsQuery,\n  useGetUserTransactionsQuery,\n  useGetUserActivityQuery,\n  useGetUserStatsQuery,\n  useGetUserPortfolioPerformanceQuery\n} from '@/store/api/userDetailsApi'\nimport {\n  ArrowLeft,\n  Edit,\n  User,\n  Mail,\n  Phone,\n  MapPin,\n  Calendar,\n  Shield,\n  CheckCircle,\n  XCircle,\n  Clock,\n  AlertCircle,\n  FileText,\n  Download,\n  Eye,\n  Wallet,\n  TrendingUp,\n  Activity,\n  CreditCard,\n  Building,\n  Globe,\n  Users,\n  Star,\n  Award,\n  Briefcase\n} from 'lucide-react'\n\nexport default function UserDetailsPage() {\n  const params = useParams()\n  const router = useRouter()\n  const userId = params?.id as string\n  \n  const { data: userResponse, isLoading: userLoading, error: userError, refetch: refetchUser } = useGetUserDetailsQuery(userId)\n  const [updateKYCStatus, { isLoading: isUpdatingKYC }] = useUpdateKYCStatusMutation()\n  const [downloadKYCPDFMutation, { isLoading: isDownloadingPDF }] = useDownloadKYCPDFMutation()\n\n  // User details APIs\n  const { data: investmentsResponse, isLoading: investmentsLoading } = useGetUserInvestmentsQuery({\n    userId,\n    page: 1,\n    limit: 10\n  })\n  const { data: transactionsResponse, isLoading: transactionsLoading } = useGetUserTransactionsQuery({\n    userId,\n    page: 1,\n    limit: 10\n  })\n  const { data: activityResponse, isLoading: activityLoading } = useGetUserActivityQuery({\n    userId,\n    page: 1,\n    limit: 20\n  })\n  const { data: statsResponse, isLoading: statsLoading } = useGetUserStatsQuery(userId)\n  const { data: portfolioResponse, isLoading: portfolioLoading } = useGetUserPortfolioPerformanceQuery({\n    userId,\n    period: '1Y'\n  })\n\n  // State hooks - must be at the top level before any conditional returns\n  const [selectedTab, setSelectedTab] = useState('overview')\n  const [showKYCModal, setShowKYCModal] = useState(false)\n  const [kycAction, setKycAction] = useState<'approve' | 'reject' | ''>('')\n  const [rejectionReason, setRejectionReason] = useState('')\n  const [reviewNotes, setReviewNotes] = useState('')\n\n  const user = userResponse?.user\n  const kyc = userResponse?.kyc\n  const wallet = userResponse?.wallet\n  const investments = investmentsResponse?.data?.investments || []\n  const transactions = transactionsResponse?.data?.transactions || []\n  const activities = activityResponse?.data?.activities || []\n  const userStats = statsResponse?.data\n  const portfolioData = portfolioResponse?.data\n\n  // Loading states\n  const isLoading = userLoading\n\n  // Error handling\n  if (userError) {\n    return (\n      <DashboardLayout>\n        <div className=\"flex items-center justify-center min-h-[400px]\">\n          <div className=\"text-center\">\n            <h2 className=\"text-2xl font-bold text-red-600 mb-2\">Error Loading User</h2>\n            <p className=\"text-gray-600\">Failed to load user details. Please try again.</p>\n          </div>\n        </div>\n      </DashboardLayout>\n    )\n  }\n\n  // Loading state\n  if (isLoading) {\n    return (\n      <DashboardLayout>\n        <div className=\"flex items-center justify-center min-h-[400px]\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Loading user details...</p>\n          </div>\n        </div>\n      </DashboardLayout>\n    )\n  }\n\n  // User not found\n  if (!user) {\n    return (\n      <DashboardLayout>\n        <div className=\"flex items-center justify-center min-h-[400px]\">\n          <div className=\"text-center\">\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-2\">User Not Found</h2>\n            <p className=\"text-gray-600\">The requested user could not be found.</p>\n          </div>\n        </div>\n      </DashboardLayout>\n    )\n  }\n\n  // KYC action handlers\n  const handleKYCAction = (action: 'approve' | 'reject') => {\n    setKycAction(action)\n    setShowKYCModal(true)\n    setRejectionReason('')\n    setReviewNotes('')\n  }\n\n  const handleKYCSubmit = async () => {\n    if (!kyc?._id) return\n\n    try {\n      const data = {\n        status: kycAction === 'approve' ? 'approved' as const : 'rejected' as const,\n        ...(kycAction === 'reject' && rejectionReason && { rejectionReason }),\n        ...(reviewNotes && { notes: reviewNotes }),\n        level: 'basic' as const\n      }\n\n      console.log('Updating KYC status:', { id: kyc._id, data })\n      await updateKYCStatus({ id: kyc._id, data }).unwrap()\n\n      toast.success(`KYC ${kycAction === 'approve' ? 'approved' : 'rejected'} successfully`)\n      setShowKYCModal(false)\n      setKycAction('')\n      setRejectionReason('')\n      setReviewNotes('')\n      refetchUser()\n    } catch (error: any) {\n      console.error('KYC update error:', error)\n      toast.error(error?.data?.message || `Failed to ${kycAction} KYC`)\n    }\n  }\n\n  const downloadKYCPDF = async () => {\n    if (!kyc?._id) return\n\n    try {\n      const blob = await downloadKYCPDFMutation(kyc._id).unwrap()\n      const url = window.URL.createObjectURL(blob)\n      const a = document.createElement('a')\n      a.href = url\n      a.download = `KYC_${user?.firstName}_${user?.lastName}.pdf`\n      document.body.appendChild(a)\n      a.click()\n      window.URL.revokeObjectURL(url)\n      document.body.removeChild(a)\n      toast.success('KYC PDF downloaded successfully')\n    } catch (error) {\n      console.error('PDF download error:', error)\n      toast.error('Failed to download KYC PDF')\n    }\n  }\n\n  if (userLoading) {\n    return (\n      <DashboardLayout>\n        <div className=\"flex items-center justify-center h-96\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Loading user details...</p>\n          </div>\n        </div>\n      </DashboardLayout>\n    )\n  }\n\n  if (userError || !user) {\n    return (\n      <DashboardLayout>\n        <div className=\"flex items-center justify-center h-96\">\n          <div className=\"text-center\">\n            <AlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">User not found</h3>\n            <p className=\"text-gray-600 mb-4\">The user you're looking for doesn't exist or has been removed.</p>\n            <Button onClick={() => router.push('/users')}>\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back to Users\n            </Button>\n          </div>\n        </div>\n      </DashboardLayout>\n    )\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active': return 'bg-green-100 text-green-800'\n      case 'inactive': return 'bg-gray-100 text-gray-800'\n      case 'suspended': return 'bg-red-100 text-red-800'\n      case 'pending_verification': return 'bg-yellow-100 text-yellow-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getKYCStatusColor = (status: string) => {\n    switch (status) {\n      case 'approved': return 'bg-green-100 text-green-800'\n      case 'pending': return 'bg-yellow-100 text-yellow-800'\n      case 'rejected': return 'bg-red-100 text-red-800'\n      case 'under_review': return 'bg-blue-100 text-blue-800'\n      case 'not_started': return 'bg-gray-100 text-gray-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getKYCStatusIcon = (status: string) => {\n    switch (status) {\n      case 'approved': return <CheckCircle className=\"h-4 w-4\" />\n      case 'pending': return <Clock className=\"h-4 w-4\" />\n      case 'rejected': return <XCircle className=\"h-4 w-4\" />\n      case 'under_review': return <Activity className=\"h-4 w-4\" />\n      case 'not_started': return <AlertCircle className=\"h-4 w-4\" />\n      default: return <AlertCircle className=\"h-4 w-4\" />\n    }\n  }\n\n  const getRoleBadge = (role: string) => {\n    switch (role) {\n      case 'ADMIN': return <Badge className=\"bg-purple-100 text-purple-800\">Admin</Badge>\n      case 'SUBADMIN': return <Badge className=\"bg-blue-100 text-blue-800\">Sub Admin</Badge>\n      case 'SALES': return <Badge className=\"bg-orange-100 text-orange-800\">Sales</Badge>\n      case 'USER': return <Badge className=\"bg-gray-100 text-gray-800\">User</Badge>\n      default: return <Badge variant=\"secondary\">{role}</Badge>\n    }\n  }\n\n\n\n  return (\n    <DashboardLayout>\n      <div className=\"h-full overflow-auto\">\n        <div className=\"max-w-7xl mx-auto p-6 space-y-6\">\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg p-6 text-white\">\n            <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n              <div className=\"flex items-center gap-4\">\n                <Button\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  onClick={() => router.back()}\n                  className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n                >\n                  <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                  Back\n                </Button>\n                <Avatar className=\"h-16 w-16 border-4 border-white/30\">\n                  <AvatarImage src=\"\" />\n                  <AvatarFallback className=\"bg-white/20 text-white text-xl font-bold\">\n                    {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}\n                  </AvatarFallback>\n                </Avatar>\n                <div>\n                  <h1 className=\"text-3xl font-bold mb-2\">\n                    {user.firstName} {user.lastName}\n                  </h1>\n                  <div className=\"flex items-center gap-3\">\n                    <Badge className={getStatusColor(user.status)}>\n                      {user.status?.replace('_', ' ') || 'Unknown'}\n                    </Badge>\n                    {getRoleBadge(user.role)}\n                    <Badge className={getKYCStatusColor(user.kyc?.status || 'not_submitted')}>\n                      {getKYCStatusIcon(user.kyc?.status || 'not_submitted')}\n                      <span className=\"ml-1\">{user.kyc?.status?.replace('_', ' ') || 'Not Submitted'}</span>\n                    </Badge>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center gap-3\">\n                <Button\n                  onClick={() => router.push(`/users/${userId}/edit`)}\n                  className=\"bg-yellow-500 hover:bg-yellow-600 text-black font-semibold\"\n                >\n                  <Edit className=\"h-4 w-4 mr-2\" />\n                  Edit User\n                </Button>\n                <Button\n                  variant=\"secondary\"\n                  className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n                >\n                  <Download className=\"h-4 w-4 mr-2\" />\n                  Export Data\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Total Investments</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">\n                      ₹{(userStats?.totalInvestments || 0).toLocaleString()}\n                    </p>\n                  </div>\n                  <TrendingUp className=\"h-8 w-8 text-green-600\" />\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Wallet Balance</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">\n                      ₹{(user.wallet?.balance || 0).toLocaleString()}\n                    </p>\n                  </div>\n                  <Wallet className=\"h-8 w-8 text-blue-600\" />\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Total Returns</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">\n                      ₹{(userStats?.totalReturns || 0).toLocaleString()}\n                    </p>\n                  </div>\n                  <Award className=\"h-8 w-8 text-purple-600\" />\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Member Since</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">\n                      {new Date(user.createdAt).toLocaleDateString('en-US', { \n                        month: 'short', \n                        year: 'numeric' \n                      })}\n                    </p>\n                  </div>\n                  <Calendar className=\"h-8 w-8 text-orange-600\" />\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Main Content Tabs */}\n          <Tabs value={selectedTab} onValueChange={setSelectedTab} className=\"space-y-6\">\n            <TabsList className=\"grid w-full grid-cols-5\">\n              <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n              <TabsTrigger value=\"kyc\">KYC Details</TabsTrigger>\n              <TabsTrigger value=\"investments\">Investments</TabsTrigger>\n              <TabsTrigger value=\"transactions\">Transactions</TabsTrigger>\n              <TabsTrigger value=\"activity\">Activity</TabsTrigger>\n            </TabsList>\n\n            {/* Overview Tab */}\n            <TabsContent value=\"overview\" className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                <Card>\n                  <CardHeader>\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <User className=\"h-5 w-5\" />\n                      Personal Information\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent className=\"space-y-4\">\n                    <div className=\"grid grid-cols-2 gap-4\">\n                      <div>\n                        <p className=\"text-sm text-gray-600\">First Name</p>\n                        <p className=\"font-medium\">{user.firstName}</p>\n                      </div>\n                      <div>\n                        <p className=\"text-sm text-gray-600\">Last Name</p>\n                        <p className=\"font-medium\">{user.lastName}</p>\n                      </div>\n                      <div>\n                        <p className=\"text-sm text-gray-600\">Email</p>\n                        <p className=\"font-medium flex items-center gap-2\">\n                          <Mail className=\"h-4 w-4\" />\n                          {user.email}\n                          {user.status === 'active' && <CheckCircle className=\"h-4 w-4 text-green-600\" />}\n                        </p>\n                      </div>\n                      <div>\n                        <p className=\"text-sm text-gray-600\">Phone</p>\n                        <p className=\"font-medium flex items-center gap-2\">\n                          <Phone className=\"h-4 w-4\" />\n                          {user.phone || 'Not provided'}\n                          {user.phone && <CheckCircle className=\"h-4 w-4 text-green-600\" />}\n                        </p>\n                      </div>\n                      <div>\n                        <p className=\"text-sm text-gray-600\">Date of Birth</p>\n                        <p className=\"font-medium\">\n                          {'Not provided'}\n                        </p>\n                      </div>\n                      <div>\n                        <p className=\"text-sm text-gray-600\">Referral Code</p>\n                        <p className=\"font-medium\">{'Not generated'}</p>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                <Card>\n                  <CardHeader>\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <Wallet className=\"h-5 w-5\" />\n                      Wallet Information\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent className=\"space-y-4\">\n                    <div className=\"grid grid-cols-1 gap-4\">\n                      <div className=\"flex justify-between items-center p-3 bg-blue-50 rounded-lg\">\n                        <span className=\"text-sm text-gray-600\">Current Balance</span>\n                        <span className=\"font-semibold text-blue-600\">₹{(user.wallet?.balance || 0).toLocaleString()}</span>\n                      </div>\n                      <div className=\"flex justify-between items-center p-3 bg-green-50 rounded-lg\">\n                        <span className=\"text-sm text-gray-600\">Total Invested</span>\n                        <span className=\"font-semibold text-green-600\">₹{(userStats?.totalInvestments || 0).toLocaleString()}</span>\n                      </div>\n                      <div className=\"flex justify-between items-center p-3 bg-purple-50 rounded-lg\">\n                        <span className=\"text-sm text-gray-600\">Total Returns</span>\n                        <span className=\"font-semibold text-purple-600\">₹{(userStats?.totalReturns || 0).toLocaleString()}</span>\n                      </div>\n                      <div className=\"flex justify-between items-center p-3 bg-orange-50 rounded-lg\">\n                        <span className=\"text-sm text-gray-600\">Portfolio Value</span>\n                        <span className=\"font-semibold text-orange-600\">₹{(userStats?.portfolioValue || 0).toLocaleString()}</span>\n                      </div>\n                      <div className=\"flex justify-between items-center p-3 bg-red-50 rounded-lg\">\n                        <span className=\"text-sm text-gray-600\">ROI</span>\n                        <span className=\"font-semibold text-red-600\">{(userStats?.roi || 0).toFixed(2)}%</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {/* Portfolio Summary */}\n                {userStats && (\n                  <Card>\n                    <CardHeader>\n                      <CardTitle className=\"flex items-center gap-2\">\n                        <TrendingUp className=\"h-5 w-5\" />\n                        Portfolio Summary\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent className=\"space-y-4\">\n                      <div className=\"grid grid-cols-2 gap-4\">\n                        <div className=\"flex justify-between items-center p-3 bg-blue-50 rounded-lg\">\n                          <span className=\"text-sm text-gray-600\">Total Investments</span>\n                          <span className=\"font-semibold text-blue-600\">{userStats.totalInvestments}</span>\n                        </div>\n                        <div className=\"flex justify-between items-center p-3 bg-green-50 rounded-lg\">\n                          <span className=\"text-sm text-gray-600\">Active Investments</span>\n                          <span className=\"font-semibold text-green-600\">{userStats.activeInvestments}</span>\n                        </div>\n                        <div className=\"flex justify-between items-center p-3 bg-purple-50 rounded-lg\">\n                          <span className=\"text-sm text-gray-600\">Portfolio Value</span>\n                          <span className=\"font-semibold text-purple-600\">₹{userStats.portfolioValue.toLocaleString()}</span>\n                        </div>\n                        <div className=\"flex justify-between items-center p-3 bg-orange-50 rounded-lg\">\n                          <span className=\"text-sm text-gray-600\">Overall ROI</span>\n                          <span className={`font-semibold ${userStats.roi >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                            {userStats.roi}%\n                          </span>\n                        </div>\n                      </div>\n                      <div className=\"grid grid-cols-1 gap-4\">\n                        <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                          <span className=\"text-sm text-gray-600\">Member Since</span>\n                          <span className=\"font-semibold\">{new Date(userStats.joinDate).toLocaleDateString()}</span>\n                        </div>\n                        <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                          <span className=\"text-sm text-gray-600\">Last Activity</span>\n                          <span className=\"font-semibold\">{new Date(userStats.lastActivity).toLocaleDateString()}</span>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n              </div>\n            </TabsContent>\n\n            {/* KYC Details Tab */}\n            <TabsContent value=\"kyc\" className=\"space-y-6\">\n              {isLoading ? (\n                <div className=\"flex items-center justify-center py-8\">\n                  <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n                  <span className=\"ml-2 text-gray-600\">Loading KYC details...</span>\n                </div>\n              ) : !kyc ? (\n                <Card>\n                  <CardContent className=\"p-12 text-center\">\n                    <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No KYC Submission</h3>\n                    <p className=\"text-gray-600\">This user hasn't submitted KYC documents yet.</p>\n                  </CardContent>\n                </Card>\n              ) : (\n                <div className=\"space-y-6\">\n                  {/* KYC Status Card */}\n                  <Card>\n                    <CardHeader>\n                      <div className=\"flex items-center justify-between\">\n                        <CardTitle className=\"flex items-center gap-2\">\n                          <Shield className=\"h-5 w-5\" />\n                          KYC Status & Actions\n                        </CardTitle>\n                        <div className=\"flex items-center gap-2\">\n                          <Badge className={getKYCStatusColor(kyc.status)}>\n                            {getKYCStatusIcon(kyc.status)}\n                            <span className=\"ml-1\">{kyc.status?.replace('_', ' ') || 'Unknown'}</span>\n                          </Badge>\n                          <Badge variant=\"outline\">{kyc.level} Level</Badge>\n                        </div>\n                      </div>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n                        <div>\n                          <p className=\"text-sm text-gray-600\">Submitted At</p>\n                          <p className=\"font-medium\">\n                            {kyc.submittedAt ? new Date(kyc.submittedAt).toLocaleString() : 'Not submitted'}\n                          </p>\n                        </div>\n                        <div>\n                          <p className=\"text-sm text-gray-600\">Reviewed At</p>\n                          <p className=\"font-medium\">\n                            {kyc.reviewedAt ? new Date(kyc.reviewedAt).toLocaleString() : 'Not reviewed'}\n                          </p>\n                        </div>\n                        <div>\n                          <p className=\"text-sm text-gray-600\">Reviewed By</p>\n                          <p className=\"font-medium\">\n                            {kyc.reviewedBy ? `${kyc.reviewedBy.firstName} ${kyc.reviewedBy.lastName}` : 'Not reviewed'}\n                          </p>\n                        </div>\n                      </div>\n\n                      {kyc.rejectionReason && (\n                        <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg mb-6\">\n                          <h4 className=\"font-medium text-red-800 mb-2\">Rejection Reason</h4>\n                          <p className=\"text-red-700\">{kyc.rejectionReason}</p>\n                        </div>\n                      )}\n\n                      <div className=\"flex items-center gap-3\">\n                        {(kyc.status === 'pending' || kyc.status === 'under_review') && (\n                          <>\n                            <Button\n                              onClick={() => handleKYCAction('approve')}\n                              disabled={isUpdatingKYC}\n                              className=\"bg-green-600 hover:bg-green-700\"\n                            >\n                              <CheckCircle className=\"h-4 w-4 mr-2\" />\n                              Approve KYC\n                            </Button>\n                            <Button\n                              onClick={() => handleKYCAction('reject')}\n                              disabled={isUpdatingKYC}\n                              variant=\"destructive\"\n                            >\n                              <XCircle className=\"h-4 w-4 mr-2\" />\n                              Reject KYC\n                            </Button>\n                          </>\n                        )}\n                        <Button\n                          onClick={downloadKYCPDF}\n                          variant=\"outline\"\n                          className=\"ml-auto\"\n                        >\n                          <Download className=\"h-4 w-4 mr-2\" />\n                          Download PDF\n                        </Button>\n                      </div>\n                    </CardContent>\n                  </Card>\n\n                  {/* Personal Information */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                    <Card>\n                      <CardHeader>\n                        <CardTitle className=\"flex items-center gap-2\">\n                          <User className=\"h-5 w-5\" />\n                          Personal Information\n                        </CardTitle>\n                      </CardHeader>\n                      <CardContent className=\"space-y-4\">\n                        <div className=\"grid grid-cols-2 gap-4\">\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Nationality</p>\n                            <p className=\"font-medium\">{kyc.personalInfo.nationality || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Place of Birth</p>\n                            <p className=\"font-medium\">{kyc.personalInfo.placeOfBirth || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Gender</p>\n                            <p className=\"font-medium\">{kyc.personalInfo.gender || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Marital Status</p>\n                            <p className=\"font-medium\">{kyc.personalInfo.maritalStatus || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Occupation</p>\n                            <p className=\"font-medium\">{(kyc.personalInfo as any).occupation || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Employer</p>\n                            <p className=\"font-medium\">{(kyc.personalInfo as any).employerName || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Annual Income</p>\n                            <p className=\"font-medium\">\n                              {(kyc.personalInfo as any).annualIncome ? `₹${(kyc.personalInfo as any).annualIncome.toLocaleString()}` : 'Not provided'}\n                            </p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Source of Funds</p>\n                            <p className=\"font-medium\">{(kyc.personalInfo as any).sourceOfFunds || 'Not provided'}</p>\n                          </div>\n                        </div>\n                      </CardContent>\n                    </Card>\n\n                    <Card>\n                      <CardHeader>\n                        <CardTitle className=\"flex items-center gap-2\">\n                          <MapPin className=\"h-5 w-5\" />\n                          Address Information\n                        </CardTitle>\n                      </CardHeader>\n                      <CardContent className=\"space-y-4\">\n                        <div className=\"space-y-3\">\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Street Address</p>\n                            <p className=\"font-medium\">{kyc.address.street || 'Not provided'}</p>\n                          </div>\n                          <div className=\"grid grid-cols-2 gap-4\">\n                            <div>\n                              <p className=\"text-sm text-gray-600\">City</p>\n                              <p className=\"font-medium\">{kyc.address.city || 'Not provided'}</p>\n                            </div>\n                            <div>\n                              <p className=\"text-sm text-gray-600\">State</p>\n                              <p className=\"font-medium\">{kyc.address.state || 'Not provided'}</p>\n                            </div>\n                            <div>\n                              <p className=\"text-sm text-gray-600\">Postal Code</p>\n                              <p className=\"font-medium\">{kyc.address.postalCode || 'Not provided'}</p>\n                            </div>\n                            <div>\n                              <p className=\"text-sm text-gray-600\">Country</p>\n                              <p className=\"font-medium\">{kyc.address.country || 'Not provided'}</p>\n                            </div>\n                            <div>\n                              <p className=\"text-sm text-gray-600\">Address Type</p>\n                              <p className=\"font-medium\">{kyc.address.addressType || 'Not provided'}</p>\n                            </div>\n                            <div>\n                              <p className=\"text-sm text-gray-600\">Residence Since</p>\n                              <p className=\"font-medium\">\n                                {kyc.address.residenceSince ? new Date(kyc.address.residenceSince).toLocaleDateString() : 'Not provided'}\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  </div>\n\n                  {/* Identity & Bank Information */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                    <Card>\n                      <CardHeader>\n                        <CardTitle className=\"flex items-center gap-2\">\n                          <CreditCard className=\"h-5 w-5\" />\n                          Identity Information\n                        </CardTitle>\n                      </CardHeader>\n                      <CardContent className=\"space-y-4\">\n                        <div className=\"grid grid-cols-1 gap-4\">\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Aadhar Number</p>\n                            <p className=\"font-medium\">{kyc.identityInfo?.aadharNumber || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">PAN Number</p>\n                            <p className=\"font-medium\">{kyc.identityInfo?.panNumber || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Passport Number</p>\n                            <p className=\"font-medium\">{kyc.identityInfo?.passportNumber || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Driving License</p>\n                            <p className=\"font-medium\">{kyc.identityInfo?.drivingLicenseNumber || 'Not provided'}</p>\n                          </div>\n                        </div>\n                      </CardContent>\n                    </Card>\n\n                    <Card>\n                      <CardHeader>\n                        <CardTitle className=\"flex items-center gap-2\">\n                          <Building className=\"h-5 w-5\" />\n                          Bank Information\n                        </CardTitle>\n                      </CardHeader>\n                      <CardContent className=\"space-y-4\">\n                        <div className=\"grid grid-cols-1 gap-4\">\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Account Number</p>\n                            <p className=\"font-medium\">{kyc.bankInfo?.accountNumber || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">IFSC Code</p>\n                            <p className=\"font-medium\">{kyc.bankInfo?.ifscCode || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Bank Name</p>\n                            <p className=\"font-medium\">{kyc.bankInfo?.bankName || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Account Type</p>\n                            <p className=\"font-medium\">{kyc.bankInfo?.accountType || 'Not provided'}</p>\n                          </div>\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Account Holder Name</p>\n                            <p className=\"font-medium\">{kyc.bankInfo?.accountHolderName || 'Not provided'}</p>\n                          </div>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  </div>\n\n                  {/* Documents */}\n                  <Card>\n                    <CardHeader>\n                      <CardTitle className=\"flex items-center gap-2\">\n                        <FileText className=\"h-5 w-5\" />\n                        Uploaded Documents\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      {kyc.documents && kyc.documents.length > 0 ? (\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                          {kyc.documents.map((doc) => (\n                            <div key={doc._id} className=\"p-4 border border-gray-200 rounded-lg\">\n                              <div className=\"flex items-start justify-between mb-2\">\n                                <div>\n                                  <h4 className=\"font-medium text-gray-900\">{doc.type}</h4>\n                                  <p className=\"text-sm text-gray-600\">{doc.subType}</p>\n                                </div>\n                                <Badge className={\n                                  doc.status === 'verified' ? 'bg-green-100 text-green-800' :\n                                  doc.status === 'rejected' ? 'bg-red-100 text-red-800' :\n                                  'bg-yellow-100 text-yellow-800'\n                                }>\n                                  {doc.status}\n                                </Badge>\n                              </div>\n\n                              {doc.documentNumber && (\n                                <p className=\"text-sm text-gray-600 mb-2\">\n                                  <strong>Number:</strong> {doc.documentNumber}\n                                </p>\n                              )}\n\n                              <div className=\"flex items-center gap-2\">\n                                <Button size=\"sm\" variant=\"outline\">\n                                  <Eye className=\"h-4 w-4 mr-2\" />\n                                  View\n                                </Button>\n                                <Button size=\"sm\" variant=\"outline\">\n                                  <Download className=\"h-4 w-4 mr-2\" />\n                                  Download\n                                </Button>\n                              </div>\n\n                              {doc.rejectionReason && (\n                                <div className=\"mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700\">\n                                  <strong>Rejected:</strong> {doc.rejectionReason}\n                                </div>\n                              )}\n                            </div>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"text-center py-8\">\n                          <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No documents uploaded</h3>\n                          <p className=\"text-gray-600\">User hasn't uploaded any KYC documents yet.</p>\n                        </div>\n                      )}\n                    </CardContent>\n                  </Card>\n                </div>\n              )}\n            </TabsContent>\n\n            {/* Other tabs placeholder */}\n            <TabsContent value=\"investments\">\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <TrendingUp className=\"h-5 w-5\" />\n                    Investment Portfolio\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  {investments.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {investments.map((investment) => (\n                        <div key={investment._id} className=\"border rounded-lg p-4\">\n                          <div className=\"flex justify-between items-start mb-3\">\n                            <div>\n                              <h4 className=\"font-semibold\">{investment.propertyName}</h4>\n                              <p className=\"text-sm text-muted-foreground\">{investment.location}</p>\n                            </div>\n                            <Badge variant={investment.status === 'active' ? 'default' : 'secondary'}>\n                              {investment.status}\n                            </Badge>\n                          </div>\n                          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                            <div>\n                              <p className=\"text-muted-foreground\">Stocks Owned</p>\n                              <p className=\"font-medium\">{investment.stocksOwned}</p>\n                            </div>\n                            <div>\n                              <p className=\"text-muted-foreground\">Total Investment</p>\n                              <p className=\"font-medium\">₹{investment.totalInvestment.toLocaleString()}</p>\n                            </div>\n                            <div>\n                              <p className=\"text-muted-foreground\">Current Value</p>\n                              <p className=\"font-medium\">₹{investment.currentValue.toLocaleString()}</p>\n                            </div>\n                            <div>\n                              <p className=\"text-muted-foreground\">ROI</p>\n                              <p className={`font-medium ${investment.roi >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                                {investment.roi}%\n                              </p>\n                            </div>\n                          </div>\n                          <div className=\"mt-3 pt-3 border-t\">\n                            <div className=\"flex justify-between text-sm\">\n                              <span className=\"text-muted-foreground\">Purchase Date:</span>\n                              <span>{new Date(investment.purchaseDate).toLocaleDateString()}</span>\n                            </div>\n                            <div className=\"flex justify-between text-sm mt-1\">\n                              <span className=\"text-muted-foreground\">Returns Received:</span>\n                              <span className=\"font-medium\">₹{investment.returns.toLocaleString()}</span>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-8 text-muted-foreground\">\n                      <Building className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                      <p>No investments found</p>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"transactions\">\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <CreditCard className=\"h-5 w-5\" />\n                    Transaction History\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  {transactions.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {transactions.map((transaction) => (\n                        <div key={transaction._id} className=\"border rounded-lg p-4\">\n                          <div className=\"flex justify-between items-start mb-3\">\n                            <div>\n                              <h4 className=\"font-semibold capitalize\">{transaction.type}</h4>\n                              <p className=\"text-sm text-muted-foreground\">{transaction.description}</p>\n                              {transaction.propertyName && (\n                                <p className=\"text-sm text-blue-600\">{transaction.propertyName}</p>\n                              )}\n                            </div>\n                            <div className=\"text-right\">\n                              <p className={`font-semibold ${\n                                transaction.type === 'deposit' || transaction.type === 'return'\n                                  ? 'text-green-600'\n                                  : 'text-red-600'\n                              }`}>\n                                {transaction.type === 'deposit' || transaction.type === 'return' ? '+' : '-'}\n                                ₹{transaction.amount.toLocaleString()}\n                              </p>\n                              <Badge variant={\n                                transaction.status === 'completed' ? 'default' :\n                                transaction.status === 'pending' ? 'secondary' :\n                                transaction.status === 'failed' ? 'destructive' : 'outline'\n                              }>\n                                {transaction.status}\n                              </Badge>\n                            </div>\n                          </div>\n                          <div className=\"flex justify-between items-center text-sm text-muted-foreground\">\n                            <span>{new Date(transaction.createdAt).toLocaleDateString()}</span>\n                            {transaction.paymentMethod && (\n                              <span className=\"capitalize\">{transaction.paymentMethod}</span>\n                            )}\n                            {transaction.reference && (\n                              <span className=\"font-mono text-xs\">Ref: {transaction.reference}</span>\n                            )}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-8 text-muted-foreground\">\n                      <CreditCard className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                      <p>No transactions found</p>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"activity\">\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Activity className=\"h-5 w-5\" />\n                    User Activity Log\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  {activities.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {activities.map((activity) => (\n                        <div key={activity._id} className=\"border rounded-lg p-4\">\n                          <div className=\"flex justify-between items-start mb-2\">\n                            <div>\n                              <h4 className=\"font-semibold capitalize\">{activity.action}</h4>\n                              <p className=\"text-sm text-muted-foreground\">{activity.description}</p>\n                            </div>\n                            <Badge variant=\"outline\" className=\"capitalize\">\n                              {activity.entity}\n                            </Badge>\n                          </div>\n                          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground mt-3\">\n                            <div>\n                              <span className=\"font-medium\">Date:</span>\n                              <p>{new Date(activity.createdAt).toLocaleString()}</p>\n                            </div>\n                            {activity.ipAddress && (\n                              <div>\n                                <span className=\"font-medium\">IP Address:</span>\n                                <p className=\"font-mono\">{activity.ipAddress}</p>\n                              </div>\n                            )}\n                            {activity.location && (\n                              <div>\n                                <span className=\"font-medium\">Location:</span>\n                                <p>{activity.location}</p>\n                              </div>\n                            )}\n                          </div>\n                          {activity.metadata && Object.keys(activity.metadata).length > 0 && (\n                            <div className=\"mt-3 pt-3 border-t\">\n                              <span className=\"text-sm font-medium text-muted-foreground\">Additional Details:</span>\n                              <pre className=\"text-xs bg-gray-50 p-2 rounded mt-1 overflow-x-auto\">\n                                {JSON.stringify(activity.metadata, null, 2)}\n                              </pre>\n                            </div>\n                          )}\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-8 text-muted-foreground\">\n                      <Activity className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                      <p>No activity found</p>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n            </TabsContent>\n          </Tabs>\n        </div>\n      </div>\n\n      {/* KYC Action Modal */}\n      <Dialog open={showKYCModal} onOpenChange={setShowKYCModal}>\n        <DialogContent className=\"sm:max-w-md\">\n          <DialogHeader>\n            <DialogTitle>\n              {kycAction === 'approve' ? 'Approve KYC' : 'Reject KYC'}\n            </DialogTitle>\n            <DialogDescription>\n              {kycAction === 'approve'\n                ? 'Are you sure you want to approve this KYC application?'\n                : 'Please provide a reason for rejecting this KYC application.'\n              }\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"space-y-4\">\n            {kycAction === 'reject' && (\n              <div>\n                <Label htmlFor=\"rejectionReason\">Rejection Reason *</Label>\n                <Textarea\n                  id=\"rejectionReason\"\n                  placeholder=\"Enter the reason for rejection...\"\n                  value={rejectionReason}\n                  onChange={(e) => setRejectionReason(e.target.value)}\n                  className=\"mt-1\"\n                />\n              </div>\n            )}\n\n            <div>\n              <Label htmlFor=\"reviewNotes\">Review Notes (Optional)</Label>\n              <Textarea\n                id=\"reviewNotes\"\n                placeholder=\"Add any additional notes...\"\n                value={reviewNotes}\n                onChange={(e) => setReviewNotes(e.target.value)}\n                className=\"mt-1\"\n              />\n            </div>\n          </div>\n\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowKYCModal(false)}\n              disabled={isUpdatingKYC}\n            >\n              Cancel\n            </Button>\n            <Button\n              onClick={handleKYCSubmit}\n              disabled={isUpdatingKYC || (kycAction === 'reject' && !rejectionReason.trim())}\n              className={kycAction === 'approve' ? 'bg-green-600 hover:bg-green-700' : ''}\n              variant={kycAction === 'reject' ? 'destructive' : 'default'}\n            >\n              {isUpdatingKYC ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\" />\n                  Processing...\n                </>\n              ) : (\n                <>\n                  {kycAction === 'approve' ? (\n                    <CheckCircle className=\"h-4 w-4 mr-2\" />\n                  ) : (\n                    <XCircle className=\"h-4 w-4 mr-2\" />\n                  )}\n                  {kycAction === 'approve' ? 'Approve' : 'Reject'}\n                </>\n              )}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAxBA;;;;;;;;;;;;;;;;;;AAoDe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,QAAQ;IAEvB,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,WAAW,EAAE,OAAO,SAAS,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,yBAAsB,AAAD,EAAE;IACtH,MAAM,CAAC,iBAAiB,EAAE,WAAW,aAAa,EAAE,CAAC,GAAG,CAAA,GAAA,kIAAA,CAAA,6BAA0B,AAAD;IACjF,MAAM,CAAC,wBAAwB,EAAE,WAAW,gBAAgB,EAAE,CAAC,GAAG,CAAA,GAAA,kIAAA,CAAA,4BAAyB,AAAD;IAE1F,oBAAoB;IACpB,MAAM,EAAE,MAAM,mBAAmB,EAAE,WAAW,kBAAkB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,6BAA0B,AAAD,EAAE;QAC9F;QACA,MAAM;QACN,OAAO;IACT;IACA,MAAM,EAAE,MAAM,oBAAoB,EAAE,WAAW,mBAAmB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,8BAA2B,AAAD,EAAE;QACjG;QACA,MAAM;QACN,OAAO;IACT;IACA,MAAM,EAAE,MAAM,gBAAgB,EAAE,WAAW,eAAe,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,0BAAuB,AAAD,EAAE;QACrF;QACA,MAAM;QACN,OAAO;IACT;IACA,MAAM,EAAE,MAAM,aAAa,EAAE,WAAW,YAAY,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,uBAAoB,AAAD,EAAE;IAC9E,MAAM,EAAE,MAAM,iBAAiB,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,sCAAmC,AAAD,EAAE;QACnG;QACA,QAAQ;IACV;IAEA,wEAAwE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA6B;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,OAAO,cAAc;IAC3B,MAAM,MAAM,cAAc;IAC1B,MAAM,SAAS,cAAc;IAC7B,MAAM,cAAc,qBAAqB,MAAM,eAAe,EAAE;IAChE,MAAM,eAAe,sBAAsB,MAAM,gBAAgB,EAAE;IACnE,MAAM,aAAa,kBAAkB,MAAM,cAAc,EAAE;IAC3D,MAAM,YAAY,eAAe;IACjC,MAAM,gBAAgB,mBAAmB;IAEzC,iBAAiB;IACjB,MAAM,YAAY;IAElB,iBAAiB;IACjB,IAAI,WAAW;QACb,qBACE,6WAAC,+IAAA,CAAA,UAAe;sBACd,cAAA,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,6WAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE,6WAAC,+IAAA,CAAA,UAAe;sBACd,cAAA,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;;;;;;sCACf,6WAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,iBAAiB;IACjB,IAAI,CAAC,MAAM;QACT,qBACE,6WAAC,+IAAA,CAAA,UAAe;sBACd,cAAA,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6WAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,sBAAsB;IACtB,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,gBAAgB;QAChB,mBAAmB;QACnB,eAAe;IACjB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,KAAK,KAAK;QAEf,IAAI;YACF,MAAM,OAAO;gBACX,QAAQ,cAAc,YAAY,aAAsB;gBACxD,GAAI,cAAc,YAAY,mBAAmB;oBAAE;gBAAgB,CAAC;gBACpE,GAAI,eAAe;oBAAE,OAAO;gBAAY,CAAC;gBACzC,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,wBAAwB;gBAAE,IAAI,IAAI,GAAG;gBAAE;YAAK;YACxD,MAAM,gBAAgB;gBAAE,IAAI,IAAI,GAAG;gBAAE;YAAK,GAAG,MAAM;YAEnD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,cAAc,YAAY,aAAa,WAAW,aAAa,CAAC;YACrF,gBAAgB;YAChB,aAAa;YACb,mBAAmB;YACnB,eAAe;YACf;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,qBAAqB;YACnC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,MAAM,WAAW,CAAC,UAAU,EAAE,UAAU,IAAI,CAAC;QAClE;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,KAAK,KAAK;QAEf,IAAI;YACF,MAAM,OAAO,MAAM,uBAAuB,IAAI,GAAG,EAAE,MAAM;YACzD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,IAAI,EAAE,MAAM,UAAU,CAAC,EAAE,MAAM,SAAS,IAAI,CAAC;YAC3D,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,OAAO,GAAG,CAAC,eAAe,CAAC;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,aAAa;QACf,qBACE,6WAAC,+IAAA,CAAA,UAAe;sBACd,cAAA,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;;;;;;sCACf,6WAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,IAAI,aAAa,CAAC,MAAM;QACtB,qBACE,6WAAC,+IAAA,CAAA,UAAe;sBACd,cAAA,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC,wSAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6WAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,6WAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;8CACjC,6WAAC,oSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAwB,OAAO;YACpC;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAY,qBAAO,6WAAC,+SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC/C,KAAK;gBAAW,qBAAO,6WAAC,wRAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAY,qBAAO,6WAAC,gSAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC3C,KAAK;gBAAgB,qBAAO,6WAAC,8RAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAe,qBAAO,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAClD;gBAAS,qBAAO,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QACzC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAS,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YACtE,KAAK;gBAAY,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACrE,KAAK;gBAAS,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YACtE,KAAK;gBAAQ,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACjE;gBAAS,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QAC9C;IACF;IAIA,qBACE,6WAAC,+IAAA,CAAA,UAAe;;0BACd,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI;gDAC1B,WAAU;;kEAEV,6WAAC,oSAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,6WAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6WAAC,kIAAA,CAAA,cAAW;wDAAC,KAAI;;;;;;kEACjB,6WAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;;4DACvB,KAAK,SAAS,EAAE,OAAO;4DAAI,KAAK,QAAQ,EAAE,OAAO;;;;;;;;;;;;;0DAGtD,6WAAC;;kEACC,6WAAC;wDAAG,WAAU;;4DACX,KAAK,SAAS;4DAAC;4DAAE,KAAK,QAAQ;;;;;;;kEAEjC,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,iIAAA,CAAA,QAAK;gEAAC,WAAW,eAAe,KAAK,MAAM;0EACzC,KAAK,MAAM,EAAE,QAAQ,KAAK,QAAQ;;;;;;4DAEpC,aAAa,KAAK,IAAI;0EACvB,6WAAC,iIAAA,CAAA,QAAK;gEAAC,WAAW,kBAAkB,KAAK,GAAG,EAAE,UAAU;;oEACrD,iBAAiB,KAAK,GAAG,EAAE,UAAU;kFACtC,6WAAC;wEAAK,WAAU;kFAAQ,KAAK,GAAG,EAAE,QAAQ,QAAQ,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMvE,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;gDAClD,WAAU;;kEAEV,6WAAC,+RAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6WAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;;kEAEV,6WAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;sCAQ7C,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,6WAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;;sEACC,6WAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,6WAAC;4DAAE,WAAU;;gEAAmC;gEAC5C,CAAC,WAAW,oBAAoB,CAAC,EAAE,cAAc;;;;;;;;;;;;;8DAGvD,6WAAC,sSAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAK5B,6WAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,6WAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;;sEACC,6WAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,6WAAC;4DAAE,WAAU;;gEAAmC;gEAC5C,CAAC,KAAK,MAAM,EAAE,WAAW,CAAC,EAAE,cAAc;;;;;;;;;;;;;8DAGhD,6WAAC,0RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAKxB,6WAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,6WAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;;sEACC,6WAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,6WAAC;4DAAE,WAAU;;gEAAmC;gEAC5C,CAAC,WAAW,gBAAgB,CAAC,EAAE,cAAc;;;;;;;;;;;;;8DAGnD,6WAAC,wRAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAKvB,6WAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,6WAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;;sEACC,6WAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,6WAAC;4DAAE,WAAU;sEACV,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC,SAAS;gEACpD,OAAO;gEACP,MAAM;4DACR;;;;;;;;;;;;8DAGJ,6WAAC,8RAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5B,6WAAC,gIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAa,eAAe;4BAAgB,WAAU;;8CACjE,6WAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,6WAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAW;;;;;;sDAC9B,6WAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAM;;;;;;sDACzB,6WAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAc;;;;;;sDACjC,6WAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAe;;;;;;sDAClC,6WAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAW;;;;;;;;;;;;8CAIhC,6WAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACtC,cAAA,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,gIAAA,CAAA,OAAI;;kEACH,6WAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6WAAC,sRAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAIhC,6WAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;kEACrB,cAAA,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;;sFACC,6WAAC;4EAAE,WAAU;sFAAwB;;;;;;sFACrC,6WAAC;4EAAE,WAAU;sFAAe,KAAK,SAAS;;;;;;;;;;;;8EAE5C,6WAAC;;sFACC,6WAAC;4EAAE,WAAU;sFAAwB;;;;;;sFACrC,6WAAC;4EAAE,WAAU;sFAAe,KAAK,QAAQ;;;;;;;;;;;;8EAE3C,6WAAC;;sFACC,6WAAC;4EAAE,WAAU;sFAAwB;;;;;;sFACrC,6WAAC;4EAAE,WAAU;;8FACX,6WAAC,sRAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFACf,KAAK,KAAK;gFACV,KAAK,MAAM,KAAK,0BAAY,6WAAC,+SAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;;;;;;;;8EAGxD,6WAAC;;sFACC,6WAAC;4EAAE,WAAU;sFAAwB;;;;;;sFACrC,6WAAC;4EAAE,WAAU;;8FACX,6WAAC,wRAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAChB,KAAK,KAAK,IAAI;gFACd,KAAK,KAAK,kBAAI,6WAAC,+SAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;;;;;;;;8EAG1C,6WAAC;;sFACC,6WAAC;4EAAE,WAAU;sFAAwB;;;;;;sFACrC,6WAAC;4EAAE,WAAU;sFACV;;;;;;;;;;;;8EAGL,6WAAC;;sFACC,6WAAC;4EAAE,WAAU;sFAAwB;;;;;;sFACrC,6WAAC;4EAAE,WAAU;sFAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMpC,6WAAC,gIAAA,CAAA,OAAI;;kEACH,6WAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6WAAC,0RAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAIlC,6WAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;kEACrB,cAAA,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6WAAC;4EAAK,WAAU;;gFAA8B;gFAAE,CAAC,KAAK,MAAM,EAAE,WAAW,CAAC,EAAE,cAAc;;;;;;;;;;;;;8EAE5F,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6WAAC;4EAAK,WAAU;;gFAA+B;gFAAE,CAAC,WAAW,oBAAoB,CAAC,EAAE,cAAc;;;;;;;;;;;;;8EAEpG,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6WAAC;4EAAK,WAAU;;gFAAgC;gFAAE,CAAC,WAAW,gBAAgB,CAAC,EAAE,cAAc;;;;;;;;;;;;;8EAEjG,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6WAAC;4EAAK,WAAU;;gFAAgC;gFAAE,CAAC,WAAW,kBAAkB,CAAC,EAAE,cAAc;;;;;;;;;;;;;8EAEnG,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6WAAC;4EAAK,WAAU;;gFAA8B,CAAC,WAAW,OAAO,CAAC,EAAE,OAAO,CAAC;gFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAOtF,2BACC,6WAAC,gIAAA,CAAA,OAAI;;kEACH,6WAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6WAAC,sSAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAItC,6WAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFAAK,WAAU;0FAAwB;;;;;;0FACxC,6WAAC;gFAAK,WAAU;0FAA+B,UAAU,gBAAgB;;;;;;;;;;;;kFAE3E,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFAAK,WAAU;0FAAwB;;;;;;0FACxC,6WAAC;gFAAK,WAAU;0FAAgC,UAAU,iBAAiB;;;;;;;;;;;;kFAE7E,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFAAK,WAAU;0FAAwB;;;;;;0FACxC,6WAAC;gFAAK,WAAU;;oFAAgC;oFAAE,UAAU,cAAc,CAAC,cAAc;;;;;;;;;;;;;kFAE3F,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFAAK,WAAU;0FAAwB;;;;;;0FACxC,6WAAC;gFAAK,WAAW,CAAC,cAAc,EAAE,UAAU,GAAG,IAAI,IAAI,mBAAmB,gBAAgB;;oFACvF,UAAU,GAAG;oFAAC;;;;;;;;;;;;;;;;;;;0EAIrB,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFAAK,WAAU;0FAAwB;;;;;;0FACxC,6WAAC;gFAAK,WAAU;0FAAiB,IAAI,KAAK,UAAU,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;kFAElF,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFAAK,WAAU;0FAAwB;;;;;;0FACxC,6WAAC;gFAAK,WAAU;0FAAiB,IAAI,KAAK,UAAU,YAAY,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAUlG,6WAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAM,WAAU;8CAChC,0BACC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;;;;;0DACf,6WAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;+CAErC,CAAC,oBACH,6WAAC,gIAAA,CAAA,OAAI;kDACH,cAAA,6WAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6WAAC,kSAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6WAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6WAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;6DAIjC,6WAAC;wCAAI,WAAU;;0DAEb,6WAAC,gIAAA,CAAA,OAAI;;kEACH,6WAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,6WAAC;4DAAI,WAAU;;8EACb,6WAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;;sFACnB,6WAAC,0RAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAY;;;;;;;8EAGhC,6WAAC;oEAAI,WAAU;;sFACb,6WAAC,iIAAA,CAAA,QAAK;4EAAC,WAAW,kBAAkB,IAAI,MAAM;;gFAC3C,iBAAiB,IAAI,MAAM;8FAC5B,6WAAC;oFAAK,WAAU;8FAAQ,IAAI,MAAM,EAAE,QAAQ,KAAK,QAAQ;;;;;;;;;;;;sFAE3D,6WAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;;gFAAW,IAAI,KAAK;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;kEAI1C,6WAAC,gIAAA,CAAA,cAAW;;0EACV,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;;0FACC,6WAAC;gFAAE,WAAU;0FAAwB;;;;;;0FACrC,6WAAC;gFAAE,WAAU;0FACV,IAAI,WAAW,GAAG,IAAI,KAAK,IAAI,WAAW,EAAE,cAAc,KAAK;;;;;;;;;;;;kFAGpE,6WAAC;;0FACC,6WAAC;gFAAE,WAAU;0FAAwB;;;;;;0FACrC,6WAAC;gFAAE,WAAU;0FACV,IAAI,UAAU,GAAG,IAAI,KAAK,IAAI,UAAU,EAAE,cAAc,KAAK;;;;;;;;;;;;kFAGlE,6WAAC;;0FACC,6WAAC;gFAAE,WAAU;0FAAwB;;;;;;0FACrC,6WAAC;gFAAE,WAAU;0FACV,IAAI,UAAU,GAAG,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,UAAU,CAAC,QAAQ,EAAE,GAAG;;;;;;;;;;;;;;;;;;4DAKlF,IAAI,eAAe,kBAClB,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAG,WAAU;kFAAgC;;;;;;kFAC9C,6WAAC;wEAAE,WAAU;kFAAgB,IAAI,eAAe;;;;;;;;;;;;0EAIpD,6WAAC;gEAAI,WAAU;;oEACZ,CAAC,IAAI,MAAM,KAAK,aAAa,IAAI,MAAM,KAAK,cAAc,mBACzD;;0FACE,6WAAC,kIAAA,CAAA,SAAM;gFACL,SAAS,IAAM,gBAAgB;gFAC/B,UAAU;gFACV,WAAU;;kGAEV,6WAAC,+SAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAG1C,6WAAC,kIAAA,CAAA,SAAM;gFACL,SAAS,IAAM,gBAAgB;gFAC/B,UAAU;gFACV,SAAQ;;kGAER,6WAAC,gSAAA,CAAA,UAAO;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;kFAK1C,6WAAC,kIAAA,CAAA,SAAM;wEACL,SAAS;wEACT,SAAQ;wEACR,WAAU;;0FAEV,6WAAC,8RAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0DAQ7C,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,gIAAA,CAAA,OAAI;;0EACH,6WAAC,gIAAA,CAAA,aAAU;0EACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;;sFACnB,6WAAC,sRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAY;;;;;;;;;;;;0EAIhC,6WAAC,gIAAA,CAAA,cAAW;gEAAC,WAAU;0EACrB,cAAA,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,YAAY,CAAC,WAAW,IAAI;;;;;;;;;;;;sFAE9D,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,YAAY,CAAC,YAAY,IAAI;;;;;;;;;;;;sFAE/D,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,YAAY,CAAC,MAAM,IAAI;;;;;;;;;;;;sFAEzD,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,YAAY,CAAC,aAAa,IAAI;;;;;;;;;;;;sFAEhE,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,AAAC,IAAI,YAAY,CAAS,UAAU,IAAI;;;;;;;;;;;;sFAEtE,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,AAAC,IAAI,YAAY,CAAS,YAAY,IAAI;;;;;;;;;;;;sFAExE,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FACV,AAAC,IAAI,YAAY,CAAS,YAAY,GAAG,CAAC,CAAC,EAAE,AAAC,IAAI,YAAY,CAAS,YAAY,CAAC,cAAc,IAAI,GAAG;;;;;;;;;;;;sFAG9G,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,AAAC,IAAI,YAAY,CAAS,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAM/E,6WAAC,gIAAA,CAAA,OAAI;;0EACH,6WAAC,gIAAA,CAAA,aAAU;0EACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;;sFACnB,6WAAC,8RAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAY;;;;;;;;;;;;0EAIlC,6WAAC,gIAAA,CAAA,cAAW;gEAAC,WAAU;0EACrB,cAAA,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,OAAO,CAAC,MAAM,IAAI;;;;;;;;;;;;sFAEpD,6WAAC;4EAAI,WAAU;;8FACb,6WAAC;;sGACC,6WAAC;4FAAE,WAAU;sGAAwB;;;;;;sGACrC,6WAAC;4FAAE,WAAU;sGAAe,IAAI,OAAO,CAAC,IAAI,IAAI;;;;;;;;;;;;8FAElD,6WAAC;;sGACC,6WAAC;4FAAE,WAAU;sGAAwB;;;;;;sGACrC,6WAAC;4FAAE,WAAU;sGAAe,IAAI,OAAO,CAAC,KAAK,IAAI;;;;;;;;;;;;8FAEnD,6WAAC;;sGACC,6WAAC;4FAAE,WAAU;sGAAwB;;;;;;sGACrC,6WAAC;4FAAE,WAAU;sGAAe,IAAI,OAAO,CAAC,UAAU,IAAI;;;;;;;;;;;;8FAExD,6WAAC;;sGACC,6WAAC;4FAAE,WAAU;sGAAwB;;;;;;sGACrC,6WAAC;4FAAE,WAAU;sGAAe,IAAI,OAAO,CAAC,OAAO,IAAI;;;;;;;;;;;;8FAErD,6WAAC;;sGACC,6WAAC;4FAAE,WAAU;sGAAwB;;;;;;sGACrC,6WAAC;4FAAE,WAAU;sGAAe,IAAI,OAAO,CAAC,WAAW,IAAI;;;;;;;;;;;;8FAEzD,6WAAC;;sGACC,6WAAC;4FAAE,WAAU;sGAAwB;;;;;;sGACrC,6WAAC;4FAAE,WAAU;sGACV,IAAI,OAAO,CAAC,cAAc,GAAG,IAAI,KAAK,IAAI,OAAO,CAAC,cAAc,EAAE,kBAAkB,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAUxG,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,gIAAA,CAAA,OAAI;;0EACH,6WAAC,gIAAA,CAAA,aAAU;0EACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;;sFACnB,6WAAC,sSAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEAAY;;;;;;;;;;;;0EAItC,6WAAC,gIAAA,CAAA,cAAW;gEAAC,WAAU;0EACrB,cAAA,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,YAAY,EAAE,gBAAgB;;;;;;;;;;;;sFAEhE,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,YAAY,EAAE,aAAa;;;;;;;;;;;;sFAE7D,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,YAAY,EAAE,kBAAkB;;;;;;;;;;;;sFAElE,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,YAAY,EAAE,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAM9E,6WAAC,gIAAA,CAAA,OAAI;;0EACH,6WAAC,gIAAA,CAAA,aAAU;0EACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;;sFACnB,6WAAC,8RAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAY;;;;;;;;;;;;0EAIpC,6WAAC,gIAAA,CAAA,cAAW;gEAAC,WAAU;0EACrB,cAAA,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,QAAQ,EAAE,iBAAiB;;;;;;;;;;;;sFAE7D,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,QAAQ,EAAE,YAAY;;;;;;;;;;;;sFAExD,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,QAAQ,EAAE,YAAY;;;;;;;;;;;;sFAExD,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,QAAQ,EAAE,eAAe;;;;;;;;;;;;sFAE3D,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,IAAI,QAAQ,EAAE,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQzE,6WAAC,gIAAA,CAAA,OAAI;;kEACH,6WAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6WAAC,kSAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAIpC,6WAAC,gIAAA,CAAA,cAAW;kEACT,IAAI,SAAS,IAAI,IAAI,SAAS,CAAC,MAAM,GAAG,kBACvC,6WAAC;4DAAI,WAAU;sEACZ,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,oBAClB,6WAAC;oEAAkB,WAAU;;sFAC3B,6WAAC;4EAAI,WAAU;;8FACb,6WAAC;;sGACC,6WAAC;4FAAG,WAAU;sGAA6B,IAAI,IAAI;;;;;;sGACnD,6WAAC;4FAAE,WAAU;sGAAyB,IAAI,OAAO;;;;;;;;;;;;8FAEnD,6WAAC,iIAAA,CAAA,QAAK;oFAAC,WACL,IAAI,MAAM,KAAK,aAAa,gCAC5B,IAAI,MAAM,KAAK,aAAa,4BAC5B;8FAEC,IAAI,MAAM;;;;;;;;;;;;wEAId,IAAI,cAAc,kBACjB,6WAAC;4EAAE,WAAU;;8FACX,6WAAC;8FAAO;;;;;;gFAAgB;gFAAE,IAAI,cAAc;;;;;;;sFAIhD,6WAAC;4EAAI,WAAU;;8FACb,6WAAC,kIAAA,CAAA,SAAM;oFAAC,MAAK;oFAAK,SAAQ;;sGACxB,6WAAC,oRAAA,CAAA,MAAG;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;8FAGlC,6WAAC,kIAAA,CAAA,SAAM;oFAAC,MAAK;oFAAK,SAAQ;;sGACxB,6WAAC,8RAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;wEAKxC,IAAI,eAAe,kBAClB,6WAAC;4EAAI,WAAU;;8FACb,6WAAC;8FAAO;;;;;;gFAAkB;gFAAE,IAAI,eAAe;;;;;;;;mEAlC3C,IAAI,GAAG;;;;;;;;;iFAyCrB,6WAAC;4DAAI,WAAU;;8EACb,6WAAC,kSAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6WAAC;oEAAG,WAAU;8EAAyC;;;;;;8EACvD,6WAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAU3C,6WAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CACjB,cAAA,6WAAC,gIAAA,CAAA,OAAI;;0DACH,6WAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6WAAC,sSAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAItC,6WAAC,gIAAA,CAAA,cAAW;0DACT,YAAY,MAAM,GAAG,kBACpB,6WAAC;oDAAI,WAAU;8DACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,6WAAC;4DAAyB,WAAU;;8EAClC,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;;8FACC,6WAAC;oFAAG,WAAU;8FAAiB,WAAW,YAAY;;;;;;8FACtD,6WAAC;oFAAE,WAAU;8FAAiC,WAAW,QAAQ;;;;;;;;;;;;sFAEnE,6WAAC,iIAAA,CAAA,QAAK;4EAAC,SAAS,WAAW,MAAM,KAAK,WAAW,YAAY;sFAC1D,WAAW,MAAM;;;;;;;;;;;;8EAGtB,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;8FAAe,WAAW,WAAW;;;;;;;;;;;;sFAEpD,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;;wFAAc;wFAAE,WAAW,eAAe,CAAC,cAAc;;;;;;;;;;;;;sFAExE,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAU;;wFAAc;wFAAE,WAAW,YAAY,CAAC,cAAc;;;;;;;;;;;;;sFAErE,6WAAC;;8FACC,6WAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6WAAC;oFAAE,WAAW,CAAC,YAAY,EAAE,WAAW,GAAG,IAAI,IAAI,mBAAmB,gBAAgB;;wFACnF,WAAW,GAAG;wFAAC;;;;;;;;;;;;;;;;;;;8EAItB,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAI,WAAU;;8FACb,6WAAC;oFAAK,WAAU;8FAAwB;;;;;;8FACxC,6WAAC;8FAAM,IAAI,KAAK,WAAW,YAAY,EAAE,kBAAkB;;;;;;;;;;;;sFAE7D,6WAAC;4EAAI,WAAU;;8FACb,6WAAC;oFAAK,WAAU;8FAAwB;;;;;;8FACxC,6WAAC;oFAAK,WAAU;;wFAAc;wFAAE,WAAW,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;2DArC7D,WAAW,GAAG;;;;;;;;;yEA4C5B,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,8RAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6WAAC;sEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOb,6WAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CACjB,cAAA,6WAAC,gIAAA,CAAA,OAAI;;0DACH,6WAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6WAAC,sSAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAItC,6WAAC,gIAAA,CAAA,cAAW;0DACT,aAAa,MAAM,GAAG,kBACrB,6WAAC;oDAAI,WAAU;8DACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,6WAAC;4DAA0B,WAAU;;8EACnC,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;;8FACC,6WAAC;oFAAG,WAAU;8FAA4B,YAAY,IAAI;;;;;;8FAC1D,6WAAC;oFAAE,WAAU;8FAAiC,YAAY,WAAW;;;;;;gFACpE,YAAY,YAAY,kBACvB,6WAAC;oFAAE,WAAU;8FAAyB,YAAY,YAAY;;;;;;;;;;;;sFAGlE,6WAAC;4EAAI,WAAU;;8FACb,6WAAC;oFAAE,WAAW,CAAC,cAAc,EAC3B,YAAY,IAAI,KAAK,aAAa,YAAY,IAAI,KAAK,WACnD,mBACA,gBACJ;;wFACC,YAAY,IAAI,KAAK,aAAa,YAAY,IAAI,KAAK,WAAW,MAAM;wFAAI;wFAC3E,YAAY,MAAM,CAAC,cAAc;;;;;;;8FAErC,6WAAC,iIAAA,CAAA,QAAK;oFAAC,SACL,YAAY,MAAM,KAAK,cAAc,YACrC,YAAY,MAAM,KAAK,YAAY,cACnC,YAAY,MAAM,KAAK,WAAW,gBAAgB;8FAEjD,YAAY,MAAM;;;;;;;;;;;;;;;;;;8EAIzB,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;sFAAM,IAAI,KAAK,YAAY,SAAS,EAAE,kBAAkB;;;;;;wEACxD,YAAY,aAAa,kBACxB,6WAAC;4EAAK,WAAU;sFAAc,YAAY,aAAa;;;;;;wEAExD,YAAY,SAAS,kBACpB,6WAAC;4EAAK,WAAU;;gFAAoB;gFAAM,YAAY,SAAS;;;;;;;;;;;;;;2DAjC3D,YAAY,GAAG;;;;;;;;;yEAwC7B,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,sSAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6WAAC;sEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOb,6WAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CACjB,cAAA,6WAAC,gIAAA,CAAA,OAAI;;0DACH,6WAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6WAAC,8RAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIpC,6WAAC,gIAAA,CAAA,cAAW;0DACT,WAAW,MAAM,GAAG,kBACnB,6WAAC;oDAAI,WAAU;8DACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6WAAC;4DAAuB,WAAU;;8EAChC,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;;8FACC,6WAAC;oFAAG,WAAU;8FAA4B,SAAS,MAAM;;;;;;8FACzD,6WAAC;oFAAE,WAAU;8FAAiC,SAAS,WAAW;;;;;;;;;;;;sFAEpE,6WAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAChC,SAAS,MAAM;;;;;;;;;;;;8EAGpB,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;;8FACC,6WAAC;oFAAK,WAAU;8FAAc;;;;;;8FAC9B,6WAAC;8FAAG,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc;;;;;;;;;;;;wEAEhD,SAAS,SAAS,kBACjB,6WAAC;;8FACC,6WAAC;oFAAK,WAAU;8FAAc;;;;;;8FAC9B,6WAAC;oFAAE,WAAU;8FAAa,SAAS,SAAS;;;;;;;;;;;;wEAG/C,SAAS,QAAQ,kBAChB,6WAAC;;8FACC,6WAAC;oFAAK,WAAU;8FAAc;;;;;;8FAC9B,6WAAC;8FAAG,SAAS,QAAQ;;;;;;;;;;;;;;;;;;gEAI1B,SAAS,QAAQ,IAAI,OAAO,IAAI,CAAC,SAAS,QAAQ,EAAE,MAAM,GAAG,mBAC5D,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAK,WAAU;sFAA4C;;;;;;sFAC5D,6WAAC;4EAAI,WAAU;sFACZ,KAAK,SAAS,CAAC,SAAS,QAAQ,EAAE,MAAM;;;;;;;;;;;;;2DAhCvC,SAAS,GAAG;;;;;;;;;yEAwC1B,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,8RAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6WAAC;sEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWnB,6WAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAc,cAAc;0BACxC,cAAA,6WAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6WAAC,kIAAA,CAAA,eAAY;;8CACX,6WAAC,kIAAA,CAAA,cAAW;8CACT,cAAc,YAAY,gBAAgB;;;;;;8CAE7C,6WAAC,kIAAA,CAAA,oBAAiB;8CACf,cAAc,YACX,2DACA;;;;;;;;;;;;sCAKR,6WAAC;4BAAI,WAAU;;gCACZ,cAAc,0BACb,6WAAC;;sDACC,6WAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAkB;;;;;;sDACjC,6WAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CAClD,WAAU;;;;;;;;;;;;8CAKhB,6WAAC;;sDACC,6WAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,6WAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,6WAAC,kIAAA,CAAA,eAAY;;8CACX,6WAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,gBAAgB;oCAC/B,UAAU;8CACX;;;;;;8CAGD,6WAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,iBAAkB,cAAc,YAAY,CAAC,gBAAgB,IAAI;oCAC3E,WAAW,cAAc,YAAY,oCAAoC;oCACzE,SAAS,cAAc,WAAW,gBAAgB;8CAEjD,8BACC;;0DACE,6WAAC;gDAAI,WAAU;;;;;;4CAAmE;;qEAIpF;;4CACG,cAAc,0BACb,6WAAC,+SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,6WAAC,gSAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAEpB,cAAc,YAAY,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD", "debugId": null}}]}