(()=>{var e={};e.id=22,e.ids=[22],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14065:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>d.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=a(10557),t=a(68490),l=a(13172),d=a.n(l),i=a(68835),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);a.d(s,n);let o={children:["",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,39384)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\reports\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\reports\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/reports/page",pathname:"/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32554:(e,s,a)=>{Promise.resolve().then(a.bind(a,51193))},33873:e=>{"use strict";e.exports=require("path")},39384:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\reports\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\reports\\page.tsx","default")},51193:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>B});var r=a(40969),t=a(73356),l=a(88251),d=a(66949),i=a(46411),n=a(76650),o=a(2223),c=a(14493),m=a(77060),x=a(51603),u=a(28501),h=a(83427),p=a(79123),g=a(91798),y=a(35342),v=a(98085);let j=(0,v.A)("chart-line",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]]);var b=a(66282);let N=(0,v.A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);var f=a(21764);let{useGetPerformanceMetricsQuery:w,useGetLeadAnalyticsQuery:T,useGetCommissionAnalyticsQuery:q,useGetCustomerAnalyticsQuery:R,useGetSalesFunnelAnalysisQuery:A,useGetRevenueAnalysisQuery:k,useGetActivityReportQuery:C,useGetComparativeAnalysisQuery:L,useGenerateCustomReportMutation:P,useGetSalesReportsQuery:D,useGetReportByIdQuery:_,useDownloadReportMutation:$,useDeleteReportMutation:S,useGetDashboardAnalyticsQuery:E}=a(53412).q.injectEndpoints({endpoints:e=>({getPerformanceMetrics:e.query({query:e=>({url:"/sales/reports/performance",params:e}),providesTags:["Report"]}),getLeadAnalytics:e.query({query:e=>({url:"/sales/reports/leads",params:e}),providesTags:["Report","Lead"]}),getCommissionAnalytics:e.query({query:e=>({url:"/sales/reports/commission",params:e}),providesTags:["Report","Commission"]}),getCustomerAnalytics:e.query({query:e=>({url:"/sales/reports/customers",params:e}),providesTags:["Report","Customer"]}),getSalesFunnelAnalysis:e.query({query:e=>({url:"/sales/reports/funnel-analysis",params:e}),providesTags:["Report"]}),getRevenueAnalysis:e.query({query:e=>({url:"/sales/reports/revenue",params:e}),providesTags:["Report"]}),getActivityReport:e.query({query:e=>({url:"/sales/reports/activities",params:e}),providesTags:["Report"]}),getComparativeAnalysis:e.query({query:e=>({url:"/sales/reports/comparative",params:e}),providesTags:["Report"]}),generateCustomReport:e.mutation({query:e=>({url:"/sales/reports/generate",method:"POST",body:e}),invalidatesTags:["Report"]}),getSalesReports:e.query({query:e=>({url:"/sales/reports",params:e}),providesTags:["Report"]}),getReportById:e.query({query:e=>`/sales/reports/${e}`,providesTags:(e,s,a)=>[{type:"Report",id:a}]}),downloadReport:e.mutation({query:e=>({url:`/sales/reports/${e}/download`,method:"GET",responseHandler:e=>e.blob()})}),deleteReport:e.mutation({query:e=>({url:`/sales/reports/${e}`,method:"DELETE"}),invalidatesTags:["Report"]}),getDashboardAnalytics:e.query({query:()=>"/sales/reports/dashboard",providesTags:["Report","Dashboard"]})})});var Z=a(99206);function B(){let[e,s]=(0,t.useState)("monthly"),[a,v]=(0,t.useState)("last_6_months"),{data:w,isLoading:q,error:R}=E(),{data:A,isLoading:C}=k({period:e,salesRepId:void 0}),{data:L,isLoading:P}=T({period:e,salesRepId:void 0}),{data:D,isLoading:_}=(0,Z.WD)({page:1,limit:10});if(q||C||P||_)return(0,r.jsx)(l.A,{title:"Reports & Analytics",children:(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)(c.A,{className:"w-8 h-8 animate-spin text-sky-500"})})})});if(R)return(0,r.jsx)(l.A,{title:"Reports & Analytics",children:(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(m.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Failed to load reports"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Please try refreshing the page"}),(0,r.jsx)(i.$,{className:"bg-sky-500 hover:bg-sky-600",children:"Try Again"})]})})})});let $=w?.data,S=A?.data,B=L?.data,M=D?.data?.targets;return(0,r.jsx)(l.A,{title:"Reports & Analytics",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Reports & Analytics"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Comprehensive sales performance and analytics dashboard"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(o.l6,{value:e,onValueChange:s,children:[(0,r.jsx)(o.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,r.jsx)(o.yv,{})}),(0,r.jsxs)(o.gC,{children:[(0,r.jsx)(o.eb,{value:"daily",children:"Daily"}),(0,r.jsx)(o.eb,{value:"weekly",children:"Weekly"}),(0,r.jsx)(o.eb,{value:"monthly",children:"Monthly"}),(0,r.jsx)(o.eb,{value:"quarterly",children:"Quarterly"}),(0,r.jsx)(o.eb,{value:"yearly",children:"Yearly"})]})]}),(0,r.jsxs)(o.l6,{value:a,onValueChange:v,children:[(0,r.jsx)(o.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,r.jsx)(o.yv,{})}),(0,r.jsxs)(o.gC,{children:[(0,r.jsx)(o.eb,{value:"last_7_days",children:"Last 7 Days"}),(0,r.jsx)(o.eb,{value:"last_30_days",children:"Last 30 Days"}),(0,r.jsx)(o.eb,{value:"last_3_months",children:"Last 3 Months"}),(0,r.jsx)(o.eb,{value:"last_6_months",children:"Last 6 Months"}),(0,r.jsx)(o.eb,{value:"last_year",children:"Last Year"})]})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"border-green-300 text-green-600 hover:bg-green-50",children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Export"]}),(0,r.jsxs)(i.$,{className:"bg-sky-500 hover:bg-sky-600 text-white",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Generate Report"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-sky-100 rounded-lg",children:(0,r.jsx)(h.A,{className:"w-6 h-6 text-sky-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,f.vv)($?.overview?.totalRevenue||S?.totalRevenue||0)}),(0,r.jsxs)("p",{className:"text-xs text-green-600 mt-1",children:["+",$?.overview?.revenueGrowth||S?.revenueGrowth||0,"% from last period"]})]})]})})}),(0,r.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,r.jsx)(p.A,{className:"w-6 h-6 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Leads"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,f.ZV)($?.overview?.totalLeads||B?.totalLeads||0)}),(0,r.jsxs)("p",{className:"text-xs text-green-600 mt-1",children:["+",$?.overview?.leadsGrowth||B?.leadsGrowth||0,"% from last period"]})]})]})})}),(0,r.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,r.jsx)(g.A,{className:"w-6 h-6 text-yellow-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Conversion Rate"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[($?.overview?.conversionRate||B?.conversionRate||0).toFixed(1),"%"]}),(0,r.jsxs)("p",{className:"text-xs text-green-600 mt-1",children:["Qualified: ",(0,f.ZV)(B?.qualifiedLeads||0)]})]})]})})}),(0,r.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,r.jsx)(y.A,{className:"w-6 h-6 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg Deal Size"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,f.vv)($?.overview?.averageDealSize||0)}),(0,r.jsxs)("p",{className:"text-xs text-sky-600 mt-1",children:["Target Achievement: ",M?.currentTargets?.[0]?.percentage||0,"%"]})]})]})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,r.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)(d.ZB,{className:"flex items-center text-gray-900",children:[(0,r.jsx)(j,{className:"w-5 h-5 mr-2 text-sky-500"}),"Revenue Trend"]})}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"h-64 flex items-center justify-center bg-gradient-to-br from-sky-50 to-sky-100 rounded-lg",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(b.A,{className:"w-16 h-16 text-sky-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Revenue chart visualization"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:[S?.revenueByMonth?.length||0," data points available"]})]})})})]}),(0,r.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,r.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)(d.ZB,{className:"flex items-center text-gray-900",children:[(0,r.jsx)(b.A,{className:"w-5 h-5 mr-2 text-green-500"}),"Leads Analysis"]})}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"h-64 flex items-center justify-center bg-gradient-to-br from-green-50 to-green-100 rounded-lg",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(p.A,{className:"w-16 h-16 text-green-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Leads conversion analysis"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:[B?.leadsByMonth?.length||0," monthly records"]})]})})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,r.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)(d.ZB,{className:"flex items-center text-gray-900",children:[(0,r.jsx)(N,{className:"w-5 h-5 mr-2 text-yellow-500"}),"Lead Sources"]})}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"space-y-3",children:B?.leadsBySource?.slice(0,5).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:`w-3 h-3 rounded-full mr-3 ${0===s?"bg-sky-500":1===s?"bg-green-500":2===s?"bg-yellow-500":3===s?"bg-purple-500":"bg-gray-500"}`}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:e.source})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.count}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[e.percentage,"%"]})]})]},e.source))||(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(N,{className:"w-12 h-12 text-gray-300 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No source data available"})]})})})]}),(0,r.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,r.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)(d.ZB,{className:"flex items-center text-gray-900",children:[(0,r.jsx)(b.A,{className:"w-5 h-5 mr-2 text-purple-500"}),"Revenue by Type"]})}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"space-y-3",children:S?.revenueByProperty?.slice(0,4).map((e,s)=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-700",children:e.propertyType}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:(0,f.vv)(e.revenue)})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:`h-2 rounded-full ${0===s?"bg-sky-500":1===s?"bg-green-500":2===s?"bg-yellow-500":"bg-purple-500"}`,style:{width:`${e.percentage}%`}})})]},e.propertyType))||(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(b.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No property data available"})]})})})]}),(0,r.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,r.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)(d.ZB,{className:"flex items-center text-gray-900",children:[(0,r.jsx)(y.A,{className:"w-5 h-5 mr-2 text-green-500"}),"Top Performers"]})}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"space-y-3",children:$?.topPerformers?.slice(0,5).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold mr-3 ${0===s?"bg-yellow-500":1===s?"bg-gray-400":2===s?"bg-yellow-600":"bg-gray-300"}`,children:s+1}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[e.deals," deals"]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(0,f.vv)(e.revenue)}),(0,r.jsxs)("div",{className:"text-xs text-green-600",children:[e.conversionRate,"% rate"]})]})]},e.salesRepId))||(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(y.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No performance data available"})]})})})]})]}),M?.currentTargets&&M.currentTargets.length>0&&(0,r.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,r.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)(d.ZB,{className:"flex items-center text-gray-900",children:[(0,r.jsx)(y.A,{className:"w-5 h-5 mr-2 text-sky-500"}),"Target Achievement"]})}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:M.currentTargets.map(e=>(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,r.jsx)(n.E,{className:`text-xs ${"exceeded"===e.status?"bg-green-100 text-green-800":"on_track"===e.status?"bg-sky-100 text-sky-800":"bg-red-100 text-red-800"}`,children:e.status.replace("_"," ").toUpperCase()})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Progress"}),(0,r.jsxs)("span",{className:"font-medium",children:[e.percentage,"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${e.percentage>=100?"bg-green-500":e.percentage>=75?"bg-sky-500":e.percentage>=50?"bg-yellow-500":"bg-red-500"}`,style:{width:`${Math.min(e.percentage,100)}%`}})}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,r.jsx)("span",{children:(0,f.vv)(e.achievedAmount)}),(0,r.jsx)("span",{children:(0,f.vv)(e.targetAmount)})]})]})]},e.id))})})]})]})})}},51603:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(98085).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77060:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(98085).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},79551:e=>{"use strict";e.exports=require("url")},98050:(e,s,a)=>{Promise.resolve().then(a.bind(a,39384))},99206:(e,s,a)=>{"use strict";a.d(s,{$V:()=>i,FO:()=>o,Hu:()=>l,OT:()=>R,Pb:()=>t,WD:()=>v,Zu:()=>b,aT:()=>y,cm:()=>j,nK:()=>u,pv:()=>q,ro:()=>N});let r=a(53412).q.injectEndpoints({endpoints:e=>({getDashboardStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getDashboardActivities:e.query({query:e=>({url:"/dashboard/activities",params:e}),providesTags:["Dashboard"]}),getSalesStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getLeads:e.query({query:e=>({url:"/sales/leads",params:e}),providesTags:["Lead"]}),getLeadById:e.query({query:e=>`/sales/leads/${e}`,providesTags:(e,s,a)=>[{type:"Lead",id:a}]}),createLead:e.mutation({query:e=>({url:"/sales/leads",method:"POST",body:e}),invalidatesTags:["Lead","Dashboard"]}),updateLead:e.mutation({query:({id:e,data:s})=>({url:`/sales/leads/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:a})=>[{type:"Lead",id:a},"Lead","Dashboard"]}),deleteLead:e.mutation({query:e=>({url:`/sales/leads/${e}`,method:"DELETE"}),invalidatesTags:["Lead","Dashboard"]}),assignLead:e.mutation({query:({leadId:e,salesRepId:s})=>({url:`/sales/leads/${e}/assign`,method:"POST",body:{salesRepId:s}}),invalidatesTags:["Lead"]}),getCustomers:e.query({query:e=>({url:"/sales/customers",params:e}),providesTags:["Customer"]}),getCustomerById:e.query({query:e=>`/sales/customers/${e}`,providesTags:(e,s,a)=>[{type:"Customer",id:a}]}),createCustomer:e.mutation({query:e=>({url:"/sales/customers",method:"POST",body:e}),invalidatesTags:["Customer","Dashboard"]}),updateCustomer:e.mutation({query:({id:e,data:s})=>({url:`/sales/customers/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:a})=>[{type:"Customer",id:a},"Customer"]}),getCommissions:e.query({query:e=>({url:"/sales/commissions",params:e}),providesTags:["Commission"]}),getSalesTargets:e.query({query:()=>"/sales/targets",providesTags:["Report"]}),createSalesTarget:e.mutation({query:e=>({url:"/sales/targets",method:"POST",body:e}),invalidatesTags:["Report","Dashboard"]}),updateSalesTarget:e.mutation({query:({id:e,data:s})=>({url:`/sales/targets/${e}`,method:"PUT",body:s}),invalidatesTags:["Report","Dashboard"]}),getFollowUps:e.query({query:e=>({url:"/follow-ups",params:e}),providesTags:["Task"]}),createFollowUp:e.mutation({query:e=>({url:"/follow-ups",method:"POST",body:e}),invalidatesTags:["Task","Dashboard"]}),updateFollowUp:e.mutation({query:({id:e,data:s})=>({url:`/follow-ups/${e}`,method:"PUT",body:s}),invalidatesTags:["Task","Dashboard"]}),deleteFollowUp:e.mutation({query:e=>({url:`/follow-ups/${e}`,method:"DELETE"}),invalidatesTags:["Task","Dashboard"]})})}),{useGetDashboardStatsQuery:t,useGetDashboardActivitiesQuery:l,useGetSalesStatsQuery:d,useGetLeadsQuery:i,useGetLeadByIdQuery:n,useCreateLeadMutation:o,useUpdateLeadMutation:c,useDeleteLeadMutation:m,useAssignLeadMutation:x,useGetCustomersQuery:u,useGetCustomerByIdQuery:h,useCreateCustomerMutation:p,useUpdateCustomerMutation:g,useGetCommissionsQuery:y,useGetSalesTargetsQuery:v,useCreateSalesTargetMutation:j,useUpdateSalesTargetMutation:b,useGetFollowUpsQuery:N,useCreateFollowUpMutation:f,useUpdateFollowUpMutation:w,useDeleteFollowUpMutation:T}=r,{useGetCustomersQuery:q,useGetCommissionsQuery:R}=r}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[755,598,544,29,796,286,447,512],()=>a(14065));module.exports=r})();