(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520,7627],{1128:(e,t,a)=>{"use strict";a.d(t,{B3:()=>n,Lv:()=>u,Ng:()=>i,_L:()=>r,ac:()=>g,ge:()=>s,nd:()=>d,pv:()=>x,tl:()=>c,uU:()=>o});let{useLoginMutation:r,useRegisterMutation:s,useLogoutMutation:i,useRefreshTokenMutation:l,useVerifyEmailMutation:o,useResendVerificationEmailMutation:n,useForgotPasswordMutation:d,useVerifyPasswordResetOTPMutation:c,useResetPasswordMutation:u,useChangePasswordMutation:m,useVerifyPhoneMutation:h,useSendPhoneOTPMutation:f,useSendEmailOTPMutation:g,useVerifyEmailOTPMutation:x,useGetCurrentUserQuery:p,useGetUserProfileQuery:y,useCheckAuthStatusQuery:v}=a(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({login:e.mutation({query:e=>({url:"/auth/login",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),register:e.mutation({query:e=>({url:"/auth/register",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),logout:e.mutation({query:()=>({url:"/auth/logout",method:"POST"}),invalidatesTags:["Auth","User"]}),refreshToken:e.mutation({query:e=>({url:"/auth/refresh",method:"POST",body:e})}),verifyEmail:e.mutation({query:e=>({url:"/auth/verify-email",method:"POST",body:e}),invalidatesTags:["User"]}),resendVerificationEmail:e.mutation({query:e=>({url:"/auth/send-verification",method:"POST",body:e})}),forgotPassword:e.mutation({query:e=>({url:"/auth/forgot-password",method:"POST",body:e})}),verifyPasswordResetOTP:e.mutation({query:e=>({url:"/auth/verify-reset-otp",method:"POST",body:e})}),resetPassword:e.mutation({query:e=>({url:"/auth/reset-password",method:"POST",body:e})}),changePassword:e.mutation({query:e=>({url:"/auth/change-password",method:"POST",body:e}),invalidatesTags:["User"]}),verifyPhone:e.mutation({query:e=>({url:"/auth/verify-phone",method:"POST",body:e}),invalidatesTags:["User"]}),sendPhoneOTP:e.mutation({query:e=>({url:"/auth/send-phone-otp",method:"POST",body:e})}),sendEmailOTP:e.mutation({query:e=>({url:"/auth/send-otp",method:"POST",body:e})}),verifyEmailOTP:e.mutation({query:e=>({url:"/auth/verify-otp",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),getCurrentUser:e.query({query:()=>"/auth/me",providesTags:["User"]}),checkAuthStatus:e.query({query:()=>"/auth/status",providesTags:["Auth"]}),getUserProfile:e.query({query:()=>"/auth/profile",providesTags:["User","Auth"]})})})},1158:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(5050).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},1470:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(5050).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2855:(e,t,a)=>{"use strict";a.d(t,{SettingsProvider:()=>o,YK:()=>d});var r=a(9605),s=a(9585);let{useGetPublicSettingsQuery:i}=a(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getPublicSettings:e.query({query:()=>"/settings/public",providesTags:["Settings"]})})}),l=(0,s.createContext)(void 0),o=e=>{let{children:t}=e,[a,o]=(0,s.useState)({}),{data:n,isLoading:d,error:c,refetch:u}=i(void 0,{skip:!1,refetchOnMountOrArgChange:!0,refetchOnFocus:!1,refetchOnReconnect:!0});return(0,s.useEffect)(()=>{var e;(null==n?void 0:n.success)&&(null==(e=n.data)?void 0:e.settings)&&o(e=>{var t;return{...e,...null==(t=n.data)?void 0:t.settings}})},[n]),(0,s.useEffect)(()=>{c&&console.warn("Failed to load settings from server, using defaults:",c)},[c]),(0,s.useEffect)(()=>{let e=setInterval(()=>{u()},3e5);return()=>clearInterval(e)},[u]),(0,r.jsx)(l.Provider,{value:{settings:a,isLoading:d,error:c,isEmailVerificationEnabled:()=>{var e;return null==(e=a.EMAIL_VERIFICATION_ENABLED)||e},isPhoneVerificationEnabled:()=>{var e;return null!=(e=a.PHONE_VERIFICATION_ENABLED)&&e},isKYCRequired:()=>{var e;return null==(e=a.KYC_REQUIRED_FOR_ACTIVATION)||e},isKYCPriorityOverEmail:()=>{var e;return null==(e=a.KYC_PRIORITY_OVER_EMAIL)||e},allowKYCForInactiveUsers:()=>{var e;return null==(e=a.ALLOW_KYC_FOR_INACTIVE_USERS)||e},getMinInvestmentAmount:()=>{var e;return null!=(e=a.MIN_INVESTMENT_AMOUNT)?e:1e3},getMaxInvestmentAmount:()=>{var e;return null!=(e=a.MAX_INVESTMENT_AMOUNT)?e:1e6},isFeatureEnabled:e=>{var t;return null==(t=a["FEATURE_".concat(e.toUpperCase(),"_ENABLED")])||t}},children:t})},n=()=>{let e=(0,s.useContext)(l);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e},d=()=>{let{isEmailVerificationEnabled:e,isKYCRequired:t,isKYCPriorityOverEmail:a,allowKYCForInactiveUsers:r}=n();return{emailVerificationEnabled:e(),kycRequired:t(),kycPriorityOverEmail:a(),allowKYCForInactive:r(),shouldShowEmailFirst:()=>e()&&!a(),shouldShowKYCFirst:()=>t()&&a(),getNextStepForUser:r=>{if(!r)return null;let s=t()&&["not_started","not_submitted","incomplete","rejected"].includes(r.kycStatus||"not_started"),i=e()&&!r.emailVerified;return"pending"===r.kycStatus||"under_review"===r.kycStatus?i?"email_verification":null:s&&a()?"kyc":i&&!a()?"email_verification":s?"kyc":i?"email_verification":null}}}},4454:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(5050).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4578:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>E});var r=a(9605),s=a(9585),i=a(5935),l=a(6762),o=a.n(l),n=a(2407),d=a(7730),c=a(5706),u=a(1158),m=a(4454),h=a(7530),f=a(1470),g=a(6793),x=a(8120),p=a(2933),y=a(8063),v=a(1128),b=a(9311),j=a(3815),w=a(2855),N=a(6845);let T=c.Ik({email:c.Yj().email("Please enter a valid email address"),password:c.Yj().min(6,"Password must be at least 6 characters"),rememberMe:c.zM().optional(),referralCode:c.Yj().optional()});function k(){let e=(0,i.useRouter)(),t=(0,i.useSearchParams)(),a=(0,b.jL)(),l=(0,b.GV)(j.Kc),[c,k]=(0,s.useState)(!1),[E,{isLoading:S}]=(0,v._L)(),{getNextStepForUser:C}=(0,w.YK)(),{register:A,handleSubmit:P,formState:{errors:M},setError:O,setValue:I}=(0,n.mN)({resolver:(0,d.u)(T),defaultValues:{email:"",password:"",rememberMe:!1,referralCode:""}});(0,s.useEffect)(()=>{let a=(()=>{let e=document.cookie,t=e.includes("accessToken="),a=e.includes("token=");return console.log("\uD83C\uDF6A User Login Cookie check:",{allCookies:e,hasAccessToken:t,hasToken:a,cookieLength:e.length,isAuthenticated:l}),t||a})();if(l||a){var r;console.log("\uD83D\uDD04 User: Token found, redirecting from login page");let a=t.get("redirect")||(null==(r=document.cookie.split("; ").find(e=>e.startsWith("redirectTo=")))?void 0:r.split("=")[1])||localStorage.getItem("redirectTo")||"/dashboard";document.cookie="redirectTo=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;",localStorage.removeItem("redirectTo"),setTimeout(()=>{e.replace(decodeURIComponent(a))},100)}},[]),(0,s.useEffect)(()=>{let e=t.get("referral")||t.get("ref");e&&(I("referralCode",e),N.toast.info('Referral code "'.concat(e,'" has been applied')));let a=localStorage.getItem("loginCredentials");if(a)try{let e=JSON.parse(a);I("email",e.email),e.referralCode&&I("referralCode",e.referralCode),localStorage.removeItem("loginCredentials"),"true"===t.get("verified")?N.toast.success("Email verified successfully! Please login with your credentials."):"true"===t.get("registered")&&N.toast.success("Registration completed! Please login with your credentials.")}catch(e){console.error("Error parsing stored credentials:",e)}},[t,I]);let _=async r=>{var s,i;try{let i=await E(r).unwrap();if(i.success&&i.data){a((0,j.LA)({user:i.data.user,token:i.data.token,refreshToken:i.data.refreshToken}));let r=C(i.data.user);if("kyc"===r)N.toast.info("Please complete your KYC verification to unlock all features."),e.push("/kyc");else if("email_verification"===r)N.toast.warning("Please verify your email to activate your account."),e.push("/dashboard?verification=required");else{let a=t.get("redirect")||(null==(s=document.cookie.split("; ").find(e=>e.startsWith("redirectTo=")))?void 0:s.split("=")[1])||localStorage.getItem("redirectTo")||"/dashboard";document.cookie="redirectTo=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;",localStorage.removeItem("redirectTo"),N.toast.success("Login successful! Welcome back."),setTimeout(()=>{console.log("\uD83D\uDD04 User: Redirecting after login success to:",a),e.push(decodeURIComponent(a))},500)}}}catch(e){console.error("Login error:",e),401===e.status?(O("email",{message:"Invalid email or password"}),O("password",{message:"Invalid email or password"})):(null==(i=e.data)?void 0:i.message)?N.toast.error(e.data.message):N.toast.error("Login failed. Please try again.")}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8 animate-fade-in",children:[(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsx)(x.A,{size:"lg",variant:"full"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-black mb-2",children:"Welcome Back"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Continue your investment journey"}),(0,r.jsx)("div",{className:"w-24 h-1 bg-sky-500 mx-auto mt-4 rounded-full"})]}),(0,r.jsxs)(y.Zp,{className:"shadow-2xl border-0 backdrop-blur-sm bg-white/90 relative overflow-hidden animate-scale-in",children:[(0,r.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500"}),(0,r.jsxs)(y.aR,{className:"space-y-1 relative",children:[(0,r.jsx)(y.ZB,{className:"text-2xl font-bold text-center bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent",children:"Sign In"}),(0,r.jsx)(y.BT,{className:"text-center text-gray-600",children:"Enter your credentials to access your account"})]}),(0,r.jsxs)(y.Wu,{children:[(0,r.jsxs)("form",{onSubmit:P(_),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...A("email"),type:"email",id:"email",placeholder:"Enter your email",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),M.email&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:M.email.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...A("password"),type:c?"text":"password",id:"password",placeholder:"Enter your password",className:"w-full pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)("button",{type:"button",onClick:()=>k(!c),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:c?(0,r.jsx)(h.A,{className:"h-4 w-4"}):(0,r.jsx)(f.A,{className:"h-4 w-4"})})]}),M.password&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:M.password.message})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{...A("rememberMe"),type:"checkbox",id:"rememberMe",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"rememberMe",className:"ml-2 text-sm text-gray-700",children:"Remember me"})]}),(0,r.jsx)(o(),{href:"/forgot-password",className:"text-sm text-blue-600 hover:text-blue-500",children:"Forgot password?"})]}),(0,r.jsx)(p.$,{type:"submit",disabled:S,className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors",children:S?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Signing in..."]}):"Sign In"})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Don't have an account?"})})]})}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsx)(o(),{href:"/register",className:"text-blue-600 hover:text-blue-500 font-medium",children:"Create a new account"})})]})]}),(0,r.jsx)("div",{className:"mt-8 text-center text-sm text-gray-600",children:(0,r.jsxs)("p",{children:["By signing in, you agree to our"," ",(0,r.jsx)(o(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",(0,r.jsx)(o(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})})]})})}function E(){return(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),children:(0,r.jsx)(k,{})})}},7530:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(5050).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},8120:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(9605);a(9585);let s=e=>{let{size:t="md",variant:a="full",className:s=""}=e,i={sm:"h-8 w-8",md:"h-12 w-12",lg:"h-16 w-16",xl:"h-24 w-24"},l={sm:"text-lg",md:"text-2xl",lg:"text-3xl",xl:"text-4xl"},o=()=>(0,r.jsx)("div",{className:"".concat(i[t]," ").concat(s," relative"),children:(0,r.jsxs)("svg",{viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"w-full h-full",children:[(0,r.jsx)("circle",{cx:"50",cy:"50",r:"48",fill:"#0ea5e9",stroke:"#ffffff",strokeWidth:"2"}),(0,r.jsxs)("g",{transform:"translate(20, 25)",children:[(0,r.jsx)("rect",{x:"15",y:"20",width:"30",height:"35",fill:"#ffffff",rx:"2"}),(0,r.jsx)("rect",{x:"20",y:"25",width:"6",height:"6",fill:"#0ea5e9"}),(0,r.jsx)("rect",{x:"34",y:"25",width:"6",height:"6",fill:"#0ea5e9"}),(0,r.jsx)("rect",{x:"20",y:"35",width:"6",height:"6",fill:"#0ea5e9"}),(0,r.jsx)("rect",{x:"34",y:"35",width:"6",height:"6",fill:"#0ea5e9"}),(0,r.jsx)("rect",{x:"27",y:"45",width:"6",height:"10",fill:"#fbbf24"}),(0,r.jsx)("rect",{x:"5",y:"30",width:"8",height:"25",fill:"#ffffff",rx:"1"}),(0,r.jsx)("rect",{x:"47",y:"35",width:"8",height:"20",fill:"#ffffff",rx:"1"}),(0,r.jsx)("path",{d:"M10 15 L20 5 L30 10 L40 2 L50 8",stroke:"#fbbf24",strokeWidth:"3",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("polygon",{points:"45,2 50,8 45,8",fill:"#fbbf24"})]}),(0,r.jsx)("text",{x:"50",y:"75",textAnchor:"middle",fill:"#ffffff",fontSize:"12",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:"SGM"})]})});switch(a){case"icon":return(0,r.jsx)(o,{});case"text":return(0,r.jsx)(()=>(0,r.jsxs)("div",{className:"".concat(s," flex items-center"),children:[(0,r.jsx)("span",{className:"".concat(l[t]," font-bold sgm-accent-text"),children:"SGM"}),(0,r.jsx)("span",{className:"".concat(l[t]," font-light sgm-primary-text ml-1"),children:"Investments"})]}),{});default:return(0,r.jsx)(()=>(0,r.jsxs)("div",{className:"".concat(s," flex items-center space-x-3"),children:[(0,r.jsx)(o,{}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"".concat(l[t]," font-bold sgm-accent-text leading-none"),children:"SGM"}),(0,r.jsx)("span",{className:"text-sm sgm-primary-text leading-none",children:"Investments"})]})]}),{})}}},8824:(e,t,a)=>{Promise.resolve().then(a.bind(a,4578))},9311:(e,t,a)=>{"use strict";a.d(t,{M_:()=>Y,jL:()=>K,GV:()=>B});var r=a(7895),s=a(6597),i=a(9559),l=a(6965),o=a(3815);let n={sidebarCollapsed:!1,sidebarMobileOpen:!1,theme:"light",loading:{global:!1,page:!1,component:{}},modals:{investmentModal:!1,withdrawalModal:!1,addMoneyModal:!1,profileModal:!1,supportModal:!1},notifications:{show:!1,unreadCount:0},searchQuery:"",filters:{}},d=(0,r.Z0)({name:"ui",initialState:n,reducers:{toggleSidebar:e=>{e.sidebarCollapsed=!e.sidebarCollapsed},setSidebarCollapsed:(e,t)=>{e.sidebarCollapsed=t.payload},toggleMobileSidebar:e=>{e.sidebarMobileOpen=!e.sidebarMobileOpen},setMobileSidebarOpen:(e,t)=>{e.sidebarMobileOpen=t.payload},setTheme:(e,t)=>{e.theme=t.payload},toggleTheme:e=>{e.theme="light"===e.theme?"dark":"light"},setGlobalLoading:(e,t)=>{e.loading.global=t.payload},setPageLoading:(e,t)=>{e.loading.page=t.payload},setComponentLoading:(e,t)=>{let{component:a,loading:r}=t.payload;e.loading.component[a]=r},openModal:(e,t)=>{e.modals[t.payload]=!0},closeModal:(e,t)=>{e.modals[t.payload]=!1},closeAllModals:e=>{Object.keys(e.modals).forEach(t=>{e.modals[t]=!1})},toggleNotifications:e=>{e.notifications.show=!e.notifications.show},setNotificationsOpen:(e,t)=>{e.notifications.show=t.payload},setUnreadCount:(e,t)=>{e.notifications.unreadCount=t.payload},setSearchQuery:(e,t)=>{e.searchQuery=t.payload},setFilter:(e,t)=>{let{key:a,value:r}=t.payload;e.filters[a]=r},clearFilters:e=>{e.filters={}},resetUI:e=>({...n,theme:e.theme})}}),{toggleSidebar:c,setSidebarCollapsed:u,toggleMobileSidebar:m,setMobileSidebarOpen:h,setTheme:f,toggleTheme:g,setGlobalLoading:x,setPageLoading:p,setComponentLoading:y,openModal:v,closeModal:b,closeAllModals:j,toggleNotifications:w,setNotificationsOpen:N,setUnreadCount:T,setSearchQuery:k,setFilter:E,clearFilters:S,resetUI:C}=d.actions,A=d.reducer,P=(0,r.Z0)({name:"notifications",initialState:{notifications:[],unreadCount:0,loading:!1,error:null},reducers:{setNotifications:(e,t)=>{e.notifications=t.payload,e.unreadCount=t.payload.filter(e=>!e.read).length},addNotification:(e,t)=>{e.notifications.unshift(t.payload),t.payload.read||(e.unreadCount+=1)},markAsRead:(e,t)=>{let a=e.notifications.find(e=>e.id===t.payload);a&&!a.read&&(a.read=!0,e.unreadCount=Math.max(0,e.unreadCount-1))},markAllAsRead:e=>{e.notifications.forEach(e=>{e.read=!0}),e.unreadCount=0},removeNotification:(e,t)=>{let a=e.notifications.findIndex(e=>e.id===t.payload);-1!==a&&(e.notifications[a].read||(e.unreadCount=Math.max(0,e.unreadCount-1)),e.notifications.splice(a,1))},clearAllNotifications:e=>{e.notifications=[],e.unreadCount=0},setLoading:(e,t)=>{e.loading=t.payload},setError:(e,t)=>{e.error=t.payload},receiveNotification:(e,t)=>{if(e.notifications.unshift(t.payload),t.payload.read||(e.unreadCount+=1),e.notifications.length>50){let t=e.notifications.splice(50).filter(e=>!e.read).length;e.unreadCount=Math.max(0,e.unreadCount-t)}}}}),{setNotifications:M,addNotification:O,markAsRead:I,markAllAsRead:_,removeNotification:U,clearAllNotifications:q,setLoading:R,setError:L,receiveNotification:F}=P.actions,V=P.reducer,Y=(0,r.U1)({reducer:{[l.q.reducerPath]:l.q.reducer,auth:o.Ay,ui:A,notifications:V},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE","persist/PAUSE","persist/PURGE","persist/REGISTER"]}}).concat(l.q.middleware),devTools:!1});(0,s.$k)(Y.dispatch);let K=()=>(0,i.wA)(),B=i.d4}},e=>{var t=t=>e(e.s=t);e.O(0,[2094,5315,7436,3403,1147,390,110,7358],()=>t(8824)),_N_E=e.O()}]);