(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[655],{192:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(6501).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},783:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(6501).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2731:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(6501).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3757:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(6501).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5855:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>P});var t=a(9605),l=a(9585),r=a(6440),d=a(8063),i=a(2933),n=a(2790),c=a(9577),o=a(4685),h=a(7971),g=a(6467),m=a(5097),x=a(3757),u=a(6501);let p=(0,u.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var j=a(4844),y=a(8347),v=a(5048),N=a(9581),b=a(6901),f=a(4842),w=a(783),T=a(192),A=a(1713),D=a(7418),k=a(2731);let C=(0,u.A)("trophy",[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]]);var q=a(6886),L=a(9192),S=a(6994),F=a(8642);let E=e=>{switch(e){case"on_track":case"completed":return"bg-green-100 text-green-800";case"behind":return"bg-red-100 text-red-800";case"exceeded":return"bg-sky-100 text-sky-800";default:return"bg-gray-100 text-gray-800"}},J=e=>{switch(e){case"on_track":case"completed":return(0,t.jsx)(x.A,{className:"w-4 h-4 text-green-600"});case"behind":return(0,t.jsx)(p,{className:"w-4 h-4 text-red-600"});case"exceeded":return(0,t.jsx)(j.A,{className:"w-4 h-4 text-sky-600"});default:return(0,t.jsx)(y.A,{className:"w-4 h-4 text-gray-600"})}},M=e=>{switch(e){case"sales":return(0,t.jsx)(v.A,{className:"w-5 h-5"});case"revenue":return(0,t.jsx)(N.A,{className:"w-5 h-5"});case"leads":return(0,t.jsx)(b.A,{className:"w-5 h-5"});default:return(0,t.jsx)(f.A,{className:"w-5 h-5"})}};function P(){var e;let[s,a]=(0,l.useState)("monthly"),[u,p]=(0,l.useState)(!1),[j,y]=(0,l.useState)(!1),[N,P]=(0,l.useState)(null),[V,U]=(0,l.useState)({title:"",description:"",period:"monthly",targetType:"sales",targetAmount:"",targetLeads:"",targetDeals:"",startDate:"",endDate:"",notes:""}),{data:O,isLoading:Z,error:$,refetch:_}=(0,F.WD)(),[z,{isLoading:R}]=(0,F.cm)(),[I,{isLoading:W}]=(0,F.Zu)(),H=((null==O||null==(e=O.data)?void 0:e.targets)||[]).filter(e=>"all"===s||e.period===s),B=H.filter(e=>e.createdBy!==e.salesRepId),Q=H.filter(e=>e.createdBy===e.salesRepId),G=()=>{U({title:"",description:"",period:"monthly",targetType:"sales",targetAmount:"",targetLeads:"",targetDeals:"",startDate:"",endDate:"",notes:""})},Y=async()=>{try{if(!V.period||!V.targetAmount||!V.startDate||!V.endDate)return void console.error("Missing required fields");let e={period:V.period,targetType:V.targetType||"sales",targetValue:parseFloat(V.targetAmount)||0,targetAmount:parseFloat(V.targetAmount)||0,targetLeads:parseInt(V.targetLeads)||0,targetDeals:parseInt(V.targetDeals)||0,startDate:V.startDate,endDate:V.endDate,notes:V.notes||"",title:V.title||"",description:V.description||""};console.log("Creating target with data:",e),await z(e).unwrap(),p(!1),G(),_()}catch(e){console.error("Failed to create target:",e)}},K=e=>{var s,a,t;P(e),U({title:e.title||"",description:e.description||"",period:e.period||"monthly",targetType:e.targetType||"sales",targetAmount:(null==(s=e.targetAmount)?void 0:s.toString())||"",targetLeads:(null==(a=e.targetLeads)?void 0:a.toString())||"",targetDeals:(null==(t=e.targetDeals)?void 0:t.toString())||"",startDate:e.startDate?e.startDate.split("T")[0]:"",endDate:e.endDate?e.endDate.split("T")[0]:"",notes:e.notes||""}),y(!0)},X=async()=>{if(N)try{let e={period:V.period,targetType:V.targetType,targetValue:V.targetAmount?parseFloat(V.targetAmount):0,targetAmount:V.targetAmount?parseFloat(V.targetAmount):0,targetLeads:V.targetLeads?parseInt(V.targetLeads):0,targetDeals:V.targetDeals?parseInt(V.targetDeals):0,startDate:V.startDate,endDate:V.endDate,notes:V.notes,title:V.title,description:V.description};console.log("Updating target with data:",e),await I({id:N.id||N._id,data:e}).unwrap(),y(!1),P(null),G(),_()}catch(e){console.error("Failed to update target:",e)}};return Z?(0,t.jsx)(r.A,{title:"Sales Targets",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)(w.A,{className:"w-8 h-8 animate-spin text-sky-500"})})})}):$?(0,t.jsx)(r.A,{title:"Sales Targets",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(T.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Failed to load targets"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Please try refreshing the page"}),(0,t.jsx)(i.$,{onClick:()=>_(),className:"bg-sky-500 hover:bg-sky-600",children:"Try Again"})]})})})}):(0,t.jsxs)(r.A,{title:"Sales Targets",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)("div",{className:"p-2 bg-sky-500 rounded-lg",children:(0,t.jsx)(f.A,{className:"w-5 h-5 text-white"})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Sales Targets"})]}),(0,t.jsx)("p",{className:"text-gray-600",children:"Track and achieve your sales goals"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-sm text-gray-500",children:[(0,t.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,t.jsx)(A.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"all"===s?"All Time":s.charAt(0).toUpperCase()+s.slice(1)})]}),(0,t.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,t.jsx)(D.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[H.length," targets"]})]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsxs)(c.l6,{value:s,onValueChange:a,children:[(0,t.jsx)(c.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,t.jsx)(c.yv,{})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"all",children:"All Periods"}),(0,t.jsx)(c.eb,{value:"monthly",children:"Monthly"}),(0,t.jsx)(c.eb,{value:"quarterly",children:"Quarterly"}),(0,t.jsx)(c.eb,{value:"yearly",children:"Yearly"})]})]}),(0,t.jsxs)(i.$,{className:"bg-sky-500 hover:bg-sky-600 text-white",onClick:()=>p(!0),children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"New Target"]})]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(d.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)(x.A,{className:"w-5 h-5 text-green-600"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Targets"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-900",children:H.filter(e=>"active"===e.status).length})]})]})})}),(0,t.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(d.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-sky-100 rounded-lg",children:(0,t.jsx)(f.A,{className:"w-5 h-5 text-sky-600"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Admin Assigned"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-900",children:B.length})]})]})})}),(0,t.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(d.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,t.jsx)(D.A,{className:"w-5 h-5 text-yellow-600"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Self Created"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-900",children:Q.length})]})]})})}),(0,t.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(d.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)(C,{className:"w-5 h-5 text-green-600"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-900",children:H.filter(e=>"completed"===e.status).length})]})]})})})]}),(0,t.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,t.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,t.jsxs)(d.ZB,{className:"text-gray-900",children:["Sales Targets (",H.length,")"]})}),(0,t.jsxs)(d.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"space-y-0",children:H.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-100 hover:bg-sky-50 transition-colors",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,t.jsx)("div",{className:"flex-shrink-0 p-2 bg-gray-100 rounded-lg",children:M(e.type)}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,t.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:e.title||"Sales Target"}),(0,t.jsx)(n.E,{className:"text-xs ".concat(E(e.status)),children:e.status.replace("_"," ").toUpperCase()}),(0,t.jsx)(n.E,{variant:"outline",className:"text-xs border-sky-200 text-sky-700",children:e.period})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Progress"}),(0,t.jsxs)("span",{className:"font-medium",children:[e.percentage||0,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat((e.percentage||0)>=100?"bg-green-500":(e.percentage||0)>=75?"bg-sky-500":(e.percentage||0)>=50?"bg-yellow-500":"bg-red-500"),style:{width:"".concat(Math.min(e.percentage||0,100),"%")}})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-2 text-sm text-gray-600",children:[(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"w-3 h-3 mr-1 text-sky-500"}),"Target: ",e.targetAmount?(0,S.vv)(e.targetAmount):(0,S.ZV)(e.targetValue||0)]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(v.A,{className:"w-3 h-3 mr-1 text-green-500"}),"Achieved: ",e.achievedAmount?(0,S.vv)(e.achievedAmount):(0,S.ZV)(e.currentValue||0)]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"w-3 h-3 mr-1 text-yellow-500"}),"Leads: ",e.achievedLeads||0,"/",e.targetLeads||0]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(A.A,{className:"w-3 h-3 mr-1 text-gray-500"}),"Due: ",(0,S.Yq)(e.endDate||e.dueDate)]})]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[J(e.status),(0,t.jsx)(i.$,{size:"sm",variant:"outline",className:"border-sky-300 text-sky-600 hover:bg-sky-50",onClick:()=>K(e),children:(0,t.jsx)(q.A,{className:"w-3 h-3"})}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",className:"border-green-300 text-green-600 hover:bg-green-50",title:"View Details",children:(0,t.jsx)(L.A,{className:"w-3 h-3"})})]})]},e.id))}),0===H.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(f.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500",children:"No targets found"}),(0,t.jsx)(i.$,{className:"mt-4 bg-sky-500 hover:bg-sky-600 text-white",onClick:()=>_(),children:"Refresh"})]})]})]})]}),(0,t.jsx)(o.lG,{open:u,onOpenChange:e=>{p(e),e||G()},children:(0,t.jsxs)(o.Cf,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(o.c7,{children:(0,t.jsxs)(o.L3,{className:"text-xl font-semibold flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"w-5 h-5 text-sky-500"}),"Create New Target"]})}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"title",children:"Target Title"}),(0,t.jsx)(h.p,{id:"title",value:V.title,onChange:e=>U(s=>({...s,title:e.target.value})),placeholder:"Enter target title",className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"description",children:"Description"}),(0,t.jsx)(g.T,{id:"description",value:V.description,onChange:e=>U(s=>({...s,description:e.target.value})),placeholder:"Enter target description",className:"w-full",rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"period",children:"Period *"}),(0,t.jsxs)(c.l6,{value:V.period,onValueChange:e=>U(s=>({...s,period:e})),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{placeholder:"Select period"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"monthly",children:"Monthly"}),(0,t.jsx)(c.eb,{value:"quarterly",children:"Quarterly"}),(0,t.jsx)(c.eb,{value:"yearly",children:"Yearly"})]})]})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"targetType",children:"Target Type *"}),(0,t.jsxs)(c.l6,{value:V.targetType,onValueChange:e=>U(s=>({...s,targetType:e})),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{placeholder:"Select type"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"sales",children:"Sales Volume"}),(0,t.jsx)(c.eb,{value:"revenue",children:"Revenue"}),(0,t.jsx)(c.eb,{value:"leads",children:"Leads"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"targetAmount",children:"Target Amount *"}),(0,t.jsx)(h.p,{id:"targetAmount",type:"number",value:V.targetAmount,onChange:e=>U(s=>({...s,targetAmount:e.target.value})),placeholder:"Enter target amount",className:"w-full",required:!0,min:"1"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"targetLeads",children:"Target Leads"}),(0,t.jsx)(h.p,{id:"targetLeads",type:"number",value:V.targetLeads,onChange:e=>U(s=>({...s,targetLeads:e.target.value})),placeholder:"0",className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"targetDeals",children:"Target Deals"}),(0,t.jsx)(h.p,{id:"targetDeals",type:"number",value:V.targetDeals,onChange:e=>U(s=>({...s,targetDeals:e.target.value})),placeholder:"0",className:"w-full"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"startDate",children:"Start Date *"}),(0,t.jsx)(h.p,{id:"startDate",type:"date",value:V.startDate,onChange:e=>U(s=>({...s,startDate:e.target.value})),className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"endDate",children:"End Date *"}),(0,t.jsx)(h.p,{id:"endDate",type:"date",value:V.endDate,onChange:e=>U(s=>({...s,endDate:e.target.value})),className:"w-full"})]})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"notes",children:"Notes"}),(0,t.jsx)(g.T,{id:"notes",value:V.notes,onChange:e=>U(s=>({...s,notes:e.target.value})),placeholder:"Additional notes or comments",className:"w-full",rows:2})]})]}),(0,t.jsxs)(o.Es,{children:[(0,t.jsx)(i.$,{variant:"outline",onClick:()=>p(!1),children:"Cancel"}),(0,t.jsxs)(i.$,{onClick:Y,disabled:R||!V.period||!V.targetAmount||!V.startDate||!V.endDate,className:"bg-sky-500 hover:bg-sky-600",children:[R&&(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Create Target"]})]})]})}),(0,t.jsx)(o.lG,{open:j,onOpenChange:e=>{y(e),e||(P(null),G())},children:(0,t.jsxs)(o.Cf,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(o.c7,{children:(0,t.jsxs)(o.L3,{className:"text-xl font-semibold flex items-center gap-2",children:[(0,t.jsx)(q.A,{className:"w-5 h-5 text-sky-500"}),"Edit Target"]})}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-title",children:"Target Title *"}),(0,t.jsx)(h.p,{id:"edit-title",value:V.title,onChange:e=>U(s=>({...s,title:e.target.value})),placeholder:"Enter target title",className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-description",children:"Description"}),(0,t.jsx)(g.T,{id:"edit-description",value:V.description,onChange:e=>U(s=>({...s,description:e.target.value})),placeholder:"Enter target description",className:"w-full",rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-period",children:"Period *"}),(0,t.jsxs)(c.l6,{value:V.period,onValueChange:e=>U(s=>({...s,period:e})),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{placeholder:"Select period"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"monthly",children:"Monthly"}),(0,t.jsx)(c.eb,{value:"quarterly",children:"Quarterly"}),(0,t.jsx)(c.eb,{value:"yearly",children:"Yearly"})]})]})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-targetType",children:"Target Type *"}),(0,t.jsxs)(c.l6,{value:V.targetType,onValueChange:e=>U(s=>({...s,targetType:e})),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{placeholder:"Select type"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"sales",children:"Sales Volume"}),(0,t.jsx)(c.eb,{value:"revenue",children:"Revenue"}),(0,t.jsx)(c.eb,{value:"leads",children:"Leads"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-targetAmount",children:"Target Amount"}),(0,t.jsx)(h.p,{id:"edit-targetAmount",type:"number",value:V.targetAmount,onChange:e=>U(s=>({...s,targetAmount:e.target.value})),placeholder:"0",className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-targetLeads",children:"Target Leads"}),(0,t.jsx)(h.p,{id:"edit-targetLeads",type:"number",value:V.targetLeads,onChange:e=>U(s=>({...s,targetLeads:e.target.value})),placeholder:"0",className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-targetDeals",children:"Target Deals"}),(0,t.jsx)(h.p,{id:"edit-targetDeals",type:"number",value:V.targetDeals,onChange:e=>U(s=>({...s,targetDeals:e.target.value})),placeholder:"0",className:"w-full"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-startDate",children:"Start Date *"}),(0,t.jsx)(h.p,{id:"edit-startDate",type:"date",value:V.startDate,onChange:e=>U(s=>({...s,startDate:e.target.value})),className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-endDate",children:"End Date *"}),(0,t.jsx)(h.p,{id:"edit-endDate",type:"date",value:V.endDate,onChange:e=>U(s=>({...s,endDate:e.target.value})),className:"w-full"})]})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-notes",children:"Notes"}),(0,t.jsx)(g.T,{id:"edit-notes",value:V.notes,onChange:e=>U(s=>({...s,notes:e.target.value})),placeholder:"Additional notes or comments",className:"w-full",rows:2})]})]}),(0,t.jsxs)(o.Es,{children:[(0,t.jsx)(i.$,{variant:"outline",onClick:()=>y(!1),children:"Cancel"}),(0,t.jsxs)(i.$,{onClick:X,disabled:W||!V.period||!V.targetAmount||!V.startDate||!V.endDate,className:"bg-sky-500 hover:bg-sky-600",children:[W&&(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Update Target"]})]})]})})]})}},6886:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(6501).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},8642:(e,s,a)=>{"use strict";a.d(s,{$V:()=>i,FO:()=>c,Hu:()=>r,OT:()=>D,Pb:()=>l,WD:()=>y,Zu:()=>N,aT:()=>j,cm:()=>v,nK:()=>m,pv:()=>A,ro:()=>b});let t=a(6701).q.injectEndpoints({endpoints:e=>({getDashboardStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getDashboardActivities:e.query({query:e=>({url:"/dashboard/activities",params:e}),providesTags:["Dashboard"]}),getSalesStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getLeads:e.query({query:e=>({url:"/sales/leads",params:e}),providesTags:["Lead"]}),getLeadById:e.query({query:e=>"/sales/leads/".concat(e),providesTags:(e,s,a)=>[{type:"Lead",id:a}]}),createLead:e.mutation({query:e=>({url:"/sales/leads",method:"POST",body:e}),invalidatesTags:["Lead","Dashboard"]}),updateLead:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/sales/leads/".concat(s),method:"PUT",body:a}},invalidatesTags:(e,s,a)=>{let{id:t}=a;return[{type:"Lead",id:t},"Lead","Dashboard"]}}),deleteLead:e.mutation({query:e=>({url:"/sales/leads/".concat(e),method:"DELETE"}),invalidatesTags:["Lead","Dashboard"]}),assignLead:e.mutation({query:e=>{let{leadId:s,salesRepId:a}=e;return{url:"/sales/leads/".concat(s,"/assign"),method:"POST",body:{salesRepId:a}}},invalidatesTags:["Lead"]}),getCustomers:e.query({query:e=>({url:"/sales/customers",params:e}),providesTags:["Customer"]}),getCustomerById:e.query({query:e=>"/sales/customers/".concat(e),providesTags:(e,s,a)=>[{type:"Customer",id:a}]}),createCustomer:e.mutation({query:e=>({url:"/sales/customers",method:"POST",body:e}),invalidatesTags:["Customer","Dashboard"]}),updateCustomer:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/sales/customers/".concat(s),method:"PUT",body:a}},invalidatesTags:(e,s,a)=>{let{id:t}=a;return[{type:"Customer",id:t},"Customer"]}}),getCommissions:e.query({query:e=>({url:"/sales/commissions",params:e}),providesTags:["Commission"]}),getSalesTargets:e.query({query:()=>"/sales/targets",providesTags:["Report"]}),createSalesTarget:e.mutation({query:e=>({url:"/sales/targets",method:"POST",body:e}),invalidatesTags:["Report","Dashboard"]}),updateSalesTarget:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/sales/targets/".concat(s),method:"PUT",body:a}},invalidatesTags:["Report","Dashboard"]}),getFollowUps:e.query({query:e=>({url:"/follow-ups",params:e}),providesTags:["Task"]}),createFollowUp:e.mutation({query:e=>({url:"/follow-ups",method:"POST",body:e}),invalidatesTags:["Task","Dashboard"]}),updateFollowUp:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/follow-ups/".concat(s),method:"PUT",body:a}},invalidatesTags:["Task","Dashboard"]}),deleteFollowUp:e.mutation({query:e=>({url:"/follow-ups/".concat(e),method:"DELETE"}),invalidatesTags:["Task","Dashboard"]})})}),{useGetDashboardStatsQuery:l,useGetDashboardActivitiesQuery:r,useGetSalesStatsQuery:d,useGetLeadsQuery:i,useGetLeadByIdQuery:n,useCreateLeadMutation:c,useUpdateLeadMutation:o,useDeleteLeadMutation:h,useAssignLeadMutation:g,useGetCustomersQuery:m,useGetCustomerByIdQuery:x,useCreateCustomerMutation:u,useUpdateCustomerMutation:p,useGetCommissionsQuery:j,useGetSalesTargetsQuery:y,useCreateSalesTargetMutation:v,useUpdateSalesTargetMutation:N,useGetFollowUpsQuery:b,useCreateFollowUpMutation:f,useUpdateFollowUpMutation:w,useDeleteFollowUpMutation:T}=t,{useGetCustomersQuery:A,useGetCommissionsQuery:D}=t},9127:(e,s,a)=>{Promise.resolve().then(a.bind(a,5855))},9192:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(6501).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[399,995,713,668,663,75,440,390,110,358],()=>s(9127)),_N_E=e.O()}]);