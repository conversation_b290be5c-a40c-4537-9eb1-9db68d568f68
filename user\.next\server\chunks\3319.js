"use strict";exports.id=3319,exports.ids=[3319],exports.modules={7409:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(73356),o=globalThis?.document?r.useLayoutEffect:()=>{}},7957:(e,t,n)=>{n.d(t,{jH:()=>u});var r=n(73356);n(40969);var o=r.createContext(void 0);function u(e){let t=r.useContext(o);return e||t||"ltr"}},22195:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(73356),o=n(24629),u=n(7409),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),a=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=l(a.current);s.current="mounted"===f?e:"none"},[f]),(0,u.N)(()=>{let t=a.current,n=c.current;if(n!==e){let r=s.current,o=l(t);e?d("MOUNT"):"none"===o||t?.display==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),c.current=e}},[e,d]),(0,u.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,n=n=>{let r=l(a.current).includes(n.animationName);if(n.target===o&&r&&(d("ANIMATION_END"),!c.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(s.current=l(a.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(t),a="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),c=(0,o.s)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||i.isPresent?r.cloneElement(a,{ref:c}):null};function l(e){return e?.animationName||"none"}i.displayName="Presence"},26314:(e,t,n)=>{n.d(t,{i:()=>l});var r,o=n(73356),u=n(7409),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[u,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),u=o.useRef(n),l=o.useRef(t);return i(()=>{l.current=t},[t]),o.useEffect(()=>{u.current!==n&&(l.current?.(n),u.current=n)},[n,u]),[n,r,l]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[c,e,l,a])]}Symbol("RADIX:SYNC_STATE")},59705:(e,t,n)=>{n.d(t,{B:()=>a});var r,o=n(73356),u=n(7409),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function a(e){let[t,n]=o.useState(i());return(0,u.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},60308:(e,t,n)=>{n.d(t,{N:()=>a});var r=n(73356),o=n(64680),u=n(24629),i=n(19334),l=n(40969);function a(e){let t=e+"CollectionProvider",[n,a]=(0,o.A)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:n}=e,o=r.useRef(null),u=r.useRef(new Map).current;return(0,l.jsx)(c,{scope:t,itemMap:u,collectionRef:o,children:n})};f.displayName=t;let d=e+"CollectionSlot",m=(0,i.TL)(d),p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(d,n),i=(0,u.s)(t,o.collectionRef);return(0,l.jsx)(m,{ref:i,children:r})});p.displayName=d;let y=e+"CollectionItemSlot",N="data-radix-collection-item",v=(0,i.TL)(y),M=r.forwardRef((e,t)=>{let{scope:n,children:o,...i}=e,a=r.useRef(null),c=(0,u.s)(t,a),f=s(y,n);return r.useEffect(()=>(f.itemMap.set(a,{ref:a,...i}),()=>void f.itemMap.delete(a))),(0,l.jsx)(v,{...{[N]:""},ref:c,children:o})});return M.displayName=y,[{Provider:f,Slot:p,ItemSlot:M},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${N}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},a]}var c=new WeakMap;function s(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=f(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function f(e){return e!=e||0===e?0:Math.trunc(e)}},60952:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},63407:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(99024).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},71060:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(73356);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}}};