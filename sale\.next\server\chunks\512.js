"use strict";exports.id=512,exports.ids=[512],exports.modules={2223:(e,s,t)=>{t.d(s,{bq:()=>x,eb:()=>p,gC:()=>g,l6:()=>o,yv:()=>m});var a=t(40969),r=t(73356),l=t(31960),i=t(67117),n=t(9484),d=t(13315),c=t(21764);let o=l.bL;l.YJ;let m=l.WT,x=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.l9,{ref:r,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=l.l9.displayName;let h=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.PP,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));h.displayName=l.PP.displayName;let u=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.wn,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})}));u.displayName=l.wn.displayName;let g=r.forwardRef(({className:e,children:s,position:t="popper",...r},i)=>(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:i,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[(0,a.jsx)(h,{}),(0,a.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(u,{})]})}));g.displayName=l.UC.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.JU,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=l.JU.displayName;let p=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.q7,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:s})]}));p.displayName=l.q7.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.wv,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=l.wv.displayName},4942:(e,s,t)=>{t.r(s),t.d(s,{default:()=>r});var a=t(62544);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},12053:(e,s,t)=>{t.d(s,{J:()=>c});var a=t(40969),r=t(73356),l=t(2724),i=t(52774),n=t(21764);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.b,{ref:t,className:(0,n.cn)(d(),e),...s}));c.displayName=l.b.displayName},21764:(e,s,t)=>{t.d(s,{Yq:()=>d,ZV:()=>n,cn:()=>l,r6:()=>c,tP:()=>o,vv:()=>i});var a=t(95534),r=t(86026);function l(...e){return(0,r.QP)((0,a.$)(e))}function i(e){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}function n(e){return new Intl.NumberFormat("en-IN").format(e)}function d(e){if(!e)return"N/A";let s=new Date(e);return isNaN(s.getTime())?"Invalid Date":new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric"}).format(s)}function c(e){if(!e)return"N/A";let s=new Date(e);return isNaN(s.getTime())?"Invalid Date":new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(s)}function o(e,s){return 0===s?100*(e>0):(e-s)/s*100}},36577:(e,s,t)=>{t.d(s,{A:()=>r});var a=t(40969);t(73356);let r=({size:e="md",variant:s="full",theme:t="default",className:r=""})=>{let l={sm:"h-8 w-8",md:"h-12 w-12",lg:"h-16 w-16",xl:"h-24 w-24"},i={sm:"text-lg",md:"text-2xl",lg:"text-3xl",xl:"text-4xl"},n=(()=>{switch(t){case"white":return{circle:"#ffffff",circleStroke:"#0ea5e9",building:"#0ea5e9",windows:"#ffffff",door:"#fbbf24",arrow:"#fbbf24",text:"#0ea5e9",accent:"#ffffff",primary:"#f1f5f9"};case"dark":return{circle:"#1e293b",circleStroke:"#0ea5e9",building:"#ffffff",windows:"#0ea5e9",door:"#fbbf24",arrow:"#fbbf24",text:"#ffffff",accent:"#0ea5e9",primary:"#64748b"};default:return{circle:"#0ea5e9",circleStroke:"#ffffff",building:"#ffffff",windows:"#0ea5e9",door:"#fbbf24",arrow:"#fbbf24",text:"#ffffff",accent:"#0ea5e9",primary:"#64748b"}}})(),d=()=>(0,a.jsx)("div",{className:`${l[e]} ${r} relative`,children:(0,a.jsxs)("svg",{viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"w-full h-full",children:[(0,a.jsx)("circle",{cx:"50",cy:"50",r:"48",fill:n.circle,stroke:n.circleStroke,strokeWidth:"2"}),(0,a.jsxs)("g",{transform:"translate(20, 25)",children:[(0,a.jsx)("rect",{x:"15",y:"20",width:"30",height:"35",fill:n.building,rx:"2"}),(0,a.jsx)("rect",{x:"20",y:"25",width:"6",height:"6",fill:n.windows}),(0,a.jsx)("rect",{x:"34",y:"25",width:"6",height:"6",fill:n.windows}),(0,a.jsx)("rect",{x:"20",y:"35",width:"6",height:"6",fill:n.windows}),(0,a.jsx)("rect",{x:"34",y:"35",width:"6",height:"6",fill:n.windows}),(0,a.jsx)("rect",{x:"27",y:"45",width:"6",height:"10",fill:n.door}),(0,a.jsx)("rect",{x:"5",y:"30",width:"8",height:"25",fill:n.building,rx:"1"}),(0,a.jsx)("rect",{x:"47",y:"35",width:"8",height:"20",fill:n.building,rx:"1"}),(0,a.jsx)("path",{d:"M10 15 L20 5 L30 10 L40 2 L50 8",stroke:n.arrow,strokeWidth:"3",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("polygon",{points:"45,2 50,8 45,8",fill:n.arrow})]}),(0,a.jsx)("text",{x:"50",y:"75",textAnchor:"middle",fill:n.text,fontSize:"12",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:"SGM"})]})});switch(s){case"icon":return(0,a.jsx)(d,{});case"text":return(0,a.jsx)(()=>(0,a.jsxs)("div",{className:`${r} flex items-center`,children:[(0,a.jsx)("span",{className:`${i[e]} font-bold`,style:{color:n.accent},children:"SGM"}),(0,a.jsx)("span",{className:`${i[e]} font-light ml-1`,style:{color:n.primary},children:"Sales"})]}),{});default:return(0,a.jsx)(()=>(0,a.jsxs)("div",{className:`${r} flex items-center space-x-3`,children:[(0,a.jsx)(d,{}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:`${i[e]} font-bold leading-none`,style:{color:n.accent},children:"SGM"}),(0,a.jsx)("span",{className:"text-sm leading-none",style:{color:n.primary},children:"Sales Portal"})]})]}),{})}}},46411:(e,s,t)=>{t.d(s,{$:()=>c});var a=t(40969),r=t(73356),l=t(19334),i=t(52774),n=t(21764);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef(({className:e,variant:s,size:t,asChild:r=!1,...i},c)=>{let o=r?l.DX:"button";return(0,a.jsx)(o,{className:(0,n.cn)(d({variant:s,size:t,className:e})),ref:c,...i})});c.displayName="Button"},54289:(e,s,t)=>{t.d(s,{T:()=>i});var a=t(40969),r=t(73356),l=t(21764);let i=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));i.displayName="Textarea"},54885:(e,s,t)=>{t.d(s,{b:()=>k});var a=t(40969),r=t(73356),l=t(96727),i=t(46411),n=t(76650),d=t(91714),c=t(93012),o=t(33763);function m({images:e,alt:s,className:t="",autoSlide:l=!0,slideInterval:n=4e3}){let[m,x]=(0,r.useState)(0),h=e=>{x(e)};if(!e||0===e.length)return(0,a.jsx)("div",{className:`bg-gradient-to-br from-sky-100 to-sky-200 flex items-center justify-center ${t}`,children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center text-sky-400",children:[(0,a.jsx)(d.A,{className:"w-16 h-16 mb-2"}),(0,a.jsx)("span",{className:"text-sm",children:"No Images"})]})});let u=e[m],g="string"==typeof u?u:u?.url;return(0,a.jsxs)("div",{className:`relative overflow-hidden group ${t}`,children:[(0,a.jsx)("img",{src:g,alt:s,className:"w-full h-full object-cover transition-transform duration-500 group-hover:scale-105",onError:e=>{e.target.src="https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop"}}),e.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.$,{variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300",onClick:()=>{x(0===m?e.length-1:m-1)},children:(0,a.jsx)(c.A,{className:"w-4 h-4"})}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300",onClick:()=>{x(m===e.length-1?0:m+1)},children:(0,a.jsx)(o.A,{className:"w-4 h-4"})})]}),e.length>1&&(0,a.jsx)("div",{className:"absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-2",children:e.map((e,s)=>(0,a.jsx)("button",{className:`w-2 h-2 rounded-full transition-all duration-300 ${s===m?"bg-white scale-125":"bg-white/50 hover:bg-white/75"}`,onClick:()=>h(s)},s))}),e.length>1&&(0,a.jsxs)("div",{className:"absolute top-3 right-3 bg-black/70 text-white text-xs px-2 py-1 rounded-full",children:[m+1," / ",e.length]})]})}var x=t(21764),h=t(83099),u=t(61115),g=t(27470),p=t(82039),b=t(56252),f=t(91798),y=t(16355),v=t(35918),j=t(28501),N=t(97009),w=t(32954);function k({property:e,isOpen:s,onClose:t,onCopyReferral:r,copiedReferralId:d}){if(!e)return null;let c=e=>{window.open(e,"_blank")};return(0,a.jsx)(l.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(l.Cf,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(l.c7,{children:(0,a.jsx)(l.L3,{className:"text-2xl font-bold text-gray-900",children:e.name})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"h-80 rounded-lg overflow-hidden",children:(0,a.jsx)(m,{images:e.images&&e.images.length>0?e.images:[{url:"residential"===e.propertyType?"https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop":"commercial"===e.propertyType?"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop":"industrial"===e.propertyType?"https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=800&h=600&fit=crop":"eco_friendly"===e.propertyType?"https://images.unsplash.com/photo-1518780664697-55e3ad937233?w=800&h=600&fit=crop":"https://images.unsplash.com/photo-1613977257363-707ba9348227?w=800&h=600&fit=crop",name:`${e.name} - Main View`}],alt:e.name,className:"h-full",autoSlide:!0,slideInterval:5e3})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(n.E,{className:"bg-blue-100 text-blue-800 text-sm",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-1"}),e.propertyType?.replace("_"," ").toUpperCase()]}),(0,a.jsx)(n.E,{className:"bg-green-100 text-green-800 text-sm",children:e.status?.replace("_"," ").toUpperCase()})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 text-yellow-500 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.location?.address||"Address"}),(0,a.jsxs)("p",{className:"text-gray-600",children:[e.location?.city||"City",", ",e.location?.state||"State"," - ",e.location?.pincode||"Pincode"]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(h.A,{className:"w-5 h-5 mr-2 text-blue-500"}),"Developer Information"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Name:"}),(0,a.jsx)("span",{className:"font-medium",children:e.developer?.name||"Developer Name"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Experience:"}),(0,a.jsxs)("span",{className:"font-medium flex items-center",children:[(0,a.jsx)(g.A,{className:"w-4 h-4 mr-1 text-purple-500"}),e.developer?.experience||0," years"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Contact:"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(i.$,{size:"sm",variant:"outline",className:"text-xs",children:[(0,a.jsx)(p.A,{className:"w-3 h-3 mr-1"}),e.developer?.contact||"Contact"]}),(0,a.jsxs)(i.$,{size:"sm",variant:"outline",className:"text-xs",children:[(0,a.jsx)(b.A,{className:"w-3 h-3 mr-1"}),"Email"]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-sky-50 to-blue-50 p-4 rounded-lg border border-sky-100",children:[(0,a.jsxs)("h3",{className:"font-semibold text-sky-700 mb-3 flex items-center",children:[(0,a.jsx)(f.A,{className:"w-5 h-5 mr-2"}),"Investment Overview"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[e.expectedReturns||0,"%"]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Expected Returns"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e.maturityPeriodMonths||0}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Months"})]})]})]}),e.stock&&(0,a.jsxs)("div",{className:"bg-white border border-gray-200 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(y.A,{className:"w-5 h-5 mr-2 text-purple-500"}),"Stock Details"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Stock Price:"}),(0,a.jsx)("span",{className:"font-bold text-sky-600",children:(0,x.vv)(e.stock?.stockPrice||0)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Available:"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:(0,x.ZV)(e.stock?.availableStocks||0)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Sold:"}),(0,a.jsx)("span",{className:"font-medium text-orange-600",children:(0,x.ZV)(e.stock?.stocksSold||0)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Min Investment:"}),(0,a.jsx)("span",{className:"font-medium",children:(0,x.ZV)(e.stock?.minimumPurchase||0)})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-600 mb-1",children:[(0,a.jsx)("span",{children:"Sales Progress"}),(0,a.jsxs)("span",{children:[(e.stock?.soldPercentage||0).toFixed(1),"% sold"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full",style:{width:`${Math.min(e.stock?.soldPercentage||0,100)}%`}})})]})]}),e.stock&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-semibold text-green-700 mb-3 flex items-center",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 mr-2"}),"Commission Structure"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"text-center bg-white p-3 rounded",children:[(0,a.jsxs)("div",{className:"text-xl font-bold text-green-600",children:[e.stock?.salesCommissionRate||0,"%"]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Sales Commission"})]}),(0,a.jsxs)("div",{className:"text-center bg-white p-3 rounded",children:[(0,a.jsxs)("div",{className:"text-xl font-bold text-blue-600",children:[e.stock?.referralCommissionRate||0,"%"]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Referral Commission"})]})]})]}),e.documents&&e.documents.length>0&&(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 mr-2 text-gray-600"}),"Documents (",e.documents.length,")"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[e.documents.slice(0,3).map((e,s)=>(0,a.jsxs)(i.$,{variant:"outline",size:"sm",className:"w-full justify-between text-left",onClick:()=>c(e.url||e),children:[(0,a.jsx)("span",{className:"truncate",children:e.name||`Document ${s+1}`}),(0,a.jsx)(N.A,{className:"w-4 h-4"})]},s)),e.documents.length>3&&(0,a.jsxs)("p",{className:"text-sm text-gray-500 text-center",children:["+",e.documents.length-3," more documents"]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(i.$,{className:"w-full bg-green-500 hover:bg-green-600 text-white",children:[(0,a.jsx)(y.A,{className:"w-4 h-4 mr-2"}),"Start Investment Process"]}),(0,a.jsx)(i.$,{variant:"outline",className:"w-full border-blue-300 text-blue-600 hover:bg-blue-50",onClick:()=>r(e.id,e.name),children:d===e.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"Referral Link Copied!"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"w-4 h-4 mr-2"}),"Copy Referral Link"]})})]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Description"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]}),e.amenities&&e.amenities.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Amenities"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.amenities.map((e,s)=>(0,a.jsx)(n.E,{variant:"outline",className:"text-sm",children:e},s))})]})]})})}},57387:(e,s,t)=>{t.d(s,{p:()=>i});var a=t(40969),r=t(73356),l=t(21764);let i=r.forwardRef(({className:e,type:s,...t},r)=>(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...t}));i.displayName="Input"},66949:(e,s,t)=>{t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n});var a=t(40969),r=t(73356),l=t(21764);let i=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let n=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},76650:(e,s,t)=>{t.d(s,{E:()=>n});var a=t(40969);t(73356);var r=t(52774),l=t(21764);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function n({className:e,variant:s,...t}){return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:s}),e),...t})}},86400:(e,s,t)=>{t.d(s,{AG:()=>a,R2:()=>n,S3:()=>l,TS:()=>T,Yp:()=>u,Z4:()=>f,Zz:()=>i,dy:()=>x,eV:()=>h,mc:()=>p,rh:()=>d,xc:()=>o});let{useGetTasksQuery:a,useGetTaskByIdQuery:r,useCreateTaskMutation:l,useUpdateTaskMutation:i,useDeleteTaskMutation:n,useCompleteTaskMutation:d,useGetTaskStatsQuery:c,useGetCalendarEventsQuery:o,useGetCalendarEventByIdQuery:m,useCreateCalendarEventMutation:x,useUpdateCalendarEventMutation:h,useDeleteCalendarEventMutation:u,useGetTodaysTasksQuery:g,useGetUpcomingTasksQuery:p,useGetOverdueTasksQuery:b,useGetTodaysEventsQuery:f,useGetUpcomingEventsQuery:y,useBulkUpdateTasksMutation:v,useBulkDeleteTasksMutation:j,useGetTaskTemplatesQuery:N,useCreateTaskFromTemplateMutation:w,useSnoozeTaskMutation:k,useAddTaskReminderMutation:D,useGetSalesTeamQuery:T,useGetUserByIdQuery:C}=t(53412).q.injectEndpoints({endpoints:e=>({getTasks:e.query({query:e=>({url:"/sales/tasks",params:e}),providesTags:["Task"]}),getTaskById:e.query({query:e=>`/sales/tasks/${e}`,providesTags:(e,s,t)=>[{type:"Task",id:t}]}),createTask:e.mutation({query:e=>({url:"/sales/tasks",method:"POST",body:e}),invalidatesTags:["Task","Calendar","Dashboard"]}),updateTask:e.mutation({query:({id:e,data:s})=>({url:`/sales/tasks/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:t})=>[{type:"Task",id:t},"Task","Calendar","Dashboard"]}),deleteTask:e.mutation({query:e=>({url:`/sales/tasks/${e}`,method:"DELETE"}),invalidatesTags:["Task","Calendar","Dashboard"]}),completeTask:e.mutation({query:({id:e,notes:s,actualDuration:t})=>({url:`/sales/tasks/${e}/complete`,method:"POST",body:{notes:s,actualDuration:t}}),invalidatesTags:(e,s,{id:t})=>[{type:"Task",id:t},"Task","Dashboard"]}),getTaskStats:e.query({query:e=>({url:"/sales/tasks/stats",params:e}),providesTags:["Task","Dashboard"]}),getCalendarEvents:e.query({query:e=>({url:"/sales/calendar/events",params:e}),providesTags:["Calendar"]}),getCalendarEventById:e.query({query:e=>`/sales/calendar/events/${e}`,providesTags:(e,s,t)=>[{type:"Calendar",id:t}]}),createCalendarEvent:e.mutation({query:e=>({url:"/sales/calendar/events",method:"POST",body:e}),invalidatesTags:["Calendar","Task","Dashboard"]}),updateCalendarEvent:e.mutation({query:({id:e,data:s})=>({url:`/sales/calendar/events/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:t})=>[{type:"Calendar",id:t},"Calendar","Task"]}),deleteCalendarEvent:e.mutation({query:e=>({url:`/sales/calendar/events/${e}`,method:"DELETE"}),invalidatesTags:["Calendar","Task"]}),getTodaysTasks:e.query({query:()=>"/sales/tasks/today",providesTags:["Task","Dashboard"]}),getUpcomingTasks:e.query({query:e=>({url:"/sales/tasks/upcoming",params:e}),providesTags:["Task","Dashboard"]}),getOverdueTasks:e.query({query:()=>"/sales/tasks/overdue",providesTags:["Task","Dashboard"]}),getTodaysEvents:e.query({query:()=>"/sales/calendar/today",providesTags:["Calendar","Dashboard"]}),getUpcomingEvents:e.query({query:e=>({url:"/sales/calendar/upcoming",params:e}),providesTags:["Calendar","Dashboard"]}),bulkUpdateTasks:e.mutation({query:e=>({url:"/sales/tasks/bulk-update",method:"PUT",body:e}),invalidatesTags:["Task"]}),bulkDeleteTasks:e.mutation({query:e=>({url:"/sales/tasks/bulk-delete",method:"DELETE",body:{taskIds:e}}),invalidatesTags:["Task"]}),getTaskTemplates:e.query({query:()=>"/sales/tasks/templates",providesTags:["Task"]}),createTaskFromTemplate:e.mutation({query:e=>({url:"/sales/tasks/from-template",method:"POST",body:e}),invalidatesTags:["Task","Calendar"]}),snoozeTask:e.mutation({query:({id:e,snoozeUntil:s})=>({url:`/sales/tasks/${e}/snooze`,method:"POST",body:{snoozeUntil:s}}),invalidatesTags:(e,s,{id:t})=>[{type:"Task",id:t},"Task"]}),addTaskReminder:e.mutation({query:({id:e,reminderTime:s})=>({url:`/sales/tasks/${e}/reminders`,method:"POST",body:{reminderTime:s}}),invalidatesTags:(e,s,{id:t})=>[{type:"Task",id:t},"Task"]}),getSalesTeam:e.query({query:e=>({url:"/sales/team",params:{limit:100,...e}}),providesTags:["User"]}),getUserById:e.query({query:e=>`/users/${e}`,providesTags:(e,s,t)=>[{type:"User",id:t}]})})})},88251:(e,s,t)=>{t.d(s,{A:()=>er});var a=t(40969),r=t(73356),l=t(27092),i=t.n(l),n=t(12011),d=t(21764),c=t(36577),o=t(83788),m=t(79123),x=t(58557),h=t(54076),u=t(77994),g=t(92409),p=t(91798),b=t(83427),f=t(35342),y=t(66282),v=t(28501),j=t(33808),N=t(68506),w=t(16860);let k=[{name:"Dashboard",href:"/dashboard",icon:o.A,description:"SGM overview & analytics"},{name:"Leads",href:"/leads",icon:m.A,description:"Manage property leads"},{name:"Customers",href:"/customers",icon:x.A,description:"Customer management"},{name:"Properties",href:"/properties",icon:h.A,description:"SGM property portfolio"},{name:"Tasks",href:"/tasks",icon:u.A,description:"Sales tasks & activities"},{name:"Calendar",href:"/calendar",icon:g.A,description:"Events & meetings"},{name:"Sales",href:"/sales",icon:p.A,description:"Track sales performance"},{name:"Commissions",href:"/commissions",icon:b.A,description:"Commission tracking"},{name:"Targets",href:"/targets",icon:f.A,description:"Sales targets & goals"},{name:"Reports",href:"/reports",icon:y.A,description:"Analytics & insights"},{name:"Documents",href:"/documents",icon:v.A,description:"Sales documents"},{name:"Support",href:"/support",icon:j.A,description:"Get help & create tickets"}],D=[{name:"Settings",href:"/settings",icon:N.A,description:"Account settings"}];function T({className:e}){let s=(0,n.usePathname)();return(0,a.jsxs)("div",{className:(0,d.cn)("flex h-full w-64 flex-col bg-gradient-to-b from-white via-sky-25 to-sky-50 border-r border-sky-200 shadow-xl",e),children:[(0,a.jsx)("div",{className:"flex h-20 items-center px-6 border-b border-sky-200 bg-gradient-to-r from-sky-100 via-white to-sky-100 shadow-sm",children:(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsx)(c.A,{size:"md",variant:"full"})})}),(0,a.jsx)("div",{className:"flex-1 px-4 py-6 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-200 scrollbar-track-transparent",children:(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-xs font-bold text-sky-600 uppercase tracking-wider mb-4 px-2",children:"Main Menu"}),(0,a.jsx)("nav",{className:"space-y-2",children:k.map(e=>{let t=s===e.href||s.startsWith(e.href+"/");return(0,a.jsxs)(i(),{href:e.href,className:(0,d.cn)("group relative flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 transform hover:scale-[1.02]",t?"bg-gradient-to-r from-sky-500 to-sky-600 text-white shadow-lg shadow-sky-200 border border-sky-400":"text-gray-700 hover:bg-gradient-to-r hover:from-sky-50 hover:to-sky-100 hover:text-sky-700 hover:shadow-md hover:border hover:border-sky-200"),children:[(0,a.jsx)("div",{className:(0,d.cn)("mr-4 p-2.5 rounded-lg transition-all duration-300",t?"bg-white/20 shadow-sm":"bg-sky-50 group-hover:bg-sky-100 group-hover:shadow-sm"),children:(0,a.jsx)(e.icon,{className:(0,d.cn)("h-5 w-5 flex-shrink-0 transition-all duration-300",t?"text-white":"text-sky-600 group-hover:text-sky-700")})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("div",{className:(0,d.cn)("font-semibold truncate",t?"text-white":"text-gray-900 group-hover:text-sky-900"),children:e.name}),(0,a.jsx)("div",{className:(0,d.cn)("text-xs transition-colors truncate mt-0.5",t?"text-sky-100":"text-gray-500 group-hover:text-sky-600"),children:e.description})]}),t&&(0,a.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full shadow-sm animate-pulse"})}),!t&&(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity duration-300 absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-sky-400 rounded-full"})})]},e.name)})})]})}),(0,a.jsx)("div",{className:"px-4 py-5 border-t border-sky-200 bg-gradient-to-r from-sky-50 via-white to-sky-50",children:(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h3",{className:"text-xs font-bold text-sky-600 uppercase tracking-wider mb-3 px-2",children:"Account"}),(0,a.jsxs)("div",{className:"space-y-2",children:[D.map(e=>{let t=s===e.href;return(0,a.jsxs)(i(),{href:e.href,className:(0,d.cn)("group relative flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 transform hover:scale-[1.02]",t?"bg-gradient-to-r from-sky-500 to-sky-600 text-white shadow-lg shadow-sky-200":"text-gray-700 hover:bg-gradient-to-r hover:from-sky-50 hover:to-sky-100 hover:text-sky-700 hover:shadow-md"),children:[(0,a.jsx)("div",{className:(0,d.cn)("mr-4 p-2.5 rounded-lg transition-all duration-300",t?"bg-white/20 shadow-sm":"bg-sky-50 group-hover:bg-sky-100"),children:(0,a.jsx)(e.icon,{className:(0,d.cn)("h-5 w-5 flex-shrink-0 transition-colors",t?"text-white":"text-sky-600 group-hover:text-sky-700")})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:(0,d.cn)("font-semibold",t?"text-white":"text-gray-900 group-hover:text-sky-900"),children:e.name}),(0,a.jsx)("div",{className:(0,d.cn)("text-xs transition-colors",t?"text-sky-100":"text-gray-500 group-hover:text-sky-600"),children:e.description})]}),t&&(0,a.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full shadow-sm animate-pulse"})})]},e.name)}),(0,a.jsxs)("button",{className:"w-full group relative flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 transform hover:scale-[1.02] text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-red-100 hover:shadow-md mt-2",children:[(0,a.jsx)("div",{className:"mr-4 p-2.5 rounded-lg bg-red-50 group-hover:bg-red-100 transition-all duration-300 shadow-sm",children:(0,a.jsx)(w.A,{className:"h-5 w-5 flex-shrink-0 text-red-500 group-hover:text-red-600"})}),(0,a.jsxs)("div",{className:"flex-1 text-left",children:[(0,a.jsx)("div",{className:"font-semibold text-red-700 group-hover:text-red-800",children:"Logout"}),(0,a.jsx)("div",{className:"text-xs text-red-500 group-hover:text-red-600",children:"Sign out safely"})]}),(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity duration-300 absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-red-400 rounded-full"})})]})]})]})}),(0,a.jsx)("div",{className:"px-4 py-4 bg-gradient-to-r from-gray-50 via-sky-25 to-gray-50 border-t border-sky-200",children:(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-sky-500 to-sky-600 rounded-lg flex items-center justify-center shadow-sm",children:(0,a.jsx)("div",{className:"w-3 h-3 bg-white rounded-sm"})}),(0,a.jsx)("p",{className:"text-xs text-gray-700 font-bold",children:"SGM Sales Portal"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-1",children:[(0,a.jsx)("div",{className:"w-1 h-1 bg-sky-400 rounded-full"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"\xa9 2024 All rights reserved"}),(0,a.jsx)("div",{className:"w-1 h-1 bg-sky-400 rounded-full"})]}),(0,a.jsx)("div",{className:"flex items-center justify-center space-x-1"})]})})]})}var C=t(28460),A=t(71727),F=t(40716),S=t(93094),E=t(77272),$=t(67117),q=t(46411);function J({onMenuClick:e,title:s="Dashboard",showSearch:t=!0}){let[l,i]=(0,r.useState)(!1);return(0,a.jsx)("header",{className:"bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg border-b border-sky-700 flex-shrink-0 z-40",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(q.$,{variant:"ghost",size:"icon",onClick:e,className:"lg:hidden hover:bg-white/20 text-white",children:(0,a.jsx)(C.A,{className:"h-5 w-5"})}),t&&(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2.5 min-w-[350px] border border-white/30",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 text-white/70"}),(0,a.jsx)("input",{type:"text",placeholder:"Search leads, properties, customers...",className:"bg-transparent border-none outline-none flex-1 text-sm placeholder:text-white/70 text-white"}),(0,a.jsx)("kbd",{className:"hidden lg:inline-flex items-center px-2 py-1 text-xs font-medium text-sky-600 bg-white rounded border border-white/30",children:"⌘K"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(q.$,{variant:"ghost",size:"icon",className:"hover:bg-white/20 text-white",children:(0,a.jsx)(F.A,{className:"h-5 w-5"})}),(0,a.jsxs)(q.$,{variant:"ghost",size:"icon",className:"relative hover:bg-white/20 text-white",children:[(0,a.jsx)(S.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 h-5 w-5 bg-yellow-500 text-black text-xs rounded-full flex items-center justify-center font-semibold",children:"3"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(q.$,{variant:"ghost",onClick:()=>i(!l),className:"flex items-center space-x-3 px-3 py-2 hover:bg-white/20 rounded-lg text-white",children:[(0,a.jsxs)("div",{className:"hidden sm:block text-right",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-white",children:"John Doe"}),(0,a.jsx)("div",{className:"flex items-center justify-end space-x-1",children:(0,a.jsx)("div",{className:"bg-yellow-500 px-2 py-0.5 rounded-full",children:(0,a.jsx)("span",{className:"text-xs font-medium text-black capitalize",children:"SGM Sales Executive"})})})]}),(0,a.jsx)("div",{className:"h-10 w-10 bg-white/20 rounded-full flex items-center justify-center shadow-sm border border-white/30",children:(0,a.jsx)(E.A,{className:"h-5 w-5 text-white"})}),(0,a.jsx)($.A,{className:"h-4 w-4 text-white/70"})]}),l&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sky-200 py-2 z-50",children:[(0,a.jsxs)("div",{className:"px-4 py-2 border-b border-sky-100",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"John Doe"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"<EMAIL>"})]}),(0,a.jsxs)(q.$,{variant:"ghost",className:"w-full justify-start px-4 py-2 text-sm text-gray-700 hover:bg-sky-50",children:[(0,a.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Profile"]}),(0,a.jsxs)(q.$,{variant:"ghost",className:"w-full justify-start px-4 py-2 text-sm text-gray-700 hover:bg-sky-50",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Settings"]}),(0,a.jsx)("div",{className:"border-t border-sky-100 mt-2 pt-2",children:(0,a.jsxs)(q.$,{variant:"ghost",className:"w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Sign out"]})})]})]})]})]})})})}var L=t(48567),P=t(95003),O=t(96727),z=t(57387),U=t(54289),R=t(2223),I=t(12053),M=t(8713),V=t(21857),B=t(92739),G=t(86400),_=t(61631);function Z({isOpen:e,onClose:s,onSuccess:t}){let[l,i]=(0,r.useState)({title:"",description:"",type:"call",priority:"medium",dueDate:"",dueTime:"",estimatedDuration:30,tags:"",notes:""}),[n,{isLoading:d}]=(0,G.S3)(),c=(0,L.G)(_.mB),o=(0,L.j)(),m=async e=>{e.preventDefault();try{let e={...l,assignedTo:c?.id,assignedToName:`${c?.firstName} ${c?.lastName}`,createdBy:c?.id,createdByName:`${c?.firstName} ${c?.lastName}`,tags:l.tags.split(",").map(e=>e.trim()).filter(Boolean),status:"pending"};await n(e).unwrap(),i({title:"",description:"",type:"call",priority:"medium",dueDate:"",dueTime:"",estimatedDuration:30,tags:"",notes:""}),o((0,P.$U)({type:"success",title:"Task Created",message:"Task has been created successfully"})),o((0,P.Oo)("create-task")),t?.()}catch(e){console.error("Failed to create task:",e),o((0,P.$U)({type:"error",title:"Error",message:"Failed to create task. Please try again."}))}},x=()=>{o((0,P.Oo)("create-task")),s?.()};return(0,a.jsx)(O.lG,{open:e,onOpenChange:x,children:(0,a.jsxs)(O.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(O.c7,{children:(0,a.jsxs)(O.L3,{className:"text-xl font-semibold flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 text-blue-600"}),"Create New Task"]})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("form",{onSubmit:m,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"title",className:"text-sm font-medium text-black",children:"Task Title *"}),(0,a.jsx)(z.p,{id:"title",value:l.title,onChange:e=>i({...l,title:e.target.value}),placeholder:"Enter task title",required:!0,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"description",className:"text-sm font-medium text-black",children:"Description"}),(0,a.jsx)(U.T,{id:"description",value:l.description,onChange:e=>i({...l,description:e.target.value}),placeholder:"Describe the task",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"type",className:"text-sm font-medium text-black",children:"Task Type"}),(0,a.jsxs)(R.l6,{value:l.type,onValueChange:e=>i({...l,type:e}),children:[(0,a.jsx)(R.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)(R.yv,{})}),(0,a.jsxs)(R.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)(R.eb,{value:"call",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDE Phone Call"}),(0,a.jsx)(R.eb,{value:"email",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCE7 Email"}),(0,a.jsx)(R.eb,{value:"meeting",className:"text-black hover:bg-sky-100",children:"\uD83E\uDD1D Meeting"}),(0,a.jsx)(R.eb,{value:"follow_up",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD04 Follow Up"}),(0,a.jsx)(R.eb,{value:"demo",className:"text-black hover:bg-sky-100",children:"\uD83C\uDFAF Demo"}),(0,a.jsx)(R.eb,{value:"proposal",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCCB Proposal"}),(0,a.jsx)(R.eb,{value:"other",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDD Other"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"priority",className:"text-sm font-medium text-black",children:"Priority"}),(0,a.jsxs)(R.l6,{value:l.priority,onValueChange:e=>i({...l,priority:e}),children:[(0,a.jsx)(R.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)(R.yv,{})}),(0,a.jsxs)(R.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)(R.eb,{value:"low",className:"text-black hover:bg-green-100",children:"\uD83D\uDFE2 Low"}),(0,a.jsx)(R.eb,{value:"medium",className:"text-black hover:bg-yellow-100",children:"\uD83D\uDFE1 Medium"}),(0,a.jsx)(R.eb,{value:"high",className:"text-black hover:bg-yellow-100",children:"\uD83D\uDFE0 High"}),(0,a.jsx)(R.eb,{value:"urgent",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD34 Urgent"})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"dueDate",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(M.A,{className:"w-4 h-4"}),"Due Date *"]}),(0,a.jsx)(z.p,{id:"dueDate",type:"date",value:l.dueDate,onChange:e=>i({...l,dueDate:e.target.value}),required:!0,className:"w-full bg-white text-black border-gray-300"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"dueTime",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(V.A,{className:"w-4 h-4"}),"Due Time"]}),(0,a.jsx)(z.p,{id:"dueTime",type:"time",value:l.dueTime,onChange:e=>i({...l,dueTime:e.target.value}),className:"w-full bg-white text-black border-gray-300"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"duration",className:"text-sm font-medium text-black",children:"Estimated Duration (minutes)"}),(0,a.jsx)(z.p,{id:"duration",type:"number",value:l.estimatedDuration,onChange:e=>i({...l,estimatedDuration:parseInt(e.target.value)||30}),min:"5",max:"480",className:"w-full bg-white text-black border-gray-300"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"tags",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(B.A,{className:"w-4 h-4"}),"Tags (comma separated)"]}),(0,a.jsx)(z.p,{id:"tags",value:l.tags,onChange:e=>i({...l,tags:e.target.value}),placeholder:"lead, follow-up, urgent",className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"notes",className:"text-sm font-medium text-black",children:"Notes"}),(0,a.jsx)(U.T,{id:"notes",value:l.notes,onChange:e=>i({...l,notes:e.target.value}),placeholder:"Additional notes or instructions",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2 bg-sky-50 p-3 rounded-md border border-sky-200",children:[(0,a.jsx)(I.J,{className:"text-sm font-medium text-black",children:"Assigned To (Auto-assigned)"}),(0,a.jsxs)("div",{className:"text-sm text-black bg-white p-2 rounded border",children:["\uD83D\uDC64 ",c?.firstName," ",c?.lastName," (",c?.email,")"]}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Tasks are automatically assigned to you as the creator."})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-300",children:[(0,a.jsx)(q.$,{type:"button",variant:"outline",onClick:x,className:"border-gray-300 text-black hover:bg-gray-100",children:"Cancel"}),(0,a.jsx)(q.$,{type:"submit",disabled:d,className:"bg-sky-500 hover:bg-sky-600 text-white",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Create Task"]})})]})]})})]})})}function W({isOpen:e,onClose:s,onSuccess:t,task:l}){let[i,n]=(0,r.useState)({title:"",description:"",type:"call",priority:"medium",status:"pending",dueDate:"",dueTime:"",estimatedDuration:30,tags:"",notes:""}),[d,{isLoading:c}]=(0,G.Zz)(),o=(0,L.G)(_.mB),m=(0,L.j)(),x=async e=>{if(e.preventDefault(),l)try{let e={...i,tags:i.tags.split(",").map(e=>e.trim()).filter(Boolean)};await d({id:l.id,...e}).unwrap(),m((0,P.$U)({type:"success",title:"Task Updated",message:"Task has been updated successfully"})),m((0,P.Oo)("edit-task")),t?.()}catch(e){console.error("Failed to update task:",e),m((0,P.$U)({type:"error",title:"Error",message:"Failed to update task. Please try again."}))}},h=()=>{m((0,P.Oo)("edit-task")),s?.()};return l?(0,a.jsx)(O.lG,{open:e,onOpenChange:h,children:(0,a.jsxs)(O.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(O.c7,{children:(0,a.jsxs)(O.L3,{className:"text-xl font-semibold flex items-center gap-2 text-black",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 text-sky-500"}),"Edit Task"]})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("form",{onSubmit:x,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"title",className:"text-sm font-medium text-black",children:"Task Title *"}),(0,a.jsx)(z.p,{id:"title",value:i.title,onChange:e=>n({...i,title:e.target.value}),placeholder:"Enter task title",required:!0,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"description",className:"text-sm font-medium text-black",children:"Description"}),(0,a.jsx)(U.T,{id:"description",value:i.description,onChange:e=>n({...i,description:e.target.value}),placeholder:"Describe the task",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"type",className:"text-sm font-medium text-black",children:"Task Type"}),(0,a.jsxs)(R.l6,{value:i.type,onValueChange:e=>n({...i,type:e}),children:[(0,a.jsx)(R.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)(R.yv,{})}),(0,a.jsxs)(R.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)(R.eb,{value:"call",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDE Phone Call"}),(0,a.jsx)(R.eb,{value:"email",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCE7 Email"}),(0,a.jsx)(R.eb,{value:"meeting",className:"text-black hover:bg-sky-100",children:"\uD83E\uDD1D Meeting"}),(0,a.jsx)(R.eb,{value:"follow_up",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD04 Follow Up"}),(0,a.jsx)(R.eb,{value:"demo",className:"text-black hover:bg-sky-100",children:"\uD83C\uDFAF Demo"}),(0,a.jsx)(R.eb,{value:"proposal",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCCB Proposal"}),(0,a.jsx)(R.eb,{value:"other",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDD Other"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"priority",className:"text-sm font-medium text-black",children:"Priority"}),(0,a.jsxs)(R.l6,{value:i.priority,onValueChange:e=>n({...i,priority:e}),children:[(0,a.jsx)(R.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)(R.yv,{})}),(0,a.jsxs)(R.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)(R.eb,{value:"low",className:"text-black hover:bg-green-100",children:"\uD83D\uDFE2 Low"}),(0,a.jsx)(R.eb,{value:"medium",className:"text-black hover:bg-yellow-100",children:"\uD83D\uDFE1 Medium"}),(0,a.jsx)(R.eb,{value:"high",className:"text-black hover:bg-yellow-100",children:"\uD83D\uDFE0 High"}),(0,a.jsx)(R.eb,{value:"urgent",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD34 Urgent"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"status",className:"text-sm font-medium text-black",children:"Status"}),(0,a.jsxs)(R.l6,{value:i.status,onValueChange:e=>n({...i,status:e}),children:[(0,a.jsx)(R.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)(R.yv,{})}),(0,a.jsxs)(R.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)(R.eb,{value:"pending",className:"text-black hover:bg-yellow-100",children:"⏳ Pending"}),(0,a.jsx)(R.eb,{value:"in_progress",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD04 In Progress"}),(0,a.jsx)(R.eb,{value:"completed",className:"text-black hover:bg-green-100",children:"✅ Completed"}),(0,a.jsx)(R.eb,{value:"cancelled",className:"text-black hover:bg-gray-100",children:"❌ Cancelled"}),(0,a.jsx)(R.eb,{value:"overdue",className:"text-black hover:bg-yellow-100",children:"⚠️ Overdue"})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"dueDate",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(M.A,{className:"w-4 h-4"}),"Due Date *"]}),(0,a.jsx)(z.p,{id:"dueDate",type:"date",value:i.dueDate,onChange:e=>n({...i,dueDate:e.target.value}),required:!0,className:"w-full bg-white text-black border-gray-300"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"dueTime",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(V.A,{className:"w-4 h-4"}),"Due Time"]}),(0,a.jsx)(z.p,{id:"dueTime",type:"time",value:i.dueTime,onChange:e=>n({...i,dueTime:e.target.value}),className:"w-full bg-white text-black border-gray-300"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"duration",className:"text-sm font-medium text-black",children:"Estimated Duration (minutes)"}),(0,a.jsx)(z.p,{id:"duration",type:"number",value:i.estimatedDuration,onChange:e=>n({...i,estimatedDuration:parseInt(e.target.value)||30}),min:"5",max:"480",className:"w-full bg-white text-black border-gray-300"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"tags",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(B.A,{className:"w-4 h-4"}),"Tags (comma separated)"]}),(0,a.jsx)(z.p,{id:"tags",value:i.tags,onChange:e=>n({...i,tags:e.target.value}),placeholder:"lead, follow-up, urgent",className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"notes",className:"text-sm font-medium text-black",children:"Notes"}),(0,a.jsx)(U.T,{id:"notes",value:i.notes,onChange:e=>n({...i,notes:e.target.value}),placeholder:"Additional notes or instructions",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2 bg-sky-50 p-3 rounded-md border border-sky-200",children:[(0,a.jsx)(I.J,{className:"text-sm font-medium text-black",children:"Assignment Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"bg-white p-2 rounded border",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Assigned To:"}),(0,a.jsxs)("span",{className:"text-black font-medium ml-2",children:["\uD83D\uDC64 ",l.assignedToName||`${o?.firstName} ${o?.lastName}`]})]}),(0,a.jsxs)("div",{className:"bg-white p-2 rounded border",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Created By:"}),(0,a.jsxs)("span",{className:"text-black font-medium ml-2",children:["\uD83D\uDC64 ",l.createdByName||`${o?.firstName} ${o?.lastName}`]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-300",children:[(0,a.jsx)(q.$,{type:"button",variant:"outline",onClick:h,className:"border-gray-300 text-black hover:bg-gray-100",children:"Cancel"}),(0,a.jsx)(q.$,{type:"submit",disabled:c,className:"bg-sky-500 hover:bg-sky-600 text-white",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Updating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Update Task"]})})]})]})})]})}):null}var Y=t(76650),H=t(32550),K=t(11627),Q=t(61115),X=t(58896);function ee({isOpen:e,onClose:s,onSuccess:t}){let[l,i]=(0,r.useState)({title:"",description:"",type:"meeting",startDate:"",startTime:"",endDate:"",endTime:"",location:"",isAllDay:!1,meetingLink:"",notes:""}),[n,d]=(0,r.useState)([]),[c,o]=(0,r.useState)(""),[x,h]=(0,r.useState)(!1),[u,{isLoading:g}]=(0,G.dy)();(0,L.G)(_.mB);let p=(0,L.j)(),{data:b,isLoading:f}=(0,G.TS)({search:c,status:"active"}),y=(b?.data||[]).filter(e=>!n.find(s=>s.id===e.id)),v=e=>{d([...n,e]),o(""),h(!1)},j=e=>{d(n.filter(s=>s.id!==e))},N=async e=>{e.preventDefault();try{if(!l.title||!l.startDate||!l.startTime||!l.endDate||!l.endTime)return void p((0,P.$U)({type:"error",title:"Validation Error",message:"Please fill in all required fields"}));let e={title:l.title,description:l.description,type:l.type,startDate:l.startDate,startTime:l.startTime,endDate:l.endDate,endTime:l.endTime,location:l.location,isAllDay:l.isAllDay,meetingLink:l.meetingLink,notes:l.notes,attendees:n.map(e=>({id:e.id,name:`${e.firstName} ${e.lastName}`,email:e.email,type:"internal",status:"pending"}))};await u(e).unwrap(),i({title:"",description:"",type:"meeting",startDate:"",startTime:"",endDate:"",endTime:"",location:"",isAllDay:!1,meetingLink:"",notes:""}),d([]),o(""),h(!1),p((0,P.$U)({type:"success",title:"Event Created",message:"Calendar event has been created successfully"})),p((0,P.Oo)("create-event")),t?.()}catch(e){console.error("Failed to create event:",e),p((0,P.$U)({type:"error",title:"Error",message:"Failed to create event. Please try again."}))}},w=()=>{p((0,P.Oo)("create-event")),s?.()};return(0,a.jsx)(O.lG,{open:e,onOpenChange:w,children:(0,a.jsxs)(O.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(O.c7,{children:(0,a.jsxs)(O.L3,{className:"text-xl font-semibold flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"w-5 h-5 text-green-600"}),"Create New Event"]})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"title",className:"text-sm font-medium text-black",children:"Event Title *"}),(0,a.jsx)(z.p,{id:"title",value:l.title,onChange:e=>i({...l,title:e.target.value}),placeholder:"Enter event title",required:!0,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"description",className:"text-sm font-medium text-black",children:"Description"}),(0,a.jsx)(U.T,{id:"description",value:l.description,onChange:e=>i({...l,description:e.target.value}),placeholder:"Describe the event",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"type",className:"text-sm font-medium text-black",children:"Event Type"}),(0,a.jsxs)(R.l6,{value:l.type,onValueChange:e=>i({...l,type:e}),children:[(0,a.jsx)(R.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)(R.yv,{})}),(0,a.jsxs)(R.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)(R.eb,{value:"meeting",className:"text-black hover:bg-sky-100",children:"\uD83E\uDD1D Meeting"}),(0,a.jsx)(R.eb,{value:"call",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDE Call"}),(0,a.jsx)(R.eb,{value:"demo",className:"text-black hover:bg-sky-100",children:"\uD83C\uDFAF Demo"}),(0,a.jsx)(R.eb,{value:"site_visit",className:"text-black hover:bg-sky-100",children:"\uD83C\uDFD7️ Site Visit"}),(0,a.jsx)(R.eb,{value:"follow_up",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD04 Follow Up"}),(0,a.jsx)(R.eb,{value:"other",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDD Other"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),"Assign to Users"]}),n.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 p-3 bg-sky-50 rounded-md border border-sky-200",children:n.map(e=>(0,a.jsxs)(Y.E,{className:"bg-sky-500 text-white hover:bg-sky-600 flex items-center gap-1 px-3 py-1",children:["\uD83D\uDC64 ",e.firstName," ",e.lastName,(0,a.jsx)("button",{type:"button",onClick:()=>j(e.id),className:"ml-1 hover:bg-sky-700 rounded-full p-0.5",children:(0,a.jsx)(K.A,{className:"w-3 h-3"})})]},e.id))}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(z.p,{value:c,onChange:e=>{o(e.target.value),h(e.target.value.length>0)},placeholder:"Search users to assign...",className:"bg-white text-black border-gray-300",onFocus:()=>h(c.length>0)}),(0,a.jsx)(q.$,{type:"button",variant:"outline",onClick:()=>h(!x),className:"border-gray-300 text-black hover:bg-sky-100",children:(0,a.jsx)(A.A,{className:"w-4 h-4"})})]}),x&&!f&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg z-[70] max-h-48 overflow-y-auto",children:y.length>0?y.map(e=>(0,a.jsxs)("button",{type:"button",onClick:()=>v(e),className:"w-full text-left p-3 hover:bg-sky-100 border-b border-gray-100 last:border-b-0 transition-colors",children:[(0,a.jsxs)("div",{className:"text-black font-medium",children:["\uD83D\uDC64 ",e.firstName," ",e.lastName]}),(0,a.jsxs)("div",{className:"text-gray-600 text-sm",children:[e.email," • ",e.role]})]},e.id)):(0,a.jsx)("div",{className:"p-3 text-gray-500 text-sm text-center",children:c?"No users found":"Start typing to search users..."})}),x&&f&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg z-[70] p-3",children:(0,a.jsx)("div",{className:"text-gray-500 text-sm text-center",children:"Loading users..."})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"startDate",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(M.A,{className:"w-4 h-4"}),"Start Date *"]}),(0,a.jsx)(z.p,{id:"startDate",type:"date",value:l.startDate,onChange:e=>i({...l,startDate:e.target.value}),required:!0,className:"w-full bg-white text-black border-gray-300"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"startTime",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(V.A,{className:"w-4 h-4"}),"Start Time *"]}),(0,a.jsx)(z.p,{id:"startTime",type:"time",value:l.startTime,onChange:e=>i({...l,startTime:e.target.value}),required:!0,className:"w-full bg-white text-black border-gray-300"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"endDate",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(M.A,{className:"w-4 h-4"}),"End Date *"]}),(0,a.jsx)(z.p,{id:"endDate",type:"date",value:l.endDate,onChange:e=>i({...l,endDate:e.target.value}),required:!0,className:"w-full bg-white text-black border-gray-300"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"endTime",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(V.A,{className:"w-4 h-4"}),"End Time *"]}),(0,a.jsx)(z.p,{id:"endTime",type:"time",value:l.endTime,onChange:e=>i({...l,endTime:e.target.value}),required:!0,className:"w-full bg-white text-black border-gray-300"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"location",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(Q.A,{className:"w-4 h-4"}),"Location"]}),(0,a.jsx)(z.p,{id:"location",value:l.location,onChange:e=>i({...l,location:e.target.value}),placeholder:"Meeting location or address",className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"meetingLink",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(X.A,{className:"w-4 h-4"}),"Meeting Link"]}),(0,a.jsx)(z.p,{id:"meetingLink",value:l.meetingLink,onChange:e=>i({...l,meetingLink:e.target.value}),placeholder:"Zoom, Teams, or other meeting link",className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"notes",className:"text-sm font-medium text-black",children:"Notes"}),(0,a.jsx)(U.T,{id:"notes",value:l.notes,onChange:e=>i({...l,notes:e.target.value}),placeholder:"Additional notes or agenda",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-300",children:[(0,a.jsx)(q.$,{type:"button",variant:"outline",onClick:w,className:"border-gray-300 text-black hover:bg-gray-100",children:"Cancel"}),(0,a.jsx)(q.$,{type:"submit",disabled:g,className:"bg-green-500 hover:bg-green-600 text-white",children:g?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(H.A,{className:"w-4 h-4 mr-2"}),"Create Event"]})})]})]})})]})})}function es({isOpen:e,onClose:s,onSuccess:t,event:l}){let[i,n]=(0,r.useState)({title:"",description:"",type:"meeting",startDate:"",startTime:"",endDate:"",endTime:"",location:"",isAllDay:!1,meetingLink:"",notes:""}),[d,c]=(0,r.useState)([]),[o,x]=(0,r.useState)(""),[h,u]=(0,r.useState)(!1),[g,{isLoading:p}]=(0,G.eV)(),b=(0,L.G)(_.mB),f=(0,L.j)(),{data:y,isLoading:v}=(0,G.TS)({search:o,status:"active"}),j=(y?.data||[]).filter(e=>!d.find(s=>s.id===e.id)),N=e=>{c([...d,e]),x(""),u(!1)},w=e=>{c(d.filter(s=>s.id!==e))},k=async e=>{if(e.preventDefault(),l)try{if(!i.title||!i.startDate||!i.startTime||!i.endDate||!i.endTime)return void f((0,P.$U)({type:"error",title:"Validation Error",message:"Please fill in all required fields"}));let e={title:i.title,description:i.description,type:i.type,startDate:i.startDate,startTime:i.startTime,endDate:i.endDate,endTime:i.endTime,location:i.location,isAllDay:i.isAllDay,meetingLink:i.meetingLink,notes:i.notes,attendees:d.map(e=>({id:e.id,name:`${e.firstName} ${e.lastName}`,email:e.email,type:"internal",status:"pending"}))};await g({id:l.id,...e}).unwrap(),f((0,P.$U)({type:"success",title:"Event Updated",message:"Calendar event has been updated successfully"})),f((0,P.Oo)("edit-event")),t?.()}catch(e){console.error("Failed to update event:",e),f((0,P.$U)({type:"error",title:"Error",message:"Failed to update event. Please try again."}))}},D=()=>{f((0,P.Oo)("edit-event")),s?.()};return l?(0,a.jsx)(O.lG,{open:e,onOpenChange:D,children:(0,a.jsxs)(O.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(O.c7,{children:(0,a.jsxs)(O.L3,{className:"text-xl font-semibold flex items-center gap-2 text-black",children:[(0,a.jsx)(H.A,{className:"w-5 h-5 text-green-500"}),"Edit Event"]})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("form",{onSubmit:k,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"title",className:"text-sm font-medium text-black",children:"Event Title *"}),(0,a.jsx)(z.p,{id:"title",value:i.title,onChange:e=>n({...i,title:e.target.value}),placeholder:"Enter event title",required:!0,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"description",className:"text-sm font-medium text-black",children:"Description"}),(0,a.jsx)(U.T,{id:"description",value:i.description,onChange:e=>n({...i,description:e.target.value}),placeholder:"Describe the event",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"type",className:"text-sm font-medium text-black",children:"Event Type"}),(0,a.jsxs)(R.l6,{value:i.type,onValueChange:e=>n({...i,type:e}),children:[(0,a.jsx)(R.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)(R.yv,{})}),(0,a.jsxs)(R.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)(R.eb,{value:"meeting",className:"text-black hover:bg-sky-100",children:"\uD83E\uDD1D Meeting"}),(0,a.jsx)(R.eb,{value:"call",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDE Call"}),(0,a.jsx)(R.eb,{value:"demo",className:"text-black hover:bg-sky-100",children:"\uD83C\uDFAF Demo"}),(0,a.jsx)(R.eb,{value:"site_visit",className:"text-black hover:bg-sky-100",children:"\uD83C\uDFD7️ Site Visit"}),(0,a.jsx)(R.eb,{value:"follow_up",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD04 Follow Up"}),(0,a.jsx)(R.eb,{value:"other",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDD Other"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),"Assign to Users"]}),d.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 p-3 bg-sky-50 rounded-md border border-sky-200",children:d.map(e=>(0,a.jsxs)(Y.E,{className:"bg-sky-500 text-white hover:bg-sky-600 flex items-center gap-1 px-3 py-1",children:["\uD83D\uDC64 ",e.firstName," ",e.lastName,(0,a.jsx)("button",{type:"button",onClick:()=>w(e.id),className:"ml-1 hover:bg-sky-700 rounded-full p-0.5",children:(0,a.jsx)(K.A,{className:"w-3 h-3"})})]},e.id))}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(z.p,{value:o,onChange:e=>{x(e.target.value),u(e.target.value.length>0)},placeholder:"Search users to assign...",className:"bg-white text-black border-gray-300",onFocus:()=>u(o.length>0)}),(0,a.jsx)(q.$,{type:"button",variant:"outline",onClick:()=>u(!h),className:"border-gray-300 text-black hover:bg-sky-100",children:(0,a.jsx)(A.A,{className:"w-4 h-4"})})]}),h&&!v&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg z-[70] max-h-48 overflow-y-auto",children:j.length>0?j.map(e=>(0,a.jsxs)("button",{type:"button",onClick:()=>N(e),className:"w-full text-left p-3 hover:bg-sky-100 border-b border-gray-100 last:border-b-0 transition-colors",children:[(0,a.jsxs)("div",{className:"text-black font-medium",children:["\uD83D\uDC64 ",e.firstName," ",e.lastName]}),(0,a.jsxs)("div",{className:"text-gray-600 text-sm",children:[e.email," • ",e.role]})]},e.id)):(0,a.jsx)("div",{className:"p-3 text-gray-500 text-sm text-center",children:o?"No users found":"Start typing to search users..."})}),h&&v&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg z-[70] p-3",children:(0,a.jsx)("div",{className:"text-gray-500 text-sm text-center",children:"Loading users..."})})]})]}),(0,a.jsxs)("div",{className:"space-y-2 bg-green-50 p-3 rounded-md border border-green-200",children:[(0,a.jsx)(I.J,{className:"text-sm font-medium text-black",children:"Event Information"}),(0,a.jsxs)("div",{className:"bg-white p-2 rounded border text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Created By:"}),(0,a.jsxs)("span",{className:"text-black font-medium ml-2",children:["\uD83D\uDC64 ",l.createdByName||`${b?.firstName} ${b?.lastName}`]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-300",children:[(0,a.jsx)(q.$,{type:"button",variant:"outline",onClick:D,className:"border-gray-300 text-black hover:bg-gray-100",children:"Cancel"}),(0,a.jsx)(q.$,{type:"submit",disabled:p,className:"bg-green-500 hover:bg-green-600 text-white",children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Updating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(H.A,{className:"w-4 h-4 mr-2"}),"Update Event"]})})]})]})})]})}):null}var et=t(54885);function ea(){let e=(0,L.G)(e=>e.ui.modals),s=(0,L.j)(),t=e.find(e=>"create-task"===e.type&&e.isOpen),r=e.find(e=>"edit-task"===e.type&&e.isOpen),l=e.find(e=>"create-event"===e.type&&e.isOpen),i=e.find(e=>"edit-event"===e.type&&e.isOpen),n=e.find(e=>"property-details"===e.type&&e.isOpen),d=e=>{s((0,P.Oo)(e))};return(0,a.jsxs)(a.Fragment,{children:[t&&(0,a.jsx)(Z,{isOpen:t.isOpen,onClose:()=>d(t.id),onSuccess:()=>{window.location.reload()}}),r&&(0,a.jsx)(W,{isOpen:r.isOpen,onClose:()=>d(r.id),task:r.data?.task||null,onSuccess:()=>{window.location.reload()}}),l&&(0,a.jsx)(ee,{isOpen:l.isOpen,onClose:()=>d(l.id),onSuccess:()=>{window.location.reload()}}),i&&(0,a.jsx)(es,{isOpen:i.isOpen,onClose:()=>d(i.id),event:i.data?.event||null,onSuccess:()=>{window.location.reload()}}),n&&(0,a.jsx)(et.b,{property:n.data?.property,isOpen:n.isOpen,onClose:()=>d(n.id),onCopyReferral:n.data?.onCopyReferral||(()=>{}),copiedReferralId:n.data?.copiedReferralId||null})]})}function er({children:e,title:s,showSearch:t=!0}){let[l,i]=(0,r.useState)(!1);return(0,a.jsxs)("div",{className:"h-screen flex bg-gradient-to-br from-gray-50 to-sky-50",children:[(0,a.jsx)("div",{className:(0,d.cn)("fixed inset-y-0 left-0 z-50 w-64 transform transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",l?"translate-x-0 shadow-2xl":"-translate-x-full"),children:(0,a.jsx)(T,{})}),l&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden backdrop-blur-sm transition-all duration-300",onClick:()=>i(!1)}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(J,{onMenuClick:()=>i(!l),title:s,showSearch:t}),(0,a.jsx)("main",{className:"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-200 scrollbar-track-transparent",children:(0,a.jsx)("div",{className:"p-6",children:e})})]}),(0,a.jsx)(ea,{})]})}},96727:(e,s,t)=>{t.d(s,{Cf:()=>m,Es:()=>h,L3:()=>u,c7:()=>x,lG:()=>d});var a=t(40969),r=t(73356),l=t(5626),i=t(11627),n=t(21764);let d=l.bL;l.l9;let c=l.ZL;l.bm;let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.hJ,{ref:t,className:(0,n.cn)("fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));o.displayName=l.hJ.displayName;let m=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(c,{children:[(0,a.jsx)(o,{}),(0,a.jsxs)(l.UC,{ref:r,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-gray-300 bg-white text-black p-6 shadow-xl duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[s,(0,a.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=l.UC.displayName;let x=({className:e,...s})=>(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left text-black",e),...s});x.displayName="DialogHeader";let h=({className:e,...s})=>(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});h.displayName="DialogFooter";let u=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.hE,{ref:t,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight text-black",e),...s}));u.displayName=l.hE.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.VY,{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s})).displayName=l.VY.displayName}};