import { createA<PERSON>, fetchB<PERSON><PERSON><PERSON><PERSON>, BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query/react'
import { RootState } from '@/store'
import { CookieUtils } from '@/utils/cookies'

// Define common API response interface
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
  meta?: {
    pagination?: {
      currentPage: number
      totalPages: number
      totalItems: number
      itemsPerPage: number
    }
    timestamp?: string
    version?: string
  }
}

// Base query configuration with authentication and error handling
const baseQuery = fetchBaseQuery({
  baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api',
  credentials: 'include',
  prepareHeaders: (headers, { getState }) => {
    // Get token from cookies first, then localStorage, then Redux state
    let token = null
    if (typeof window !== 'undefined') {
      token = CookieUtils.getAuthToken() || localStorage.getItem('accessToken')
    }

    if (!token) {
      token = (getState() as RootState)?.auth?.user?.accessToken
    }

    if (token) {
      headers.set('authorization', `Bearer ${token}`)
    }

    // Set common headers
    headers.set('content-type', 'application/json')
    headers.set('accept', 'application/json')
    
    // Add client type for backend identification
    headers.set('x-client-type', 'admin-dashboard')
    headers.set('x-client-version', '1.0.0')

    return headers
  },
})

// Enhanced base query with automatic token refresh and error handling
const baseQueryWithReauth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  let result = await baseQuery(args, api, extraOptions)

  // Log API errors for debugging
  if (result.error) {
    console.error('API Error:', result.error)
  }

  // Handle network errors or API server not available
  if (result.error && (result.error.status === 'FETCH_ERROR' || result.error.status === 'PARSING_ERROR')) {
    console.error('API server not available. Please ensure the backend server is running.')
    // Don't return mock data - let the error propagate to the UI
  }

  // Handle 401 Unauthorized - attempt token refresh
  if (result.error && result.error.status === 401) {
    console.log('Token expired, attempting refresh...')
    
    // Try to refresh token
    const refreshResult = await baseQuery(
      {
        url: '/auth/refresh',
        method: 'POST',
        body: {
          refreshToken: typeof window !== 'undefined' ? localStorage.getItem('refreshToken') : null
        }
      },
      api,
      extraOptions
    )

    if (refreshResult.data) {
      // Store new token
      const newTokenData = refreshResult.data as any
      if (newTokenData.success && newTokenData.data?.accessToken) {
        if (typeof window !== 'undefined') {
          localStorage.setItem('accessToken', newTokenData.data.accessToken)
        }

        // Retry original request with new token
        result = await baseQuery(args, api, extraOptions)
      }
    } else {
      // Refresh failed - redirect to login
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
      }
      
      // Dispatch logout action if available
      if (typeof window !== 'undefined') {
        window.location.href = '/login'
      }
    }
  }

  // Handle other common errors
  if (result.error) {
    console.error('API Error:', result.error)
    
    // Handle network errors
    if (result.error.status === 'FETCH_ERROR') {
      console.error('Network error - check if backend is running')
    }
    
    // Handle server errors
    if (result.error.status === 500) {
      console.error('Server error - check backend logs')
    }
  }

  return result
}

// Create base API slice that other APIs will extend
export const baseApi = createApi({
  reducerPath: 'baseApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    'User', 'UserStats',
    'Property', 'PropertyStats',
    'Lead', 'LeadStats',
    'Transaction', 'TransactionStats',
    'Dashboard', 'DashboardStats',
    'PropertyOwner', 'PropertyOwnerStats',
    'Support', 'SupportStats',
    'Finance', 'FinanceStats',
    'Stock', 'StockStats',
    'Settings', 'SystemSettings',
    'Notification', 'Activity',
    'KYC', 'Analytics',
    'System', 'AuditLog', 'Backup',
    'Communication', 'Campaign', 'NotificationLog',
    'PaymentMethod', 'Dispute',
    'Sales', 'SalesStats',
    'SalesTeam', 'SalesTask', 'SalesTarget',
    'Commission', 'Calendar',
    'Investment', 'Portfolio', 'Wallet'
  ],
  endpoints: () => ({}), // Base API has no endpoints, extended APIs will add them
})

// Common query parameters interface
export interface BaseQueryParams {
  page?: number
  limit?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  dateRange?: {
    start?: string
    end?: string
  }
}

// Common mutation response interface
export interface MutationResponse<T = any> extends ApiResponse<T> {
  timestamp: string
}

// Utility function to create standardized query parameters
export const createQueryParams = (params: BaseQueryParams = {}) => ({
  page: params.page || 1,
  limit: params.limit || 10,
  search: params.search,
  sortBy: params.sortBy,
  sortOrder: params.sortOrder,
  startDate: params.dateRange?.start,
  endDate: params.dateRange?.end,
})

// Utility function to handle file uploads
export const createFormDataQuery = (data: Record<string, any>) => {
  const formData = new FormData()
  
  Object.entries(data).forEach(([key, value]) => {
    if (value instanceof File || value instanceof Blob) {
      formData.append(key, value)
    } else if (Array.isArray(value)) {
      value.forEach((item, index) => {
        if (item instanceof File || item instanceof Blob) {
          formData.append(`${key}[${index}]`, item)
        } else {
          formData.append(`${key}[${index}]`, JSON.stringify(item))
        }
      })
    } else if (typeof value === 'object' && value !== null) {
      formData.append(key, JSON.stringify(value))
    } else {
      formData.append(key, String(value))
    }
  })
  
  return formData
}

// Export common types and utilities
// Types exported in individual API files
