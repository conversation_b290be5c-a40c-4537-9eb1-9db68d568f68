exports.id=447,exports.ids=[447],exports.modules={10241:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var a=s(40969);s(73356);var r=s(12011),o=s(48567),i=s(61631);function l({children:e}){(0,o.j)(),(0,o.G)(i.Kc);let t=(0,o.G)(i.H$),s=(0,r.usePathname)();return t&&"/login"!==s?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-sky-50 via-white to-sky-100",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-sky-600 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Checking authentication..."})]})}):(0,a.jsx)(a.Fragment,{children:e})}},29459:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,89896,23)),Promise.resolve().then(s.t.bind(s,65958,23)),Promise.resolve().then(s.t.bind(s,5958,23)),Promise.resolve().then(s.t.bind(s,45545,23)),Promise.resolve().then(s.t.bind(s,55021,23)),Promise.resolve().then(s.t.bind(s,85621,23)),Promise.resolve().then(s.t.bind(s,16605,23)),Promise.resolve().then(s.t.bind(s,72007,23))},41669:()=>{},43619:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\components\\\\auth\\\\AuthCheck.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\components\\auth\\AuthCheck.tsx","default")},48567:(e,t,s)=>{"use strict";s.d(t,{G:()=>o,j:()=>r});var a=s(29079);let r=()=>(0,a.wA)(),o=a.d4},53019:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,26390,23)),Promise.resolve().then(s.t.bind(s,23904,23)),Promise.resolve().then(s.t.bind(s,13172,23)),Promise.resolve().then(s.t.bind(s,81351,23)),Promise.resolve().then(s.t.bind(s,62735,23)),Promise.resolve().then(s.t.bind(s,82471,23)),Promise.resolve().then(s.t.bind(s,51287,23)),Promise.resolve().then(s.t.bind(s,20737,23))},53412:(e,t,s)=>{"use strict";s.d(t,{q:()=>n});var a=s(99553),r=s(82845);class o{static getCookie(e){if("undefined"==typeof document)return null;let t=`; ${document.cookie}`.split(`; ${e}=`);if(2===t.length){let e=t.pop()?.split(";").shift();return e?decodeURIComponent(e):null}return null}static setCookie(e,t,s={}){if("undefined"==typeof document)return;let a=`${e}=${encodeURIComponent(t)}`;s.expires&&("string"==typeof s.expires?a+=`; expires=${s.expires}`:a+=`; expires=${s.expires.toUTCString()}`),s.maxAge&&(a+=`; max-age=${s.maxAge}`),s.path?a+=`; path=${s.path}`:a+="; path=/",s.domain&&(a+=`; domain=${s.domain}`),s.secure&&(a+="; secure"),s.sameSite&&(a+=`; samesite=${s.sameSite}`),document.cookie=a}static deleteCookie(e,t={}){this.setCookie(e,"",{...t,expires:new Date(0)})}static hasCookie(e){return null!==this.getCookie(e)}static getAllCookies(){if("undefined"==typeof document)return{};let e={};return document.cookie.split(";").forEach(t=>{let[s,a]=t.trim().split("=");s&&a&&(e[s]=decodeURIComponent(a))}),e}static getAuthToken(){return this.getCookie("accessToken")||this.getCookie("token")}static getRefreshToken(){return this.getCookie("refreshToken")}static isAuthenticated(){return this.hasCookie("accessToken")||this.hasCookie("token")}static clearAuthCookies(){this.deleteCookie("accessToken"),this.deleteCookie("refreshToken"),this.deleteCookie("token")}static getRedirectUrl(){return this.getCookie("redirectTo")}static setRedirectUrl(e){this.setCookie("redirectTo",e,{maxAge:300,sameSite:"lax"})}static clearRedirectUrl(){this.deleteCookie("redirectTo")}static parseCookieString(e){let t={};return e.split(";").forEach(e=>{let[s,a]=e.trim().split("=");s&&a&&(t[s]=decodeURIComponent(a))}),t}static setSessionCookie(e,t,s={}){this.setCookie(e,t,{...s,expires:void 0,maxAge:void 0})}static setPersistentCookie(e,t,s=7,a={}){let r=new Date;r.setDate(r.getDate()+s),this.setCookie(e,t,{...a,expires:r})}}let i=(0,a.cw)({baseUrl:"http://localhost:5000/api",credentials:"include",prepareHeaders:(e,{getState:t})=>{let s=null;return s=t()?.auth?.token,s&&e.set("authorization",`Bearer ${s}`),e.set("content-type","application/json"),e.set("accept","application/json"),e.set("x-client-type","sales-dashboard"),e.set("x-client-version","1.0.0"),e}}),l=async(e,t,s)=>{let a=await i(e,t,s);if(a.error&&401===a.error.status){console.log("Access token expired, attempting refresh...");let r=o.getRefreshToken();if(r){let o=await i({url:"/auth/refresh",method:"POST",body:{refreshToken:r}},t,s);if(o.data){let{accessToken:r}=o.data.data;a=await i(e,t,s)}}}return a},n=(0,r.xP)({reducerPath:"api",baseQuery:l,tagTypes:["User","Lead","Customer","Property","Commission","Report","Team","Task","Calendar","Notification","Dashboard"],endpoints:()=>({})})},61631:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>k,H$:()=>f,Kc:()=>y,PK:()=>n,ZB:()=>b,_v:()=>o,mB:()=>g});var a=s(98571);let r={setCurrentUser:e=>{},clearCurrentUser:()=>{}},o=(0,a.zD)("auth/login",async(e,{rejectWithValue:t})=>{try{console.log("\uD83D\uDD10 Sales backend login attempt:",e.email);let s=await fetch("http://localhost:5000/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Client-Type":"sales-dashboard","X-Client-Version":"1.0.0"},credentials:"include",body:JSON.stringify(e)}),a=await s.json();if(!s.ok)throw Error(a.message||"Login failed");console.log("✅ Sales: Backend login response:",a);let{user:o,accessToken:i,refreshToken:l}=a.data||a;if(!o||!i)return t("Invalid response format");try{return r.setCurrentUser(o),console.log("✅ Sales login successful, backend set HttpOnly cookies:",{user:o.email,role:o.role,note:"Tokens stored in HttpOnly cookies (not accessible from JS)"}),{user:o,accessToken:i,refreshToken:l}}catch(e){return console.error("Sales user storage failed:",e),t("Failed to store user data")}}catch(e){return console.error("Sales login error:",e),t(e.message||"Login failed")}}),i=(0,a.zD)("auth/logout",async()=>{try{await fetch("http://localhost:5000/api/auth/logout",{method:"POST",headers:{"Content-Type":"application/json","X-Client-Type":"sales-dashboard"},credentials:"include"})}catch(e){console.error("Sales logout API error:",e)}finally{r.clearCurrentUser()}}),l=(0,a.zD)("auth/refreshUser",async(e,{rejectWithValue:t})=>{try{let e=await fetch("http://localhost:5000/api/auth/profile",{method:"GET",headers:{"Content-Type":"application/json","X-Client-Type":"sales-dashboard"},credentials:"include"}),t=await e.json();if(!e.ok)throw Error(t.message||"Failed to get profile");let s=t.data||t;return r.setCurrentUser(s),s}catch(e){return t(e.message||"Failed to refresh user")}}),n=(0,a.zD)("auth/checkAuth",async(e,{rejectWithValue:t})=>{try{console.log("\uD83D\uDD0D Sales: Checking auth with backend (HttpOnly cookies)...");let e=await fetch("http://localhost:5000/api/auth/check",{method:"GET",headers:{"Content-Type":"application/json","X-Client-Type":"sales-dashboard"},credentials:"include"}),s=await e.json();if(console.log("✅ Sales: Backend auth response:",s),e.ok&&s.success&&s.data)return r.setCurrentUser(s.data),s.data;return console.log("❌ Sales: Backend auth failed:",s.message),r.clearCurrentUser(),t(s.message||"Authentication failed")}catch(e){return console.error("❌ Sales: Auth check error:",e.message),r.clearCurrentUser(),t(e.message||"Authentication check failed")}}),d=(0,a.Z0)({name:"auth",initialState:{user:null,isAuthenticated:!1,loading:!1,error:null,sessionExpiry:null},reducers:{clearError:e=>{e.error=null},setUser:(e,t)=>{e.user=t.payload,e.isAuthenticated=!0,r.setCurrentUser(t.payload)},clearAuth:e=>{e.user=null,e.isAuthenticated=!1,e.error=null,e.sessionExpiry=null,r.clearCurrentUser()},setSessionExpiry:(e,t)=>{e.sessionExpiry=t.payload},updateUserProfile:(e,t)=>{e.user&&(e.user={...e.user,...t.payload},r.setCurrentUser(e.user))}},extraReducers:e=>{e.addCase(o.pending,e=>{e.loading=!0,e.error=null}).addCase(o.fulfilled,(e,t)=>{e.loading=!1,e.user=t.payload.user||t.payload,e.isAuthenticated=!0,e.error=null}).addCase(o.rejected,(e,t)=>{e.loading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null}),e.addCase(i.pending,e=>{e.loading=!0}).addCase(i.fulfilled,e=>{e.loading=!1,e.user=null,e.isAuthenticated=!1,e.error=null,e.sessionExpiry=null}).addCase(i.rejected,e=>{e.loading=!1,e.user=null,e.isAuthenticated=!1,e.error=null,e.sessionExpiry=null}),e.addCase(l.pending,e=>{e.loading=!0}).addCase(l.fulfilled,(e,t)=>{e.loading=!1,e.user=t.payload.user||t.payload,e.error=null}).addCase(l.rejected,(e,t)=>{e.loading=!1,e.error=t.payload}),e.addCase(n.pending,e=>{e.loading=!0}).addCase(n.fulfilled,(e,t)=>{e.loading=!1,e.user=t.payload.user||t.payload,e.isAuthenticated=!0,e.error=null}).addCase(n.rejected,(e,t)=>{e.loading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null})}}),{clearError:c,setUser:u,clearAuth:p,setSessionExpiry:h,updateUserProfile:m}=d.actions,g=e=>e.auth.user,y=e=>e.auth.isAuthenticated,f=e=>e.auth.loading,b=e=>e.auth.error,k=d.reducer},65152:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\store\\\\Provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\store\\Provider.tsx","default")},70038:(e,t,s)=>{"use strict";s.d(t,{default:()=>Q});var a=s(40969),r=s(29079),o=s(22387),i=s(98571),l=s(99553),n=s(60890),d=s(402),c=s(18534),u=s(53412),p=s(61631);let h={filters:{dateRange:{startDate:new Date(Date.now()-2592e6).toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0],preset:"month"}},view:{layout:"grid",widgets:[{id:"stats-overview",type:"stats",position:{x:0,y:0,w:12,h:2},visible:!0},{id:"leads-chart",type:"chart",position:{x:0,y:2,w:6,h:4},visible:!0},{id:"recent-activities",type:"table",position:{x:6,y:2,w:6,h:4},visible:!0},{id:"upcoming-tasks",type:"tasks",position:{x:0,y:6,w:4,h:3},visible:!0},{id:"calendar-events",type:"calendar",position:{x:4,y:6,w:4,h:3},visible:!0},{id:"notifications",type:"notifications",position:{x:8,y:6,w:4,h:3},visible:!0}],refreshInterval:300,autoRefresh:!0},quickActions:{recentLeads:[],recentCustomers:[],recentProperties:[],bookmarkedReports:[],frequentTasks:[]},isLoading:!1,lastUpdated:null,notifications:{show:!0,count:0},sidebarCollapsed:!1,theme:"light"},m=(0,i.Z0)({name:"dashboard",initialState:h,reducers:{setFilters:(e,t)=>{e.filters={...e.filters,...t.payload}},setDateRange:(e,t)=>{e.filters.dateRange=t.payload},updateWidget:(e,t)=>{let{id:s,updates:a}=t.payload,r=e.view.widgets.findIndex(e=>e.id===s);-1!==r&&(e.view.widgets[r]={...e.view.widgets[r],...a})},toggleWidget:(e,t)=>{let s=e.view.widgets.findIndex(e=>e.id===t.payload);-1!==s&&(e.view.widgets[s].visible=!e.view.widgets[s].visible)},reorderWidgets:(e,t)=>{e.view.widgets=t.payload},setLayout:(e,t)=>{e.view.layout=t.payload},setAutoRefresh:(e,t)=>{e.view.autoRefresh=t.payload},setRefreshInterval:(e,t)=>{e.view.refreshInterval=t.payload},addToQuickActions:(e,t)=>{let{type:s,id:a}=t.payload;!e.quickActions[s].includes(a)&&(e.quickActions[s].unshift(a),e.quickActions[s].length>10&&(e.quickActions[s]=e.quickActions[s].slice(0,10)))},removeFromQuickActions:(e,t)=>{let{type:s,id:a}=t.payload;e.quickActions[s]=e.quickActions[s].filter(e=>e!==a)},setLoading:(e,t)=>{e.isLoading=t.payload},setLastUpdated:(e,t)=>{e.lastUpdated=t.payload},setNotificationCount:(e,t)=>{e.notifications.count=t.payload},toggleNotifications:e=>{e.notifications.show=!e.notifications.show},toggleSidebar:e=>{e.sidebarCollapsed=!e.sidebarCollapsed},setSidebarCollapsed:(e,t)=>{e.sidebarCollapsed=t.payload},setTheme:(e,t)=>{e.theme=t.payload},resetDashboard:e=>({...h,theme:e.theme}),saveDashboardLayout:(e,t)=>{e.view=t.payload},loadDashboardLayout:(e,t)=>{e.view=t.payload}}}),{setFilters:g,setDateRange:y,updateWidget:f,toggleWidget:b,reorderWidgets:k,setLayout:v,setAutoRefresh:C,setRefreshInterval:w,addToQuickActions:x,removeFromQuickActions:S,setLoading:A,setLastUpdated:T,setNotificationCount:P,toggleNotifications:j,toggleSidebar:R,setSidebarCollapsed:U,setTheme:D,resetDashboard:E,saveDashboardLayout:I,loadDashboardLayout:q}=m.actions,O=m.reducer;var $=s(95003);let L={key:"sales-root",storage:d.A,whitelist:["auth","dashboard","ui"],blacklist:[u.q.reducerPath]},B=(0,c.HY)({auth:p.Ay,dashboard:O,ui:$.Ay,[u.q.reducerPath]:u.q.reducer}),F=(0,n.rL)(L,B),N=(0,i.U1)({reducer:F,middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE","persist/PAUSE","persist/PURGE","persist/REGISTER"]}}).concat(u.q.middleware),devTools:!1}),G=(0,n.GM)(N);(0,l.$k)(N.dispatch);var H=s(14493);let M=()=>(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(H.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading Sales Dashboard..."})]})});function Q({children:e}){return(0,a.jsx)(r.Kq,{store:N,children:(0,a.jsx)(o.Q,{loading:(0,a.jsx)(M,{}),persistor:G,children:e})})}},90255:(e,t,s)=>{Promise.resolve().then(s.bind(s,26759)),Promise.resolve().then(s.bind(s,43619)),Promise.resolve().then(s.bind(s,65152))},92603:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>d});var a=s(59355),r=s(4798),o=s.n(r);s(41669);var i=s(65152),l=s(26759),n=s(43619);let d={title:"SGM Sales Portal - Property Sales Dashboard",description:"Professional sales dashboard for SGM property management and lead tracking"};function c({children:e}){return(0,a.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,a.jsx)("body",{className:`${o().variable} font-sans antialiased`,children:(0,a.jsxs)(i.default,{children:[(0,a.jsx)(n.default,{children:e}),(0,a.jsx)(l.Toaster,{position:"top-right",toastOptions:{duration:4e3,style:{background:"white",color:"black",border:"1px solid #e5e7eb"}}})]})})})}},95003:(e,t,s)=>{"use strict";s.d(t,{$U:()=>c,Ay:()=>F,Oo:()=>l,qf:()=>i});var a=s(98571);let r={modals:[],toasts:[],loading:{global:!1,components:{}},tables:{},search:{query:"",filters:[],suggestions:[],recentSearches:[],isSearching:!1},sidebar:{isOpen:!0,activeSection:null},breadcrumbs:[],pageTitle:"",pageSubtitle:void 0,quickFilters:{},bulkActions:{selectedItems:[],availableActions:[]},preferences:{density:"comfortable",animations:!0,soundEffects:!1,autoSave:!0,confirmActions:!0}},o=(0,a.Z0)({name:"ui",initialState:r,reducers:{openModal:(e,t)=>{let s=e.modals.find(e=>e.id===t.payload.id);s?(s.isOpen=!0,s.data=t.payload.data):e.modals.push({...t.payload,isOpen:!0})},closeModal:(e,t)=>{let s=e.modals.find(e=>e.id===t.payload);s&&(s.isOpen=!1)},closeAllModals:e=>{e.modals.forEach(e=>{e.isOpen=!1})},updateModalData:(e,t)=>{let s=e.modals.find(e=>e.id===t.payload.id);s&&(s.data=t.payload.data)},addToast:(e,t)=>{let s=Date.now().toString();e.toasts.push({id:s,...t.payload})},removeToast:(e,t)=>{e.toasts=e.toasts.filter(e=>e.id!==t.payload)},clearToasts:e=>{e.toasts=[]},setGlobalLoading:(e,t)=>{e.loading.global=t.payload},setComponentLoading:(e,t)=>{e.loading.components[t.payload.component]=t.payload.loading},setTableState:(e,t)=>{let{tableId:s,state:a}=t.payload;e.tables[s]={...e.tables[s],...a}},selectTableRows:(e,t)=>{let{tableId:s,rowIds:a}=t.payload;e.tables[s]||(e.tables[s]={selectedRows:[],filters:[],sort:null,pagination:{page:1,limit:10,total:0},view:"table",columns:[]}),e.tables[s].selectedRows=a},toggleTableRow:(e,t)=>{let{tableId:s,rowId:a}=t.payload;if(!e.tables[s])return;let r=e.tables[s].selectedRows,o=r.indexOf(a);o>-1?r.splice(o,1):r.push(a)},clearTableSelection:(e,t)=>{e.tables[t.payload]&&(e.tables[t.payload].selectedRows=[])},setSearchQuery:(e,t)=>{e.search.query=t.payload},setSearchFilters:(e,t)=>{e.search.filters=t.payload},addSearchFilter:(e,t)=>{let s=e.search.filters.findIndex(e=>e.id===t.payload.id);s>-1?e.search.filters[s]=t.payload:e.search.filters.push(t.payload)},removeSearchFilter:(e,t)=>{e.search.filters=e.search.filters.filter(e=>e.id!==t.payload)},setSearchSuggestions:(e,t)=>{e.search.suggestions=t.payload},addRecentSearch:(e,t)=>{let s=t.payload.trim();s&&!e.search.recentSearches.includes(s)&&(e.search.recentSearches.unshift(s),e.search.recentSearches.length>10&&(e.search.recentSearches=e.search.recentSearches.slice(0,10)))},clearRecentSearches:e=>{e.search.recentSearches=[]},setSearching:(e,t)=>{e.search.isSearching=t.payload},setSidebarOpen:(e,t)=>{e.sidebar.isOpen=t.payload},setActiveSection:(e,t)=>{e.sidebar.activeSection=t.payload},setBreadcrumbs:(e,t)=>{e.breadcrumbs=t.payload},setPageTitle:(e,t)=>{e.pageTitle=t.payload},setPageSubtitle:(e,t)=>{e.pageSubtitle=t.payload},setQuickFilter:(e,t)=>{e.quickFilters[t.payload.key]=t.payload.value},clearQuickFilters:e=>{e.quickFilters={}},setBulkSelectedItems:(e,t)=>{e.bulkActions.selectedItems=t.payload},setBulkAvailableActions:(e,t)=>{e.bulkActions.availableActions=t.payload},clearBulkSelection:e=>{e.bulkActions.selectedItems=[]},setPreferences:(e,t)=>{e.preferences={...e.preferences,...t.payload}},resetUI:e=>({...r,preferences:e.preferences})}}),{openModal:i,closeModal:l,closeAllModals:n,updateModalData:d,addToast:c,removeToast:u,clearToasts:p,setGlobalLoading:h,setComponentLoading:m,setTableState:g,selectTableRows:y,toggleTableRow:f,clearTableSelection:b,setSearchQuery:k,setSearchFilters:v,addSearchFilter:C,removeSearchFilter:w,setSearchSuggestions:x,addRecentSearch:S,clearRecentSearches:A,setSearching:T,setSidebarOpen:P,setActiveSection:j,setBreadcrumbs:R,setPageTitle:U,setPageSubtitle:D,setQuickFilter:E,clearQuickFilters:I,setBulkSelectedItems:q,setBulkAvailableActions:O,clearBulkSelection:$,setPreferences:L,resetUI:B}=o.actions,F=o.reducer},98503:(e,t,s)=>{Promise.resolve().then(s.bind(s,42605)),Promise.resolve().then(s.bind(s,10241)),Promise.resolve().then(s.bind(s,70038))}};