(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{893:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>N});var a=s(9605),t=s(9585),n=s(5935),i=s(6762),l=s.n(i),o=s(8063),d=s(2933),c=s(7971),m=s(5097),u=s(8595),f=s(4252),x=s(3299),h=s(8232),p=s(1468),g=s(5857),v=s(3480);function N(){let[e,r]=(0,t.useState)({firstName:"",lastName:"",email:"",phone:"",password:"",confirmPassword:""}),[s,i]=(0,t.useState)(!1);(0,n.useRouter)();let N=e=>{r(r=>({...r,[e.target.name]:e.target.value}))},y=async r=>{if(r.preventDefault(),!e.firstName||!e.lastName||!e.email||!e.password)return void u.o.error("Please fill in all required fields");if(e.password!==e.confirmPassword)return void u.o.error("Passwords do not match");if(e.password.length<6)return void u.o.error("Password must be at least 6 characters long");i(!0);try{u.o.info("Signup functionality will be implemented soon. Please contact your administrator for account creation.")}catch(e){u.o.error(e.message||"Signup failed")}finally{i(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)(f.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"BuilderFarm"})]}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-700",children:"Sales Portal"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Create your sales account"})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),"Create Account"]}),(0,a.jsx)(o.BT,{children:"Join the sales team and start managing leads"})]}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"firstName",children:"First Name"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(h.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,a.jsx)(c.p,{id:"firstName",name:"firstName",type:"text",placeholder:"John",value:e.firstName,onChange:N,className:"pl-10",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"lastName",children:"Last Name"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(h.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,a.jsx)(c.p,{id:"lastName",name:"lastName",type:"text",placeholder:"Doe",value:e.lastName,onChange:N,className:"pl-10",required:!0})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"email",children:"Email"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,a.jsx)(c.p,{id:"email",name:"email",type:"email",placeholder:"<EMAIL>",value:e.email,onChange:N,className:"pl-10",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"phone",children:"Phone (Optional)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,a.jsx)(c.p,{id:"phone",name:"phone",type:"tel",placeholder:"+****************",value:e.phone,onChange:N,className:"pl-10"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"password",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,a.jsx)(c.p,{id:"password",name:"password",type:"password",placeholder:"Enter your password",value:e.password,onChange:N,className:"pl-10",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,a.jsx)(c.p,{id:"confirmPassword",name:"confirmPassword",type:"password",placeholder:"Confirm your password",value:e.confirmPassword,onChange:N,className:"pl-10",required:!0})]})]}),(0,a.jsx)(d.$,{type:"submit",className:"w-full h-11 bg-blue-600 hover:bg-blue-700",disabled:s,children:s?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Creating Account..."]}):"\uD83D\uDE80 Create Account"})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,a.jsx)(l(),{href:"/login",className:"text-blue-600 hover:text-blue-700 font-medium",children:"Sign in here"})]})})]})]}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("p",{className:"text-sm text-blue-700",children:[(0,a.jsx)("strong",{children:"Note:"})," Account creation requires administrator approval. Contact your system administrator if you need immediate access."]})})]})})}},2933:(e,r,s)=>{"use strict";s.d(r,{$:()=>d});var a=s(9605),t=s(9585),n=s(8130),i=s(7276),l=s(6994);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=t.forwardRef((e,r)=>{let{className:s,variant:t,size:i,asChild:d=!1,...c}=e,m=d?n.DX:"button";return(0,a.jsx)(m,{className:(0,l.cn)(o({variant:t,size:i,className:s})),ref:r,...c})});d.displayName="Button"},3299:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(6501).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},3480:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(6501).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},5077:(e,r,s)=>{Promise.resolve().then(s.bind(s,893))},5097:(e,r,s)=>{"use strict";s.d(r,{J:()=>d});var a=s(9605),t=s(9585),n=s(8436),i=s(7276),l=s(6994);let o=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(n.b,{ref:r,className:(0,l.cn)(o(),s),...t})});d.displayName=n.b.displayName},5935:(e,r,s)=>{"use strict";var a=s(5383);s.o(a,"usePathname")&&s.d(r,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(r,{useRouter:function(){return a.useRouter}})},6994:(e,r,s)=>{"use strict";s.d(r,{Yq:()=>o,ZV:()=>l,cn:()=>n,r6:()=>d,tP:()=>c,vv:()=>i});var a=s(8330),t=s(398);function n(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,t.QP)((0,a.$)(r))}function i(e){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}function l(e){return new Intl.NumberFormat("en-IN").format(e)}function o(e){if(!e)return"N/A";let r=new Date(e);return isNaN(r.getTime())?"Invalid Date":new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric"}).format(r)}function d(e){if(!e)return"N/A";let r=new Date(e);return isNaN(r.getTime())?"Invalid Date":new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(r)}function c(e,r){return 0===r?100*(e>0):(e-r)/r*100}},7971:(e,r,s)=>{"use strict";s.d(r,{p:()=>i});var a=s(9605),t=s(9585),n=s(6994);let i=t.forwardRef((e,r)=>{let{className:s,type:t,...i}=e;return(0,a.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:r,...i})});i.displayName="Input"},8063:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>l});var a=s(9605),t=s(9585),n=s(6994);let i=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...t})});i.displayName="Card";let l=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...t})});l.displayName="CardHeader";let o=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...t})});o.displayName="CardTitle";let d=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",s),...t})});d.displayName="CardDescription";let c=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",s),...t})});c.displayName="CardContent",t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",s),...t})}).displayName="CardFooter"}},e=>{var r=r=>e(e.s=r);e.O(0,[995,668,595,390,110,358],()=>r(5077)),_N_E=e.O()}]);