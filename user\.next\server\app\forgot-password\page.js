(()=>{var e={};e.id=2162,e.ids=[2162],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4942:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(62544);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13348:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\forgot-password\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24689:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},26806:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},28496:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40002:(e,t,s)=>{"use strict";s.d(t,{B3:()=>l,Lv:()=>u,Ng:()=>i,_L:()=>r,ac:()=>x,ge:()=>a,nd:()=>d,pv:()=>f,tl:()=>c,uU:()=>n});let{useLoginMutation:r,useRegisterMutation:a,useLogoutMutation:i,useRefreshTokenMutation:o,useVerifyEmailMutation:n,useResendVerificationEmailMutation:l,useForgotPasswordMutation:d,useVerifyPasswordResetOTPMutation:c,useResetPasswordMutation:u,useChangePasswordMutation:m,useVerifyPhoneMutation:h,useSendPhoneOTPMutation:p,useSendEmailOTPMutation:x,useVerifyEmailOTPMutation:f,useGetCurrentUserQuery:g,useGetUserProfileQuery:y,useCheckAuthStatusQuery:b}=s(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({login:e.mutation({query:e=>({url:"/auth/login",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),register:e.mutation({query:e=>({url:"/auth/register",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),logout:e.mutation({query:()=>({url:"/auth/logout",method:"POST"}),invalidatesTags:["Auth","User"]}),refreshToken:e.mutation({query:e=>({url:"/auth/refresh",method:"POST",body:e})}),verifyEmail:e.mutation({query:e=>({url:"/auth/verify-email",method:"POST",body:e}),invalidatesTags:["User"]}),resendVerificationEmail:e.mutation({query:e=>({url:"/auth/send-verification",method:"POST",body:e})}),forgotPassword:e.mutation({query:e=>({url:"/auth/forgot-password",method:"POST",body:e})}),verifyPasswordResetOTP:e.mutation({query:e=>({url:"/auth/verify-reset-otp",method:"POST",body:e})}),resetPassword:e.mutation({query:e=>({url:"/auth/reset-password",method:"POST",body:e})}),changePassword:e.mutation({query:e=>({url:"/auth/change-password",method:"POST",body:e}),invalidatesTags:["User"]}),verifyPhone:e.mutation({query:e=>({url:"/auth/verify-phone",method:"POST",body:e}),invalidatesTags:["User"]}),sendPhoneOTP:e.mutation({query:e=>({url:"/auth/send-phone-otp",method:"POST",body:e})}),sendEmailOTP:e.mutation({query:e=>({url:"/auth/send-otp",method:"POST",body:e})}),verifyEmailOTP:e.mutation({query:e=>({url:"/auth/verify-otp",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),getCurrentUser:e.query({query:()=>"/auth/me",providesTags:["User"]}),checkAuthStatus:e.query({query:()=>"/auth/status",providesTags:["Auth"]}),getUserProfile:e.query({query:()=>"/auth/profile",providesTags:["User","Auth"]})})})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},81117:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(10557),a=s(68490),i=s(13172),o=s.n(i),n=s(68835),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,13348)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\forgot-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\forgot-password\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},82846:(e,t,s)=>{Promise.resolve().then(s.bind(s,88014))},87574:(e,t,s)=>{Promise.resolve().then(s.bind(s,13348))},88014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(40969),a=s(73356),i=s(27092),o=s.n(i),n=s(12011),l=s(58121),d=s(66782),c=s(65406),u=s(24689),m=s(63980),h=s(5825),p=s(15423),x=s(28496),f=s(26806),g=s(46411),y=s(66949),b=s(40002),v=s(1507);let j=c.Ik({email:c.Yj().email("Please enter a valid email address")}),w=c.Ik({otp:c.Yj().min(6,"OTP must be 6 digits").max(6,"OTP must be 6 digits")});function N(){let e=(0,n.useRouter)(),[t,s]=(0,a.useState)("email"),[i,c]=(0,a.useState)(""),[N,P]=(0,a.useState)(0),[T,{isLoading:S}]=(0,b.nd)(),[k,{isLoading:A}]=(0,b.tl)(),q=(0,l.mN)({resolver:(0,d.u)(j)}),C=(0,l.mN)({resolver:(0,d.u)(w)}),O=async e=>{try{(await T(e).unwrap()).success&&(c(e.email),s("otp"),P(60),v.toast.success("Password reset code sent to your email!"))}catch(e){e.data?.error==="PASSWORD_RESET_DISABLED"?v.toast.error("Password reset is currently disabled. Please contact support for assistance."):v.toast.error(e.data?.message||"Failed to send reset code")}},E=async t=>{try{let s=await k({email:i,otp:t.otp}).unwrap();s.success&&s.data?.resetToken&&(sessionStorage.setItem("resetToken",s.data.resetToken),e.push("/reset-password"),v.toast.success("Code verified! Redirecting to reset password..."))}catch(e){v.toast.error(e.data?.message||"Invalid or expired code")}},R=async()=>{if(!(N>0))try{(await T({email:i}).unwrap()).success&&(P(60),v.toast.success("New code sent to your email!"))}catch(e){v.toast.error(e.data?.message||"Failed to resend code")}};return"otp"===t?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,r.jsx)(u.A,{className:"h-12 w-12 text-blue-600"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"PropertyInvest"})]}),(0,r.jsxs)(y.Zp,{className:"shadow-xl border-0",children:[(0,r.jsxs)(y.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(m.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsx)(y.ZB,{className:"text-2xl font-bold",children:"Enter Verification Code"}),(0,r.jsxs)(y.BT,{children:["We've sent a 6-digit code to ",i]})]}),(0,r.jsxs)(y.Wu,{className:"space-y-6",children:[(0,r.jsxs)("form",{onSubmit:C.handleSubmit(E),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"otp",className:"text-sm font-medium text-gray-700",children:"Verification Code"}),(0,r.jsx)("input",{...C.register("otp"),type:"text",id:"otp",placeholder:"Enter 6-digit code",maxLength:6,className:"w-full px-4 py-3 text-center text-2xl font-mono border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent tracking-widest"}),C.formState.errors.otp&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:C.formState.errors.otp.message})]}),(0,r.jsx)(g.$,{type:"submit",disabled:A,className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors",children:A?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Verifying..."]}):"Verify Code"})]}),(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:"bg-amber-50 p-4 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 text-amber-600 mr-2"}),(0,r.jsx)("p",{className:"text-sm text-amber-700",children:"Code expires in 15 minutes"})]})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(g.$,{onClick:R,disabled:N>0||S,className:"w-full",variant:"outline",children:S?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending..."]}):N>0?`Resend in ${N}s`:"Resend Code"}),(0,r.jsxs)(g.$,{onClick:()=>s("email"),variant:"outline",className:"w-full",children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Change Email"]})]})]})]})]})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,r.jsx)(u.A,{className:"h-12 w-12 text-blue-600"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"PropertyInvest"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Reset your password"})]}),(0,r.jsxs)(y.Zp,{className:"shadow-xl border-0",children:[(0,r.jsxs)(y.aR,{className:"space-y-1",children:[(0,r.jsx)(y.ZB,{className:"text-2xl font-bold text-center",children:"Forgot Password?"}),(0,r.jsx)(y.BT,{className:"text-center",children:"Enter your email address and we'll send you a verification code to reset your password"})]}),(0,r.jsxs)(y.Wu,{children:[(0,r.jsxs)("form",{onSubmit:q.handleSubmit(O),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...q.register("email"),type:"email",id:"email",placeholder:"Enter your email address",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),q.formState.errors.email&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:q.formState.errors.email.message})]}),(0,r.jsx)(g.$,{type:"submit",disabled:S,className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors",children:S?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending code..."]}):"Send Verification Code"})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)(o(),{href:"/login",className:"text-blue-600 hover:text-blue-500 font-medium flex items-center justify-center",children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Back to Login"]})})]})]}),(0,r.jsx)("div",{className:"mt-8 text-center text-sm text-gray-600",children:(0,r.jsxs)("p",{children:["Remember your password?"," ",(0,r.jsx)(o(),{href:"/login",className:"text-blue-600 hover:text-blue-500",children:"Sign in here"})]})})]})})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[755,3777,2544,7092,3035,2487],()=>s(81117));module.exports=r})();