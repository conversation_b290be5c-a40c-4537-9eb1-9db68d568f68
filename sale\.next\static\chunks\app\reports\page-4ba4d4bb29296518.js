(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[22],{192:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(6501).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},636:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(6501).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},783:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(6501).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},3317:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>M});var r=a(9605),t=a(9585),l=a(6440),d=a(8063),i=a(2933),n=a(2790),c=a(9577),o=a(783),m=a(192),x=a(636),u=a(6351),h=a(9581),y=a(6901),g=a(5048),p=a(4842),v=a(6501);let j=(0,v.A)("chart-line",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]]);var N=a(7418);let b=(0,v.A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);var f=a(6994);let{useGetPerformanceMetricsQuery:w,useGetLeadAnalyticsQuery:T,useGetCommissionAnalyticsQuery:q,useGetCustomerAnalyticsQuery:A,useGetSalesFunnelAnalysisQuery:R,useGetRevenueAnalysisQuery:k,useGetActivityReportQuery:L,useGetComparativeAnalysisQuery:C,useGenerateCustomReportMutation:D,useGetSalesReportsQuery:S,useGetReportByIdQuery:Z,useDownloadReportMutation:_,useDeleteReportMutation:P,useGetDashboardAnalyticsQuery:E}=a(6701).q.injectEndpoints({endpoints:e=>({getPerformanceMetrics:e.query({query:e=>({url:"/sales/reports/performance",params:e}),providesTags:["Report"]}),getLeadAnalytics:e.query({query:e=>({url:"/sales/reports/leads",params:e}),providesTags:["Report","Lead"]}),getCommissionAnalytics:e.query({query:e=>({url:"/sales/reports/commission",params:e}),providesTags:["Report","Commission"]}),getCustomerAnalytics:e.query({query:e=>({url:"/sales/reports/customers",params:e}),providesTags:["Report","Customer"]}),getSalesFunnelAnalysis:e.query({query:e=>({url:"/sales/reports/funnel-analysis",params:e}),providesTags:["Report"]}),getRevenueAnalysis:e.query({query:e=>({url:"/sales/reports/revenue",params:e}),providesTags:["Report"]}),getActivityReport:e.query({query:e=>({url:"/sales/reports/activities",params:e}),providesTags:["Report"]}),getComparativeAnalysis:e.query({query:e=>({url:"/sales/reports/comparative",params:e}),providesTags:["Report"]}),generateCustomReport:e.mutation({query:e=>({url:"/sales/reports/generate",method:"POST",body:e}),invalidatesTags:["Report"]}),getSalesReports:e.query({query:e=>({url:"/sales/reports",params:e}),providesTags:["Report"]}),getReportById:e.query({query:e=>"/sales/reports/".concat(e),providesTags:(e,s,a)=>[{type:"Report",id:a}]}),downloadReport:e.mutation({query:e=>({url:"/sales/reports/".concat(e,"/download"),method:"GET",responseHandler:e=>e.blob()})}),deleteReport:e.mutation({query:e=>({url:"/sales/reports/".concat(e),method:"DELETE"}),invalidatesTags:["Report"]}),getDashboardAnalytics:e.query({query:()=>"/sales/reports/dashboard",providesTags:["Report","Dashboard"]})})});var B=a(8642);function M(){var e,s,a,v,w,q,A,R,L,C,D,S,Z,_;let[P,M]=(0,t.useState)("monthly"),[W,O]=(0,t.useState)("last_6_months"),{data:U,isLoading:F,error:G}=E(),{data:z,isLoading:I}=k({period:P,salesRepId:void 0}),{data:V,isLoading:Q}=T({period:P,salesRepId:void 0}),{data:$,isLoading:H}=(0,B.WD)({page:1,limit:10});if(F||I||Q||H)return(0,r.jsx)(l.A,{title:"Reports & Analytics",children:(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)(o.A,{className:"w-8 h-8 animate-spin text-sky-500"})})})});if(G)return(0,r.jsx)(l.A,{title:"Reports & Analytics",children:(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(m.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Failed to load reports"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Please try refreshing the page"}),(0,r.jsx)(i.$,{className:"bg-sky-500 hover:bg-sky-600",children:"Try Again"})]})})})});let Y=null==U?void 0:U.data,K=null==z?void 0:z.data,J=null==V?void 0:V.data,X=null==$||null==(e=$.data)?void 0:e.targets;return(0,r.jsx)(l.A,{title:"Reports & Analytics",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Reports & Analytics"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Comprehensive sales performance and analytics dashboard"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(c.l6,{value:P,onValueChange:M,children:[(0,r.jsx)(c.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"daily",children:"Daily"}),(0,r.jsx)(c.eb,{value:"weekly",children:"Weekly"}),(0,r.jsx)(c.eb,{value:"monthly",children:"Monthly"}),(0,r.jsx)(c.eb,{value:"quarterly",children:"Quarterly"}),(0,r.jsx)(c.eb,{value:"yearly",children:"Yearly"})]})]}),(0,r.jsxs)(c.l6,{value:W,onValueChange:O,children:[(0,r.jsx)(c.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"last_7_days",children:"Last 7 Days"}),(0,r.jsx)(c.eb,{value:"last_30_days",children:"Last 30 Days"}),(0,r.jsx)(c.eb,{value:"last_3_months",children:"Last 3 Months"}),(0,r.jsx)(c.eb,{value:"last_6_months",children:"Last 6 Months"}),(0,r.jsx)(c.eb,{value:"last_year",children:"Last Year"})]})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"border-green-300 text-green-600 hover:bg-green-50",children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Export"]}),(0,r.jsxs)(i.$,{className:"bg-sky-500 hover:bg-sky-600 text-white",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Generate Report"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-sky-100 rounded-lg",children:(0,r.jsx)(h.A,{className:"w-6 h-6 text-sky-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,f.vv)((null==Y||null==(s=Y.overview)?void 0:s.totalRevenue)||(null==K?void 0:K.totalRevenue)||0)}),(0,r.jsxs)("p",{className:"text-xs text-green-600 mt-1",children:["+",(null==Y||null==(a=Y.overview)?void 0:a.revenueGrowth)||(null==K?void 0:K.revenueGrowth)||0,"% from last period"]})]})]})})}),(0,r.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,r.jsx)(y.A,{className:"w-6 h-6 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Leads"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,f.ZV)((null==Y||null==(v=Y.overview)?void 0:v.totalLeads)||(null==J?void 0:J.totalLeads)||0)}),(0,r.jsxs)("p",{className:"text-xs text-green-600 mt-1",children:["+",(null==Y||null==(w=Y.overview)?void 0:w.leadsGrowth)||(null==J?void 0:J.leadsGrowth)||0,"% from last period"]})]})]})})}),(0,r.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,r.jsx)(g.A,{className:"w-6 h-6 text-yellow-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Conversion Rate"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[((null==Y||null==(q=Y.overview)?void 0:q.conversionRate)||(null==J?void 0:J.conversionRate)||0).toFixed(1),"%"]}),(0,r.jsxs)("p",{className:"text-xs text-green-600 mt-1",children:["Qualified: ",(0,f.ZV)((null==J?void 0:J.qualifiedLeads)||0)]})]})]})})}),(0,r.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,r.jsx)(p.A,{className:"w-6 h-6 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg Deal Size"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,f.vv)((null==Y||null==(A=Y.overview)?void 0:A.averageDealSize)||0)}),(0,r.jsxs)("p",{className:"text-xs text-sky-600 mt-1",children:["Target Achievement: ",(null==X||null==(L=X.currentTargets)||null==(R=L[0])?void 0:R.percentage)||0,"%"]})]})]})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,r.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)(d.ZB,{className:"flex items-center text-gray-900",children:[(0,r.jsx)(j,{className:"w-5 h-5 mr-2 text-sky-500"}),"Revenue Trend"]})}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"h-64 flex items-center justify-center bg-gradient-to-br from-sky-50 to-sky-100 rounded-lg",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(N.A,{className:"w-16 h-16 text-sky-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Revenue chart visualization"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:[(null==K||null==(C=K.revenueByMonth)?void 0:C.length)||0," data points available"]})]})})})]}),(0,r.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,r.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)(d.ZB,{className:"flex items-center text-gray-900",children:[(0,r.jsx)(N.A,{className:"w-5 h-5 mr-2 text-green-500"}),"Leads Analysis"]})}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"h-64 flex items-center justify-center bg-gradient-to-br from-green-50 to-green-100 rounded-lg",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(y.A,{className:"w-16 h-16 text-green-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Leads conversion analysis"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:[(null==J||null==(D=J.leadsByMonth)?void 0:D.length)||0," monthly records"]})]})})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,r.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)(d.ZB,{className:"flex items-center text-gray-900",children:[(0,r.jsx)(b,{className:"w-5 h-5 mr-2 text-yellow-500"}),"Lead Sources"]})}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"space-y-3",children:(null==J||null==(S=J.leadsBySource)?void 0:S.slice(0,5).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full mr-3 ".concat(0===s?"bg-sky-500":1===s?"bg-green-500":2===s?"bg-yellow-500":3===s?"bg-purple-500":"bg-gray-500")}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:e.source})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.count}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[e.percentage,"%"]})]})]},e.source)))||(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(b,{className:"w-12 h-12 text-gray-300 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No source data available"})]})})})]}),(0,r.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,r.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)(d.ZB,{className:"flex items-center text-gray-900",children:[(0,r.jsx)(N.A,{className:"w-5 h-5 mr-2 text-purple-500"}),"Revenue by Type"]})}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"space-y-3",children:(null==K||null==(Z=K.revenueByProperty)?void 0:Z.slice(0,4).map((e,s)=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-700",children:e.propertyType}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:(0,f.vv)(e.revenue)})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full ".concat(0===s?"bg-sky-500":1===s?"bg-green-500":2===s?"bg-yellow-500":"bg-purple-500"),style:{width:"".concat(e.percentage,"%")}})})]},e.propertyType)))||(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(N.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No property data available"})]})})})]}),(0,r.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,r.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)(d.ZB,{className:"flex items-center text-gray-900",children:[(0,r.jsx)(p.A,{className:"w-5 h-5 mr-2 text-green-500"}),"Top Performers"]})}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"space-y-3",children:(null==Y||null==(_=Y.topPerformers)?void 0:_.slice(0,5).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold mr-3 ".concat(0===s?"bg-yellow-500":1===s?"bg-gray-400":2===s?"bg-yellow-600":"bg-gray-300"),children:s+1}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[e.deals," deals"]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(0,f.vv)(e.revenue)}),(0,r.jsxs)("div",{className:"text-xs text-green-600",children:[e.conversionRate,"% rate"]})]})]},e.salesRepId)))||(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(p.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No performance data available"})]})})})]})]}),(null==X?void 0:X.currentTargets)&&X.currentTargets.length>0&&(0,r.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,r.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)(d.ZB,{className:"flex items-center text-gray-900",children:[(0,r.jsx)(p.A,{className:"w-5 h-5 mr-2 text-sky-500"}),"Target Achievement"]})}),(0,r.jsx)(d.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:X.currentTargets.map(e=>(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,r.jsx)(n.E,{className:"text-xs ".concat("exceeded"===e.status?"bg-green-100 text-green-800":"on_track"===e.status?"bg-sky-100 text-sky-800":"bg-red-100 text-red-800"),children:e.status.replace("_"," ").toUpperCase()})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Progress"}),(0,r.jsxs)("span",{className:"font-medium",children:[e.percentage,"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(e.percentage>=100?"bg-green-500":e.percentage>=75?"bg-sky-500":e.percentage>=50?"bg-yellow-500":"bg-red-500"),style:{width:"".concat(Math.min(e.percentage,100),"%")}})}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,r.jsx)("span",{children:(0,f.vv)(e.achievedAmount)}),(0,r.jsx)("span",{children:(0,f.vv)(e.targetAmount)})]})]})]},e.id))})})]})]})})}},8582:(e,s,a)=>{Promise.resolve().then(a.bind(a,3317))},8642:(e,s,a)=>{"use strict";a.d(s,{$V:()=>i,FO:()=>c,Hu:()=>l,OT:()=>A,Pb:()=>t,WD:()=>v,Zu:()=>N,aT:()=>p,cm:()=>j,nK:()=>u,pv:()=>q,ro:()=>b});let r=a(6701).q.injectEndpoints({endpoints:e=>({getDashboardStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getDashboardActivities:e.query({query:e=>({url:"/dashboard/activities",params:e}),providesTags:["Dashboard"]}),getSalesStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getLeads:e.query({query:e=>({url:"/sales/leads",params:e}),providesTags:["Lead"]}),getLeadById:e.query({query:e=>"/sales/leads/".concat(e),providesTags:(e,s,a)=>[{type:"Lead",id:a}]}),createLead:e.mutation({query:e=>({url:"/sales/leads",method:"POST",body:e}),invalidatesTags:["Lead","Dashboard"]}),updateLead:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/sales/leads/".concat(s),method:"PUT",body:a}},invalidatesTags:(e,s,a)=>{let{id:r}=a;return[{type:"Lead",id:r},"Lead","Dashboard"]}}),deleteLead:e.mutation({query:e=>({url:"/sales/leads/".concat(e),method:"DELETE"}),invalidatesTags:["Lead","Dashboard"]}),assignLead:e.mutation({query:e=>{let{leadId:s,salesRepId:a}=e;return{url:"/sales/leads/".concat(s,"/assign"),method:"POST",body:{salesRepId:a}}},invalidatesTags:["Lead"]}),getCustomers:e.query({query:e=>({url:"/sales/customers",params:e}),providesTags:["Customer"]}),getCustomerById:e.query({query:e=>"/sales/customers/".concat(e),providesTags:(e,s,a)=>[{type:"Customer",id:a}]}),createCustomer:e.mutation({query:e=>({url:"/sales/customers",method:"POST",body:e}),invalidatesTags:["Customer","Dashboard"]}),updateCustomer:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/sales/customers/".concat(s),method:"PUT",body:a}},invalidatesTags:(e,s,a)=>{let{id:r}=a;return[{type:"Customer",id:r},"Customer"]}}),getCommissions:e.query({query:e=>({url:"/sales/commissions",params:e}),providesTags:["Commission"]}),getSalesTargets:e.query({query:()=>"/sales/targets",providesTags:["Report"]}),createSalesTarget:e.mutation({query:e=>({url:"/sales/targets",method:"POST",body:e}),invalidatesTags:["Report","Dashboard"]}),updateSalesTarget:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/sales/targets/".concat(s),method:"PUT",body:a}},invalidatesTags:["Report","Dashboard"]}),getFollowUps:e.query({query:e=>({url:"/follow-ups",params:e}),providesTags:["Task"]}),createFollowUp:e.mutation({query:e=>({url:"/follow-ups",method:"POST",body:e}),invalidatesTags:["Task","Dashboard"]}),updateFollowUp:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/follow-ups/".concat(s),method:"PUT",body:a}},invalidatesTags:["Task","Dashboard"]}),deleteFollowUp:e.mutation({query:e=>({url:"/follow-ups/".concat(e),method:"DELETE"}),invalidatesTags:["Task","Dashboard"]})})}),{useGetDashboardStatsQuery:t,useGetDashboardActivitiesQuery:l,useGetSalesStatsQuery:d,useGetLeadsQuery:i,useGetLeadByIdQuery:n,useCreateLeadMutation:c,useUpdateLeadMutation:o,useDeleteLeadMutation:m,useAssignLeadMutation:x,useGetCustomersQuery:u,useGetCustomerByIdQuery:h,useCreateCustomerMutation:y,useUpdateCustomerMutation:g,useGetCommissionsQuery:p,useGetSalesTargetsQuery:v,useCreateSalesTargetMutation:j,useUpdateSalesTargetMutation:N,useGetFollowUpsQuery:b,useCreateFollowUpMutation:f,useUpdateFollowUpMutation:w,useDeleteFollowUpMutation:T}=r,{useGetCustomersQuery:q,useGetCommissionsQuery:A}=r}},e=>{var s=s=>e(e.s=s);e.O(0,[399,995,713,668,663,75,440,390,110,358],()=>s(8582)),_N_E=e.O()}]);