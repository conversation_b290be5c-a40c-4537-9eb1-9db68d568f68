(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662],{2266:(e,s,l)=>{"use strict";l.d(s,{A:()=>r});let r=(0,l(6501).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},4046:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>N});var r=l(9605),i=l(9585),a=l(6440),d=l(8063),c=l(2933),n=l(7971),t=l(5097),x=l(2790),h=l(9577),m=l(8232),j=l(6466),o=l(2266),u=l(6501);let p=(0,u.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]);var y=l(4842);let f=(0,u.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),v=(0,u.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);function N(){let[e,s]=(0,i.useState)("profile"),l=[{id:"profile",label:"Profile",icon:m.A},{id:"notifications",label:"Notifications",icon:j.A},{id:"security",label:"Security",icon:o.A},{id:"preferences",label:"Preferences",icon:p},{id:"targets",label:"Targets",icon:y.A}];return(0,r.jsx)(a.A,{title:"Settings",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Settings"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage your account settings and preferences"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsx)(d.Zp,{children:(0,r.jsx)(d.Wu,{className:"p-4",children:(0,r.jsx)("nav",{className:"space-y-1",children:l.map(l=>(0,r.jsxs)("button",{onClick:()=>s(l.id),className:"w-full flex items-center space-x-3 px-3 py-2 text-left rounded-lg transition-colors ".concat(e===l.id?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)(l.icon,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:l.label})]},l.id))})})})}),(0,r.jsxs)("div",{className:"lg:col-span-3",children:["profile"===e&&(0,r.jsxs)(d.Zp,{children:[(0,r.jsx)(d.aR,{children:(0,r.jsx)(d.ZB,{children:"Profile Information"})}),(0,r.jsxs)(d.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(m.A,{className:"w-8 h-8 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(f,{className:"w-4 h-4 mr-2"}),"Change Photo"]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"JPG, PNG up to 2MB"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"firstName",children:"First Name"}),(0,r.jsx)(n.p,{id:"firstName",defaultValue:"John"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"lastName",children:"Last Name"}),(0,r.jsx)(n.p,{id:"lastName",defaultValue:"Doe"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"email",children:"Email"}),(0,r.jsx)(n.p,{id:"email",type:"email",defaultValue:"<EMAIL>"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"phone",children:"Phone"}),(0,r.jsx)(n.p,{id:"phone",defaultValue:"+91 98765 43210"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"department",children:"Department"}),(0,r.jsxs)(h.l6,{defaultValue:"sales",children:[(0,r.jsx)(h.bq,{children:(0,r.jsx)(h.yv,{})}),(0,r.jsxs)(h.gC,{children:[(0,r.jsx)(h.eb,{value:"sales",children:"Sales"}),(0,r.jsx)(h.eb,{value:"marketing",children:"Marketing"}),(0,r.jsx)(h.eb,{value:"support",children:"Support"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"territory",children:"Territory"}),(0,r.jsxs)(h.l6,{defaultValue:"mumbai",children:[(0,r.jsx)(h.bq,{children:(0,r.jsx)(h.yv,{})}),(0,r.jsxs)(h.gC,{children:[(0,r.jsx)(h.eb,{value:"mumbai",children:"Mumbai"}),(0,r.jsx)(h.eb,{value:"delhi",children:"Delhi"}),(0,r.jsx)(h.eb,{value:"bangalore",children:"Bangalore"})]})]})]})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsxs)(c.$,{children:[(0,r.jsx)(v,{className:"w-4 h-4 mr-2"}),"Save Changes"]})})]})]}),"notifications"===e&&(0,r.jsxs)(d.Zp,{children:[(0,r.jsx)(d.aR,{children:(0,r.jsx)(d.ZB,{children:"Notification Preferences"})}),(0,r.jsxs)(d.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Email Notifications"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Receive notifications via email"})]}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Push Notifications"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Receive push notifications in browser"})]}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"SMS Notifications"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Receive important updates via SMS"})]}),(0,r.jsx)("input",{type:"checkbox",className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Lead Assignments"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Get notified when new leads are assigned"})]}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Follow-up Reminders"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Reminders for scheduled follow-ups"})]}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsxs)(c.$,{children:[(0,r.jsx)(v,{className:"w-4 h-4 mr-2"}),"Save Preferences"]})})]})]}),"security"===e&&(0,r.jsxs)(d.Zp,{children:[(0,r.jsx)(d.aR,{children:(0,r.jsx)(d.ZB,{children:"Security Settings"})}),(0,r.jsxs)(d.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,r.jsx)(n.p,{id:"currentPassword",type:"password"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"newPassword",children:"New Password"}),(0,r.jsx)(n.p,{id:"newPassword",type:"password"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,r.jsx)(n.p,{id:"confirmPassword",type:"password"})]})]}),(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h4",{className:"font-medium mb-4",children:"Two-Factor Authentication"}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"font-medium",children:"Enable 2FA"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Add an extra layer of security to your account"})]}),(0,r.jsx)(x.E,{variant:"outline",children:"Disabled"})]})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsxs)(c.$,{children:[(0,r.jsx)(v,{className:"w-4 h-4 mr-2"}),"Update Security"]})})]})]}),"preferences"===e&&(0,r.jsxs)(d.Zp,{children:[(0,r.jsx)(d.aR,{children:(0,r.jsx)(d.ZB,{children:"Preferences"})}),(0,r.jsxs)(d.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"language",children:"Language"}),(0,r.jsxs)(h.l6,{defaultValue:"en",children:[(0,r.jsx)(h.bq,{children:(0,r.jsx)(h.yv,{})}),(0,r.jsxs)(h.gC,{children:[(0,r.jsx)(h.eb,{value:"en",children:"English"}),(0,r.jsx)(h.eb,{value:"hi",children:"Hindi"}),(0,r.jsx)(h.eb,{value:"mr",children:"Marathi"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"timezone",children:"Timezone"}),(0,r.jsxs)(h.l6,{defaultValue:"ist",children:[(0,r.jsx)(h.bq,{children:(0,r.jsx)(h.yv,{})}),(0,r.jsxs)(h.gC,{children:[(0,r.jsx)(h.eb,{value:"ist",children:"IST (UTC+5:30)"}),(0,r.jsx)(h.eb,{value:"utc",children:"UTC"}),(0,r.jsx)(h.eb,{value:"est",children:"EST (UTC-5)"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"currency",children:"Currency"}),(0,r.jsxs)(h.l6,{defaultValue:"inr",children:[(0,r.jsx)(h.bq,{children:(0,r.jsx)(h.yv,{})}),(0,r.jsxs)(h.gC,{children:[(0,r.jsx)(h.eb,{value:"inr",children:"INR (₹)"}),(0,r.jsx)(h.eb,{value:"usd",children:"USD ($)"}),(0,r.jsx)(h.eb,{value:"eur",children:"EUR (€)"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"dateFormat",children:"Date Format"}),(0,r.jsxs)(h.l6,{defaultValue:"dd/mm/yyyy",children:[(0,r.jsx)(h.bq,{children:(0,r.jsx)(h.yv,{})}),(0,r.jsxs)(h.gC,{children:[(0,r.jsx)(h.eb,{value:"dd/mm/yyyy",children:"DD/MM/YYYY"}),(0,r.jsx)(h.eb,{value:"mm/dd/yyyy",children:"MM/DD/YYYY"}),(0,r.jsx)(h.eb,{value:"yyyy-mm-dd",children:"YYYY-MM-DD"})]})]})]})]}),(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h4",{className:"font-medium mb-4",children:"Dashboard Preferences"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Show welcome message"}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Auto-refresh dashboard"}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Compact view"}),(0,r.jsx)("input",{type:"checkbox",className:"rounded"})]})]})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsxs)(c.$,{children:[(0,r.jsx)(v,{className:"w-4 h-4 mr-2"}),"Save Preferences"]})})]})]}),"targets"===e&&(0,r.jsxs)(d.Zp,{children:[(0,r.jsx)(d.aR,{children:(0,r.jsx)(d.ZB,{children:"Target Settings"})}),(0,r.jsxs)(d.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"monthlySalesTarget",children:"Monthly Sales Target"}),(0,r.jsx)(n.p,{id:"monthlySalesTarget",type:"number",defaultValue:"25"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"monthlyRevenueTarget",children:"Monthly Revenue Target"}),(0,r.jsx)(n.p,{id:"monthlyRevenueTarget",type:"number",defaultValue:"3000000"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"monthlyLeadsTarget",children:"Monthly Leads Target"}),(0,r.jsx)(n.p,{id:"monthlyLeadsTarget",type:"number",defaultValue:"100"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.J,{htmlFor:"commissionRate",children:"Commission Rate (%)"}),(0,r.jsx)(n.p,{id:"commissionRate",type:"number",defaultValue:"5",step:"0.1"})]})]}),(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h4",{className:"font-medium mb-4",children:"Reminder Settings"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Daily target reminders"}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Weekly progress reports"}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Monthly target alerts"}),(0,r.jsx)("input",{type:"checkbox",className:"rounded"})]})]})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsxs)(c.$,{children:[(0,r.jsx)(v,{className:"w-4 h-4 mr-2"}),"Save Target Settings"]})})]})]})]})]})]})})}},4368:(e,s,l)=>{Promise.resolve().then(l.bind(l,4046))}},e=>{var s=s=>e(e.s=s);e.O(0,[399,995,713,668,663,75,440,390,110,358],()=>s(4368)),_N_E=e.O()}]);