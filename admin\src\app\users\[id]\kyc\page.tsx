'use client'

import React, { useState } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  ArrowLeft, 
  FileText, 
  Download, 
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  User,

  AlertTriangle,
  Image as ImageIcon
} from 'lucide-react'
import {
  useGetUserKYCQuery,
  useGetUserDetailsQuery
} from '@/store/api/adminUserApi'
import { useUpdateKYCStatusMutation } from '@/store/api/adminKycApi'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { formatDate, formatTime } from '@/lib/utils'

export default function UserKYCDetailPage() {
  const params = useParams()
  const router = useRouter()
  const userId = params?.id as string
  
  const [kycStatus, setKycStatus] = useState('')
  const [rejectionReason, setRejectionReason] = useState('')

  const {
    data: userResponse,
    isLoading: isLoadingUser
  } = useGetUserDetailsQuery(userId)

  const {
    data: kycData,
    isLoading: isLoadingKYC,
    refetch: refetchKYC
  } = useGetUserKYCQuery(userId)

  const [updateKYCStatus, { isLoading: isUpdating }] = useUpdateKYCStatusMutation()

  const user = userResponse?.user || kycData?.user

  React.useEffect(() => {
    if (kycData?.kyc) {
      setKycStatus(kycData.kyc.status)
    }
  }, [kycData])

  const handleStatusUpdate = async () => {
    if (!kycStatus) {
      toast.error('Please select a status')
      return
    }

    try {
      await updateKYCStatus({
        id: userId, // Use userId since KYC object doesn't have _id
        data: {
          status: kycStatus as 'approved' | 'rejected',
          rejectionReason: kycStatus === 'rejected' ? rejectionReason : undefined,
          level: kycStatus === 'approved' ? 'basic' : undefined
        }
      }).unwrap()

      toast.success(`KYC status updated to ${kycStatus}`)
      refetchKYC()
    } catch (error: any) {
      console.error('Error updating KYC status:', error)
      toast.error(error?.data?.message || 'Failed to update KYC status')
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
      case 'under_review':
        return <Badge className="bg-blue-100 text-blue-800"><Clock className="h-3 w-3 mr-1" />Under Review</Badge>
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>
      case 'not_submitted':
        return <Badge className="bg-gray-100 text-gray-800"><AlertTriangle className="h-3 w-3 mr-1" />Not Submitted</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'aadhar':
      case 'pan':
      case 'passport':
      case 'drivers_license':
        return <FileText className="h-5 w-5" />
      case 'selfie':
        return <ImageIcon className="h-5 w-5" />
      case 'digital_signature':
        return <FileText className="h-5 w-5" />
      default:
        return <FileText className="h-5 w-5" />
    }
  }

  const getDocumentTypeLabel = (type: string) => {
    const labels = {
      aadhar: 'Aadhar Card',
      pan: 'PAN Card',
      passport: 'Passport',
      drivers_license: 'Driver\'s License',
      selfie: 'Selfie',
      digital_signature: 'Digital Signature'
    }
    return labels[type as keyof typeof labels] || type
  }

  const getCategoryBadge = (category: string) => {
    const colors = {
      identity: 'bg-blue-100 text-blue-800',
      address: 'bg-green-100 text-green-800',
      additional: 'bg-purple-100 text-purple-800'
    }
    return <Badge className={colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>{category}</Badge>
  }

  if (isLoadingUser || isLoadingKYC) {
    return (
      <DashboardLayout>
        <div className="max-w-6xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="space-y-6">
              <div className="h-48 bg-gray-200 rounded"></div>
              <div className="h-96 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!user || !kycData) {
    return (
      <DashboardLayout>
        <div className="max-w-6xl mx-auto">
          <Button
            variant="ghost"
            onClick={() => router.push('/users')}
            className="mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Button>
          <Card>
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">KYC Data Not Found</h2>
              <p className="text-gray-600">The KYC information for this user is not available.</p>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => router.push('/users')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Users
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">KYC Details</h1>
              <p className="text-gray-600">{user?.firstName} {user?.lastName} ({user?.email})</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {getStatusBadge(kycData?.kyc?.status || 'not_submitted')}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* KYC Documents */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    KYC Documents ({kycData?.kyc?.documents?.length || 0})
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {kycData?.kyc?.documents && kycData.kyc.documents.length > 0 ? (
                  <div className="space-y-4">
                    {kycData.kyc.documents.map((document: any, index: number) => (
                      <div
                        key={index}
                        className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="p-2 bg-blue-100 rounded-lg">
                              {getDocumentIcon(document.type)}
                            </div>
                            <div>
                              <h3 className="font-medium">{getDocumentTypeLabel(document.type)}</h3>
                              <div className="flex items-center space-x-2 mt-1">
                                {getCategoryBadge(document.category)}
                                {document.documentNumber && (
                                  <span className="text-sm text-gray-600">
                                    #{document.documentNumber}
                                  </span>
                                )}
                              </div>
                              <p className="text-xs text-gray-500 mt-1">
                                Uploaded: {formatDate(document.uploadedAt)} {formatTime(document.uploadedAt)}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(document.documentUrl, '_blank')}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const link = document.createElement('a')
                                link.href = document.documentUrl
                                link.download = `${document.type}_${document.documentNumber || 'document'}`
                                link.click()
                              }}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Download
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Documents Uploaded</h3>
                    <p className="text-gray-600">The user hasn't uploaded any KYC documents yet.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* KYC Actions */}
          <div className="space-y-6">
            {/* User Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  User Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold">
                      {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium">{user?.firstName} {user?.lastName}</p>
                    <p className="text-sm text-gray-600">User</p>
                  </div>
                </div>
                <div className="space-y-2 text-sm">
                  <p><strong>Email:</strong> {user?.email}</p>
                  <p><strong>Phone:</strong> {user?.phone}</p>
                  <p><strong>Status:</strong> Active</p>
                  <p><strong>User ID:</strong> {user?._id}</p>
                </div>
              </CardContent>
            </Card>

            {/* KYC Status Update */}
            <Card>
              <CardHeader>
                <CardTitle>Update KYC Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Status</label>
                  <Select value={kycStatus} onValueChange={setKycStatus}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="under_review">Under Review</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {kycStatus === 'rejected' && (
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">Rejection Reason</label>
                    <Textarea
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                      placeholder="Please provide a reason for rejection..."
                      rows={3}
                    />
                  </div>
                )}

                <Button
                  onClick={handleStatusUpdate}
                  disabled={isUpdating}
                  className="w-full"
                >
                  {isUpdating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Updating...
                    </>
                  ) : (
                    'Update Status'
                  )}
                </Button>

                <div className="pt-4 border-t">
                  <div className="text-sm text-gray-600 space-y-1">
                    <p><strong>Current Status:</strong> {kycData?.kyc?.status || 'not_submitted'}</p>
                    <p><strong>Verification Level:</strong> {kycData?.kyc?.verificationLevel || 'none'}</p>
                    <p><strong>Last Updated:</strong> {kycData?.kyc?.updatedAt ? formatDate(kycData.kyc.updatedAt) + ' ' + formatTime(kycData.kyc.updatedAt) : 'Never'}</p>
                    <p><strong>Documents:</strong> {kycData?.kyc?.documents?.length || 0} uploaded</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
