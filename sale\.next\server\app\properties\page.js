(()=>{var e={};e.id=754,e.ids=[754],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13800:(e,s,t)=>{Promise.resolve().then(t.bind(t,80242))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20571:(e,s,t)=>{"use strict";t.d(s,{Cl:()=>m,af:()=>b,dy:()=>h,zq:()=>r});let{useGetPropertiesQuery:r,useGetPropertyByIdQuery:a,useGetMyPropertiesQuery:l,useAssignPropertyToSalesRepMutation:i,useGetPropertyInquiriesQuery:o,useCreatePropertyInquiryMutation:n,useUpdatePropertyInquiryMutation:c,useGetPropertyAnalyticsQuery:d,useSearchPropertiesQuery:p,useGetPropertyTypesQuery:m,useGetPropertyLocationsQuery:h,useAddToWatchlistMutation:x,useRemoveFromWatchlistMutation:u,useGetWatchlistQuery:y,useComparePropertiesQuery:g,useGetPropertyStockQuery:v,useGetPropertiesWithStocksQuery:b}=t(53412).q.injectEndpoints({endpoints:e=>({getProperties:e.query({query:e=>({url:"/sales/properties",params:e}),providesTags:["Property"]}),getPropertyById:e.query({query:e=>`/sales/properties/${e}`,providesTags:(e,s,t)=>[{type:"Property",id:t}]}),getMyProperties:e.query({query:()=>"/sales/properties/my-properties",providesTags:["Property"]}),assignPropertyToSalesRep:e.mutation({query:({propertyId:e,salesRepId:s})=>({url:`/sales/properties/${e}/assign`,method:"POST",body:{salesRepId:s}}),invalidatesTags:["Property"]}),getPropertyInquiries:e.query({query:e=>({url:"/sales/property-inquiries",params:e}),providesTags:["Property"]}),createPropertyInquiry:e.mutation({query:e=>({url:"/sales/property-inquiries",method:"POST",body:e}),invalidatesTags:["Property","Lead"]}),updatePropertyInquiry:e.mutation({query:({id:e,data:s})=>({url:`/sales/property-inquiries/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:t})=>[{type:"Property",id:t},"Property"]}),getPropertyAnalytics:e.query({query:e=>({url:"/sales/properties/analytics",params:e}),providesTags:["Property","Dashboard"]}),searchProperties:e.query({query:({query:e,filters:s})=>({url:"/sales/properties/search",params:{query:e,...s}}),providesTags:["Property"]}),getPropertyTypes:e.query({query:()=>"/sales/properties/types",providesTags:["Property"]}),getPropertyLocations:e.query({query:()=>"/sales/properties/locations",providesTags:["Property"]}),addToWatchlist:e.mutation({query:e=>({url:`/sales/properties/${e}/watchlist`,method:"POST"}),invalidatesTags:["Property"]}),removeFromWatchlist:e.mutation({query:e=>({url:`/sales/properties/${e}/watchlist`,method:"DELETE"}),invalidatesTags:["Property"]}),getWatchlist:e.query({query:()=>"/sales/properties/watchlist",providesTags:["Property"]}),compareProperties:e.query({query:e=>({url:"/sales/properties/compare",method:"POST",body:{propertyIds:e}}),providesTags:["Property"]}),getPropertyStock:e.query({query:e=>`/sales/properties/${e}/stock`,providesTags:(e,s,t)=>[{type:"Property",id:`${t}-stock`}]}),getPropertiesWithStocks:e.query({query:e=>({url:"/sales/properties/with-stocks",params:e}),providesTags:["Property"]})})})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31435:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(98085).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33873:e=>{"use strict";e.exports=require("path")},54236:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eK});var r,a=t(40969),l=t(73356),i=t(88251),o=t(66949),n=t(46411),c=t(57387),d=t(76650),p=t(2223);let m={data:""},h=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||m,x=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,u=/\/\*[^]*?\*\/|  +/g,y=/\n+/g,g=(e,s)=>{let t="",r="",a="";for(let l in e){let i=e[l];"@"==l[0]?"i"==l[1]?t=l+" "+i+";":r+="f"==l[1]?g(i,l):l+"{"+g(i,"k"==l[1]?"":s)+"}":"object"==typeof i?r+=g(i,s?s.replace(/([^,])+/g,e=>l.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,s=>/&/.test(s)?s.replace(/&/g,e):e?e+" "+s:s)):l):null!=i&&(l=/^--/.test(l)?l:l.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=g.p?g.p(l,i):l+":"+i+";")}return t+(s&&a?s+"{"+a+"}":a)+r},v={},b=e=>{if("object"==typeof e){let s="";for(let t in e)s+=t+b(e[t]);return s}return e},f=(e,s,t,r,a)=>{let l=b(e),i=v[l]||(v[l]=(e=>{let s=0,t=11;for(;s<e.length;)t=101*t+e.charCodeAt(s++)>>>0;return"go"+t})(l));if(!v[i]){let s=l!==e?e:(e=>{let s,t,r=[{}];for(;s=x.exec(e.replace(u,""));)s[4]?r.shift():s[3]?(t=s[3].replace(y," ").trim(),r.unshift(r[0][t]=r[0][t]||{})):r[0][s[1]]=s[2].replace(y," ").trim();return r[0]})(e);v[i]=g(a?{["@keyframes "+i]:s}:s,t?"":"."+i)}let o=t&&v.g?v.g:null;return t&&(v.g=v[i]),((e,s,t,r)=>{r?s.data=s.data.replace(r,e):-1===s.data.indexOf(e)&&(s.data=t?e+s.data:s.data+e)})(v[i],s,r,o),i},j=(e,s,t)=>e.reduce((e,r,a)=>{let l=s[a];if(l&&l.call){let e=l(t),s=e&&e.props&&e.props.className||/^go/.test(e)&&e;l=s?"."+s:e&&"object"==typeof e?e.props?"":g(e,""):!1===e?"":e}return e+r+(null==l?"":l)},"");function N(e){let s=this||{},t=e.call?e(s.p):e;return f(t.unshift?t.raw?j(t,[].slice.call(arguments,1),s.p):t.reduce((e,t)=>Object.assign(e,t&&t.call?t(s.p):t),{}):t,h(s.target),s.g,s.o,s.k)}N.bind({g:1});let k,w,P,A=N.bind({k:1});function C(e,s){let t=this||{};return function(){let r=arguments;function a(l,i){let o=Object.assign({},l),n=o.className||a.className;t.p=Object.assign({theme:w&&w()},o),t.o=/ *go\d+/.test(n),o.className=N.apply(t,r)+(n?" "+n:""),s&&(o.ref=i);let c=e;return e[0]&&(c=o.as||e,delete o.as),P&&c[0]&&P(o),k(c,o)}return s?s(a):a}}var q=e=>"function"==typeof e,M=(e,s)=>q(e)?e(s):e,T=(()=>{let e=0;return()=>(++e).toString()})(),S=(()=>{let e;return()=>e})(),$=(e,s)=>{switch(s.type){case 0:return{...e,toasts:[s.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case 2:let{toast:t}=s;return $(e,{type:+!!e.toasts.find(e=>e.id===t.id),toast:t});case 3:let{toastId:r}=s;return{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===s.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)};case 5:return{...e,pausedAt:s.time};case 6:let a=s.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},D=[],E={toasts:[],pausedAt:void 0},R=e=>{E=$(E,e),D.forEach(e=>{e(E)})},z={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},_=(e={})=>{let[s,t]=(0,l.useState)(E),r=(0,l.useRef)(E);(0,l.useEffect)(()=>(r.current!==E&&t(E),D.push(t),()=>{let e=D.indexOf(t);e>-1&&D.splice(e,1)}),[]);let a=s.toasts.map(s=>{var t,r,a;return{...e,...e[s.type],...s,removeDelay:s.removeDelay||(null==(t=e[s.type])?void 0:t.removeDelay)||(null==e?void 0:e.removeDelay),duration:s.duration||(null==(r=e[s.type])?void 0:r.duration)||(null==e?void 0:e.duration)||z[s.type],style:{...e.style,...null==(a=e[s.type])?void 0:a.style,...s.style}}});return{...s,toasts:a}},L=(e,s="blank",t)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:s,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...t,id:(null==t?void 0:t.id)||T()}),F=e=>(s,t)=>{let r=L(s,e,t);return R({type:2,toast:r}),r.id},O=(e,s)=>F("blank")(e,s);O.error=F("error"),O.success=F("success"),O.loading=F("loading"),O.custom=F("custom"),O.dismiss=e=>{R({type:3,toastId:e})},O.remove=e=>R({type:4,toastId:e}),O.promise=(e,s,t)=>{let r=O.loading(s.loading,{...t,...null==t?void 0:t.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=s.success?M(s.success,e):void 0;return a?O.success(a,{id:r,...t,...null==t?void 0:t.success}):O.dismiss(r),e}).catch(e=>{let a=s.error?M(s.error,e):void 0;a?O.error(a,{id:r,...t,...null==t?void 0:t.error}):O.dismiss(r)}),e};var I=(e,s)=>{R({type:1,toast:{id:e,height:s}})},H=()=>{R({type:5,time:Date.now()})},Z=new Map,B=1e3,V=(e,s=B)=>{if(Z.has(e))return;let t=setTimeout(()=>{Z.delete(e),R({type:4,toastId:e})},s);Z.set(e,t)},W=e=>{let{toasts:s,pausedAt:t}=_(e);(0,l.useEffect)(()=>{if(t)return;let e=Date.now(),r=s.map(s=>{if(s.duration===1/0)return;let t=(s.duration||0)+s.pauseDuration-(e-s.createdAt);if(t<0){s.visible&&O.dismiss(s.id);return}return setTimeout(()=>O.dismiss(s.id),t)});return()=>{r.forEach(e=>e&&clearTimeout(e))}},[s,t]);let r=(0,l.useCallback)(()=>{t&&R({type:6,time:Date.now()})},[t]),a=(0,l.useCallback)((e,t)=>{let{reverseOrder:r=!1,gutter:a=8,defaultPosition:l}=t||{},i=s.filter(s=>(s.position||l)===(e.position||l)&&s.height),o=i.findIndex(s=>s.id===e.id),n=i.filter((e,s)=>s<o&&e.visible).length;return i.filter(e=>e.visible).slice(...r?[n+1]:[0,n]).reduce((e,s)=>e+(s.height||0)+a,0)},[s]);return(0,l.useEffect)(()=>{s.forEach(e=>{if(e.dismissed)V(e.id,e.removeDelay);else{let s=Z.get(e.id);s&&(clearTimeout(s),Z.delete(e.id))}})},[s]),{toasts:s,handlers:{updateHeight:I,startPause:H,endPause:r,calculateOffset:a}}},U=A`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,G=A`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Q=A`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,K=C("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${U} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${G} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Q} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,X=A`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Y=C("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${X} 1s linear infinite;
`,J=A`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,ee=A`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,es=C("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${J} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${ee} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,et=C("div")`
  position: absolute;
`,er=C("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,ea=A`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,el=C("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${ea} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,ei=({toast:e})=>{let{icon:s,type:t,iconTheme:r}=e;return void 0!==s?"string"==typeof s?l.createElement(el,null,s):s:"blank"===t?null:l.createElement(er,null,l.createElement(Y,{...r}),"loading"!==t&&l.createElement(et,null,"error"===t?l.createElement(K,{...r}):l.createElement(es,{...r})))},eo=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,en=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ec=C("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,ed=C("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ep=(e,s)=>{let t=e.includes("top")?1:-1,[r,a]=S()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[eo(t),en(t)];return{animation:s?`${A(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${A(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},em=l.memo(({toast:e,position:s,style:t,children:r})=>{let a=e.height?ep(e.position||s||"top-center",e.visible):{opacity:0},i=l.createElement(ei,{toast:e}),o=l.createElement(ed,{...e.ariaProps},M(e.message,e));return l.createElement(ec,{className:e.className,style:{...a,...t,...e.style}},"function"==typeof r?r({icon:i,message:o}):l.createElement(l.Fragment,null,i,o))});r=l.createElement,g.p=void 0,k=r,w=void 0,P=void 0;var eh=({id:e,className:s,style:t,onHeightUpdate:r,children:a})=>{let i=l.useCallback(s=>{if(s){let t=()=>{r(e,s.getBoundingClientRect().height)};t(),new MutationObserver(t).observe(s,{subtree:!0,childList:!0,characterData:!0})}},[e,r]);return l.createElement("div",{ref:i,className:s,style:t},a)},ex=(e,s)=>{let t=e.includes("top"),r=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:S()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${s*(t?1:-1)}px)`,...t?{top:0}:{bottom:0},...r}},eu=N`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,ey=({reverseOrder:e,position:s="top-center",toastOptions:t,gutter:r,children:a,containerStyle:i,containerClassName:o})=>{let{toasts:n,handlers:c}=W(t);return l.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...i},className:o,onMouseEnter:c.startPause,onMouseLeave:c.endPause},n.map(t=>{let i=t.position||s,o=ex(i,c.calculateOffset(t,{reverseOrder:e,gutter:r,defaultPosition:s}));return l.createElement(eh,{id:t.id,key:t.id,onHeightUpdate:c.updateHeight,className:t.visible?eu:"",style:o},"custom"===t.type?M(t.message,t):a?a(t):l.createElement(em,{toast:t,position:i}))}))},eg=t(98085);let ev=(0,eg.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var eb=t(83099);let ef=(0,eg.A)("factory",[["path",{d:"M12 16h.01",key:"1drbdi"}],["path",{d:"M16 16h.01",key:"1f9h7w"}],["path",{d:"M3 19a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a.5.5 0 0 0-.769-.422l-4.462 2.844A.5.5 0 0 1 15 10.5v-2a.5.5 0 0 0-.769-.422L9.77 10.922A.5.5 0 0 1 9 10.5V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2z",key:"1iv0i2"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]),ej=(0,eg.A)("tree-pine",[["path",{d:"m17 14 3 3.3a1 1 0 0 1-.7 1.7H4.7a1 1 0 0 1-.7-1.7L7 14h-.3a1 1 0 0 1-.7-1.7L9 9h-.2A1 1 0 0 1 8 7.3L12 3l4 4.3a1 1 0 0 1-.8 1.7H15l3 3.3a1 1 0 0 1-.7 1.7H17Z",key:"cpyugq"}],["path",{d:"M12 22v-3",key:"kmzjlo"}]]),eN=(0,eg.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var ek=t(54076),ew=t(14493),eP=t(77060),eA=t(31435),eC=t(16355);let eq=(0,eg.A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);var eM=t(79123),eT=t(83427),eS=t(71727);let e$=(0,eg.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),eD=(0,eg.A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);var eE=t(61115);let eR=(0,eg.A)("arrow-up-narrow-wide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]]),ez=(0,eg.A)("arrow-down-wide-narrow",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"M11 4h10",key:"1w87gc"}],["path",{d:"M11 8h7",key:"djye34"}],["path",{d:"M11 12h4",key:"q8tih4"}]]);var e_=t(82583),eL=t(35342);let eF=(0,eg.A)("bed",[["path",{d:"M2 4v16",key:"vw9hq8"}],["path",{d:"M2 8h18a2 2 0 0 1 2 2v10",key:"1dgv2r"}],["path",{d:"M2 17h20",key:"18nfp3"}],["path",{d:"M6 8v9",key:"1yriud"}]]),eO=(0,eg.A)("bath",[["path",{d:"M10 4 8 6",key:"1rru8s"}],["path",{d:"M17 19v2",key:"ts1sot"}],["path",{d:"M2 12h20",key:"9i4pu4"}],["path",{d:"M7 19v2",key:"12npes"}],["path",{d:"M9 5 7.621 3.621A2.121 2.121 0 0 0 4 5v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-5",key:"14ym8i"}]]),eI=(0,eg.A)("car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]]);var eH=t(92386),eZ=t(32954),eB=t(97009),eV=t(91798),eW=t(21764),eU=t(20571),eG=t(54885);let eQ=e=>{switch(e){case"residential":return(0,a.jsx)(ev,{className:"w-4 h-4"});case"commercial":return(0,a.jsx)(eb.A,{className:"w-4 h-4"});case"industrial":return(0,a.jsx)(ef,{className:"w-4 h-4"});case"land":case"eco_friendly":return(0,a.jsx)(ej,{className:"w-4 h-4"});case"luxury":return(0,a.jsx)(eN,{className:"w-4 h-4"});default:return(0,a.jsx)(ek.A,{className:"w-4 h-4"})}};function eK(){let[e,s]=(0,l.useState)(""),[t,r]=(0,l.useState)(""),[m,h]=(0,l.useState)("all"),[x,u]=(0,l.useState)("all"),[y,g]=(0,l.useState)("all"),[v,b]=(0,l.useState)(1),[f,j]=(0,l.useState)("grid"),[N,k]=(0,l.useState)("createdAt"),[w,P]=(0,l.useState)("desc"),[A,C]=(0,l.useState)({min:"",max:""}),[q,M]=(0,l.useState)({min:"",max:""}),[T,S]=(0,l.useState)(!1),[$,D]=(0,l.useState)("all"),[E,R]=(0,l.useState)("all"),[z,_]=(0,l.useState)(null),[L,F]=(0,l.useState)(null),[I,H]=(0,l.useState)(!1),Z=async(e,s)=>{let t=`${window.location.origin}/property/${e}?ref=sales-user-123`;try{await navigator.clipboard.writeText(t),_(e),O.success(`Referral link copied for ${s}!`,{duration:3e3,position:"top-right",icon:"\uD83D\uDD17"}),setTimeout(()=>_(null),2e3)}catch(e){console.error("Failed to copy referral link:",e),O.error("Failed to copy referral link. Please try again.",{duration:3e3,position:"top-right"})}},B=()=>{s(""),r(""),h("all"),u("all"),g("all"),C({min:"",max:""}),M({min:"",max:""}),D("all"),R("all"),b(1)},V=e=>{F(e),H(!0)},{data:W,isLoading:U,error:G,refetch:Q}=(0,eU.af)({page:v,limit:12,search:t||void 0,type:"all"!==m?m:void 0,status:"all"!==x?x:void 0,city:"all"!==y?y:void 0,minPrice:A.min?Number(A.min):void 0,maxPrice:A.max?Number(A.max):void 0,sortBy:N,sortOrder:w,includeStocks:!0}),{data:K}=(0,eU.Cl)(),{data:X}=(0,eU.dy)(),Y=W?.data?.properties||[],J=W?.data?.pagination,ee=K?.data||[],es=X?.data||[];return U?(0,a.jsx)(i.A,{title:"Properties",children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)(ew.A,{className:"w-8 h-8 animate-spin text-sky-500"})})})}):G?(0,a.jsx)(i.A,{title:"Properties",children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(eP.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Failed to load properties"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Please try refreshing the page"}),(0,a.jsx)(n.$,{onClick:()=>Q(),className:"bg-sky-500 hover:bg-sky-600",children:"Try Again"})]})})})}):(0,a.jsxs)(i.A,{title:"Properties",children:[(0,a.jsx)(ey,{}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Properties"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Browse and manage property listings with stock details"})]}),(0,a.jsxs)(n.$,{className:"bg-sky-500 hover:bg-sky-600 text-white",children:[(0,a.jsx)(eA.A,{className:"w-4 h-4 mr-2"}),"Add Property"]})]}),Y.length>0&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(o.Zp,{className:"border-green-200 bg-green-50",children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-green-600",children:"Available Properties"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-700",children:Y.filter(e=>e.stock?.isAvailable).length})]}),(0,a.jsx)(eC.A,{className:"w-8 h-8 text-green-500"})]})})}),(0,a.jsx)(o.Zp,{className:"border-blue-200 bg-blue-50",children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-600",children:"Total Stocks"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-700",children:(0,eW.ZV)(Y.reduce((e,s)=>e+(s.stock?.totalStocks||0),0))})]}),(0,a.jsx)(eq,{className:"w-8 h-8 text-blue-500"})]})})}),(0,a.jsx)(o.Zp,{className:"border-orange-200 bg-orange-50",children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-orange-600",children:"Stocks Sold"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-700",children:(0,eW.ZV)(Y.reduce((e,s)=>e+(s.stock?.stocksSold||0),0))})]}),(0,a.jsx)(eM.A,{className:"w-8 h-8 text-orange-500"})]})})}),(0,a.jsx)(o.Zp,{className:"border-purple-200 bg-purple-50",children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-purple-600",children:"Total Revenue"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-700",children:(0,eW.vv)(Y.reduce((e,s)=>e+(s.stock?.totalRevenue||0),0))})]}),(0,a.jsx)(eT.A,{className:"w-8 h-8 text-purple-500"})]})})})]}),(0,a.jsx)(o.Zp,{className:"border-gray-200 shadow-sm",children:(0,a.jsxs)(o.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 mb-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(eS.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)(c.p,{placeholder:"Search properties by name, location, developer, or description...",value:e,onChange:e=>s(e.target.value),className:"pl-10 border-gray-300 focus:border-sky-500 focus:ring-sky-500"})]})}),(0,a.jsxs)("div",{className:"flex border border-gray-300 rounded-lg overflow-hidden",children:[(0,a.jsx)(n.$,{variant:"grid"===f?"default":"ghost",size:"sm",onClick:()=>j("grid"),className:"rounded-none border-0",children:(0,a.jsx)(e$,{className:"w-4 h-4"})}),(0,a.jsx)(n.$,{variant:"list"===f?"default":"ghost",size:"sm",onClick:()=>j("list"),className:"rounded-none border-0",children:(0,a.jsx)(eD,{className:"w-4 h-4"})})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[(0,a.jsxs)(p.l6,{value:m,onValueChange:h,children:[(0,a.jsx)(p.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,a.jsx)(p.yv,{placeholder:"Property Type"})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"all",children:"All Types"}),ee.map(e=>(0,a.jsx)(p.eb,{value:e.type||e,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[eQ(e.type||e),(e.type||e)?.charAt(0).toUpperCase()+(e.type||e)?.slice(1).replace("_"," ")]})},e.type||e))]})]}),(0,a.jsxs)(p.l6,{value:x,onValueChange:u,children:[(0,a.jsx)(p.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,a.jsx)(p.yv,{placeholder:"Status"})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"all",children:"All Status"}),(0,a.jsx)(p.eb,{value:"active",children:"Active"}),(0,a.jsx)(p.eb,{value:"under_construction",children:"Under Construction"}),(0,a.jsx)(p.eb,{value:"completed",children:"Completed"})]})]}),(0,a.jsxs)(p.l6,{value:y,onValueChange:g,children:[(0,a.jsx)(p.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,a.jsx)(p.yv,{placeholder:"City"})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"all",children:"All Cities"}),es.map(e=>(0,a.jsx)(p.eb,{value:e.city,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eE.A,{className:"w-3 h-3"}),e.city]})},e.city))]})]}),(0,a.jsxs)(p.l6,{value:N,onValueChange:k,children:[(0,a.jsx)(p.bq,{className:"w-48 border-gray-300 focus:border-sky-500",children:(0,a.jsx)(p.yv,{placeholder:"Sort By"})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"createdAt",children:"Latest Properties"}),(0,a.jsx)(p.eb,{value:"expectedReturns",children:"Highest Returns"}),(0,a.jsx)(p.eb,{value:"stock.salesCommissionRate",children:"Sales Commission"}),(0,a.jsx)(p.eb,{value:"stock.referralCommissionRate",children:"Referral Commission"}),(0,a.jsx)(p.eb,{value:"stock.availableStocks",children:"Most Available"}),(0,a.jsx)(p.eb,{value:"stock.stocksSold",children:"Best Selling"}),(0,a.jsx)(p.eb,{value:"stock.totalRevenue",children:"Highest Revenue"}),(0,a.jsx)(p.eb,{value:"maturityPeriodMonths",children:"Quick Returns"}),(0,a.jsx)(p.eb,{value:"name",children:"Property Name"})]})]}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>P("asc"===w?"desc":"asc"),className:"border-gray-300",children:"asc"===w?(0,a.jsx)(eR,{className:"w-4 h-4"}):(0,a.jsx)(ez,{className:"w-4 h-4"})}),(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>S(!T),className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:[(0,a.jsx)(e_.A,{className:"w-4 h-4 mr-2"}),"Advanced"]}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:B,className:"border-red-300 text-red-600 hover:bg-red-50",children:"Reset"})]}),T&&(0,a.jsxs)("div",{className:"border-t pt-4 space-y-4",children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-700 mb-3 flex items-center",children:[(0,a.jsx)(eL.A,{className:"w-4 h-4 mr-2 text-sky-500"}),"Sales Performance Filters"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Sales Commission (%)"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(c.p,{placeholder:"Min %",value:A.min,onChange:e=>C(s=>({...s,min:e.target.value})),className:"text-sm"}),(0,a.jsx)(c.p,{placeholder:"Max %",value:A.max,onChange:e=>C(s=>({...s,max:e.target.value})),className:"text-sm"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Expected Returns (%)"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(c.p,{placeholder:"Min %",value:q.min,onChange:e=>M(s=>({...s,min:e.target.value})),className:"text-sm"}),(0,a.jsx)(c.p,{placeholder:"Max %",value:q.max,onChange:e=>M(s=>({...s,max:e.target.value})),className:"text-sm"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Investment Risk"}),(0,a.jsxs)(p.l6,{value:$,onValueChange:D,children:[(0,a.jsx)(p.bq,{className:"border-gray-300 focus:border-sky-500",children:(0,a.jsx)(p.yv,{placeholder:"Risk Level"})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"all",children:"All Risk Levels"}),(0,a.jsx)(p.eb,{value:"low",children:"\uD83D\uDFE2 Low Risk"}),(0,a.jsx)(p.eb,{value:"medium",children:"\uD83D\uDFE1 Medium Risk"}),(0,a.jsx)(p.eb,{value:"high",children:"\uD83D\uDD34 High Risk"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Sales Performance"}),(0,a.jsxs)(p.l6,{value:E,onValueChange:R,children:[(0,a.jsx)(p.bq,{className:"border-gray-300 focus:border-sky-500",children:(0,a.jsx)(p.yv,{placeholder:"Performance"})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"all",children:"All Properties"}),(0,a.jsx)(p.eb,{value:"hot",children:"\uD83D\uDD25 Hot Selling (>50% sold)"}),(0,a.jsx)(p.eb,{value:"trending",children:"\uD83D\uDCC8 Trending (20-50% sold)"}),(0,a.jsx)(p.eb,{value:"new",children:"✨ New Launch (<20% sold)"}),(0,a.jsx)(p.eb,{value:"available",children:"\uD83D\uDC8E High Availability"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Investment Period"}),(0,a.jsxs)(p.l6,{value:E,onValueChange:R,children:[(0,a.jsx)(p.bq,{className:"border-gray-300 focus:border-sky-500",children:(0,a.jsx)(p.yv,{placeholder:"Period"})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"all",children:"All Periods"}),(0,a.jsx)(p.eb,{value:"quick",children:"⚡ Quick (≤ 12 months)"}),(0,a.jsx)(p.eb,{value:"medium",children:"⏰ Medium (1-3 years)"}),(0,a.jsx)(p.eb,{value:"long",children:"\uD83C\uDFAF Long Term (> 3 years)"})]})]})]})]})]})]})}),Y.length>0&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",Y.length," of ",J?.totalItems||0," properties",e&&` for "${e}"`]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Page ",J?.currentPage||1," of ",J?.totalPages||1]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)("div",{className:`grid gap-4 ${"grid"===f?"grid-cols-1 md:grid-cols-2 lg:grid-cols-3":"grid-cols-1"}`,children:Y.map(e=>(0,a.jsxs)(o.Zp,{className:`border-gray-200 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer group overflow-hidden bg-white ${e.featured?"ring-2 ring-yellow-400 ring-opacity-50":""}`,children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"h-32 relative bg-gradient-to-br from-sky-100 to-blue-100",children:e.images&&e.images.length>0?(0,a.jsx)("img",{src:e.images[0].url||e.images[0],alt:e.name,className:"w-full h-full object-cover",onError:s=>{let t=s.target,r={residential:"https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=400&h=300&fit=crop",commercial:"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop",industrial:"https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=400&h=300&fit=crop",eco_friendly:"https://images.unsplash.com/photo-1518780664697-55e3ad937233?w=400&h=300&fit=crop",luxury:"https://images.unsplash.com/photo-1613977257363-707ba9348227?w=400&h=300&fit=crop"};t.src=r[e.propertyType]||r.residential}}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-gray-400",children:[eQ(e.propertyType),(0,a.jsx)("div",{className:"text-xs mt-1",children:e.propertyType?.replace("_"," ")})]})})}),e.featured&&(0,a.jsx)("div",{className:"absolute top-2 left-2 z-10",children:(0,a.jsxs)(d.E,{className:"bg-yellow-400 text-yellow-900 text-xs font-bold shadow-lg animate-pulse",children:[(0,a.jsx)(eN,{className:"w-3 h-3 mr-1"}),"FEATURED"]})})]}),(0,a.jsx)(o.Wu,{className:"p-3",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-bold text-gray-900 leading-tight truncate",children:e.name||"Property Name"}),(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-600 mt-1",children:[(0,a.jsx)(eE.A,{className:"w-3 h-3 mr-1 text-yellow-500"}),(0,a.jsxs)("span",{className:"truncate",children:[e.location?.city||"City",", ",e.location?.state||"State"]})]})]}),e.stock&&(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 p-2 rounded border border-green-200",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-green-600",children:[e.stock?.salesCommissionRate||0,"%"]}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Commission"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-sky-600",children:(0,eW.vv)(e.stock?.stockPrice||0)}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Stock Price"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-sm font-bold text-blue-600",children:[e.expectedReturns||0,"%"]}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Returns"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-sm font-bold text-purple-600",children:[e.maturityPeriodMonths||0,"m"]}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Period"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(eb.A,{className:"w-3 h-3 mr-1 text-blue-500"}),(0,a.jsx)("span",{className:"truncate",children:e.developer?.name||"Developer"})]}),(0,a.jsxs)("span",{children:[e.developer?.experience||0,"y exp"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[e.bedrooms&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(eF,{className:"w-4 h-4 mr-1 text-green-500"}),(0,a.jsxs)("span",{children:[e.bedrooms," Beds"]})]}),e.bathrooms&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(eO,{className:"w-4 h-4 mr-1 text-sky-500"}),(0,a.jsxs)("span",{children:[e.bathrooms," Baths"]})]}),e.parking&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(eI,{className:"w-4 h-4 mr-1 text-gray-500"}),(0,a.jsx)("span",{children:e.parking})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.description}),e.stock&&(0,a.jsxs)("div",{className:"text-xs text-gray-600 flex items-center justify-between",children:[(0,a.jsxs)("span",{children:["Available: ",(0,eW.ZV)(e.stock?.availableStocks||0)]}),(0,a.jsxs)("span",{children:["Min: ",(0,eW.vv)(e.stock?.minimumPurchase||0)]})]}),(0,a.jsxs)("div",{className:"flex space-x-2 pt-2 border-t border-gray-100",children:[(0,a.jsxs)(n.$,{size:"sm",className:"flex-1 bg-sky-500 hover:bg-sky-600 text-white text-xs",onClick:()=>V(e),children:[(0,a.jsx)(eH.A,{className:"w-3 h-3 mr-1"}),"Details"]}),(0,a.jsx)(n.$,{size:"sm",variant:"outline",className:`border-blue-300 text-blue-600 hover:bg-blue-50 text-xs ${z===e.id?"bg-blue-50 border-blue-400":""}`,onClick:()=>Z(e.id,e.name),children:z===e.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(eZ.A,{className:"w-3 h-3 mr-1"}),"Copied!"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(eB.A,{className:"w-3 h-3 mr-1"}),"Referral"]})})]})]})})]},e.id))})}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)(o.Zp,{className:"border-gray-200 shadow-sm sticky top-6",children:[(0,a.jsx)(o.aR,{className:"pb-3",children:(0,a.jsxs)(o.ZB,{className:"text-lg font-bold text-gray-900 flex items-center",children:[(0,a.jsx)(eN,{className:"w-5 h-5 mr-2 text-yellow-500"}),"Featured Properties"]})}),(0,a.jsxs)(o.Wu,{className:"space-y-3",children:[Y.filter(e=>e.featured).slice(0,3).map(e=>(0,a.jsx)("div",{className:"p-3 border border-yellow-200 rounded-lg bg-yellow-50 hover:bg-yellow-100 cursor-pointer transition-colors",onClick:()=>V(e),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded flex items-center justify-center",children:e.images&&e.images.length>0?(0,a.jsx)("img",{src:e.images[0].url||e.images[0],alt:e.name,className:"w-full h-full object-cover rounded",onError:e=>{e.target.src="https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=100&h=100&fit=crop"}}):(0,a.jsx)(eN,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-gray-900 truncate",children:e.name||"Property Name"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 truncate",children:e.location?.city||"City"}),e.stock&&(0,a.jsxs)("div",{className:"space-y-1 mt-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-xs font-medium text-green-600",children:[e.stock?.salesCommissionRate||0,"% Commission"]}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[e.expectedReturns||0,"% Returns"]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("span",{className:"text-xs font-bold text-sky-600",children:[(0,eW.vv)(e.stock?.stockPrice||0)," per stock"]})})]})]})]})},`featured-${e.id}`)),0===Y.filter(e=>e.featured).length&&(0,a.jsxs)("div",{className:"text-center py-4 text-gray-500",children:[(0,a.jsx)(eN,{className:"w-8 h-8 mx-auto mb-2 text-gray-300"}),(0,a.jsx)("p",{className:"text-sm",children:"No featured properties available"})]})]})]})})]}),0===Y.length&&!U&&(0,a.jsx)(o.Zp,{className:"border-gray-200",children:(0,a.jsx)(o.Wu,{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(ek.A,{className:"w-10 h-10 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e||"all"!==m||"all"!==y?"No properties found":"No properties available"}),(0,a.jsx)("p",{className:"text-gray-500 max-w-md mx-auto",children:e||"all"!==m||"all"!==y?"Try adjusting your search criteria or filters to find more properties.":"There are currently no properties available. Check back later for new listings."})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(e||"all"!==m||"all"!==y)&&(0,a.jsx)(n.$,{variant:"outline",onClick:B,className:"border-gray-300",children:"Clear Filters"}),(0,a.jsxs)(n.$,{className:"bg-sky-500 hover:bg-sky-600 text-white",onClick:()=>Q(),children:[(0,a.jsx)(eS.A,{className:"w-4 h-4 mr-2"}),"Refresh"]})]})]})})}),J&&J.totalPages>1&&(0,a.jsx)(o.Zp,{className:"border-gray-200",children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(J.currentPage-1)*J.itemsPerPage+1," to"," ",Math.min(J.currentPage*J.itemsPerPage,J.totalItems)," of"," ",J.totalItems," properties"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>b(1),disabled:1===J.currentPage,className:"border-gray-300",children:"First"}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>b(J.currentPage-1),disabled:1===J.currentPage,className:"border-gray-300",children:"Previous"}),(0,a.jsx)("div",{className:"flex space-x-1",children:Array.from({length:Math.min(5,J.totalPages)},(e,s)=>{let t=Math.max(1,J.currentPage-2)+s;return t<=J.totalPages?(0,a.jsx)(n.$,{variant:t===J.currentPage?"default":"outline",size:"sm",onClick:()=>b(t),className:t===J.currentPage?"bg-sky-500 hover:bg-sky-600":"border-gray-300",children:t},`page-${t}`):null})}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>b(J.currentPage+1),disabled:J.currentPage===J.totalPages,className:"border-gray-300",children:"Next"}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>b(J.totalPages),disabled:J.currentPage===J.totalPages,className:"border-gray-300",children:"Last"})]})]})})}),(0,a.jsx)("div",{className:"fixed bottom-6 right-6 z-50",children:(0,a.jsx)(n.$,{className:"w-14 h-14 rounded-full bg-sky-500 hover:bg-sky-600 text-white shadow-lg hover:shadow-xl transition-all duration-300",onClick:()=>window.scrollTo({top:0,behavior:"smooth"}),children:(0,a.jsx)(eV.A,{className:"w-6 h-6"})})}),(0,a.jsx)(eG.b,{property:L,isOpen:I,onClose:()=>{F(null),H(!1)},onCopyReferral:Z,copiedReferralId:z})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75281:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=t(10557),a=t(68490),l=t(13172),i=t.n(l),o=t(68835),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(s,n);let c={children:["",{children:["properties",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80242)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\properties\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\properties\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/properties/page",pathname:"/properties",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},77060:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(98085).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},77352:(e,s,t)=>{Promise.resolve().then(t.bind(t,54236))},79551:e=>{"use strict";e.exports=require("url")},80242:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\properties\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\properties\\page.tsx","default")},82583:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(98085).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},92386:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(98085).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[755,598,544,29,796,286,447,512],()=>t(75281));module.exports=r})();