'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'
import EnhancedS3Upload from '@/components/FileUpload/EnhancedS3Upload'
import PropertyDocumentUpload from './PropertyDocumentUpload'
import { useCreatePropertyMutation } from '@/store/api/propertiesApi'
import { useGetPropertyOwnersQuery } from '@/store/api/propertyOwnersApi'
import { Property } from '@/types'
import { useRouter } from 'next/navigation'
import {
  Building,
  Save,
  ArrowLeft,
  Upload,
  MapPin,
  Calendar,
  DollarSign,
  Home,
  Building2,
  Star,
  Image,
  Plus,
  X,
  AlertCircle,
  CheckCircle,
  Eye,
  FileText,
  Navigation,
  Loader2
} from 'lucide-react'

interface PropertyFormData {
  name: string
  description: string
  propertyType: string // Changed from 'type' to match model
  location: {
    address: string
    city: string
    state: string
    pincode: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }

  // Required financial fields from model
  expectedReturns: number
  maturityPeriodMonths: number

  // Construction details
  constructionStatus: string
  launchDate: string
  expectedCompletion: string
  actualCompletion?: string

  // Developer details (structured object from model)
  developer: {
    name: string
    contact: string
    email: string
    experience?: number
  }

  amenities: string[]

  // Images with proper structure from model
  images: Array<{
    key: string
    name: string
    type: string
    url: string
    uploadedAt?: Date
  }>

  // Documents with proper structure from model
  documents: Array<{
    key: string
    name: string
    type: string
    url: string
    uploadedAt?: Date
  }>

  // Videos with proper structure from model
  videos: Array<{
    key: string
    name: string
    type: string
    url: string
    uploadedAt?: Date
  }>

  // Legal documents array
  legalDocuments: string[]

  // Required fields from model
  ownerId: string
  status: string
  featured: boolean
  priorityOrder: number
}

export default function AddPropertyPage() {
  const router = useRouter()
  const [createProperty, { isLoading: isCreating }] = useCreatePropertyMutation()
  const { data: propertyOwnersData, isLoading: isLoadingOwners } = useGetPropertyOwnersQuery({
    page: 1,
    limit: 100,
    status: 'active'
  })

  // Coordinate detection state
  const [isDetectingLocation, setIsDetectingLocation] = useState(false)
  const [locationError, setLocationError] = useState<string | null>(null)

  const [formData, setFormData] = useState<PropertyFormData>({
    name: '',
    description: '',
    propertyType: 'residential',
    location: {
      address: '',
      city: '',
      state: '',
      pincode: '',
      coordinates: {
        latitude: 0,
        longitude: 0
      }
    },
    expectedReturns: 0,
    maturityPeriodMonths: 12,
    constructionStatus: 'planning',
    launchDate: '',
    expectedCompletion: '',
    actualCompletion: '',
    developer: {
      name: '',
      contact: '',
      email: '',
      experience: 0
    },
    amenities: [],
    images: [],
    documents: [],
    videos: [],
    legalDocuments: [],
    ownerId: '',
    status: 'active',
    featured: false,
    priorityOrder: 0
  })

  const [newAmenity, setNewAmenity] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})



  // Owner selection state
  const [ownerSearchQuery, setOwnerSearchQuery] = useState('')
  const [showOwnerDropdown, setShowOwnerDropdown] = useState(false)
  const [selectedOwner, setSelectedOwner] = useState<any>(null)
  const [highlightedIndex, setHighlightedIndex] = useState(-1)

  // Dummy owners data for development
  const dummyOwners = [
    {
      id: 'owner1',
      name: 'Rajesh Kumar',
      email: '<EMAIL>',
      company: 'Kumar Enterprises',
      status: 'verified',
      properties: 5,
      phone: '+91 98765 43210'
    },
    {
      id: 'owner2',
      name: 'Priya Sharma',
      email: '<EMAIL>',
      company: 'Sharma Constructions',
      status: 'verified',
      properties: 3,
      phone: '+91 98765 43211'
    },
    {
      id: 'owner3',
      name: 'Amit Patel',
      email: '<EMAIL>',
      company: null,
      status: 'pending',
      properties: 1,
      phone: '+91 98765 43212'
    },
    {
      id: 'owner4',
      name: 'Sunita Gupta',
      email: '<EMAIL>',
      company: 'Gupta Real Estate',
      status: 'verified',
      properties: 8,
      phone: '+91 98765 43213'
    },
    {
      id: 'owner5',
      name: 'Vikram Singh',
      email: '<EMAIL>',
      company: 'Singh Properties',
      status: 'verified',
      properties: 12,
      phone: '+91 98765 43214'
    },
    {
      id: 'owner6',
      name: 'Neha Agarwal',
      email: '<EMAIL>',
      company: null,
      status: 'verified',
      properties: 2,
      phone: '+91 98765 43215'
    },
    {
      id: 'owner7',
      name: 'Ravi Mehta',
      email: '<EMAIL>',
      company: 'Mehta Developers',
      status: 'pending',
      properties: 0,
      phone: '+91 98765 43216'
    },
    {
      id: 'owner8',
      name: 'Kavita Joshi',
      email: '<EMAIL>',
      company: 'Joshi Investments',
      status: 'verified',
      properties: 6,
      phone: '+91 98765 43217'
    }
  ]

  // Use real API data
  const realOwners = propertyOwnersData?.data?.data || []

  // Use real data if available, fallback to dummy data for development
  const availableOwners = realOwners.length > 0 ? realOwners.map(owner => ({
    id: owner._id || owner.id,
    name: owner.fullName || `${owner.firstName} ${owner.lastName}`,
    email: owner.email,
    company: owner.company || null,
    status: owner.verificationStatus || 'pending',
    properties: owner.totalProperties || 0,
    phone: owner.phone
  })) : dummyOwners

  // Filter owners based on search query
  const filteredOwners = availableOwners.filter(owner => {
    if (!ownerSearchQuery) return true

    const searchLower = ownerSearchQuery.toLowerCase()
    return (
      owner.name.toLowerCase().includes(searchLower) ||
      owner.email.toLowerCase().includes(searchLower) ||
      (owner.company && owner.company.toLowerCase().includes(searchLower)) ||
      owner.phone.includes(ownerSearchQuery)
    )
  })

  // Owner selection functions
  const selectOwner = (owner: any) => {
    setSelectedOwner(owner)
    setOwnerSearchQuery(owner.name)
    setShowOwnerDropdown(false)
    handleInputChange('ownerId', owner.id)
  }

  const clearSelectedOwner = () => {
    setSelectedOwner(null)
    setOwnerSearchQuery('')
    setHighlightedIndex(-1)
    handleInputChange('ownerId', '')
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showOwnerDropdown) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setHighlightedIndex(prev =>
          prev < filteredOwners.length - 1 ? prev + 1 : 0
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setHighlightedIndex(prev =>
          prev > 0 ? prev - 1 : filteredOwners.length - 1
        )
        break
      case 'Enter':
        e.preventDefault()
        if (highlightedIndex >= 0 && filteredOwners[highlightedIndex]) {
          selectOwner(filteredOwners[highlightedIndex])
        }
        break
      case 'Escape':
        setShowOwnerDropdown(false)
        setHighlightedIndex(-1)
        break
    }
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (!target.closest('.owner-search-container')) {
        setShowOwnerDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Property types from server enums
  const propertyTypes = [
    { value: 'residential', label: 'Residential', icon: Home, description: 'Houses, apartments, condos' },
    { value: 'commercial', label: 'Commercial', icon: Building2, description: 'Offices, retail, warehouses' },
    { value: 'mixed', label: 'Mixed Use', icon: Building, description: 'Residential + Commercial' },
    { value: 'luxury', label: 'Luxury', icon: Star, description: 'High-end premium properties' },
    { value: 'eco_friendly', label: 'Eco-Friendly', icon: Home, description: 'Sustainable green buildings' }
  ]

  // Construction status from server enums
  const constructionStatuses = [
    { value: 'planning', label: 'Planning', description: 'In planning phase' },
    { value: 'under_construction', label: 'Under Construction', description: 'Currently being built' },
    { value: 'completed', label: 'Completed', description: 'Construction finished' },
    { value: 'ready_to_move', label: 'Ready to Move', description: 'Ready for occupancy' }
  ]

  // Property status from server enums
  const propertyStatuses = [
    { value: 'active', label: 'Active', description: 'Available for investment' },
    { value: 'inactive', label: 'Inactive', description: 'Not available' },
    { value: 'sold_out', label: 'Sold Out', description: 'All stocks sold' },
    { value: 'completed', label: 'Completed', description: 'Investment completed' }
  ]

  const categories = [
    { value: 'Luxury', label: 'Luxury', description: 'High-end premium properties' },
    { value: 'Premium', label: 'Premium', description: 'Premium quality properties' },
    { value: 'Mid-Range', label: 'Mid-Range', description: 'Affordable quality properties' },
    { value: 'Budget', label: 'Budget', description: 'Budget-friendly properties' }
  ]

  const statusOptions = [
    { value: 'upcoming', label: 'Upcoming' },
    { value: 'active', label: 'Active' },
    { value: 'sold', label: 'Sold Out' },
    { value: 'inactive', label: 'Inactive' }
  ]

  const commonAmenities = [
    'Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Clubhouse',
    'Playground', 'Elevator', 'Power Backup', 'Water Supply', 'CCTV',
    'Intercom', 'Fire Safety', 'Waste Management'
  ]

  const handleInputChange = (field: string, value: string | boolean | number) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')

      // Handle nested objects like location and developer
      if (parent === 'location' && (child === 'latitude' || child === 'longitude')) {
        setFormData(prev => ({
          ...prev,
          location: {
            ...prev.location,
            coordinates: {
              latitude: child === 'latitude' ? (typeof value === 'string' ? parseFloat(value) || 0 : value as number) : prev.location.coordinates?.latitude || 0,
              longitude: child === 'longitude' ? (typeof value === 'string' ? parseFloat(value) || 0 : value as number) : prev.location.coordinates?.longitude || 0
            }
          }
        }))
      } else if (parent === 'developer') {
        setFormData(prev => ({
          ...prev,
          developer: {
            ...prev.developer,
            [child]: value
          }
        }))
      } else {
        setFormData(prev => ({
          ...prev,
          [parent]: {
            ...prev[parent as keyof typeof prev] as any,
            [child]: value
          }
        }))
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const addAmenity = (amenity: string) => {
    if (amenity && !formData.amenities.includes(amenity)) {
      setFormData(prev => ({
        ...prev,
        amenities: [...prev.amenities, amenity]
      }))
    }
    setNewAmenity('')
  }

  const removeAmenity = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.filter(a => a !== amenity)
    }))
  }

  // Remove feature functions as they're not in the model

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) newErrors.name = 'Property name is required'
    if (!formData.description.trim()) newErrors.description = 'Description is required'
    if (!formData.location.address.trim()) newErrors['location.address'] = 'Address is required'
    if (!formData.location.city.trim()) newErrors['location.city'] = 'City is required'
    if (!formData.launchDate) newErrors.launchDate = 'Launch date is required'
    if (!formData.expectedCompletion) newErrors.expectedCompletion = 'Expected completion date is required'
    if (!formData.ownerId) newErrors.ownerId = 'Property owner is required'
    if (!formData.developer.name.trim()) newErrors['developer.name'] = 'Developer name is required'
    if (!formData.developer.contact.trim()) newErrors['developer.contact'] = 'Developer contact is required'
    if (!formData.developer.email.trim()) newErrors['developer.email'] = 'Developer email is required'
    if (formData.expectedReturns <= 0) newErrors.expectedReturns = 'Expected returns must be greater than 0'
    if (formData.maturityPeriodMonths <= 0) newErrors.maturityPeriodMonths = 'Maturity period must be greater than 0'

    // Basic document validation
    if (formData.documents.length === 0) {
      newErrors.documents = 'At least one document is required'
    }

    // Image validation - at least one image should be provided
    if (formData.images.length === 0) {
      newErrors.images = 'Property images are required - please upload at least 1 image (max 10 images)'
    } else if (formData.images.length > 10) {
      newErrors.images = 'Maximum 10 images allowed'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleDocumentUpload = (documentType: string, uploadResults: Array<{ fileKey: string; publicUrl: string; fileName: string }>) => {
    const documents = uploadResults.map(result => ({
      url: result.publicUrl,
      name: result.fileName,
      size: 0 // Size would be available from the original file
    }))

    setFormData(prev => ({
      ...prev,
      documents: {
        ...prev.documents,
        [documentType]: documents
      }
    }))

    toast.success(`${uploadResults.length} document(s) uploaded successfully for ${documentType}`)
  }

  const handleImageUpload = (uploadResults: Array<{ fileKey: string; publicUrl: string; fileName: string }>) => {
    const images = uploadResults.map((result) => ({
      key: result.fileKey,
      name: result.fileName,
      type: 'image',
      url: result.publicUrl,
      uploadedAt: new Date()
    }))

    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...images]
    }))

    toast.success(`${uploadResults.length} image(s) uploaded successfully`)
  }

  const handleUploadError = (error: string) => {
    toast.error(`Upload failed: ${error}`)
  }



  // Coordinate detection and address auto-fill
  const detectCurrentLocation = async () => {
    setIsDetectingLocation(true)
    setLocationError(null)

    try {
      if (!navigator.geolocation) {
        throw new Error('Geolocation is not supported by this browser')
      }

      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        })
      })

      const { latitude, longitude } = position.coords

      // Update coordinates in form
      setFormData(prev => ({
        ...prev,
        location: {
          ...prev.location,
          latitude: latitude.toString(),
          longitude: longitude.toString()
        }
      }))

      // Reverse geocoding to get address details
      await reverseGeocode(latitude, longitude)

      toast.success('Location detected successfully!')
    } catch (error: any) {
      const errorMessage = error.code === 1 ? 'Location access denied' :
                          error.code === 2 ? 'Location unavailable' :
                          error.code === 3 ? 'Location request timeout' :
                          error.message || 'Failed to detect location'

      setLocationError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsDetectingLocation(false)
    }
  }

  // Reverse geocoding to get address from coordinates
  const reverseGeocode = async (lat: number, lng: number) => {
    try {
      // Using a CORS-friendly geocoding service
      const response = await fetch(
        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`
      )

      if (!response.ok) {
        throw new Error('Failed to fetch address details')
      }

      const data = await response.json()

      if (data) {
        setFormData(prev => ({
          ...prev,
          location: {
            ...prev.location,
            address: data.locality || data.city || data.countryName || prev.location.address,
            city: data.city || data.locality || prev.location.city,
            state: data.principalSubdivision || data.principalSubdivisionCode || prev.location.state,
            pincode: data.postcode || data.postalCode || prev.location.pincode,
            coordinates: {
              latitude: lat,
              longitude: lng
            }
          }
        }))

        toast.success('Address details filled automatically!')
      }
    } catch (error) {
      console.error('Reverse geocoding error:', error)
      // Fallback: Just fill coordinates and let user fill address manually
      toast.success('Location coordinates detected! Please fill address details manually.')
    }
  }

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }

  const removeDocument = (index: number) => {
    setFormData(prev => ({
      ...prev,
      documents: prev.documents.filter((_, i) => i !== index)
    }))
  }

  const setPrimaryImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.map((img, i) => ({
        ...img,
        isPrimary: i === index
      }))
    }))
  }

  const handlePreview = () => {
    if (!validateForm()) {
      toast.error('Please fill in all required fields before previewing')
      return
    }
    // You can implement preview functionality here
    toast.info('Preview functionality coming soon!')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsSubmitting(true)

    try {
      // Transform form data to match backend Property model
      const propertyData = {
        name: formData.name,
        description: formData.description,
        location: {
          address: formData.location.address,
          city: formData.location.city,
          state: formData.location.state,
          pincode: formData.location.pincode,
          coordinates: formData.location.coordinates
        },
        propertyType: formData.propertyType.toUpperCase(),
        expectedReturns: formData.expectedReturns,
        maturityPeriodMonths: formData.maturityPeriodMonths,
        constructionStatus: formData.constructionStatus.toUpperCase(),
        launchDate: formData.launchDate,
        expectedCompletion: formData.expectedCompletion,
        actualCompletion: formData.actualCompletion,
        developer: formData.developer,
        amenities: formData.amenities,
        images: formData.images,
        documents: formData.documents,
        videos: formData.videos,
        legalDocuments: formData.legalDocuments,
        ownerId: formData.ownerId,
        status: formData.status.toUpperCase(),
        featured: formData.featured,
        priorityOrder: formData.priorityOrder
      }

      console.log('Creating property with data:', propertyData)

      const result = await createProperty(propertyData).unwrap()

      toast.success('Property created successfully!')
      console.log('Property created:', result)

      // Redirect to properties list or property detail page
      router.push('/properties')

    } catch (error: any) {
      console.error('Error creating property:', error)
      toast.error(error?.data?.message || 'Failed to create property')
    } finally {
      setIsSubmitting(false)
    }
  }



  return (
    <DashboardLayout>
      <div className="h-full overflow-auto">
        <div className="max-w-6xl mx-auto p-6 space-y-6">
        {/* Beautiful Header with Gradient */}
        <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg p-6 text-white">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-4">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => window.history.back()}
                className="bg-white/20 hover:bg-white/30 text-white border-white/30"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Properties
              </Button>
              <div>
                <h1 className="text-3xl font-bold mb-2 flex items-center gap-3">
                  <Building className="h-8 w-8" />
                  Add New Property
                </h1>
                <p className="text-emerald-100">Create a new property listing with complete details and documentation</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="secondary"
                onClick={handlePreview}
                className="bg-white/20 hover:bg-white/30 text-white border-white/30"
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || isCreating}
                className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold"
              >
                {(isSubmitting || isCreating) ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Create Property
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Form Content */}
        {/* <div className="flex items-center gap-4">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Properties
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Building className="h-8 w-8 text-blue-600" />
              Add New Property
            </h1>
            <p className="text-gray-600 mt-1">Create a new property listing for investment</p>
          </div>
        </div> */}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>Enter the property's basic details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Property Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter property name"
                  />
                  {errors.name && (
                    <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.name}
                    </p>
                  )}
                </div>

                {/* Developer Information */}
                <div className="space-y-4 p-4 border border-gray-200 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900">Developer Information *</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Developer Name *
                      </label>
                      <input
                        type="text"
                        value={formData.developer.name}
                        onChange={(e) => handleInputChange('developer.name', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          errors['developer.name'] ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Enter developer name"
                      />
                      {errors['developer.name'] && (
                        <p className="text-red-500 text-sm mt-1">{errors['developer.name']}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Contact Number *
                      </label>
                      <input
                        type="tel"
                        value={formData.developer.contact}
                        onChange={(e) => handleInputChange('developer.contact', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          errors['developer.contact'] ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Enter contact number"
                      />
                      {errors['developer.contact'] && (
                        <p className="text-red-500 text-sm mt-1">{errors['developer.contact']}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        value={formData.developer.email}
                        onChange={(e) => handleInputChange('developer.email', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          errors['developer.email'] ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Enter email address"
                      />
                      {errors['developer.email'] && (
                        <p className="text-red-500 text-sm mt-1">{errors['developer.email']}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Experience (Years)
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={formData.developer.experience || ''}
                        onChange={(e) => handleInputChange('developer.experience', parseInt(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Years of experience"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Property Owner (Optional)
                  </label>
                  <div className="space-y-2 owner-search-container">
                    <div className="relative">
                      <input
                        type="text"
                        value={ownerSearchQuery}
                        onChange={(e) => {
                          setOwnerSearchQuery(e.target.value)
                          setHighlightedIndex(-1)
                        }}
                        onFocus={() => setShowOwnerDropdown(true)}
                        onKeyDown={handleKeyDown}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Search for property owner..."
                        autoComplete="off"
                      />

                      {showOwnerDropdown && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                          {filteredOwners.length > 0 ? (
                            <>
                              <div className="px-3 py-2 text-xs text-gray-500 bg-gray-50 border-b">
                                {filteredOwners.length} owner(s) found
                              </div>
                              {filteredOwners.map((owner, index) => (
                                <div
                                  key={owner.id}
                                  onClick={() => selectOwner(owner)}
                                  className={`px-3 py-2 cursor-pointer border-b border-gray-100 last:border-b-0 ${
                                    index === highlightedIndex
                                      ? 'bg-blue-100 text-blue-900'
                                      : 'hover:bg-blue-50'
                                  }`}
                                >
                                  <div className="flex items-center justify-between">
                                    <div>
                                      <p className="font-medium text-gray-900">{owner.name}</p>
                                      <p className="text-sm text-gray-600">{owner.email}</p>
                                      {owner.company && (
                                        <p className="text-xs text-gray-500">{owner.company}</p>
                                      )}
                                    </div>
                                    <div className="text-right">
                                      <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                                        owner.status === 'verified'
                                          ? 'bg-green-100 text-green-800'
                                          : 'bg-yellow-100 text-yellow-800'
                                      }`}>
                                        {owner.status}
                                      </span>
                                      {owner.properties && (
                                        <p className="text-xs text-gray-500 mt-1">
                                          {owner.properties} properties
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </>
                          ) : ownerSearchQuery ? (
                            <div className="px-3 py-4 text-center text-gray-500">
                              <p>No owners found matching "{ownerSearchQuery}"</p>
                              <button
                                type="button"
                                onClick={() => {
                                  setShowOwnerDropdown(false)
                                  // You could open a "Create New Owner" modal here
                                }}
                                className="mt-2 text-blue-600 hover:text-blue-800 text-sm"
                              >
                                Create new owner
                              </button>
                            </div>
                          ) : (
                            <div className="px-3 py-4 text-center text-gray-500">
                              <p>Start typing to search for owners</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {selectedOwner && (
                      <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-blue-600 font-semibold">
                              {selectedOwner.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{selectedOwner.name}</p>
                            <p className="text-sm text-gray-600">{selectedOwner.email}</p>
                            {selectedOwner.company && (
                              <p className="text-xs text-gray-500">{selectedOwner.company}</p>
                            )}
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={clearSelectedOwner}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <X className="h-5 w-5" />
                        </button>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center justify-between mt-2">
                    <p className="text-gray-500 text-xs">
                      You can assign an owner now or later from the property management page
                    </p>
                    <div className="flex items-center gap-2">
                      {isLoadingOwners && (
                        <Loader2 className="h-3 w-3 animate-spin text-blue-600" />
                      )}
                      <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                        💡 {availableOwners.length} owners available
                       
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.description ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter property description"
                />
                {errors.description && (
                  <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.description}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">
                    Property Type *
                  </Label>
                  <Select
                    value={formData.propertyType}
                    onValueChange={(value) => handleInputChange('propertyType', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select property type" />
                    </SelectTrigger>
                    <SelectContent>
                      {propertyTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center gap-2">
                            <type.icon className="h-4 w-4" />
                            <div>
                              <div className="font-medium">{type.label}</div>
                              <div className="text-xs text-gray-500">{type.description}</div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.propertyType && (
                    <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.propertyType}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Expected Returns (%) *
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={formData.expectedReturns}
                    onChange={(e) => handleInputChange('expectedReturns', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 12.5"
                  />
                  {errors.expectedReturns && (
                    <p className="text-red-500 text-sm mt-1">{errors.expectedReturns}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Maturity Period (Months) *
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={formData.maturityPeriodMonths}
                    onChange={(e) => handleInputChange('maturityPeriodMonths', parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 24"
                  />
                  {errors.maturityPeriodMonths && (
                    <p className="text-red-500 text-sm mt-1">{errors.maturityPeriodMonths}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Construction Status *
                  </label>
                  <select
                    value={formData.constructionStatus}
                    onChange={(e) => handleInputChange('constructionStatus', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="planning">Planning</option>
                    <option value="under_construction">Under Construction</option>
                    <option value="completed">Completed</option>
                    <option value="ready_to_move">Ready to Move</option>
                  </select>
                  {errors.constructionStatus && (
                    <p className="text-red-500 text-sm mt-1">{errors.constructionStatus}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {statusOptions.map((status) => (
                      <option key={status.value} value={status.value}>
                        {status.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="featured"
                  checked={formData.featured}
                  onChange={(e) => handleInputChange('featured', e.target.checked)}
                  className="rounded border-gray-300"
                />
                <label htmlFor="featured" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                  <Star className="h-4 w-4 text-yellow-500" />
                  Mark as Featured Property
                </label>
              </div>
            </CardContent>
          </Card>

          {/* Location Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Location Details
              </CardTitle>
              <CardDescription>Enter the property's location information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Coordinates Detection - Top Priority */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-gray-900">📍 Smart Location Detection</h4>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={detectCurrentLocation}
                    disabled={isDetectingLocation}
                    className="flex items-center gap-2"
                  >
                    {isDetectingLocation ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Detecting...
                      </>
                    ) : (
                      <>
                        <Navigation className="h-4 w-4" />
                        Auto-Detect Location
                      </>
                    )}
                  </Button>
                </div>

                {locationError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-3">
                    <p className="text-red-800 text-sm">
                      <AlertCircle className="h-4 w-4 inline mr-1" />
                      {locationError}
                    </p>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-700">
                      Latitude
                    </Label>
                    <Input
                      type="number"
                      step="any"
                      value={formData.location.coordinates?.latitude || ''}
                      onChange={(e) => handleInputChange('location.latitude', e.target.value)}
                      placeholder="e.g., 28.6139"
                      className="mt-1"
                    />
                    <p className="text-xs text-gray-500 mt-1">Decimal degrees format</p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-700">
                      Longitude
                    </Label>
                    <Input
                      type="number"
                      step="any"
                      value={formData.location.coordinates?.longitude || ''}
                      onChange={(e) => handleInputChange('location.longitude', e.target.value)}
                      placeholder="e.g., 77.2090"
                      className="mt-1"
                    />
                    <p className="text-xs text-gray-500 mt-1">Decimal degrees format</p>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-3">
                  <p className="text-blue-800 text-sm">
                    <strong>💡 Smart Detection:</strong>
                    Click "Auto-Detect Location" to automatically fill coordinates and all address details below
                  </p>
                </div>
              </div>

              {/* Address Information */}
              <div>
                <h4 className="text-sm font-semibold text-gray-900 mb-3">📍 Address Details</h4>
                <div>
                  <Label className="text-sm font-medium text-gray-700">
                    Full Address *
                  </Label>
                  <Input
                    type="text"
                    value={formData.location.address}
                    onChange={(e) => handleInputChange('location.address', e.target.value)}
                    className={`mt-1 ${errors['location.address'] ? 'border-red-500' : ''}`}
                    placeholder="Enter complete address"
                  />
                  {errors['location.address'] && (
                    <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors['location.address']}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-700">
                      City *
                    </Label>
                    <Input
                      type="text"
                      value={formData.location.city}
                      onChange={(e) => handleInputChange('location.city', e.target.value)}
                      className={`mt-1 ${errors['location.city'] ? 'border-red-500' : ''}`}
                      placeholder="Auto-filled from location"
                    />
                    {errors['location.city'] && (
                      <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors['location.city']}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-700">
                      State
                    </Label>
                    <Input
                      type="text"
                      value={formData.location.state}
                      onChange={(e) => handleInputChange('location.state', e.target.value)}
                      className="mt-1"
                      placeholder="Auto-filled from location"
                    />
                  </div>



                  <div>
                    <Label className="text-sm font-medium text-gray-700">
                      Pincode
                    </Label>
                    <Input
                      type="text"
                      value={formData.location.pincode}
                      onChange={(e) => handleInputChange('location.pincode', e.target.value)}
                      className="mt-1"
                      placeholder="Auto-filled from location"
                    />
                  </div>
                </div>
              </div>


            </CardContent>
          </Card>





          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Project Timeline
              </CardTitle>
              <CardDescription>Set important project dates</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Launch Date *
                  </label>
                  <input
                    type="date"
                    value={formData.launchDate}
                    onChange={(e) => handleInputChange('launchDate', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.launchDate ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.launchDate && (
                    <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.launchDate}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Expected Completion Date *
                  </label>
                  <input
                    type="date"
                    value={formData.expectedCompletion}
                    onChange={(e) => handleInputChange('expectedCompletion', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.expectedCompletion ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.expectedCompletion && (
                    <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.expectedCompletion}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Amenities */}
          <Card>
            <CardHeader>
              <CardTitle>Amenities & Features</CardTitle>
              <CardDescription>Add property amenities and special features</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Common Amenities
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {commonAmenities.map((amenity) => (
                    <Button
                      key={amenity}
                      type="button"
                      variant={formData.amenities.includes(amenity) ? "default" : "outline"}
                      size="sm"
                      onClick={() => 
                        formData.amenities.includes(amenity) 
                          ? removeAmenity(amenity)
                          : addAmenity(amenity)
                      }
                      className="justify-start"
                    >
                      {amenity}
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Custom Amenity
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={newAmenity}
                    onChange={(e) => setNewAmenity(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter custom amenity"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAmenity(newAmenity))}
                  />
                  <Button type="button" onClick={() => addAmenity(newAmenity)}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {formData.amenities.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Selected Amenities
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {formData.amenities.map((amenity) => (
                      <Badge key={amenity} variant="secondary" className="flex items-center gap-1">
                        {amenity}
                        <button
                          type="button"
                          onClick={() => removeAmenity(amenity)}
                          className="ml-1 hover:text-red-600"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Property Images */}
          <Card className="border-2 border-blue-200 bg-blue-50/30">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-900">
                <Image className="h-5 w-5" />
                Property Images *
              </CardTitle>
              <CardDescription className="text-blue-700">
                Upload high-quality images of the property. 
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {errors.images && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-600 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.images}
                  </p>
                </div>
              )}

              <EnhancedS3Upload
                uploadType="property-image"
                onUploadComplete={handleImageUpload}
                onUploadError={handleUploadError}
                maxFiles={10}
                allowMultiple={true}
                showPreview={true}
                className="border-2 border-dashed border-blue-300 rounded-lg"
              />

            

              {/* Image Management */}
              {formData.images.length > 0 && (
                <div className="space-y-4">
                  <h4 className="text-sm font-semibold text-gray-900">Image Gallery ({formData.images.length})</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {formData.images.map((image, index) => (
                      <div key={index} className="relative group">
                        <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                          <img
                            src={image.url}
                            alt={image.name}
                            className="w-full h-full object-cover"
                          />
                        </div>

                        {/* Image Controls */}
                        <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center space-x-2">
                          <Button
                            type="button"
                            variant="secondary"
                            size="sm"
                            onClick={() => setPrimaryImage(index)}
                            className={image.isPrimary ? 'bg-blue-600 text-white' : ''}
                          >
                            <Star className="h-4 w-4" />
                          </Button>
                          <Button
                            type="button"
                            variant="secondary"
                            size="sm"
                            onClick={() => window.open(image.url, '_blank')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            onClick={() => removeImage(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>

                        {/* Primary Badge */}
                        {image.isPrimary && (
                          <div className="absolute top-2 left-2">
                            <Badge className="bg-blue-600 text-white text-xs">
                              Primary
                            </Badge>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  <div className="text-sm text-gray-600">
                    <p>• Click the star icon to set an image as primary</p>
                    <p>• Primary image will be displayed as the main property image</p>
                    <p>• You can upload up to 10 images, max 5MB each</p>
                    <p>• Images are securely stored in AWS S3 cloud storage</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Upload Summary */}
          {(formData.images.length > 0 || formData.documents.length > 0) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Upload Summary
                </CardTitle>
                <CardDescription>
                  Overview of all uploaded files for this property
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Images Summary */}
                  {formData.images.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
                        <Image className="h-4 w-4" />
                        Images ({formData.images.length})
                      </h4>
                      <div className="space-y-2">
                        {formData.images.slice(0, 3).map((image, index) => (
                          <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                            <div className="w-8 h-8 rounded overflow-hidden bg-gray-100">
                              <img
                                src={image.url}
                                alt={image.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-xs font-medium text-gray-900 truncate">{image.name}</p>
                              {image.isPrimary && (
                                <span className="text-xs text-blue-600">Primary Image</span>
                              )}
                            </div>
                          </div>
                        ))}
                        {formData.images.length > 3 && (
                          <p className="text-xs text-gray-500">
                            +{formData.images.length - 3} more images
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Documents Summary */}
                  {formData.documents.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Documents ({formData.documents.length})
                      </h4>
                      <div className="space-y-2">
                        {formData.documents.slice(0, 3).map((doc, index) => (
                          <div key={index} className="flex items-center gap-2 p-1 text-xs text-gray-600">
                            <div className="w-4 h-4 bg-blue-100 rounded flex items-center justify-center">
                              <span className="text-blue-600 text-[8px] font-bold">
                                {doc.type.toUpperCase()}
                              </span>
                            </div>
                            <span className="truncate">{doc.name}</span>
                          </div>
                        ))}
                        {formData.documents.length > 3 && (
                          <p className="text-xs text-gray-400 ml-6">+{formData.documents.length - 3} more</p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Property Documents */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Property Documents
              </CardTitle>
              <CardDescription>
                Upload required legal and regulatory documents. At least one key document is required.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {errors.documents && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-600 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.documents}
                  </p>
                </div>
              )}

              {/* Key Documents */}
              <div>
                <h4 className="text-sm font-semibold text-gray-900 mb-3">Key Documents (At least one required)</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Property Title Document
                    </label>
                    <EnhancedS3Upload
                      uploadType="property-document"
                      onUploadComplete={(results) => handleDocumentUpload('propertyTitle', results)}
                      onUploadError={handleUploadError}
                      maxFiles={5}
                      allowMultiple={true}
                      className="border border-gray-300 rounded-lg"
                    />
                    <p className="text-xs text-gray-500 mt-1">Title deed, ownership certificate</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Approval Certificate
                    </label>
                    <EnhancedS3Upload
                      uploadType="property-document"
                      onUploadComplete={(results) => handleDocumentUpload('approvalCertificate', results)}
                      onUploadError={handleUploadError}
                      maxFiles={5}
                      allowMultiple={true}
                      className="border border-gray-300 rounded-lg"
                    />
                    <p className="text-xs text-gray-500 mt-1">Government approval, NOC</p>
                  </div>

                  <PropertyDocumentUpload
                    documentType="landDocuments"
                    label="Land Documents"
                    description="Land records, survey documents"
                    maxFiles={5}
                    onUploadComplete={(results) => handleDocumentUpload('landDocuments', results)}
                    onUploadError={handleUploadError}
                    existingFiles={formData.documents.landDocuments || []}
                  />

                  <PropertyDocumentUpload
                    documentType="constructionPermit"
                    label="Construction Permit"
                    description="Building permit, construction license"
                    maxFiles={5}
                    onUploadComplete={(results) => handleDocumentUpload('constructionPermit', results)}
                    onUploadError={handleUploadError}
                    existingFiles={formData.documents.constructionPermit || []}
                  />
                </div>
              </div>

              {/* Additional Documents */}
              <div>
                <h4 className="text-sm font-semibold text-gray-900 mb-3">Additional Documents (Optional)</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <PropertyDocumentUpload
                    documentType="environmentalClearance"
                    label="Environmental Clearance"
                    description="Environmental impact assessment"
                    maxFiles={3}
                    onUploadComplete={(results) => handleDocumentUpload('environmentalClearance', results)}
                    onUploadError={handleUploadError}
                    existingFiles={formData.documents.environmentalClearance || []}
                  />

                  <PropertyDocumentUpload
                    documentType="financialProjections"
                    label="Financial Projections"
                    description="ROI calculations, financial analysis"
                    maxFiles={3}
                    onUploadComplete={(results) => handleDocumentUpload('financialProjections', results)}
                    onUploadError={handleUploadError}
                    existingFiles={formData.documents.financialProjections || []}
                  />

                  <PropertyDocumentUpload
                    documentType="legalDocuments"
                    label="Legal Documents"
                    description="Legal agreements, contracts"
                    maxFiles={3}
                    onUploadComplete={(results) => handleDocumentUpload('legalDocuments', results)}
                    onUploadError={handleUploadError}
                    existingFiles={formData.documents.legalDocuments || []}
                  />

                  <PropertyDocumentUpload
                    documentType="insuranceDocuments"
                    label="Insurance Documents"
                    description="Property insurance, liability coverage"
                    maxFiles={3}
                    onUploadComplete={(results) => handleDocumentUpload('insuranceDocuments', results)}
                    onUploadError={handleUploadError}
                    existingFiles={formData.documents.insuranceDocuments || []}
                  />
                </div>
              </div>

            </CardContent>
          </Card>

          {/* Submit Buttons */}
          <div className="flex items-center justify-end gap-4">
            <Button type="button" variant="outline">
              Save as Draft
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating Property...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Property
                </>
              )}
            </Button>
          </div>
        </form>
        </div>
      </div>
    </DashboardLayout>
  )
}
