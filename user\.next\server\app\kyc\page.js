(()=>{var e={};e.id=636,e.ids=[636],e.modules={2223:(e,s,t)=>{"use strict";t.d(s,{bq:()=>x,eb:()=>g,gC:()=>p,l6:()=>o,yv:()=>m});var a=t(40969),r=t(73356),l=t(20924),i=t(9343),n=t(97586),d=t(40741),c=t(21764);let o=l.bL;l.YJ;let m=l.WT,x=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.l9,{ref:r,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=l.l9.displayName;let h=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.PP,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));h.displayName=l.PP.displayName;let u=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.wn,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})}));u.displayName=l.wn.displayName;let p=r.forwardRef(({className:e,children:s,position:t="popper",...r},i)=>(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:i,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-white text-gray-950 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[(0,a.jsx)(h,{}),(0,a.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(u,{})]})}));p.displayName=l.UC.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.JU,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=l.JU.displayName;let g=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.q7,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:s})]}));g.displayName=l.q7.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.wv,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-gray-100",e),...s})).displayName=l.wv.displayName},2963:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5568:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12053:(e,s,t)=>{"use strict";t.d(s,{J:()=>c});var a=t(40969),r=t(73356),l=t(2724),i=t(52774),n=t(21764);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.b,{ref:t,className:(0,n.cn)(d(),e),...s}));c.displayName=l.b.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26806:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},27217:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=t(10557),r=t(68490),l=t(13172),i=t.n(l),n=t(68835),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["kyc",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,62402)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\kyc\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\kyc\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/kyc/page",pathname:"/kyc",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},28496:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28966:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31124:(e,s,t)=>{Promise.resolve().then(t.bind(t,62402))},33332:(e,s,t)=>{Promise.resolve().then(t.bind(t,42143))},33873:e=>{"use strict";e.exports=require("path")},34132:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},42143:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eM});var a=t(40969),r=t(73356),l=t(12011),i=t(37020),n=t(66949),d=t(46411),c=t(76650),o=t(49610),m=t(58573),x=t(91447),h=t(99024);let u=(0,h.A)("PenTool",[["path",{d:"m12 19 7-7 3 3-7 7-3-3z",key:"rklqx2"}],["path",{d:"m18 13-1.5-7.5L2 2l3.5 14.5L13 18l5-5z",key:"1et58u"}],["path",{d:"m2 2 7.586 7.586",key:"etlp93"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]);var p=t(1110),g=t(5825),y=t(15423),f=t(26806),j=t(2408),b=t(63980),N=t(79249),v=t(31825),w=t(92462),k=t(61631),C=t(1507),A=t(58121),D=t(66782),S=t(65406),P=t(57387),F=t(12053),R=t(2223);let T=(0,h.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var q=t(28496),B=t(23031);let E=S.Ik({firstName:S.Yj().min(2,"First name is required"),lastName:S.Yj().min(2,"Last name is required"),email:S.Yj().email("Please enter a valid email address"),phone:S.Yj().min(10,"Please enter a valid phone number"),dateOfBirth:S.Yj().min(1,"Date of birth is required"),nationality:S.Yj().min(2,"Nationality is required"),placeOfBirth:S.Yj().min(2,"Place of birth is required"),gender:S.k5(["male","female","other"],{required_error:"Please select your gender"}),maritalStatus:S.k5(["single","married","divorced","widowed","separated"],{required_error:"Please select your marital status"})});function $({initialData:e,onNext:s,onPrevious:t}){let l=(0,w.GV)(k.xu),[i,{isLoading:m}]=(0,N.cs)(),[x,h]=(0,r.useState)(!0),{register:u,handleSubmit:p,formState:{errors:y},setValue:f,watch:j,reset:b}=(0,A.mN)({resolver:(0,D.u)(E),defaultValues:{firstName:l?.firstName||e?.firstName||"",lastName:l?.lastName||e?.lastName||"",email:l?.email||e?.email||"",phone:l?.phone||e?.phone||"",dateOfBirth:l?.dateOfBirth||e?.dateOfBirth||"",nationality:e?.nationality||"India",placeOfBirth:e?.placeOfBirth||"",gender:e?.gender||void 0,maritalStatus:e?.maritalStatus||void 0}}),v=async e=>{try{console.log("Submitting personal info:",e);let t=await fetch("http://localhost:5000/api/kyc",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.message||"Failed to save personal information")}let a=await t.json();console.log("Personal info saved:",a),C.toast.success("Personal information saved successfully!"),setTimeout(()=>{s()},1e3)}catch(e){console.error("Personal info update error:",e),C.toast.error(e.message||"Failed to save personal information")}};return x?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 animate-spin mx-auto text-sky-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-sky-600",children:"Loading Personal Information"}),(0,a.jsx)("p",{className:"text-sm text-sky-500",children:"Fetching your saved data..."})]})]})}):(0,a.jsx)("div",{className:"space-y-8",children:(0,a.jsxs)("form",{onSubmit:p(v),className:"space-y-6",children:[(0,a.jsxs)(n.Zp,{className:"border-0 shadow-lg bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg py-4",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,a.jsx)(T,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Basic Information"}),(0,a.jsx)(c.E,{className:"bg-white/20 text-white text-xs",children:"Update if needed"})]})}),(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"firstName",className:"text-sm font-bold text-sky-600",children:"First Name"}),(0,a.jsx)(P.p,{id:"firstName",...u("firstName"),className:"border-sky-200 focus:border-sky-500 bg-sky-50",placeholder:"Enter your first name"}),y.firstName&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:y.firstName.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"lastName",className:"text-sm font-bold text-sky-600",children:"Last Name"}),(0,a.jsx)(P.p,{id:"lastName",...u("lastName"),className:"border-sky-200 focus:border-sky-500 bg-sky-50",placeholder:"Enter your last name"}),y.lastName&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:y.lastName.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"email",className:"text-sm font-bold text-sky-600",children:"Email Address"}),(0,a.jsx)(P.p,{id:"email",type:"email",...u("email"),className:"border-sky-200 focus:border-sky-500 bg-sky-50",placeholder:"Enter your email address"}),y.email&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:y.email.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"phone",className:"text-sm font-bold text-sky-600",children:"Phone Number"}),(0,a.jsx)(P.p,{id:"phone",...u("phone"),className:"border-sky-200 focus:border-sky-500 bg-sky-50",placeholder:"Enter your phone number"}),y.phone&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:y.phone.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"dateOfBirth",className:"text-sm font-bold text-sky-600",children:"Date of Birth"}),(0,a.jsx)(P.p,{id:"dateOfBirth",type:"date",...u("dateOfBirth"),className:"border-sky-200 focus:border-sky-500 bg-sky-50"}),y.dateOfBirth&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:y.dateOfBirth.message})]})]})})]}),(0,a.jsxs)(n.Zp,{className:"border-0 shadow-lg bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg py-4",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Additional Details"})]})}),(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"nationality",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Nationality *"}),(0,a.jsx)(P.p,{...u("nationality"),id:"nationality",placeholder:"e.g., Indian",className:`h-9 text-sm border-2 transition-all duration-200 ${y.nationality?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"}`}),y.nationality&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:y.nationality.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"placeOfBirth",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Place of Birth *"}),(0,a.jsx)(P.p,{...u("placeOfBirth"),id:"placeOfBirth",placeholder:"e.g., Mumbai, India",className:`h-9 text-sm border-2 transition-all duration-200 ${y.placeOfBirth?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"}`}),y.placeOfBirth&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:y.placeOfBirth.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"gender",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Gender *"}),(0,a.jsxs)(R.l6,{onValueChange:e=>f("gender",e),children:[(0,a.jsx)(R.bq,{className:`h-9 text-sm border-2 transition-all duration-200 ${y.gender?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"}`,children:(0,a.jsx)(R.yv,{placeholder:"Select gender"})}),(0,a.jsxs)(R.gC,{children:[(0,a.jsx)(R.eb,{value:"male",children:"\uD83D\uDC68 Male"}),(0,a.jsx)(R.eb,{value:"female",children:"\uD83D\uDC69 Female"}),(0,a.jsx)(R.eb,{value:"other",children:"⚧ Other"})]})]}),y.gender&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:y.gender.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"maritalStatus",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Marital Status *"}),(0,a.jsxs)(R.l6,{onValueChange:e=>f("maritalStatus",e),children:[(0,a.jsx)(R.bq,{className:`h-9 text-sm border-2 transition-all duration-200 ${y.maritalStatus?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"}`,children:(0,a.jsx)(R.yv,{placeholder:"Select marital status"})}),(0,a.jsxs)(R.gC,{children:[(0,a.jsx)(R.eb,{value:"single",children:"\uD83D\uDC8D Single"}),(0,a.jsx)(R.eb,{value:"married",children:"\uD83D\uDC6B Married"}),(0,a.jsx)(R.eb,{value:"divorced",children:"\uD83D\uDCCB Divorced"}),(0,a.jsx)(R.eb,{value:"widowed",children:"\uD83D\uDD4A️ Widowed"})]})]}),y.maritalStatus&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:y.maritalStatus.message})]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-4",children:[(0,a.jsxs)(d.$,{type:"button",variant:"outline",onClick:t,className:"h-10 px-6 border-2 border-gray-300 text-black hover:bg-gray-50 font-medium",children:[(0,a.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Previous Step"]}),(0,a.jsx)(d.$,{type:"submit",disabled:m,className:"h-10 px-8 bg-sky-500 hover:bg-sky-600 text-white font-bold min-w-[140px]",children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:["Save & Continue",(0,a.jsx)(B.A,{className:"h-4 w-4 ml-2"})]})})]})]})})}var U=t(63407);let M=(0,h.A)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),I=S.Ik({street:S.Yj().min(5,"Street address must be at least 5 characters"),city:S.Yj().min(2,"City is required"),state:S.Yj().min(2,"State is required"),postalCode:S.Yj().min(5,"Valid postal code is required"),country:S.Yj().min(2,"Country is required"),addressType:S.k5(["permanent","current","mailing"],{required_error:"Please select address type"}),residenceSince:S.Yj().min(1,"Residence since date is required")}),L=["India","United States","United Kingdom","Canada","Australia","Singapore","UAE","Other"],_=["Andhra Pradesh","Arunachal Pradesh","Assam","Bihar","Chhattisgarh","Goa","Gujarat","Haryana","Himachal Pradesh","Jharkhand","Karnataka","Kerala","Madhya Pradesh","Maharashtra","Manipur","Meghalaya","Mizoram","Nagaland","Odisha","Punjab","Rajasthan","Sikkim","Tamil Nadu","Telangana","Tripura","Uttar Pradesh","Uttarakhand","West Bengal","Delhi","Puducherry","Chandigarh","Dadra and Nagar Haveli","Daman and Diu","Lakshadweep","Ladakh","Jammu and Kashmir"];function Y({initialData:e,onNext:s,onPrevious:t}){let[l,i]=(0,r.useState)(!1),[c,{isLoading:o}]=(0,N.Yx)(),[x,h]=(0,r.useState)(!0),{register:u,handleSubmit:p,formState:{errors:y},setValue:f,watch:j,reset:b}=(0,A.mN)({resolver:(0,D.u)(I),defaultValues:{street:e?.street||"",city:e?.city||"",state:e?.state||"",postalCode:e?.postalCode||"",country:e?.country||"India",addressType:e?.addressType||"current",residenceSince:e?.residenceSince||""}}),v=j("country"),w=async e=>{try{console.log("Submitting address info:",e);let t=await fetch("http://localhost:5000/api/kyc/address",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.message||"Failed to save address information")}let a=await t.json();console.log("Address info saved:",a),C.toast.success("Address information saved successfully!"),setTimeout(()=>{s()},1e3)}catch(e){console.error("Address update error:",e),C.toast.error(e.message||"Failed to save address information")}};return x?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 animate-spin mx-auto text-sky-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-sky-600",children:"Loading Address Information"}),(0,a.jsx)("p",{className:"text-sm text-sky-500",children:"Fetching your saved data..."})]})]})}):(0,a.jsx)("div",{className:"space-y-8",children:(0,a.jsxs)("form",{onSubmit:p(w),className:"space-y-6",children:[(0,a.jsxs)(n.Zp,{className:"border-0 shadow-lg bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg py-4",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Address Information"})]})}),(0,a.jsxs)(n.Wu,{className:"p-6 space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"addressType",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Address Type *"}),(0,a.jsxs)(R.l6,{onValueChange:e=>f("addressType",e),children:[(0,a.jsx)(R.bq,{className:`h-9 text-sm border-2 transition-all duration-200 ${y.addressType?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"}`,children:(0,a.jsx)(R.yv,{placeholder:"Select address type"})}),(0,a.jsxs)(R.gC,{children:[(0,a.jsx)(R.eb,{value:"current",children:"\uD83C\uDFE0 Current Address"}),(0,a.jsx)(R.eb,{value:"permanent",children:"\uD83C\uDFE1 Permanent Address"}),(0,a.jsx)(R.eb,{value:"mailing",children:"\uD83D\uDCEE Mailing Address"})]})]}),y.addressType&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:y.addressType.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"street",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Street Address *"}),(0,a.jsx)(P.p,{...u("street"),id:"street",placeholder:"House/Flat No., Building Name, Street",className:`h-9 text-sm border-2 transition-all duration-200 ${y.street?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"}`}),y.street&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:y.street.message})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"city",className:"text-xs font-bold text-black uppercase tracking-wide",children:"City *"}),(0,a.jsx)(P.p,{...u("city"),id:"city",placeholder:"e.g., Mumbai",className:`h-9 text-sm border-2 transition-all duration-200 ${y.city?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"}`}),y.city&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:y.city.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"postalCode",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Postal Code *"}),(0,a.jsx)(P.p,{...u("postalCode"),id:"postalCode",placeholder:"e.g., 400001",className:`h-9 text-sm border-2 transition-all duration-200 ${y.postalCode?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"}`}),y.postalCode&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:y.postalCode.message})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"state",className:"text-xs font-bold text-black uppercase tracking-wide",children:"State/Province *"}),"India"===v?(0,a.jsxs)(R.l6,{onValueChange:e=>f("state",e),children:[(0,a.jsx)(R.bq,{className:`h-9 text-sm border-2 transition-all duration-200 ${y.state?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"}`,children:(0,a.jsx)(R.yv,{placeholder:"Select state"})}),(0,a.jsx)(R.gC,{children:_.map(e=>(0,a.jsx)(R.eb,{value:e,children:e},e))})]}):(0,a.jsx)(P.p,{...u("state"),id:"state",placeholder:"e.g., California",className:`h-9 text-sm border-2 transition-all duration-200 ${y.state?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"}`}),y.state&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:y.state.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"country",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Country *"}),(0,a.jsxs)(R.l6,{onValueChange:e=>f("country",e),children:[(0,a.jsx)(R.bq,{className:`h-9 text-sm border-2 transition-all duration-200 ${y.country?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"}`,children:(0,a.jsx)(R.yv,{placeholder:"Select country"})}),(0,a.jsx)(R.gC,{children:L.map(e=>(0,a.jsxs)(R.eb,{value:e,children:["\uD83C\uDF0D ",e]},e))})]}),y.country&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:y.country.message})]})]})]})]}),(0,a.jsxs)(n.Zp,{className:"border-0 shadow-lg bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg py-4",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,a.jsx)(U.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Residence Information"})]})}),(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"residenceSince",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Living at this address since *"}),(0,a.jsx)(P.p,{...u("residenceSince"),id:"residenceSince",type:"date",className:`h-9 text-sm border-2 transition-all duration-200 ${y.residenceSince?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"}`}),y.residenceSince&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:y.residenceSince.message}),(0,a.jsx)("div",{className:"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-xs text-black font-medium",children:"\uD83D\uDCA1 This helps us verify your address stability for compliance purposes."})})]})})]}),(0,a.jsx)(n.Zp,{className:"bg-yellow-50 border-2 border-yellow-300 shadow-lg",children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-full",children:(0,a.jsx)(M,{className:"h-6 w-6 text-yellow-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-bold text-black text-lg mb-2",children:"\uD83D\uDCCB Address Verification Required"}),(0,a.jsx)("p",{className:"text-sm text-black mb-3",children:"You'll need to upload address proof documents in the next step. Accepted documents include:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-black",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,a.jsx)("span",{children:"Utility bills (not older than 3 months)"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-black",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,a.jsx)("span",{children:"Bank statements (not older than 3 months)"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-black",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,a.jsx)("span",{children:"Rental agreement or property documents"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-black",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,a.jsx)("span",{children:"Government-issued address proof"})]})]})]})]})})}),(0,a.jsxs)("div",{className:"flex justify-between pt-4",children:[(0,a.jsxs)(d.$,{type:"button",variant:"outline",onClick:t,className:"h-10 px-6 border-2 border-gray-300 text-black hover:bg-gray-50 font-medium",children:[(0,a.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Previous Step"]}),(0,a.jsx)(d.$,{type:"submit",disabled:o,className:"h-10 px-8 bg-sky-500 hover:bg-sky-600 text-white font-bold min-w-[140px]",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:["Save & Continue",(0,a.jsx)(B.A,{className:"h-4 w-4 ml-2"})]})})]})]})})}var O=t(42386),z=t(65653),Z=t(83241),W=t(5568),G=t(16716);let J=async(e,s,t)=>new Promise((a,r)=>{let l=new XMLHttpRequest;l.upload.addEventListener("progress",e=>{e.lengthComputable&&t&&t({loaded:e.loaded,total:e.total,percentage:Math.round(e.loaded/e.total*100)})}),l.addEventListener("load",()=>{200===l.status?a():r(Error(`Upload failed with status: ${l.status}`))}),l.addEventListener("error",()=>{r(Error("Upload failed due to network error"))}),l.addEventListener("abort",()=>{r(Error("Upload was aborted"))}),l.open("PUT",s),l.setRequestHeader("Content-Type",e.type),l.send(e)}),V=e=>e.size>0xa00000?{isValid:!1,error:"File size must be less than 10MB"}:["image/jpeg","image/jpg","image/png","image/webp","image/gif","image/bmp","image/tiff","image/svg+xml","image/heic","image/heif","application/pdf"].includes(e.type)?{isValid:!0}:{isValid:!1,error:"Supported formats: JPEG, PNG, WebP, GIF, BMP, TIFF, SVG, HEIC, HEIF, PDF"},K=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},H=e=>e.startsWith("image/")?"\uD83D\uDDBC️":"application/pdf"===e?"\uD83D\uDCC4":"\uD83D\uDCCE",X=e=>{let s=Date.now(),t=Math.random().toString(36).substring(2,8),a=e.split(".").pop(),r=e.replace(/\.[^/.]+$/,"");return`${r}_${s}_${t}.${a}`},Q=(e,s=1920,t=.8)=>new Promise(a=>{if(!e.type.startsWith("image/")||e.size<1048576)return void a(e);let r=document.createElement("canvas"),l=r.getContext("2d"),i=new Image;i.onload=()=>{let n=Math.min(s/i.width,s/i.height);r.width=i.width*n,r.height=i.height*n,l?.drawImage(i,0,0,r.width,r.height),r.toBlob(s=>{s?a(new File([s],e.name,{type:e.type,lastModified:Date.now()})):a(e)},e.type,t)},i.onerror=()=>a(e),i.src=URL.createObjectURL(e)}),ee=[{value:"aadhar",label:"Aadhaar Card",description:"Government issued identity card",category:"identity",required:!0},{value:"pan",label:"PAN Card",description:"Permanent Account Number card",category:"identity",required:!0},{value:"passport",label:"Passport",description:"International travel document",category:"identity",required:!1},{value:"drivers_license",label:"Driving License",description:"Government issued driving permit",category:"identity",required:!1},{value:"voter_id",label:"Voter ID Card",description:"Election commission identity card",category:"identity",required:!1},{value:"national_id",label:"National ID",description:"Government issued national identity card",category:"identity",required:!1},{value:"utility_bill",label:"Utility Bill",description:"Electricity, water, or gas bill",category:"address",required:!0},{value:"bank_statement",label:"Bank Statement",description:"Recent bank account statement",category:"address",required:!1},{value:"rental_agreement",label:"Rental Agreement",description:"Property rental contract",category:"address",required:!1}],es={identity:{title:"Identity Documents",description:"At least 2 identity documents required",required:2,options:["aadhar","pan","passport","drivers_license","voter_id","national_id"]},address:{title:"Address Proof",description:"At least 1 address proof required",required:1,options:["utility_bill","bank_statement","rental_agreement"]}};function et({onNext:e,onPrevious:s}){let[t,l]=(0,r.useState)(null),[i,h]=(0,r.useState)(null),[u,f]=(0,r.useState)(""),[j,v]=(0,r.useState)(""),[w,k]=(0,r.useState)(0),[A,D]=(0,r.useState)(!1),S=(0,r.useRef)(null),[T]=(0,N.Gy)(),[q]=(0,N.i6)(),{data:B,refetch:E}=(0,N.fW)(),$=B?.data||[],U=(()=>{let e=$.filter(e=>es.identity.options.includes(e.subType||"")&&"selfie"!==e.subType),s=$.filter(e=>es.address.options.includes(e.subType||"")),t=Math.min(e.length,es.identity.required),a=Math.min(s.length,es.address.required),r=es.identity.required+es.address.required,l=t+a;return{identityDocs:e,addressDocs:s,identityProgress:t,addressProgress:a,totalRequired:r,totalUploaded:l,progressPercentage:Math.round(l/r*100),isComplete:l>=r}})(),{isComplete:M,progressPercentage:I}=U,L=async()=>{if(!t||!u)return void C.toast.error("Please select a document and document type");try{D(!0),k(5);let s=await Q(t);k(10);let a=X(s.name),r=await q({fileName:a,fileType:s.type,fileSize:s.size,uploadType:"user-document"}).unwrap();if(!r.success||!r.data)throw Error("Failed to get upload URL");let{presignedUrl:l,uploadUrl:i}=r.data;k(20),await J(s,l,e=>{k(20+.6*e.percentage)}),k(85);let n=ee.find(e=>e.value===u),d=n?.category||"identity";await T({type:d,subType:u,documentNumber:j,fileUrl:i,fileName:s.name,fileSize:s.size,mimeType:s.type}).unwrap(),k(100),C.toast.success("Document uploaded successfully!"),setTimeout(()=>{k(0),D(!1),E(),setTimeout(()=>{e()},1e3)},1e3)}catch(e){console.error("Document upload error:",e),C.toast.error(e?.data?.message||"Failed to upload document"),D(!1),k(0)}};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)(n.Zp,{className:"border-0 shadow-xl bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-gradient-to-r from-sky-500 to-blue-600 text-white rounded-t-xl",children:(0,a.jsxs)(n.ZB,{className:"flex items-center justify-between text-xl",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(b.A,{className:"h-6 w-6"}),(0,a.jsx)("span",{children:"Document Requirements"})]}),(0,a.jsxs)(c.E,{className:"bg-white/20 text-white border-white/30",children:[U.totalUploaded,"/",U.totalRequired," Required"]})]})}),(0,a.jsxs)(n.Wu,{className:"p-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Overall Progress"}),(0,a.jsxs)("span",{className:"font-bold text-sky-600",children:[Math.round(I),"%"]})]}),(0,a.jsx)(O.k,{value:I,className:"h-3"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-blue-600"}),es.identity.title]}),(0,a.jsxs)(c.E,{variant:U.identityProgress>=es.identity.required?"default":"secondary",children:[U.identityProgress,"/",es.identity.required," Required"]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:es.identity.description}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:es.identity.options.map(e=>{let s=ee.find(s=>s.value===e),t=U.identityDocs.some(s=>s.subType===e);return(0,a.jsx)("div",{className:`p-3 rounded-lg border-2 transition-all ${t?"bg-green-50 border-green-200":"bg-gray-50 border-gray-200"}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:s?.label}),t?(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(y.A,{className:"h-4 w-4 text-gray-400"})]})},e)})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-green-600"}),es.address.title]}),(0,a.jsxs)(c.E,{variant:U.addressProgress>=es.address.required?"default":"secondary",children:[U.addressProgress,"/",es.address.required," Required"]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:es.address.description}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:es.address.options.map(e=>{let s=ee.find(s=>s.value===e),t=U.addressDocs.some(s=>s.subType===e);return(0,a.jsx)("div",{className:`p-3 rounded-lg border-2 transition-all ${t?"bg-green-50 border-green-200":"bg-gray-50 border-gray-200"}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:s?.label}),t?(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(y.A,{className:"h-4 w-4 text-gray-400"})]})},e)})})]})]}),$.length>0&&(0,a.jsxs)("div",{className:"p-6 rounded-xl border-2 bg-green-50 border-green-200 shadow-lg mt-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-white shadow-sm",children:(0,a.jsx)(p.A,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-bold text-lg text-green-700",children:"Documents Uploaded"}),(0,a.jsxs)(c.E,{className:"ml-2 bg-green-500 text-white text-xs",children:["✓ ",$.length," Documents"]})]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Great! You have uploaded ",$.length," document(s). You can upload additional documents if needed."]}),(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Uploaded Documents:"}),$.filter(e=>"selfie"!==e.subType).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-white rounded-lg border",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.subType?.replace("_"," ").toUpperCase()||"Document"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.documentNumber?`${e.documentNumber} • `:"","Uploaded: ",new Date(e.uploadedAt).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.E,{className:`text-xs ${"verified"===e.status?"bg-green-100 text-green-700":"rejected"===e.status?"bg-red-100 text-red-700":"bg-yellow-100 text-yellow-700"}`,children:e.status}),(0,a.jsx)(p.A,{className:"h-5 w-5 text-green-600"})]})]},s))]})]})]})]}),(0,a.jsxs)(n.Zp,{className:"border-0 shadow-xl bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-xl",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-3 text-xl",children:[(0,a.jsx)(x.A,{className:"h-6 w-6"}),(0,a.jsx)("span",{children:M?"Upload Additional Document":"Upload Required Documents"})]})}),(0,a.jsxs)(n.Wu,{className:"p-8 space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(F.J,{className:"text-sm font-bold text-gray-700",children:"Document Type *"}),(0,a.jsxs)(R.l6,{value:u,onValueChange:f,children:[(0,a.jsx)(R.bq,{className:"h-12 border-2 border-gray-200 focus:border-sky-500",children:(0,a.jsx)(R.yv,{placeholder:"Select document type"})}),(0,a.jsx)(R.gC,{children:ee.map(e=>(0,a.jsx)(R.eb,{value:e.value,children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:e.label}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:e.description})]})},e.value))})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(F.J,{className:"text-sm font-bold text-gray-700",children:"Document Number (Optional)"}),(0,a.jsx)(P.p,{value:j,onChange:e=>v(e.target.value),placeholder:"e.g., A1234567",className:"h-12 border-2 border-gray-200 focus:border-sky-500"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(F.J,{className:"text-sm font-bold text-gray-700",children:"Upload Document *"}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-blue-700",children:[(0,a.jsx)(z.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Supported: JPEG, PNG, WebP, GIF, BMP, TIFF, SVG, HEIC, HEIF, PDF • Max: 10MB"})]})}),(0,a.jsxs)("div",{className:`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${t?"border-green-400 bg-green-50":"border-gray-300 hover:border-sky-400 hover:bg-sky-50/50"}`,children:[(0,a.jsx)("input",{ref:S,type:"file",accept:"image/*,.pdf",onChange:e=>{let s=e.target.files?.[0];if(s){let e=V(s);if(!e.isValid)return void C.toast.error(e.error);l(s),h(URL.createObjectURL(s))}},className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer",disabled:A}),t?(0,a.jsxs)("div",{className:"space-y-4",children:[i&&(0,a.jsx)("div",{className:"mx-auto w-64 h-48 rounded-lg overflow-hidden shadow-lg border-2 border-green-200",children:"application/pdf"===t.type?(0,a.jsx)("div",{className:"w-full h-full bg-red-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(x.A,{className:"h-12 w-12 text-red-600 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm font-medium text-red-700",children:"PDF Document"}),(0,a.jsx)("p",{className:"text-xs text-red-600",children:t.name})]})}):(0,a.jsx)("img",{src:i,alt:"Document preview",className:"w-full h-full object-cover"})}),(0,a.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-green-700 mb-2",children:t.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[H(t.type)," ",K(t.size)]}),(0,a.jsxs)("div",{className:"flex justify-center space-x-3 mt-3",children:[(0,a.jsxs)(d.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{l(null),h(null),S.current&&(S.current.value="")},disabled:A,children:[(0,a.jsx)(Z.A,{className:"h-4 w-4 mr-1"}),"Remove"]}),i&&"application/pdf"!==t.type&&(0,a.jsxs)(d.$,{type:"button",variant:"outline",size:"sm",onClick:()=>window.open(i,"_blank"),children:[(0,a.jsx)(W.A,{className:"h-4 w-4 mr-1"}),"View Full"]})]})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"mx-auto w-16 h-16 bg-sky-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(G.A,{className:"h-8 w-8 text-sky-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-gray-700 mb-2",children:"Drag and drop your document here"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"or click to browse files"}),(0,a.jsxs)(d.$,{type:"button",variant:"outline",className:"border-sky-500 text-sky-600 hover:bg-sky-50",onClick:()=>S.current?.click(),disabled:A,children:[(0,a.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Choose File"]})]})]})]})]}),w>0&&w<100&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Uploading..."}),(0,a.jsxs)("span",{className:"font-bold text-sky-600",children:[w,"%"]})]}),(0,a.jsx)(O.k,{value:w,className:"h-3"})]}),(0,a.jsx)(d.$,{onClick:L,disabled:!t||!u||A,className:"w-full h-14 text-lg font-bold bg-sky-500 hover:bg-sky-600 text-white",children:A?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-3 h-5 w-5 animate-spin"}),"Uploading to Secure Storage..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(G.A,{className:"mr-3 h-5 w-5"}),"Upload Document Securely"]})})]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-6",children:[(0,a.jsx)(d.$,{onClick:s,variant:"outline",className:"px-8 py-3 text-lg",children:"Previous"}),M&&(0,a.jsx)(d.$,{onClick:e,className:"px-8 py-3 text-lg bg-green-500 hover:bg-green-600 text-white",children:"Continue"})]})]})}let ea=(0,h.A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var er=t(28966),el=t(56525);function ei({onCapture:e,onClose:s,title:t="Take Photo",instructions:l="Position yourself in the center and ensure good lighting"}){let i=(0,r.useRef)(null),c=(0,r.useRef)(null),[o,m]=(0,r.useState)(null),[x,h]=(0,r.useState)(!1),[u,p]=(0,r.useState)(null),[g,y]=(0,r.useState)(!1),f=(0,r.useCallback)(async()=>{try{y(!0);let e=await navigator.mediaDevices.getUserMedia({video:{width:{ideal:1280},height:{ideal:720},facingMode:"user"},audio:!1});i.current&&(i.current.srcObject=e,i.current.play(),m(e),h(!0))}catch(e){console.error("Error accessing camera:",e),C.toast.error("Unable to access camera. Please check permissions.")}finally{y(!1)}},[]),j=(0,r.useCallback)(()=>{o&&(o.getTracks().forEach(e=>e.stop()),m(null),h(!1))},[o]),b=(0,r.useCallback)(()=>{if(!i.current||!c.current)return;let e=i.current,s=c.current,t=s.getContext("2d");t&&(s.width=e.videoWidth,s.height=e.videoHeight,t.drawImage(e,0,0,s.width,s.height),s.toBlob(e=>{e&&(p(s.toDataURL("image/jpeg",.8)),j())},"image/jpeg",.8))},[j]),N=(0,r.useCallback)(()=>{p(null),f()},[f]),v=(0,r.useCallback)(()=>{u&&c.current&&c.current.toBlob(t=>{t&&(e(t,u),s())},"image/jpeg",.8)},[u,e,s]),w=(0,r.useCallback)(()=>{j(),s()},[j,s]),k=(0,r.useCallback)(t=>{let a=t.target.files?.[0];if(!a)return;if(!a.type.startsWith("image/"))return void C.toast.error("Please select an image file");let r=new FileReader;r.onload=t=>{let a=t.target?.result;p(a),j(),fetch(a).then(e=>e.blob()).then(t=>{e(t,a),s()})},r.readAsDataURL(a)},[e,s,j]);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,a.jsxs)(n.Zp,{className:"w-full max-w-2xl mx-4",children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between",children:[(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(er.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:t})]}),(0,a.jsx)(d.$,{variant:"ghost",size:"sm",onClick:w,children:(0,a.jsx)(Z.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:l})}),(0,a.jsxs)("div",{className:"relative bg-gray-100 rounded-lg overflow-hidden aspect-video",children:[u?(0,a.jsx)("img",{src:u,alt:"Captured",className:"w-full h-full object-cover"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("video",{ref:i,className:"w-full h-full object-cover",autoPlay:!0,playsInline:!0,muted:!0}),!x&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(er.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Camera not started"})]})})]}),x&&!u&&(0,a.jsxs)("div",{className:"absolute inset-0 pointer-events-none",children:[(0,a.jsx)("div",{className:"absolute inset-4 border-2 border-white border-dashed rounded-lg opacity-50"}),(0,a.jsx)("div",{className:"absolute top-4 left-4 right-4 text-center",children:(0,a.jsx)("p",{className:"text-white text-sm bg-black bg-opacity-50 rounded px-2 py-1",children:"Position yourself within the frame"})})]})]}),(0,a.jsxs)("div",{className:"flex justify-center space-x-4",children:[!x&&!u&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(d.$,{onClick:f,disabled:g,children:[(0,a.jsx)(er.A,{className:"h-4 w-4 mr-2"}),g?"Starting Camera...":"Start Camera"]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:k,className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer"}),(0,a.jsxs)(d.$,{variant:"outline",children:[(0,a.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Upload Image"]})]})]}),x&&!u&&(0,a.jsxs)(d.$,{onClick:b,size:"lg",children:[(0,a.jsx)(er.A,{className:"h-5 w-5 mr-2"}),"Capture Photo"]}),u&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(d.$,{variant:"outline",onClick:N,children:[(0,a.jsx)(ea,{className:"h-4 w-4 mr-2"}),"Retake"]}),(0,a.jsxs)(d.$,{onClick:v,children:[(0,a.jsx)(el.A,{className:"h-4 w-4 mr-2"}),"Use Photo"]})]})]}),(0,a.jsx)("canvas",{ref:c,className:"hidden"})]})]})})}function en({onNext:e,onPrevious:s}){let[t,l]=(0,r.useState)(null),[i,o]=(0,r.useState)(null),[m,x]=(0,r.useState)(!1),[h,g]=(0,r.useState)(!1),[y,f]=(0,r.useState)(!1),[j,b]=(0,r.useState)(!1),[v,w]=(0,r.useState)(0),k=(0,r.useRef)(null),A=(0,r.useRef)(null),D=(0,r.useRef)(null),[S]=(0,N.Gy)(),[P]=(0,N.i6)(),F=e=>{let s=k.current;if(!s)return{x:0,y:0};let t=s.getBoundingClientRect(),a=s.width/t.width,r=s.height/t.height;if(!("touches"in e))return{x:(e.clientX-t.left)*a,y:(e.clientY-t.top)*r};{let s=e.touches[0]||e.changedTouches[0];return{x:(s.clientX-t.left)*a,y:(s.clientY-t.top)*r}}},R=(0,r.useCallback)(e=>{e.preventDefault(),x(!0);let s=k.current;if(!s)return;let t=s.getContext("2d");if(!t)return;let a=F(e);t.beginPath(),t.moveTo(a.x,a.y)},[]),T=(0,r.useCallback)(e=>{if(e.preventDefault(),!m)return;let s=k.current;if(!s)return;let t=s.getContext("2d");if(!t)return;let a=F(e);t.lineTo(a.x,a.y),t.stroke()},[m]),E=(0,r.useCallback)(e=>{e.preventDefault(),x(!1)},[]),$=async()=>{try{if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)return void C.toast.error("Camera not supported on this device");let e=await navigator.mediaDevices.getUserMedia({video:{facingMode:"user",width:{ideal:640},height:{ideal:480}}});D.current=e,A.current&&(A.current.srcObject=e,A.current.onloadedmetadata=()=>{A.current?.play()},g(!0))}catch(e){console.error("Error accessing camera:",e),"NotAllowedError"===e.name?C.toast.error("Camera permission denied. Please allow camera access and try again."):"NotFoundError"===e.name?C.toast.error("No camera found on this device."):C.toast.error("Unable to access camera. Please check permissions.")}},U=()=>{D.current&&(D.current.getTracks().forEach(e=>e.stop()),D.current=null),g(!1)},M=(e,s)=>{let t=e.target.files?.[0];if(!t)return;if(t.size>5242880)return void C.toast.error("File size must be less than 5MB");if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(t.type))return void C.toast.error("Please upload a valid image file (JPEG, PNG, or WebP)");let a=new FileReader;a.onload=e=>{let t=e.target?.result;if(!t)return void C.toast.error("Failed to read file. Please try again.");"signature"===s?l(t):o(t),C.toast.success(`${"signature"===s?"Signature":"Selfie"} uploaded successfully!`)},a.onerror=()=>{C.toast.error("Failed to read file. Please try again.")},a.readAsDataURL(t),e.target.value=""},I=async(e,s,t)=>{let a=await fetch(e),r=await a.blob(),l=new File([r],s,{type:r.type});t?.(10);let i=await P({fileName:X(s),fileType:l.type,fileSize:l.size,uploadType:"user-document"}).unwrap();if(!i.success||!i.data)throw Error("Failed to get upload URL");let{presignedUrl:n,uploadUrl:d}=i.data;return t?.(30),await J(l,n,e=>{t?.(30+.6*e.percentage)}),t?.(100),d},L=async()=>{if(console.log("\uD83D\uDE80 Digital signature submit clicked",{hasSignature:!!t,hasSelfie:!!i}),!t||!i){C.toast.error("Please provide both digital signature and selfie"),console.log("❌ Missing signature or selfie",{signature:!!t,selfie:!!i});return}console.log("✅ Starting upload process..."),b(!0),w(0);try{console.log("\uD83D\uDCDD Uploading signature to S3..."),w(5);let s=await I(t,`signature_${Date.now()}.png`,e=>w(5+.4*e));console.log("✅ Signature uploaded:",s),console.log("\uD83D\uDCF8 Uploading selfie to S3..."),w(50);let a=await I(i,`selfie_${Date.now()}.jpg`,e=>w(50+.4*e));console.log("✅ Selfie uploaded:",a),console.log("\uD83D\uDCBE Saving documents to database..."),w(95),console.log("\uD83D\uDCBE Saving signature to database..."),await S({type:"other",subType:"digital_signature",fileUrl:s,fileName:`signature_${Date.now()}.png`,fileSize:0,mimeType:"image/png"}).unwrap(),console.log("✅ Signature saved to database"),console.log("\uD83D\uDCBE Saving selfie to database..."),await S({type:"other",subType:"selfie",fileUrl:a,fileName:`selfie_${Date.now()}.jpg`,fileSize:0,mimeType:"image/jpeg"}).unwrap(),console.log("✅ Selfie saved to database"),w(100),C.toast.success("Digital signature and selfie submitted successfully!"),console.log("✅ Digital signature step completed, advancing to next step..."),setTimeout(()=>{console.log("\uD83D\uDD04 Calling onNext() to advance to next step"),e()},1500)}catch(e){console.error("Upload error:",e),C.toast.error(e?.data?.message||"Failed to submit. Please try again.")}finally{b(!1),w(0)}};return(0,a.jsxs)(a.Fragment,{children:[y&&(0,a.jsx)(ei,{onCapture:(e,s)=>{o(s),f(!1),C.toast.success("Selfie captured successfully!")},onClose:()=>f(!1),title:"Take Selfie",instructions:"Position your face in the center and ensure good lighting for verification"}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)(n.Zp,{className:"border-0 shadow-lg bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg py-4",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,a.jsx)(u,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Digital Signature"}),(0,a.jsx)(c.E,{className:"bg-white/20 text-white",children:"Required"})]})}),(0,a.jsx)(n.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-medium text-sky-600 mb-2",children:"\uD83D\uDCDD Create Your Digital Signature"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Draw your signature in the box below using your mouse or finger"})]}),(0,a.jsxs)("div",{className:"bg-sky-50 border-2 border-sky-200 rounded-xl p-6",children:[(0,a.jsx)("div",{className:"text-center mb-4",children:(0,a.jsx)("p",{className:"text-xs font-medium text-sky-700 uppercase tracking-wide",children:"Signature Area"})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("canvas",{ref:k,width:600,height:200,className:"border-2 border-sky-300 rounded-lg cursor-crosshair w-full bg-white shadow-sm",style:{maxWidth:"100%",height:"auto",touchAction:"none"},onMouseDown:R,onMouseMove:T,onMouseUp:E,onMouseLeave:E,onTouchStart:R,onTouchMove:T,onTouchEnd:E}),!t&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center pointer-events-none",children:(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Sign here"})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 mt-6",children:[(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>{let e=k.current;if(!e)return;let s=e.getContext("2d");s&&(s.fillStyle="#ffffff",s.fillRect(0,0,e.width,e.height),s.strokeStyle="#000000",s.lineWidth=2,s.lineCap="round",s.lineJoin="round",l(null),C.toast.success("Signature cleared!"))},className:"flex-1 h-10 border-2 border-sky-500 text-sky-600 hover:bg-sky-50 font-medium",children:[(0,a.jsx)(ea,{className:"h-4 w-4 mr-2"}),"Clear Signature"]}),(0,a.jsxs)(d.$,{onClick:()=>{let e=k.current;if(!e)return;let s=e.getContext("2d");if(!s)return;let t=s.getImageData(0,0,e.width,e.height).data,a=!0;for(let e=0;e<t.length;e+=4)if(255!==t[e]||255!==t[e+1]||255!==t[e+2]){a=!1;break}if(a)return void C.toast.error("Please draw your signature first!");l(e.toDataURL("image/png",.8)),C.toast.success("Signature saved successfully!")},className:"flex-1 h-10 bg-sky-500 hover:bg-sky-600 text-white font-bold",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Save Signature"]})]})]}),(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-200"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-4 bg-white text-gray-500",children:"Or upload an image"})})]}),(0,a.jsx)("input",{type:"file",accept:"image/jpeg,image/jpg,image/png,image/webp",onChange:e=>M(e,"signature"),className:"hidden",id:"signature-upload"}),(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>document.getElementById("signature-upload")?.click(),className:"mt-4 h-10 border-2 border-sky-500 text-sky-600 hover:bg-sky-50 font-medium",children:[(0,a.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Upload Signature Image"]})]}),t&&(0,a.jsxs)("div",{className:"p-6 bg-sky-50 border-2 border-sky-200 rounded-xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-sky-500 rounded-full flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-bold text-sky-800",children:"Signature Saved Successfully"}),(0,a.jsx)("p",{className:"text-sm text-sky-600",children:"Your digital signature is ready"})]})]}),(0,a.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>l(null),className:"text-sky-600 hover:bg-sky-100",children:(0,a.jsx)(Z.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-white rounded-lg border border-sky-200",children:(0,a.jsx)("img",{src:t,alt:"Signature",className:"max-h-16 mx-auto"})})]})]})})]}),(0,a.jsxs)(n.Zp,{className:"border-0 shadow-lg bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg py-4",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,a.jsx)(er.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Identity Verification Selfie"}),(0,a.jsx)(c.E,{className:"bg-white/20 text-white",children:"Required"})]})}),(0,a.jsx)(n.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-medium text-sky-600 mb-2",children:"\uD83D\uDCF8 Take Your Identity Selfie"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Take a clear photo of yourself for identity verification"})]}),!i&&(0,a.jsx)("div",{className:"bg-sky-50 border-2 border-sky-200 rounded-xl p-6",children:(0,a.jsxs)("div",{className:"text-center space-y-6",children:[h?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"relative mx-auto max-w-sm",children:[(0,a.jsxs)("div",{className:"relative overflow-hidden rounded-xl border-4 border-sky-300 shadow-xl bg-black",children:[(0,a.jsx)("video",{ref:A,autoPlay:!0,playsInline:!0,muted:!0,className:"w-full h-auto object-cover",style:{aspectRatio:"3/4",maxHeight:"400px"}}),(0,a.jsxs)("div",{className:"absolute inset-0 pointer-events-none",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-48 h-64 border-2 border-white/50 rounded-full border-dashed"})}),(0,a.jsx)("div",{className:"absolute top-4 left-4 right-4",children:(0,a.jsx)("div",{className:"bg-black/70 text-white text-xs px-3 py-2 rounded-lg text-center",children:"\uD83D\uDCF8 Position your face within the oval guide"})})]}),(0,a.jsx)("div",{className:"absolute top-2 left-2 w-6 h-6 border-l-2 border-t-2 border-sky-400"}),(0,a.jsx)("div",{className:"absolute top-2 right-2 w-6 h-6 border-r-2 border-t-2 border-sky-400"}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 w-6 h-6 border-l-2 border-b-2 border-sky-400"}),(0,a.jsx)("div",{className:"absolute bottom-2 right-2 w-6 h-6 border-r-2 border-b-2 border-sky-400"})]}),(0,a.jsxs)("div",{className:"mt-3 text-center",children:[(0,a.jsx)("p",{className:"text-sm text-sky-700 font-medium",children:"\uD83D\uDCCB Make sure your face is clearly visible and well-lit"}),(0,a.jsx)("p",{className:"text-xs text-sky-600 mt-1",children:"Look directly at the camera • Remove glasses if possible"})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,a.jsxs)(d.$,{onClick:()=>{let e=A.current;if(!e||4!==e.readyState)return void C.toast.error("Camera not ready. Please wait a moment and try again.");let s=document.createElement("canvas");s.width=e.videoWidth||640,s.height=e.videoHeight||480;let t=s.getContext("2d");if(!t)return void C.toast.error("Failed to capture selfie. Please try again.");t.drawImage(e,0,0,s.width,s.height);let a=s.toDataURL("image/jpeg",.8);if("data:,"===a)return void C.toast.error("Failed to capture selfie. Please try again.");o(a),U(),C.toast.success("Selfie captured successfully!")},className:"h-14 px-8 bg-sky-500 hover:bg-sky-600 text-white font-bold text-lg shadow-lg",children:[(0,a.jsx)(er.A,{className:"h-6 w-6 mr-2"}),"\uD83D\uDCF8 Capture Photo"]}),(0,a.jsx)(d.$,{variant:"outline",onClick:U,className:"h-14 px-8 border-2 border-sky-500 text-sky-600 hover:bg-sky-50 font-medium",children:"❌ Cancel"})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"w-32 h-32 bg-gradient-to-br from-sky-100 to-sky-200 rounded-full flex items-center justify-center mx-auto shadow-lg",children:(0,a.jsx)(er.A,{className:"h-16 w-16 text-sky-600"})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-sky-800",children:"\uD83D\uDCF8 Take Your Selfie"}),(0,a.jsx)("p",{className:"text-sm text-sky-600 max-w-md mx-auto",children:"We need a clear photo of your face for identity verification. Make sure you're in a well-lit area."})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)(d.$,{onClick:()=>{f(!0)},className:"h-14 px-10 bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white font-bold text-lg shadow-lg transform hover:scale-105 transition-all duration-200",children:[(0,a.jsx)(er.A,{className:"h-6 w-6 mr-3"}),"\uD83D\uDCF7 Take Photo"]}),(0,a.jsxs)(d.$,{variant:"outline",onClick:$,className:"h-14 px-10 border-2 border-sky-500 text-sky-600 hover:bg-sky-50 font-bold text-lg",children:[(0,a.jsx)(er.A,{className:"h-6 w-6 mr-3"}),"\uD83D\uDCF9 Live Camera"]})]})]}),(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-200"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-4 bg-sky-50 text-gray-500",children:"Or upload a photo"})})]}),(0,a.jsx)("input",{type:"file",accept:"image/jpeg,image/jpg,image/png,image/webp",onChange:e=>M(e,"selfie"),className:"hidden",id:"selfie-upload"}),(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>document.getElementById("selfie-upload")?.click(),className:"mt-4 h-10 border-2 border-sky-500 text-sky-600 hover:bg-sky-50 font-medium",children:[(0,a.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Upload Selfie Image"]})]})]})}),i&&(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-green-50 to-sky-50 border-2 border-green-200 rounded-xl shadow-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg",children:(0,a.jsx)(p.A,{className:"h-6 w-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-bold text-green-800 text-lg",children:"✅ Selfie Captured Successfully!"}),(0,a.jsx)("p",{className:"text-sm text-green-600",children:"Your identity photo looks great"})]})]}),(0,a.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>o(null),className:"text-green-600 hover:bg-green-100 rounded-full",children:(0,a.jsx)(Z.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("div",{className:"inline-block p-4 bg-white rounded-2xl border-2 border-green-200 shadow-xl",children:(0,a.jsx)("img",{src:i,alt:"Captured Selfie",className:"max-h-40 max-w-40 rounded-xl object-cover shadow-lg"})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>o(null),className:"h-12 px-6 border-2 border-sky-500 text-sky-600 hover:bg-sky-50 font-medium",children:[(0,a.jsx)(er.A,{className:"h-5 w-5 mr-2"}),"\uD83D\uDCF8 Retake Photo"]}),(0,a.jsxs)(d.$,{onClick:()=>{},className:"h-12 px-6 bg-green-500 hover:bg-green-600 text-white font-medium",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 mr-2"}),"✅ Use This Photo"]})]})]})]})]})})]}),j&&v>0&&(0,a.jsx)(n.Zp,{className:"border-0 shadow-lg bg-white",children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Uploading..."}),(0,a.jsxs)("span",{className:"font-bold text-sky-600",children:[Math.round(v),"%"]})]}),(0,a.jsx)(O.k,{value:v,className:"h-3"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 text-center",children:v<50?"Uploading signature...":v<95?"Uploading selfie...":100===v?"✅ Complete! Advancing to next step...":"Saving to database..."})]})})}),(0,a.jsxs)("div",{className:"flex justify-between pt-4",children:[(0,a.jsxs)(d.$,{type:"button",variant:"outline",onClick:s,className:"h-10 px-6 border-2 border-sky-300 text-sky-600 hover:bg-sky-50 font-medium",children:[(0,a.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Previous Step"]}),(0,a.jsx)(d.$,{onClick:L,disabled:!t||!i||j,className:"h-10 px-8 bg-sky-500 hover:bg-sky-600 text-white font-bold min-w-[140px]",children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Submitting..."]}):(0,a.jsxs)(a.Fragment,{children:["Submit & Continue",(0,a.jsx)(B.A,{className:"h-4 w-4 ml-2"})]})})]})]})]})}var ed=t(24629),ec=t(64680),eo=t(60952),em=t(26314),ex=t(42291),eh=t(39827),eu=t(22195),ep=t(81861),eg="Checkbox",[ey,ef]=(0,ec.A)(eg),[ej,eb]=ey(eg);function eN(e){let{__scopeCheckbox:s,checked:t,children:l,defaultChecked:i,disabled:n,form:d,name:c,onCheckedChange:o,required:m,value:x="on",internal_do_not_use_render:h}=e,[u,p]=(0,em.i)({prop:t,defaultProp:i??!1,onChange:o,caller:eg}),[g,y]=r.useState(null),[f,j]=r.useState(null),b=r.useRef(!1),N=!g||!!d||!!g.closest("form"),v={checked:u,disabled:n,setChecked:p,control:g,setControl:y,name:c,form:d,value:x,hasConsumerStoppedPropagationRef:b,required:m,defaultChecked:!eP(i)&&i,isFormControl:N,bubbleInput:f,setBubbleInput:j};return(0,a.jsx)(ej,{scope:s,...v,children:"function"==typeof h?h(v):l})}var ev="CheckboxTrigger",ew=r.forwardRef(({__scopeCheckbox:e,onKeyDown:s,onClick:t,...l},i)=>{let{control:n,value:d,disabled:c,checked:o,required:m,setControl:x,setChecked:h,hasConsumerStoppedPropagationRef:u,isFormControl:p,bubbleInput:g}=eb(ev,e),y=(0,ed.s)(i,x),f=r.useRef(o);return r.useEffect(()=>{let e=n?.form;if(e){let s=()=>h(f.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[n,h]),(0,a.jsx)(ep.sG.button,{type:"button",role:"checkbox","aria-checked":eP(o)?"mixed":o,"aria-required":m,"data-state":eF(o),"data-disabled":c?"":void 0,disabled:c,value:d,...l,ref:y,onKeyDown:(0,eo.m)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,eo.m)(t,e=>{h(e=>!!eP(e)||!e),g&&p&&(u.current=e.isPropagationStopped(),u.current||e.stopPropagation())})})});ew.displayName=ev;var ek=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,name:r,checked:l,defaultChecked:i,required:n,disabled:d,value:c,onCheckedChange:o,form:m,...x}=e;return(0,a.jsx)(eN,{__scopeCheckbox:t,checked:l,defaultChecked:i,disabled:d,required:n,onCheckedChange:o,name:r,form:m,value:c,internal_do_not_use_render:({isFormControl:e})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ew,{...x,ref:s,__scopeCheckbox:t}),e&&(0,a.jsx)(eS,{__scopeCheckbox:t})]})})});ek.displayName=eg;var eC="CheckboxIndicator",eA=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,forceMount:r,...l}=e,i=eb(eC,t);return(0,a.jsx)(eu.C,{present:r||eP(i.checked)||!0===i.checked,children:(0,a.jsx)(ep.sG.span,{"data-state":eF(i.checked),"data-disabled":i.disabled?"":void 0,...l,ref:s,style:{pointerEvents:"none",...e.style}})})});eA.displayName=eC;var eD="CheckboxBubbleInput",eS=r.forwardRef(({__scopeCheckbox:e,...s},t)=>{let{control:l,hasConsumerStoppedPropagationRef:i,checked:n,defaultChecked:d,required:c,disabled:o,name:m,value:x,form:h,bubbleInput:u,setBubbleInput:p}=eb(eD,e),g=(0,ed.s)(t,p),y=(0,ex.Z)(n),f=(0,eh.X)(l);r.useEffect(()=>{if(!u)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!i.current;if(y!==n&&e){let t=new Event("click",{bubbles:s});u.indeterminate=eP(n),e.call(u,!eP(n)&&n),u.dispatchEvent(t)}},[u,y,n,i]);let j=r.useRef(!eP(n)&&n);return(0,a.jsx)(ep.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??j.current,required:c,disabled:o,name:m,value:x,form:h,...s,tabIndex:-1,ref:g,style:{...s.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function eP(e){return"indeterminate"===e}function eF(e){return eP(e)?"indeterminate":e?"checked":"unchecked"}eS.displayName=eD;var eR=t(40741),eT=t(21764);let eq=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(ek,{ref:t,className:(0,eT.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-gray-300 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white",e),...s,children:(0,a.jsx)(eA,{className:(0,eT.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(eR.A,{className:"h-4 w-4"})})}));eq.displayName=ek.displayName;var eB=t(34132),eE=t(2963);function e$({onPrevious:e,onStepChange:s}){let[t,l]=(0,r.useState)(!1),[i,c]=(0,r.useState)(!1),h=(0,w.GV)(k.xu),{data:u,refetch:y}=(0,N.xs)(),{data:f}=(0,N.jY)(),{data:j}=(0,N.VI)(),{data:v}=(0,N.fW)(),[A,{isLoading:D}]=(0,N.fx)();u?.data;let S=f?.data,P=j?.data,F=v?.data||[],R={firstName:S?.firstName||h?.firstName||"Not provided",lastName:S?.lastName||h?.lastName||"Not provided",email:S?.email||h?.email||"Not provided",phone:S?.phone||h?.phone||"Not provided",dateOfBirth:S?.dateOfBirth||h?.dateOfBirth||"Not provided",nationality:S?.nationality||"Not provided",placeOfBirth:S?.placeOfBirth||"Not provided",gender:S?.gender||"Not provided",maritalStatus:S?.maritalStatus||"Not provided"},T={street:P?.street||"Not provided",city:P?.city||"Not provided",state:P?.state||"Not provided",postalCode:P?.postalCode||"Not provided",country:P?.country||"Not provided",addressType:P?.addressType||"Not provided",residenceSince:P?.residenceSince||null},B=async()=>{if(!t||!i)return void C.toast.error("Please agree to all terms and conditions");try{console.log("\uD83D\uDE80 Submitting KYC for review..."),await A().unwrap(),console.log("✅ KYC submitted successfully"),C.toast.success("\uD83C\uDF89 KYC submitted successfully! We will review your documents within 1-2 business days."),y()}catch(e){console.error("KYC submission error:",e),C.toast.error(e?.data?.message||"Failed to submit KYC. Please try again.")}},E=async()=>{try{console.log("\uD83D\uDCC4 Downloading KYC PDF...");let e=await fetch("http://localhost:5000/api/kyc/download-pdf",{method:"GET",credentials:"include",headers:{Accept:"application/pdf"}});if(!e.ok)throw Error("Failed to download PDF");let s=await e.blob(),t=window.URL.createObjectURL(s),a=document.createElement("a");a.href=t,a.download=`KYC_Report_${new Date().toISOString().split("T")[0]}.pdf`,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(t),C.toast.success("PDF downloaded successfully!")}catch(e){console.error("PDF download error:",e),C.toast.error("Failed to download PDF. Please try again.")}};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)(n.Zp,{className:"border-0 shadow-xl bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-xl",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-3 text-xl",children:[(0,a.jsx)(b.A,{className:"h-6 w-6"}),(0,a.jsx)("span",{children:"KYC Review & Submission"})]})}),(0,a.jsx)(n.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-6 bg-sky-50 border-2 border-sky-200 rounded-xl",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-sky-500 rounded-full flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"h-7 w-7 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-black mb-1",children:"\uD83C\uDF89 KYC Information Complete"}),(0,a.jsx)("p",{className:"text-sky-700",children:"All required information has been provided. Please review and submit for verification."})]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Personal Information"})]}),(0,a.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>s(0),children:[(0,a.jsx)(eB.A,{className:"h-4 w-4 mr-2"}),"Edit"]})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Name:"}),(0,a.jsxs)("span",{className:"ml-2",children:[h?.firstName," ",h?.lastName]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Email:"}),(0,a.jsx)("span",{className:"ml-2",children:h?.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Phone:"}),(0,a.jsx)("span",{className:"ml-2",children:h?.phone||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Nationality:"}),(0,a.jsx)("span",{className:"ml-2",children:R.nationality||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Place of Birth:"}),(0,a.jsx)("span",{className:"ml-2",children:R.placeOfBirth||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Gender:"}),(0,a.jsx)("span",{className:"ml-2 capitalize",children:R.gender||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Marital Status:"}),(0,a.jsx)("span",{className:"ml-2 capitalize",children:R.maritalStatus||"Not provided"})]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Address Information"})]}),(0,a.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>s(1),children:[(0,a.jsx)(eB.A,{className:"h-4 w-4 mr-2"}),"Edit"]})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Address Type:"}),(0,a.jsx)("span",{className:"ml-2 capitalize",children:T.addressType||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Street Address:"}),(0,a.jsx)("span",{className:"ml-2",children:T.street||"Not provided"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"City:"}),(0,a.jsx)("span",{className:"ml-2",children:T.city||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"State:"}),(0,a.jsx)("span",{className:"ml-2",children:T.state||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Postal Code:"}),(0,a.jsx)("span",{className:"ml-2",children:T.postalCode||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Country:"}),(0,a.jsx)("span",{className:"ml-2",children:T.country||"Not provided"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Living since:"}),(0,a.jsx)("span",{className:"ml-2",children:T.residenceSince?new Date(T.residenceSince).toLocaleDateString():"Not provided"})]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Uploaded Documents"})]}),(0,a.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>s(2),children:[(0,a.jsx)(eB.A,{className:"h-4 w-4 mr-2"}),"Edit"]})]})}),(0,a.jsx)(n.Wu,{children:F.length>0?(0,a.jsx)("div",{className:"space-y-3",children:F.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-gray-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.type}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.documentNumber&&`Document #: ${e.documentNumber}`})]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:new Date(e.uploadedAt).toLocaleDateString()})]},s))}):(0,a.jsx)("p",{className:"text-gray-600",children:"No documents uploaded yet."})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Terms and Conditions"})]})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(eq,{id:"terms",checked:t,onCheckedChange:e=>l(e)}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("label",{htmlFor:"terms",className:"font-medium cursor-pointer",children:"I agree to the Terms and Conditions"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"I confirm that all information provided is accurate and complete. I understand that providing false information may result in account suspension."})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(eq,{id:"dataProcessing",checked:i,onCheckedChange:e=>c(e)}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("label",{htmlFor:"dataProcessing",className:"font-medium cursor-pointer",children:"I consent to data processing"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"I consent to the processing of my personal data for KYC verification purposes in accordance with applicable data protection laws."})]})]})]})]}),(0,a.jsx)(n.Zp,{className:"border-blue-200 bg-blue-50",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(z.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-blue-800",children:"What happens next?"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 mt-2 space-y-1",children:[(0,a.jsx)("li",{children:"• Your documents will be reviewed by our compliance team"}),(0,a.jsx)("li",{children:"• Verification typically takes 1-2 business days"}),(0,a.jsx)("li",{children:"• You'll receive an email notification once verification is complete"}),(0,a.jsx)("li",{children:"• If additional information is needed, we'll contact you directly"})]})]})]})})}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)(d.$,{type:"button",variant:"outline",onClick:e,children:[(0,a.jsx)(q.A,{className:"mr-2 h-4 w-4"}),"Previous"]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)(d.$,{type:"button",variant:"outline",onClick:E,className:"min-w-[140px]",children:[(0,a.jsx)(el.A,{className:"mr-2 h-4 w-4"}),"Download PDF"]}),(0,a.jsx)(d.$,{onClick:B,disabled:!t||!i||D,className:"min-w-[140px]",children:D?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Submitting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(eE.A,{className:"mr-2 h-4 w-4"}),"Submit for Verification"]})})]})]})]})}let eU=[{id:"personal",title:"Personal Info",description:"Basic details",icon:o.A,required:!0},{id:"address",title:"Address",description:"Location details",icon:m.A,required:!0},{id:"documents",title:"Documents",description:"ID & proof",icon:x.A,required:!0},{id:"signature",title:"Signature",description:"Digital sign",icon:u,required:!0},{id:"review",title:"Review",description:"Final check",icon:p.A,required:!0}];function eM(){(0,l.useRouter)();let e=(0,w.GV)(k.xu),[s,t]=(0,r.useState)(0),[o,m]=(0,r.useState)([]),{data:h,isLoading:u,error:A,refetch:D}=(0,N.xs)(),{data:S,isLoading:P,refetch:F}=(0,v.M7)(),[R,{isLoading:T}]=(0,N.et)(),[q,B]=(0,r.useState)({firstName:"",lastName:"",email:"",phone:"",dateOfBirth:"",nationality:"",placeOfBirth:"",gender:void 0,maritalStatus:void 0}),{data:E,isLoading:U,error:M}=(0,N.jY)(void 0,{skip:!e}),I=()=>{if(console.log("\uD83D\uDD04 KYC handleNext called",{currentStep:s,totalSteps:eU.length,currentStepTitle:eU[s]?.title}),s<eU.length-1){let e=eU[s].id,a=o.includes(e)?o:[...o,e];console.log("✅ Advancing from step",s,"to step",s+1),console.log("\uD83D\uDCDD Marking step as completed:",e),m(a),t(s+1),D()}else console.log("⚠️ Already at last step, cannot advance further")},L=()=>{s>0&&t(s-1)},_=e=>{(e<=s||o.includes(eU[e].id))&&t(e)},O=async()=>{try{console.log("\uD83D\uDCC4 Downloading KYC PDF...");let e=await R().unwrap(),s=window.URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download=`KYC_Report_${q.firstName}_${q.lastName}.pdf`,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(s),C.toast.success("KYC PDF downloaded successfully!")}catch(e){console.error("PDF download failed:",e),C.toast.error(e?.data?.message||"Failed to download PDF")}};if(u||P)return(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 animate-spin mx-auto text-sky-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-sky-600",children:"Loading KYC Information"}),(0,a.jsx)("p",{className:"text-sm text-sky-500",children:"Fetching your profile and KYC status..."})]})]})})});let z=eU[s],Z=(s+1)/eU.length*100,W=h?.data?.kyc?.status||"not_submitted";return(console.log("kycData",h),console.log("kycStatus",W),"pending_verification"===W||"under_review"===W)?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,a.jsxs)("div",{className:"text-center space-y-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"mx-auto w-24 h-24 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(y.A,{className:"h-12 w-12 text-yellow-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"KYC Under Review"}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:"Your documents are being verified by our team"})]})]}),(0,a.jsx)(n.Zp,{className:"max-w-2xl mx-auto border-yellow-200 bg-yellow-50",children:(0,a.jsx)(n.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-lg font-semibold text-yellow-800",children:"Verification in Progress"}),(0,a.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full animate-pulse"})]}),(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-700",children:"Thank you for submitting your KYC documents. Our verification team is currently reviewing your information."}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"What happens next?"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2 text-left",children:[(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{children:"Documents received and under review"})]}),(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsx)("span",{children:"Verification process (1-2 business days)"})]}),(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 text-blue-500"}),(0,a.jsx)("span",{children:"Email notification upon completion"})]})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"You will receive an email notification once the verification is complete. If you have any questions, please contact our support team."})]})]})})}),(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600",children:"Need help or want to download your KYC report?"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,a.jsxs)(d.$,{variant:"outline",className:"border-blue-300 text-blue-600 hover:bg-blue-50",onClick:O,disabled:T,children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Download PDF Report"]}),(0,a.jsxs)(d.$,{variant:"outline",className:"border-sky-300 text-sky-600 hover:bg-sky-50",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Contact Support"]})]})]})]})})}):(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"min-h-screen bg-white py-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-6",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-sky-600 mb-2 flex items-center gap-3",children:[(0,a.jsx)(b.A,{className:"h-8 w-8"}),"KYC Verification"]}),(0,a.jsx)("p",{className:"text-sky-600",children:"Complete your identity verification to unlock all investment features"})]}),(0,a.jsxs)("div",{className:"text-right space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(d.$,{onClick:O,disabled:T,variant:"outline",size:"sm",className:"border-sky-500 text-sky-600 hover:bg-sky-50",children:T?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Generating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Download PDF"]})}),(0,a.jsxs)("div",{className:`flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium ${(e=>{switch(e){case"approved":return"bg-green-100 text-green-700 border-green-300";case"pending":case"under_review":return"bg-yellow-100 text-yellow-700 border-yellow-300";case"rejected":return"bg-red-100 text-red-700 border-red-300";default:return"bg-gray-100 text-gray-700 border-gray-300"}})(W)}`,children:[(e=>{switch(e){case"approved":return(0,a.jsx)(p.A,{className:"h-4 w-4"});case"pending":case"under_review":return(0,a.jsx)(y.A,{className:"h-4 w-4"});case"rejected":return(0,a.jsx)(j.A,{className:"h-4 w-4"});default:return(0,a.jsx)(b.A,{className:"h-4 w-4"})}})(W),(0,a.jsx)("span",{className:"capitalize",children:W.replace("_"," ")})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-sm text-sky-600 mb-1",children:"Progress"}),(0,a.jsxs)("div",{className:"text-3xl font-bold text-sky-600",children:[Math.round(Z),"%"]})]})]})]})}),(0,a.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-8",children:eU.map((e,t)=>{let r=o.includes(e.id),l=t===s,i=t<=s||r;return(0,a.jsx)(n.Zp,{className:`cursor-pointer transition-all duration-200 hover:shadow-lg ${r?"border-sky-500 bg-sky-50 shadow-md":l?"border-yellow-400 bg-yellow-50 shadow-lg scale-105":i?"border-sky-200 bg-white hover:bg-sky-50":"border-gray-200 bg-gray-50 opacity-60"}`,onClick:()=>i&&_(t),children:(0,a.jsxs)(n.Wu,{className:"p-3 text-center",children:[(0,a.jsx)("div",{className:`w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2 ${r?"bg-sky-500 text-white":l?"bg-yellow-500 text-white":i?"bg-sky-100 text-sky-600":"bg-gray-100 text-gray-400"}`,children:r?(0,a.jsx)(p.A,{className:"h-5 w-5"}):(0,a.jsx)(e.icon,{className:"h-5 w-5"})}),(0,a.jsx)("h3",{className:`text-xs font-bold mb-1 ${l?"text-yellow-700":r?"text-sky-700":"text-sky-600"}`,children:e.title}),(0,a.jsx)("p",{className:`text-xs ${l?"text-yellow-600":r?"text-sky-600":"text-sky-500"}`,children:e.description})]})},e.id)})}),(0,a.jsxs)(n.Zp,{className:"shadow-lg border-sky-200 bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg",children:(0,a.jsxs)(n.ZB,{className:"flex items-center gap-3",children:[(0,a.jsx)(z.icon,{className:"h-6 w-6"}),z.title,(0,a.jsxs)(c.E,{className:"bg-white/20 text-white",children:["Step ",s+1," of ",eU.length]})]})}),(0,a.jsxs)(n.Wu,{className:"p-8",children:[0===s&&(0,a.jsx)($,{onNext:I,onPrevious:L,initialData:q}),1===s&&(0,a.jsx)(Y,{onNext:I,onPrevious:L}),2===s&&(0,a.jsx)(et,{onNext:I,onPrevious:L}),3===s&&(0,a.jsx)(en,{onNext:I,onPrevious:L}),4===s&&(0,a.jsx)(e$,{onPrevious:L,onStepChange:t})]})]})]})})})}},57387:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var a=t(40969),r=t(73356),l=t(21764);let i=r.forwardRef(({className:e,type:s,...t},r)=>(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));i.displayName="Input"},62402:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\kyc\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\kyc\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65653:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(99024).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},76650:(e,s,t)=>{"use strict";t.d(s,{E:()=>n});var a=t(40969);t(73356);var r=t(52774),l=t(21764);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:s,...t}){return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:s}),e),...t})}},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[755,3777,2544,7092,7555,3035,3319,6521,1795,2487,3427],()=>t(27217));module.exports=a})();