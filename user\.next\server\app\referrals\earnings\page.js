(()=>{var e={};e.id=7739,e.ids=[7739],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16634:(e,s,r)=>{Promise.resolve().then(r.bind(r,60567))},18703:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=r(10557),a=r(68490),l=r(13172),n=r.n(l),i=r(68835),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let c={children:["",{children:["referrals",{children:["earnings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,60567)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\referrals\\earnings\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\referrals\\earnings\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/referrals/earnings/page",pathname:"/referrals/earnings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28496:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(99024).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37079:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(99024).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},56021:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(99024).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},56525:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(99024).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},56802:(e,s,r)=>{Promise.resolve().then(r.bind(r,64089))},60567:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\referrals\\\\earnings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\referrals\\earnings\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63407:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(99024).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},64089:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>g});var t=r(40969);r(73356);var a=r(37020),l=r(85306),n=r(66949),i=r(76650),d=r(46411),c=r(56021),o=r(63407),x=r(14125),m=r(45548),u=r(29419),h=r(37079),p=r(56525);function g(){let e=[{title:"Total Earnings",value:"₹45,680",change:"+12.5%",icon:c.A,color:"text-green-600",bgColor:"bg-green-50"},{title:"This Month",value:"₹8,450",change:"+18.2%",icon:o.A,color:"text-blue-600",bgColor:"bg-blue-50"},{title:"Active Referrals",value:"12",change:"+3",icon:x.A,color:"text-purple-600",bgColor:"bg-purple-50"},{title:"Conversion Rate",value:"68%",change:"+5%",icon:m.A,color:"text-sky-600",bgColor:"bg-sky-50"}],s=e=>{switch(e){case"paid":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"processing":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},r=e=>{switch(e){case"Investment Bonus":return"\uD83D\uDCB0";case"Registration Bonus":return"\uD83C\uDF81";case"Monthly Bonus":return"\uD83D\uDCC5";default:return"\uD83D\uDCB5"}};return(0,t.jsx)(a.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(l.Ay,{title:"Referral Earnings",description:"Track your referral commissions, bonuses, and performance metrics",icon:u.A,gradient:!0,breadcrumbs:[{label:"Referrals",href:"/referrals"},{label:"Earnings"}],actions:(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(l.lX.Secondary,{children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Filter Period"]}),(0,t.jsxs)(l.lX.Primary,{children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Download Report"]})]})}),(0,t.jsx)(l.T0,{stats:[{label:"Total Earnings",value:"₹45,680",change:"+12.5%",trend:"up"},{label:"This Month",value:"₹8,450",change:"+18.2%",trend:"up"},{label:"Active Referrals",value:"12",change:"+3",trend:"up"},{label:"Conversion Rate",value:"68%",change:"+5%",trend:"up"}]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:e.map((e,s)=>{let r=e.icon;return(0,t.jsx)(n.Zp,{className:"border-0 shadow-lg",children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:e.value}),(0,t.jsxs)("div",{className:"flex items-center mt-2",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 text-green-600 mr-1"}),(0,t.jsx)("span",{className:"text-sm font-medium text-green-600",children:e.change})]})]}),(0,t.jsx)("div",{className:`p-3 rounded-full ${e.bgColor} ${e.color}`,children:(0,t.jsx)(r,{className:"h-6 w-6"})})]})})},s)})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-purple-600"}),(0,t.jsx)("span",{children:"Bonus Types"})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Investment Bonus"})]}),(0,t.jsx)("span",{className:"font-bold",children:"₹32,450"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Registration Bonus"})]}),(0,t.jsx)("span",{className:"font-bold",children:"₹8,500"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Monthly Bonus"})]}),(0,t.jsx)("span",{className:"font-bold",children:"₹4,730"})]})]})})]}),(0,t.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Payout Schedule"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Next Payout"}),(0,t.jsx)("span",{className:"font-medium",children:"Jan 30, 2024"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Pending Amount"}),(0,t.jsx)("span",{className:"font-bold text-yellow-600",children:"₹1,200"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Payout Method"}),(0,t.jsx)("span",{className:"font-medium",children:"Bank Transfer"})]}),(0,t.jsx)("div",{className:"pt-3 border-t",children:(0,t.jsx)(d.$,{variant:"outline",className:"w-full",children:"Update Payout Details"})})]})})]}),(0,t.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Performance"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-sm",children:"Monthly Target"}),(0,t.jsx)("span",{className:"text-sm",children:"84%"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"84%"}})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-sm",children:"Yearly Goal"}),(0,t.jsx)("span",{className:"text-sm",children:"67%"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"67%"}})})]})]})})]})]}),(0,t.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsx)("span",{children:"Earnings History"})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:[{id:1,date:"2024-01-15",referralName:"Amit Sharma",type:"Investment Bonus",amount:2500,status:"paid",property:"Luxury Apartments - Phase 1"},{id:2,date:"2024-01-12",referralName:"Priya Patel",type:"Registration Bonus",amount:500,status:"paid",property:"-"},{id:3,date:"2024-01-10",referralName:"Rahul Kumar",type:"Investment Bonus",amount:3750,status:"paid",property:"Commercial Complex - Tower A"},{id:4,date:"2024-01-08",referralName:"Sneha Gupta",type:"Monthly Bonus",amount:1200,status:"pending",property:"Residential Villas"},{id:5,date:"2024-01-05",referralName:"Vikash Singh",type:"Investment Bonus",amount:1800,status:"paid",property:"Green Valley Project"}].map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"text-2xl",children:r(e.type)}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:e.referralName}),(0,t.jsx)(i.E,{variant:"secondary",className:"text-xs",children:e.type})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.property}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.date})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("p",{className:"font-bold text-green-600",children:["₹",e.amount.toLocaleString()]}),(0,t.jsx)(i.E,{className:s(e.status),children:e.status})]})})]},e.id))})})]})]})})}},76650:(e,s,r)=>{"use strict";r.d(s,{E:()=>i});var t=r(40969);r(73356);var a=r(52774),l=r(21764);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:s,...r}){return(0,t.jsx)("div",{className:(0,l.cn)(n({variant:s}),e),...r})}},79551:e=>{"use strict";e.exports=require("url")},85306:(e,s,r)=>{"use strict";r.d(s,{Ay:()=>c,T0:()=>o,lX:()=>x});var t=r(40969);r(73356);var a=r(46411),l=r(76650),n=r(21764),i=r(28496),d=r(12011);function c({title:e,description:s,icon:r,badge:c,actions:o,breadcrumbs:x,showBackButton:m=!1,className:u,gradient:h=!1}){let p=(0,d.useRouter)();return(0,t.jsxs)("div",{className:(0,n.cn)("relative overflow-hidden",h&&"bg-gradient-to-r from-sky-50 via-blue-50 to-indigo-50",!h&&"bg-white",u),children:[h&&(0,t.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,t.jsx)("div",{className:"relative px-4 sm:px-6 lg:px-8 py-6 sm:py-8",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[x&&x.length>0&&(0,t.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,t.jsx)("ol",{className:"flex items-center space-x-2 text-sm",children:x.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center",children:[s>0&&(0,t.jsx)("span",{className:"mx-2 text-gray-400",children:"/"}),e.href?(0,t.jsx)("button",{onClick:()=>p.push(e.href),className:"text-gray-600 hover:text-sky-600 transition-colors",children:e.label}):(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:e.label})]},s))})}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[m&&(0,t.jsx)(a.$,{variant:"ghost",size:"icon",onClick:()=>p.back(),className:"flex-shrink-0 mt-1",children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}),r&&(0,t.jsx)("div",{className:(0,n.cn)("flex-shrink-0 p-3 rounded-xl",h?"bg-white/80 backdrop-blur-sm shadow-lg":"bg-sky-50","text-sky-600"),children:(0,t.jsx)(r,{className:"h-6 w-6"})}),(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center flex-wrap gap-3 mb-2",children:[(0,t.jsx)("h1",{className:(0,n.cn)("text-2xl sm:text-3xl font-bold text-gray-900 leading-tight",h&&"bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent"),children:e}),c&&(0,t.jsx)(l.E,{variant:c.variant||"default",className:(0,n.cn)("text-xs font-medium",c.className),children:c.text})]}),s&&(0,t.jsx)("p",{className:"text-gray-600 text-sm sm:text-base max-w-2xl leading-relaxed",children:s})]})]}),o&&(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"flex items-center space-x-3",children:o})})]})]})}),(0,t.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"})]})}function o({stats:e,className:s}){return(0,t.jsx)("div",{className:(0,n.cn)("grid grid-cols-2 sm:grid-cols-4 gap-4 mt-6",s),children:e.map((e,s)=>(0,t.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50",children:[(0,t.jsx)("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide",children:e.label}),(0,t.jsx)("p",{className:"text-lg font-bold text-gray-900 mt-1",children:e.value}),e.change&&(0,t.jsx)("p",{className:(0,n.cn)("text-xs font-medium mt-1","up"===e.trend&&"text-green-600","down"===e.trend&&"text-red-600","neutral"===e.trend&&"text-gray-600"),children:e.change})]},s))})}let x={Primary:({children:e,...s})=>(0,t.jsx)(a.$,{className:"bg-sky-600 hover:bg-sky-700 text-white shadow-lg",...s,children:e}),Secondary:({children:e,...s})=>(0,t.jsx)(a.$,{variant:"outline",className:"border-sky-200 text-sky-700 hover:bg-sky-50",...s,children:e}),Ghost:({children:e,...s})=>(0,t.jsx)(a.$,{variant:"ghost",className:"text-gray-600 hover:text-sky-600 hover:bg-sky-50",...s,children:e})}}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[755,3777,2544,7092,7555,2487,3427],()=>r(18703));module.exports=t})();