"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[440],{2790:(e,s,t)=>{t.d(s,{E:()=>n});var a=t(9605);t(9585);var l=t(7276),r=t(6994);let i=(0,l.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:t,...l}=e;return(0,a.jsx)("div",{className:(0,r.cn)(i({variant:t}),s),...l})}},2892:(e,s,t)=>{t.d(s,{b:()=>w});var a=t(9605),l=t(9585),r=t(4685),i=t(2933),n=t(2790),o=t(4128),d=t(8164),c=t(8333);function m(e){let{images:s,alt:t,className:r="",autoSlide:n=!0,slideInterval:m=4e3}=e,[h,x]=(0,l.useState)(0);(0,l.useEffect)(()=>{if(!n||s.length<=1)return;let e=setInterval(()=>{x(e=>e===s.length-1?0:e+1)},m);return()=>clearInterval(e)},[n,s.length,m]);let u=e=>{x(e)};if(!s||0===s.length)return(0,a.jsx)("div",{className:"bg-gradient-to-br from-sky-100 to-sky-200 flex items-center justify-center ".concat(r),children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center text-sky-400",children:[(0,a.jsx)(o.A,{className:"w-16 h-16 mb-2"}),(0,a.jsx)("span",{className:"text-sm",children:"No Images"})]})});let g=s[h],p="string"==typeof g?g:null==g?void 0:g.url;return(0,a.jsxs)("div",{className:"relative overflow-hidden group ".concat(r),children:[(0,a.jsx)("img",{src:p,alt:t,className:"w-full h-full object-cover transition-transform duration-500 group-hover:scale-105",onError:e=>{e.target.src="https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop"}}),s.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.$,{variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300",onClick:()=>{x(0===h?s.length-1:h-1)},children:(0,a.jsx)(d.A,{className:"w-4 h-4"})}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300",onClick:()=>{x(h===s.length-1?0:h+1)},children:(0,a.jsx)(c.A,{className:"w-4 h-4"})})]}),s.length>1&&(0,a.jsx)("div",{className:"absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-2",children:s.map((e,s)=>(0,a.jsx)("button",{className:"w-2 h-2 rounded-full transition-all duration-300 ".concat(s===h?"bg-white scale-125":"bg-white/50 hover:bg-white/75"),onClick:()=>u(s)},s))}),s.length>1&&(0,a.jsxs)("div",{className:"absolute top-3 right-3 bg-black/70 text-white text-xs px-2 py-1 rounded-full",children:[h+1," / ",s.length]})]})}var h=t(6994),x=t(1843),u=t(7229),g=t(4844),p=t(5857),b=t(1468),y=t(5048),v=t(4397),f=t(3192),j=t(6351),N=t(3183),k=t(9798);function w(e){var s,t,l,o,d,c,w,D,T,C,A,S,F,E,q,U,O;let{property:P,isOpen:J,onClose:R,onCopyReferral:L,copiedReferralId:z}=e;if(!P)return null;let $=e=>{window.open(e,"_blank")};return(0,a.jsx)(r.lG,{open:J,onOpenChange:R,children:(0,a.jsxs)(r.Cf,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(r.c7,{children:(0,a.jsx)(r.L3,{className:"text-2xl font-bold text-gray-900",children:P.name})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"h-80 rounded-lg overflow-hidden",children:(0,a.jsx)(m,{images:P.images&&P.images.length>0?P.images:[{url:"residential"===P.propertyType?"https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop":"commercial"===P.propertyType?"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop":"industrial"===P.propertyType?"https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=800&h=600&fit=crop":"eco_friendly"===P.propertyType?"https://images.unsplash.com/photo-1518780664697-55e3ad937233?w=800&h=600&fit=crop":"https://images.unsplash.com/photo-1613977257363-707ba9348227?w=800&h=600&fit=crop",name:"".concat(P.name," - Main View")}],alt:P.name,className:"h-full",autoSlide:!0,slideInterval:5e3})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(n.E,{className:"bg-blue-100 text-blue-800 text-sm",children:[(0,a.jsx)(x.A,{className:"w-4 h-4 mr-1"}),null==(s=P.propertyType)?void 0:s.replace("_"," ").toUpperCase()]}),(0,a.jsx)(n.E,{className:"bg-green-100 text-green-800 text-sm",children:null==(t=P.status)?void 0:t.replace("_"," ").toUpperCase()})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 text-yellow-500 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:(null==(l=P.location)?void 0:l.address)||"Address"}),(0,a.jsxs)("p",{className:"text-gray-600",children:[(null==(o=P.location)?void 0:o.city)||"City",", ",(null==(d=P.location)?void 0:d.state)||"State"," - ",(null==(c=P.location)?void 0:c.pincode)||"Pincode"]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 mr-2 text-blue-500"}),"Developer Information"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Name:"}),(0,a.jsx)("span",{className:"font-medium",children:(null==(w=P.developer)?void 0:w.name)||"Developer Name"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Experience:"}),(0,a.jsxs)("span",{className:"font-medium flex items-center",children:[(0,a.jsx)(g.A,{className:"w-4 h-4 mr-1 text-purple-500"}),(null==(D=P.developer)?void 0:D.experience)||0," years"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Contact:"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(i.$,{size:"sm",variant:"outline",className:"text-xs",children:[(0,a.jsx)(p.A,{className:"w-3 h-3 mr-1"}),(null==(T=P.developer)?void 0:T.contact)||"Contact"]}),(0,a.jsxs)(i.$,{size:"sm",variant:"outline",className:"text-xs",children:[(0,a.jsx)(b.A,{className:"w-3 h-3 mr-1"}),"Email"]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-sky-50 to-blue-50 p-4 rounded-lg border border-sky-100",children:[(0,a.jsxs)("h3",{className:"font-semibold text-sky-700 mb-3 flex items-center",children:[(0,a.jsx)(y.A,{className:"w-5 h-5 mr-2"}),"Investment Overview"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[P.expectedReturns||0,"%"]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Expected Returns"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:P.maturityPeriodMonths||0}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Months"})]})]})]}),P.stock&&(0,a.jsxs)("div",{className:"bg-white border border-gray-200 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 mr-2 text-purple-500"}),"Stock Details"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Stock Price:"}),(0,a.jsx)("span",{className:"font-bold text-sky-600",children:(0,h.vv)((null==(C=P.stock)?void 0:C.stockPrice)||0)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Available:"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:(0,h.ZV)((null==(A=P.stock)?void 0:A.availableStocks)||0)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Sold:"}),(0,a.jsx)("span",{className:"font-medium text-orange-600",children:(0,h.ZV)((null==(S=P.stock)?void 0:S.stocksSold)||0)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Min Investment:"}),(0,a.jsx)("span",{className:"font-medium",children:(0,h.ZV)((null==(F=P.stock)?void 0:F.minimumPurchase)||0)})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-600 mb-1",children:[(0,a.jsx)("span",{children:"Sales Progress"}),(0,a.jsxs)("span",{children:[((null==(E=P.stock)?void 0:E.soldPercentage)||0).toFixed(1),"% sold"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full",style:{width:"".concat(Math.min((null==(q=P.stock)?void 0:q.soldPercentage)||0,100),"%")}})})]})]}),P.stock&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-semibold text-green-700 mb-3 flex items-center",children:[(0,a.jsx)(f.A,{className:"w-5 h-5 mr-2"}),"Commission Structure"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"text-center bg-white p-3 rounded",children:[(0,a.jsxs)("div",{className:"text-xl font-bold text-green-600",children:[(null==(U=P.stock)?void 0:U.salesCommissionRate)||0,"%"]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Sales Commission"})]}),(0,a.jsxs)("div",{className:"text-center bg-white p-3 rounded",children:[(0,a.jsxs)("div",{className:"text-xl font-bold text-blue-600",children:[(null==(O=P.stock)?void 0:O.referralCommissionRate)||0,"%"]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Referral Commission"})]})]})]}),P.documents&&P.documents.length>0&&(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 mr-2 text-gray-600"}),"Documents (",P.documents.length,")"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[P.documents.slice(0,3).map((e,s)=>(0,a.jsxs)(i.$,{variant:"outline",size:"sm",className:"w-full justify-between text-left",onClick:()=>$(e.url||e),children:[(0,a.jsx)("span",{className:"truncate",children:e.name||"Document ".concat(s+1)}),(0,a.jsx)(N.A,{className:"w-4 h-4"})]},s)),P.documents.length>3&&(0,a.jsxs)("p",{className:"text-sm text-gray-500 text-center",children:["+",P.documents.length-3," more documents"]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(i.$,{className:"w-full bg-green-500 hover:bg-green-600 text-white",children:[(0,a.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Start Investment Process"]}),(0,a.jsx)(i.$,{variant:"outline",className:"w-full border-blue-300 text-blue-600 hover:bg-blue-50",onClick:()=>L(P.id,P.name),children:z===P.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Referral Link Copied!"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"w-4 h-4 mr-2"}),"Copy Referral Link"]})})]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Description"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:P.description})]}),P.amenities&&P.amenities.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Amenities"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:P.amenities.map((e,s)=>(0,a.jsx)(n.E,{variant:"outline",className:"text-sm",children:e},s))})]})]})})}},3500:(e,s,t)=>{t.d(s,{AG:()=>a,R2:()=>n,S3:()=>r,TS:()=>T,Yp:()=>u,Z4:()=>y,Zz:()=>i,dy:()=>h,eV:()=>x,mc:()=>p,rh:()=>o,xc:()=>c});let{useGetTasksQuery:a,useGetTaskByIdQuery:l,useCreateTaskMutation:r,useUpdateTaskMutation:i,useDeleteTaskMutation:n,useCompleteTaskMutation:o,useGetTaskStatsQuery:d,useGetCalendarEventsQuery:c,useGetCalendarEventByIdQuery:m,useCreateCalendarEventMutation:h,useUpdateCalendarEventMutation:x,useDeleteCalendarEventMutation:u,useGetTodaysTasksQuery:g,useGetUpcomingTasksQuery:p,useGetOverdueTasksQuery:b,useGetTodaysEventsQuery:y,useGetUpcomingEventsQuery:v,useBulkUpdateTasksMutation:f,useBulkDeleteTasksMutation:j,useGetTaskTemplatesQuery:N,useCreateTaskFromTemplateMutation:k,useSnoozeTaskMutation:w,useAddTaskReminderMutation:D,useGetSalesTeamQuery:T,useGetUserByIdQuery:C}=t(6701).q.injectEndpoints({endpoints:e=>({getTasks:e.query({query:e=>({url:"/sales/tasks",params:e}),providesTags:["Task"]}),getTaskById:e.query({query:e=>"/sales/tasks/".concat(e),providesTags:(e,s,t)=>[{type:"Task",id:t}]}),createTask:e.mutation({query:e=>({url:"/sales/tasks",method:"POST",body:e}),invalidatesTags:["Task","Calendar","Dashboard"]}),updateTask:e.mutation({query:e=>{let{id:s,data:t}=e;return{url:"/sales/tasks/".concat(s),method:"PUT",body:t}},invalidatesTags:(e,s,t)=>{let{id:a}=t;return[{type:"Task",id:a},"Task","Calendar","Dashboard"]}}),deleteTask:e.mutation({query:e=>({url:"/sales/tasks/".concat(e),method:"DELETE"}),invalidatesTags:["Task","Calendar","Dashboard"]}),completeTask:e.mutation({query:e=>{let{id:s,notes:t,actualDuration:a}=e;return{url:"/sales/tasks/".concat(s,"/complete"),method:"POST",body:{notes:t,actualDuration:a}}},invalidatesTags:(e,s,t)=>{let{id:a}=t;return[{type:"Task",id:a},"Task","Dashboard"]}}),getTaskStats:e.query({query:e=>({url:"/sales/tasks/stats",params:e}),providesTags:["Task","Dashboard"]}),getCalendarEvents:e.query({query:e=>({url:"/sales/calendar/events",params:e}),providesTags:["Calendar"]}),getCalendarEventById:e.query({query:e=>"/sales/calendar/events/".concat(e),providesTags:(e,s,t)=>[{type:"Calendar",id:t}]}),createCalendarEvent:e.mutation({query:e=>({url:"/sales/calendar/events",method:"POST",body:e}),invalidatesTags:["Calendar","Task","Dashboard"]}),updateCalendarEvent:e.mutation({query:e=>{let{id:s,data:t}=e;return{url:"/sales/calendar/events/".concat(s),method:"PUT",body:t}},invalidatesTags:(e,s,t)=>{let{id:a}=t;return[{type:"Calendar",id:a},"Calendar","Task"]}}),deleteCalendarEvent:e.mutation({query:e=>({url:"/sales/calendar/events/".concat(e),method:"DELETE"}),invalidatesTags:["Calendar","Task"]}),getTodaysTasks:e.query({query:()=>"/sales/tasks/today",providesTags:["Task","Dashboard"]}),getUpcomingTasks:e.query({query:e=>({url:"/sales/tasks/upcoming",params:e}),providesTags:["Task","Dashboard"]}),getOverdueTasks:e.query({query:()=>"/sales/tasks/overdue",providesTags:["Task","Dashboard"]}),getTodaysEvents:e.query({query:()=>"/sales/calendar/today",providesTags:["Calendar","Dashboard"]}),getUpcomingEvents:e.query({query:e=>({url:"/sales/calendar/upcoming",params:e}),providesTags:["Calendar","Dashboard"]}),bulkUpdateTasks:e.mutation({query:e=>({url:"/sales/tasks/bulk-update",method:"PUT",body:e}),invalidatesTags:["Task"]}),bulkDeleteTasks:e.mutation({query:e=>({url:"/sales/tasks/bulk-delete",method:"DELETE",body:{taskIds:e}}),invalidatesTags:["Task"]}),getTaskTemplates:e.query({query:()=>"/sales/tasks/templates",providesTags:["Task"]}),createTaskFromTemplate:e.mutation({query:e=>({url:"/sales/tasks/from-template",method:"POST",body:e}),invalidatesTags:["Task","Calendar"]}),snoozeTask:e.mutation({query:e=>{let{id:s,snoozeUntil:t}=e;return{url:"/sales/tasks/".concat(s,"/snooze"),method:"POST",body:{snoozeUntil:t}}},invalidatesTags:(e,s,t)=>{let{id:a}=t;return[{type:"Task",id:a},"Task"]}}),addTaskReminder:e.mutation({query:e=>{let{id:s,reminderTime:t}=e;return{url:"/sales/tasks/".concat(s,"/reminders"),method:"POST",body:{reminderTime:t}}},invalidatesTags:(e,s,t)=>{let{id:a}=t;return[{type:"Task",id:a},"Task"]}}),getSalesTeam:e.query({query:e=>({url:"/sales/team",params:{limit:100,...e}}),providesTags:["User"]}),getUserById:e.query({query:e=>"/users/".concat(e),providesTags:(e,s,t)=>[{type:"User",id:t}]})})})},4685:(e,s,t)=>{t.d(s,{Cf:()=>m,Es:()=>x,L3:()=>u,c7:()=>h,lG:()=>o});var a=t(9605),l=t(9585),r=t(2848),i=t(4721),n=t(6994);let o=r.bL;r.l9;let d=r.ZL;r.bm;let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.hJ,{ref:s,className:(0,n.cn)("fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...l})});c.displayName=r.hJ.displayName;let m=l.forwardRef((e,s)=>{let{className:t,children:l,...o}=e;return(0,a.jsxs)(d,{children:[(0,a.jsx)(c,{}),(0,a.jsxs)(r.UC,{ref:s,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-gray-300 bg-white text-black p-6 shadow-xl duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...o,children:[l,(0,a.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=r.UC.displayName;let h=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left text-black",s),...t})};h.displayName="DialogHeader";let x=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};x.displayName="DialogFooter";let u=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.hE,{ref:s,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight text-black",t),...l})});u.displayName=r.hE.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.VY,{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...l})}).displayName=r.VY.displayName},6440:(e,s,t)=>{t.d(s,{A:()=>el});var a=t(9605),l=t(9585),r=t(6762),i=t.n(r),n=t(5935),o=t(6994),d=t(9421),c=t(3188),m=t(6901),h=t(8849),x=t(4252),u=t(4726),g=t(4835),p=t(5048),b=t(9581),y=t(4842),v=t(7418),f=t(6351),j=t(6380),N=t(9714),k=t(9722);let w=[{name:"Dashboard",href:"/dashboard",icon:c.A,description:"SGM overview & analytics"},{name:"Leads",href:"/leads",icon:m.A,description:"Manage property leads"},{name:"Customers",href:"/customers",icon:h.A,description:"Customer management"},{name:"Properties",href:"/properties",icon:x.A,description:"SGM property portfolio"},{name:"Tasks",href:"/tasks",icon:u.A,description:"Sales tasks & activities"},{name:"Calendar",href:"/calendar",icon:g.A,description:"Events & meetings"},{name:"Sales",href:"/sales",icon:p.A,description:"Track sales performance"},{name:"Commissions",href:"/commissions",icon:b.A,description:"Commission tracking"},{name:"Targets",href:"/targets",icon:y.A,description:"Sales targets & goals"},{name:"Reports",href:"/reports",icon:v.A,description:"Analytics & insights"},{name:"Documents",href:"/documents",icon:f.A,description:"Sales documents"},{name:"Support",href:"/support",icon:j.A,description:"Get help & create tickets"}],D=[{name:"Settings",href:"/settings",icon:N.A,description:"Account settings"}];function T(e){let{className:s}=e,t=(0,n.usePathname)();return(0,a.jsxs)("div",{className:(0,o.cn)("flex h-full w-64 flex-col bg-gradient-to-b from-white via-sky-25 to-sky-50 border-r border-sky-200 shadow-xl",s),children:[(0,a.jsx)("div",{className:"flex h-20 items-center px-6 border-b border-sky-200 bg-gradient-to-r from-sky-100 via-white to-sky-100 shadow-sm",children:(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsx)(d.A,{size:"md",variant:"full"})})}),(0,a.jsx)("div",{className:"flex-1 px-4 py-6 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-200 scrollbar-track-transparent",children:(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-xs font-bold text-sky-600 uppercase tracking-wider mb-4 px-2",children:"Main Menu"}),(0,a.jsx)("nav",{className:"space-y-2",children:w.map(e=>{let s=t===e.href||t.startsWith(e.href+"/");return(0,a.jsxs)(i(),{href:e.href,className:(0,o.cn)("group relative flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 transform hover:scale-[1.02]",s?"bg-gradient-to-r from-sky-500 to-sky-600 text-white shadow-lg shadow-sky-200 border border-sky-400":"text-gray-700 hover:bg-gradient-to-r hover:from-sky-50 hover:to-sky-100 hover:text-sky-700 hover:shadow-md hover:border hover:border-sky-200"),children:[(0,a.jsx)("div",{className:(0,o.cn)("mr-4 p-2.5 rounded-lg transition-all duration-300",s?"bg-white/20 shadow-sm":"bg-sky-50 group-hover:bg-sky-100 group-hover:shadow-sm"),children:(0,a.jsx)(e.icon,{className:(0,o.cn)("h-5 w-5 flex-shrink-0 transition-all duration-300",s?"text-white":"text-sky-600 group-hover:text-sky-700")})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("div",{className:(0,o.cn)("font-semibold truncate",s?"text-white":"text-gray-900 group-hover:text-sky-900"),children:e.name}),(0,a.jsx)("div",{className:(0,o.cn)("text-xs transition-colors truncate mt-0.5",s?"text-sky-100":"text-gray-500 group-hover:text-sky-600"),children:e.description})]}),s&&(0,a.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full shadow-sm animate-pulse"})}),!s&&(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity duration-300 absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-sky-400 rounded-full"})})]},e.name)})})]})}),(0,a.jsx)("div",{className:"px-4 py-5 border-t border-sky-200 bg-gradient-to-r from-sky-50 via-white to-sky-50",children:(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h3",{className:"text-xs font-bold text-sky-600 uppercase tracking-wider mb-3 px-2",children:"Account"}),(0,a.jsxs)("div",{className:"space-y-2",children:[D.map(e=>{let s=t===e.href;return(0,a.jsxs)(i(),{href:e.href,className:(0,o.cn)("group relative flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 transform hover:scale-[1.02]",s?"bg-gradient-to-r from-sky-500 to-sky-600 text-white shadow-lg shadow-sky-200":"text-gray-700 hover:bg-gradient-to-r hover:from-sky-50 hover:to-sky-100 hover:text-sky-700 hover:shadow-md"),children:[(0,a.jsx)("div",{className:(0,o.cn)("mr-4 p-2.5 rounded-lg transition-all duration-300",s?"bg-white/20 shadow-sm":"bg-sky-50 group-hover:bg-sky-100"),children:(0,a.jsx)(e.icon,{className:(0,o.cn)("h-5 w-5 flex-shrink-0 transition-colors",s?"text-white":"text-sky-600 group-hover:text-sky-700")})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:(0,o.cn)("font-semibold",s?"text-white":"text-gray-900 group-hover:text-sky-900"),children:e.name}),(0,a.jsx)("div",{className:(0,o.cn)("text-xs transition-colors",s?"text-sky-100":"text-gray-500 group-hover:text-sky-600"),children:e.description})]}),s&&(0,a.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full shadow-sm animate-pulse"})})]},e.name)}),(0,a.jsxs)("button",{className:"w-full group relative flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 transform hover:scale-[1.02] text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-red-100 hover:shadow-md mt-2",children:[(0,a.jsx)("div",{className:"mr-4 p-2.5 rounded-lg bg-red-50 group-hover:bg-red-100 transition-all duration-300 shadow-sm",children:(0,a.jsx)(k.A,{className:"h-5 w-5 flex-shrink-0 text-red-500 group-hover:text-red-600"})}),(0,a.jsxs)("div",{className:"flex-1 text-left",children:[(0,a.jsx)("div",{className:"font-semibold text-red-700 group-hover:text-red-800",children:"Logout"}),(0,a.jsx)("div",{className:"text-xs text-red-500 group-hover:text-red-600",children:"Sign out safely"})]}),(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity duration-300 absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-red-400 rounded-full"})})]})]})]})}),(0,a.jsx)("div",{className:"px-4 py-4 bg-gradient-to-r from-gray-50 via-sky-25 to-gray-50 border-t border-sky-200",children:(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-sky-500 to-sky-600 rounded-lg flex items-center justify-center shadow-sm",children:(0,a.jsx)("div",{className:"w-3 h-3 bg-white rounded-sm"})}),(0,a.jsx)("p",{className:"text-xs text-gray-700 font-bold",children:"SGM Sales Portal"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-1",children:[(0,a.jsx)("div",{className:"w-1 h-1 bg-sky-400 rounded-full"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"\xa9 2024 All rights reserved"}),(0,a.jsx)("div",{className:"w-1 h-1 bg-sky-400 rounded-full"})]}),(0,a.jsx)("div",{className:"flex items-center justify-center space-x-1"})]})})]})}var C=t(6992),A=t(7183),S=t(9960),F=t(6466),E=t(8232),q=t(6277),U=t(2933);function O(e){let{onMenuClick:s,title:t="Dashboard",showSearch:r=!0}=e,[i,n]=(0,l.useState)(!1);return(0,a.jsx)("header",{className:"bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg border-b border-sky-700 flex-shrink-0 z-40",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(U.$,{variant:"ghost",size:"icon",onClick:s,className:"lg:hidden hover:bg-white/20 text-white",children:(0,a.jsx)(C.A,{className:"h-5 w-5"})}),r&&(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2.5 min-w-[350px] border border-white/30",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 text-white/70"}),(0,a.jsx)("input",{type:"text",placeholder:"Search leads, properties, customers...",className:"bg-transparent border-none outline-none flex-1 text-sm placeholder:text-white/70 text-white"}),(0,a.jsx)("kbd",{className:"hidden lg:inline-flex items-center px-2 py-1 text-xs font-medium text-sky-600 bg-white rounded border border-white/30",children:"⌘K"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(U.$,{variant:"ghost",size:"icon",className:"hover:bg-white/20 text-white",children:(0,a.jsx)(S.A,{className:"h-5 w-5"})}),(0,a.jsxs)(U.$,{variant:"ghost",size:"icon",className:"relative hover:bg-white/20 text-white",children:[(0,a.jsx)(F.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 h-5 w-5 bg-yellow-500 text-black text-xs rounded-full flex items-center justify-center font-semibold",children:"3"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(U.$,{variant:"ghost",onClick:()=>n(!i),className:"flex items-center space-x-3 px-3 py-2 hover:bg-white/20 rounded-lg text-white",children:[(0,a.jsxs)("div",{className:"hidden sm:block text-right",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-white",children:"John Doe"}),(0,a.jsx)("div",{className:"flex items-center justify-end space-x-1",children:(0,a.jsx)("div",{className:"bg-yellow-500 px-2 py-0.5 rounded-full",children:(0,a.jsx)("span",{className:"text-xs font-medium text-black capitalize",children:"SGM Sales Executive"})})})]}),(0,a.jsx)("div",{className:"h-10 w-10 bg-white/20 rounded-full flex items-center justify-center shadow-sm border border-white/30",children:(0,a.jsx)(E.A,{className:"h-5 w-5 text-white"})}),(0,a.jsx)(q.A,{className:"h-4 w-4 text-white/70"})]}),i&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sky-200 py-2 z-50",children:[(0,a.jsxs)("div",{className:"px-4 py-2 border-b border-sky-100",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"John Doe"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"<EMAIL>"})]}),(0,a.jsxs)(U.$,{variant:"ghost",className:"w-full justify-start px-4 py-2 text-sm text-gray-700 hover:bg-sky-50",children:[(0,a.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Profile"]}),(0,a.jsxs)(U.$,{variant:"ghost",className:"w-full justify-start px-4 py-2 text-sm text-gray-700 hover:bg-sky-50",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Settings"]}),(0,a.jsx)("div",{className:"border-t border-sky-100 mt-2 pt-2",children:(0,a.jsxs)(U.$,{variant:"ghost",className:"w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Sign out"]})})]})]})]})]})})})}var P=t(4965),J=t(7555),R=t(4685),L=t(7971),z=t(6467),$=t(9577),I=t(5097),M=t(1713),B=t(8347),V=t(4485),G=t(3500),_=t(3815);function Z(e){let{isOpen:s,onClose:t,onSuccess:r}=e,[i,n]=(0,l.useState)({title:"",description:"",type:"call",priority:"medium",dueDate:"",dueTime:"",estimatedDuration:30,tags:"",notes:""}),[o,{isLoading:d}]=(0,G.S3)(),c=(0,P.G)(_.mB),m=(0,P.j)(),h=async e=>{e.preventDefault();try{let e={...i,assignedTo:null==c?void 0:c.id,assignedToName:"".concat(null==c?void 0:c.firstName," ").concat(null==c?void 0:c.lastName),createdBy:null==c?void 0:c.id,createdByName:"".concat(null==c?void 0:c.firstName," ").concat(null==c?void 0:c.lastName),tags:i.tags.split(",").map(e=>e.trim()).filter(Boolean),status:"pending"};await o(e).unwrap(),n({title:"",description:"",type:"call",priority:"medium",dueDate:"",dueTime:"",estimatedDuration:30,tags:"",notes:""}),m((0,J.$U)({type:"success",title:"Task Created",message:"Task has been created successfully"})),m((0,J.Oo)("create-task")),null==r||r()}catch(e){console.error("Failed to create task:",e),m((0,J.$U)({type:"error",title:"Error",message:"Failed to create task. Please try again."}))}},x=()=>{m((0,J.Oo)("create-task")),null==t||t()};return(0,a.jsx)(R.lG,{open:s,onOpenChange:x,children:(0,a.jsxs)(R.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(R.c7,{children:(0,a.jsxs)(R.L3,{className:"text-xl font-semibold flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 text-blue-600"}),"Create New Task"]})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"title",className:"text-sm font-medium text-black",children:"Task Title *"}),(0,a.jsx)(L.p,{id:"title",value:i.title,onChange:e=>n({...i,title:e.target.value}),placeholder:"Enter task title",required:!0,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"description",className:"text-sm font-medium text-black",children:"Description"}),(0,a.jsx)(z.T,{id:"description",value:i.description,onChange:e=>n({...i,description:e.target.value}),placeholder:"Describe the task",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"type",className:"text-sm font-medium text-black",children:"Task Type"}),(0,a.jsxs)($.l6,{value:i.type,onValueChange:e=>n({...i,type:e}),children:[(0,a.jsx)($.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)($.yv,{})}),(0,a.jsxs)($.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)($.eb,{value:"call",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDE Phone Call"}),(0,a.jsx)($.eb,{value:"email",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCE7 Email"}),(0,a.jsx)($.eb,{value:"meeting",className:"text-black hover:bg-sky-100",children:"\uD83E\uDD1D Meeting"}),(0,a.jsx)($.eb,{value:"follow_up",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD04 Follow Up"}),(0,a.jsx)($.eb,{value:"demo",className:"text-black hover:bg-sky-100",children:"\uD83C\uDFAF Demo"}),(0,a.jsx)($.eb,{value:"proposal",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCCB Proposal"}),(0,a.jsx)($.eb,{value:"other",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDD Other"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"priority",className:"text-sm font-medium text-black",children:"Priority"}),(0,a.jsxs)($.l6,{value:i.priority,onValueChange:e=>n({...i,priority:e}),children:[(0,a.jsx)($.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)($.yv,{})}),(0,a.jsxs)($.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)($.eb,{value:"low",className:"text-black hover:bg-green-100",children:"\uD83D\uDFE2 Low"}),(0,a.jsx)($.eb,{value:"medium",className:"text-black hover:bg-yellow-100",children:"\uD83D\uDFE1 Medium"}),(0,a.jsx)($.eb,{value:"high",className:"text-black hover:bg-yellow-100",children:"\uD83D\uDFE0 High"}),(0,a.jsx)($.eb,{value:"urgent",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD34 Urgent"})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"dueDate",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(M.A,{className:"w-4 h-4"}),"Due Date *"]}),(0,a.jsx)(L.p,{id:"dueDate",type:"date",value:i.dueDate,onChange:e=>n({...i,dueDate:e.target.value}),required:!0,className:"w-full bg-white text-black border-gray-300"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"dueTime",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(B.A,{className:"w-4 h-4"}),"Due Time"]}),(0,a.jsx)(L.p,{id:"dueTime",type:"time",value:i.dueTime,onChange:e=>n({...i,dueTime:e.target.value}),className:"w-full bg-white text-black border-gray-300"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"duration",className:"text-sm font-medium text-black",children:"Estimated Duration (minutes)"}),(0,a.jsx)(L.p,{id:"duration",type:"number",value:i.estimatedDuration,onChange:e=>n({...i,estimatedDuration:parseInt(e.target.value)||30}),min:"5",max:"480",className:"w-full bg-white text-black border-gray-300"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"tags",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(V.A,{className:"w-4 h-4"}),"Tags (comma separated)"]}),(0,a.jsx)(L.p,{id:"tags",value:i.tags,onChange:e=>n({...i,tags:e.target.value}),placeholder:"lead, follow-up, urgent",className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"notes",className:"text-sm font-medium text-black",children:"Notes"}),(0,a.jsx)(z.T,{id:"notes",value:i.notes,onChange:e=>n({...i,notes:e.target.value}),placeholder:"Additional notes or instructions",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2 bg-sky-50 p-3 rounded-md border border-sky-200",children:[(0,a.jsx)(I.J,{className:"text-sm font-medium text-black",children:"Assigned To (Auto-assigned)"}),(0,a.jsxs)("div",{className:"text-sm text-black bg-white p-2 rounded border",children:["\uD83D\uDC64 ",null==c?void 0:c.firstName," ",null==c?void 0:c.lastName," (",null==c?void 0:c.email,")"]}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Tasks are automatically assigned to you as the creator."})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-300",children:[(0,a.jsx)(U.$,{type:"button",variant:"outline",onClick:x,className:"border-gray-300 text-black hover:bg-gray-100",children:"Cancel"}),(0,a.jsx)(U.$,{type:"submit",disabled:d,className:"bg-sky-500 hover:bg-sky-600 text-white",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Create Task"]})})]})]})})]})})}function H(e){let{isOpen:s,onClose:t,onSuccess:r,task:i}=e,[n,o]=(0,l.useState)({title:"",description:"",type:"call",priority:"medium",status:"pending",dueDate:"",dueTime:"",estimatedDuration:30,tags:"",notes:""}),[d,{isLoading:c}]=(0,G.Zz)(),m=(0,P.G)(_.mB),h=(0,P.j)();(0,l.useEffect)(()=>{i&&o({title:i.title||"",description:i.description||"",type:i.type||"call",priority:i.priority||"medium",status:i.status||"pending",dueDate:i.dueDate?i.dueDate.split("T")[0]:"",dueTime:i.dueTime||"",estimatedDuration:i.estimatedDuration||30,tags:i.tags?i.tags.join(", "):"",notes:i.notes||""})},[i]);let x=async e=>{if(e.preventDefault(),i)try{let e={...n,tags:n.tags.split(",").map(e=>e.trim()).filter(Boolean)};await d({id:i.id,...e}).unwrap(),h((0,J.$U)({type:"success",title:"Task Updated",message:"Task has been updated successfully"})),h((0,J.Oo)("edit-task")),null==r||r()}catch(e){console.error("Failed to update task:",e),h((0,J.$U)({type:"error",title:"Error",message:"Failed to update task. Please try again."}))}},g=()=>{h((0,J.Oo)("edit-task")),null==t||t()};return i?(0,a.jsx)(R.lG,{open:s,onOpenChange:g,children:(0,a.jsxs)(R.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(R.c7,{children:(0,a.jsxs)(R.L3,{className:"text-xl font-semibold flex items-center gap-2 text-black",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 text-sky-500"}),"Edit Task"]})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("form",{onSubmit:x,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"title",className:"text-sm font-medium text-black",children:"Task Title *"}),(0,a.jsx)(L.p,{id:"title",value:n.title,onChange:e=>o({...n,title:e.target.value}),placeholder:"Enter task title",required:!0,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"description",className:"text-sm font-medium text-black",children:"Description"}),(0,a.jsx)(z.T,{id:"description",value:n.description,onChange:e=>o({...n,description:e.target.value}),placeholder:"Describe the task",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"type",className:"text-sm font-medium text-black",children:"Task Type"}),(0,a.jsxs)($.l6,{value:n.type,onValueChange:e=>o({...n,type:e}),children:[(0,a.jsx)($.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)($.yv,{})}),(0,a.jsxs)($.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)($.eb,{value:"call",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDE Phone Call"}),(0,a.jsx)($.eb,{value:"email",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCE7 Email"}),(0,a.jsx)($.eb,{value:"meeting",className:"text-black hover:bg-sky-100",children:"\uD83E\uDD1D Meeting"}),(0,a.jsx)($.eb,{value:"follow_up",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD04 Follow Up"}),(0,a.jsx)($.eb,{value:"demo",className:"text-black hover:bg-sky-100",children:"\uD83C\uDFAF Demo"}),(0,a.jsx)($.eb,{value:"proposal",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCCB Proposal"}),(0,a.jsx)($.eb,{value:"other",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDD Other"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"priority",className:"text-sm font-medium text-black",children:"Priority"}),(0,a.jsxs)($.l6,{value:n.priority,onValueChange:e=>o({...n,priority:e}),children:[(0,a.jsx)($.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)($.yv,{})}),(0,a.jsxs)($.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)($.eb,{value:"low",className:"text-black hover:bg-green-100",children:"\uD83D\uDFE2 Low"}),(0,a.jsx)($.eb,{value:"medium",className:"text-black hover:bg-yellow-100",children:"\uD83D\uDFE1 Medium"}),(0,a.jsx)($.eb,{value:"high",className:"text-black hover:bg-yellow-100",children:"\uD83D\uDFE0 High"}),(0,a.jsx)($.eb,{value:"urgent",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD34 Urgent"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"status",className:"text-sm font-medium text-black",children:"Status"}),(0,a.jsxs)($.l6,{value:n.status,onValueChange:e=>o({...n,status:e}),children:[(0,a.jsx)($.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)($.yv,{})}),(0,a.jsxs)($.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)($.eb,{value:"pending",className:"text-black hover:bg-yellow-100",children:"⏳ Pending"}),(0,a.jsx)($.eb,{value:"in_progress",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD04 In Progress"}),(0,a.jsx)($.eb,{value:"completed",className:"text-black hover:bg-green-100",children:"✅ Completed"}),(0,a.jsx)($.eb,{value:"cancelled",className:"text-black hover:bg-gray-100",children:"❌ Cancelled"}),(0,a.jsx)($.eb,{value:"overdue",className:"text-black hover:bg-yellow-100",children:"⚠️ Overdue"})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"dueDate",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(M.A,{className:"w-4 h-4"}),"Due Date *"]}),(0,a.jsx)(L.p,{id:"dueDate",type:"date",value:n.dueDate,onChange:e=>o({...n,dueDate:e.target.value}),required:!0,className:"w-full bg-white text-black border-gray-300"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"dueTime",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(B.A,{className:"w-4 h-4"}),"Due Time"]}),(0,a.jsx)(L.p,{id:"dueTime",type:"time",value:n.dueTime,onChange:e=>o({...n,dueTime:e.target.value}),className:"w-full bg-white text-black border-gray-300"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"duration",className:"text-sm font-medium text-black",children:"Estimated Duration (minutes)"}),(0,a.jsx)(L.p,{id:"duration",type:"number",value:n.estimatedDuration,onChange:e=>o({...n,estimatedDuration:parseInt(e.target.value)||30}),min:"5",max:"480",className:"w-full bg-white text-black border-gray-300"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"tags",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(V.A,{className:"w-4 h-4"}),"Tags (comma separated)"]}),(0,a.jsx)(L.p,{id:"tags",value:n.tags,onChange:e=>o({...n,tags:e.target.value}),placeholder:"lead, follow-up, urgent",className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"notes",className:"text-sm font-medium text-black",children:"Notes"}),(0,a.jsx)(z.T,{id:"notes",value:n.notes,onChange:e=>o({...n,notes:e.target.value}),placeholder:"Additional notes or instructions",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2 bg-sky-50 p-3 rounded-md border border-sky-200",children:[(0,a.jsx)(I.J,{className:"text-sm font-medium text-black",children:"Assignment Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"bg-white p-2 rounded border",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Assigned To:"}),(0,a.jsxs)("span",{className:"text-black font-medium ml-2",children:["\uD83D\uDC64 ",i.assignedToName||"".concat(null==m?void 0:m.firstName," ").concat(null==m?void 0:m.lastName)]})]}),(0,a.jsxs)("div",{className:"bg-white p-2 rounded border",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Created By:"}),(0,a.jsxs)("span",{className:"text-black font-medium ml-2",children:["\uD83D\uDC64 ",i.createdByName||"".concat(null==m?void 0:m.firstName," ").concat(null==m?void 0:m.lastName)]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-300",children:[(0,a.jsx)(U.$,{type:"button",variant:"outline",onClick:g,className:"border-gray-300 text-black hover:bg-gray-100",children:"Cancel"}),(0,a.jsx)(U.$,{type:"submit",disabled:c,className:"bg-sky-500 hover:bg-sky-600 text-white",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Updating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Update Task"]})})]})]})})]})}):null}var Q=t(2790),Y=t(944),W=t(4721),K=t(7229),X=t(2910);function ee(e){let{isOpen:s,onClose:t,onSuccess:r}=e,[i,n]=(0,l.useState)({title:"",description:"",type:"meeting",startDate:"",startTime:"",endDate:"",endTime:"",location:"",isAllDay:!1,meetingLink:"",notes:""}),[o,d]=(0,l.useState)([]),[c,h]=(0,l.useState)(""),[x,u]=(0,l.useState)(!1),[g,{isLoading:p}]=(0,G.dy)();(0,P.G)(_.mB);let b=(0,P.j)(),{data:y,isLoading:v}=(0,G.TS)({search:c,status:"active"}),f=((null==y?void 0:y.data)||[]).filter(e=>!o.find(s=>s.id===e.id)),j=e=>{d([...o,e]),h(""),u(!1)},N=e=>{d(o.filter(s=>s.id!==e))},k=async e=>{e.preventDefault();try{if(!i.title||!i.startDate||!i.startTime||!i.endDate||!i.endTime)return void b((0,J.$U)({type:"error",title:"Validation Error",message:"Please fill in all required fields"}));let e={title:i.title,description:i.description,type:i.type,startDate:i.startDate,startTime:i.startTime,endDate:i.endDate,endTime:i.endTime,location:i.location,isAllDay:i.isAllDay,meetingLink:i.meetingLink,notes:i.notes,attendees:o.map(e=>({id:e.id,name:"".concat(e.firstName," ").concat(e.lastName),email:e.email,type:"internal",status:"pending"}))};await g(e).unwrap(),n({title:"",description:"",type:"meeting",startDate:"",startTime:"",endDate:"",endTime:"",location:"",isAllDay:!1,meetingLink:"",notes:""}),d([]),h(""),u(!1),b((0,J.$U)({type:"success",title:"Event Created",message:"Calendar event has been created successfully"})),b((0,J.Oo)("create-event")),null==r||r()}catch(e){console.error("Failed to create event:",e),b((0,J.$U)({type:"error",title:"Error",message:"Failed to create event. Please try again."}))}},w=()=>{b((0,J.Oo)("create-event")),null==t||t()};return(0,a.jsx)(R.lG,{open:s,onOpenChange:w,children:(0,a.jsxs)(R.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(R.c7,{children:(0,a.jsxs)(R.L3,{className:"text-xl font-semibold flex items-center gap-2",children:[(0,a.jsx)(Y.A,{className:"w-5 h-5 text-green-600"}),"Create New Event"]})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("form",{onSubmit:k,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"title",className:"text-sm font-medium text-black",children:"Event Title *"}),(0,a.jsx)(L.p,{id:"title",value:i.title,onChange:e=>n({...i,title:e.target.value}),placeholder:"Enter event title",required:!0,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"description",className:"text-sm font-medium text-black",children:"Description"}),(0,a.jsx)(z.T,{id:"description",value:i.description,onChange:e=>n({...i,description:e.target.value}),placeholder:"Describe the event",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"type",className:"text-sm font-medium text-black",children:"Event Type"}),(0,a.jsxs)($.l6,{value:i.type,onValueChange:e=>n({...i,type:e}),children:[(0,a.jsx)($.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)($.yv,{})}),(0,a.jsxs)($.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)($.eb,{value:"meeting",className:"text-black hover:bg-sky-100",children:"\uD83E\uDD1D Meeting"}),(0,a.jsx)($.eb,{value:"call",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDE Call"}),(0,a.jsx)($.eb,{value:"demo",className:"text-black hover:bg-sky-100",children:"\uD83C\uDFAF Demo"}),(0,a.jsx)($.eb,{value:"site_visit",className:"text-black hover:bg-sky-100",children:"\uD83C\uDFD7️ Site Visit"}),(0,a.jsx)($.eb,{value:"follow_up",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD04 Follow Up"}),(0,a.jsx)($.eb,{value:"other",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDD Other"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),"Assign to Users"]}),o.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 p-3 bg-sky-50 rounded-md border border-sky-200",children:o.map(e=>(0,a.jsxs)(Q.E,{className:"bg-sky-500 text-white hover:bg-sky-600 flex items-center gap-1 px-3 py-1",children:["\uD83D\uDC64 ",e.firstName," ",e.lastName,(0,a.jsx)("button",{type:"button",onClick:()=>N(e.id),className:"ml-1 hover:bg-sky-700 rounded-full p-0.5",children:(0,a.jsx)(W.A,{className:"w-3 h-3"})})]},e.id))}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(L.p,{value:c,onChange:e=>{h(e.target.value),u(e.target.value.length>0)},placeholder:"Search users to assign...",className:"bg-white text-black border-gray-300",onFocus:()=>u(c.length>0)}),(0,a.jsx)(U.$,{type:"button",variant:"outline",onClick:()=>u(!x),className:"border-gray-300 text-black hover:bg-sky-100",children:(0,a.jsx)(A.A,{className:"w-4 h-4"})})]}),x&&!v&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg z-[70] max-h-48 overflow-y-auto",children:f.length>0?f.map(e=>(0,a.jsxs)("button",{type:"button",onClick:()=>j(e),className:"w-full text-left p-3 hover:bg-sky-100 border-b border-gray-100 last:border-b-0 transition-colors",children:[(0,a.jsxs)("div",{className:"text-black font-medium",children:["\uD83D\uDC64 ",e.firstName," ",e.lastName]}),(0,a.jsxs)("div",{className:"text-gray-600 text-sm",children:[e.email," • ",e.role]})]},e.id)):(0,a.jsx)("div",{className:"p-3 text-gray-500 text-sm text-center",children:c?"No users found":"Start typing to search users..."})}),x&&v&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg z-[70] p-3",children:(0,a.jsx)("div",{className:"text-gray-500 text-sm text-center",children:"Loading users..."})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"startDate",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(M.A,{className:"w-4 h-4"}),"Start Date *"]}),(0,a.jsx)(L.p,{id:"startDate",type:"date",value:i.startDate,onChange:e=>n({...i,startDate:e.target.value}),required:!0,className:"w-full bg-white text-black border-gray-300"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"startTime",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(B.A,{className:"w-4 h-4"}),"Start Time *"]}),(0,a.jsx)(L.p,{id:"startTime",type:"time",value:i.startTime,onChange:e=>n({...i,startTime:e.target.value}),required:!0,className:"w-full bg-white text-black border-gray-300"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"endDate",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(M.A,{className:"w-4 h-4"}),"End Date *"]}),(0,a.jsx)(L.p,{id:"endDate",type:"date",value:i.endDate,onChange:e=>n({...i,endDate:e.target.value}),required:!0,className:"w-full bg-white text-black border-gray-300"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"endTime",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(B.A,{className:"w-4 h-4"}),"End Time *"]}),(0,a.jsx)(L.p,{id:"endTime",type:"time",value:i.endTime,onChange:e=>n({...i,endTime:e.target.value}),required:!0,className:"w-full bg-white text-black border-gray-300"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"location",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(K.A,{className:"w-4 h-4"}),"Location"]}),(0,a.jsx)(L.p,{id:"location",value:i.location,onChange:e=>n({...i,location:e.target.value}),placeholder:"Meeting location or address",className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{htmlFor:"meetingLink",className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(X.A,{className:"w-4 h-4"}),"Meeting Link"]}),(0,a.jsx)(L.p,{id:"meetingLink",value:i.meetingLink,onChange:e=>n({...i,meetingLink:e.target.value}),placeholder:"Zoom, Teams, or other meeting link",className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"notes",className:"text-sm font-medium text-black",children:"Notes"}),(0,a.jsx)(z.T,{id:"notes",value:i.notes,onChange:e=>n({...i,notes:e.target.value}),placeholder:"Additional notes or agenda",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-300",children:[(0,a.jsx)(U.$,{type:"button",variant:"outline",onClick:w,className:"border-gray-300 text-black hover:bg-gray-100",children:"Cancel"}),(0,a.jsx)(U.$,{type:"submit",disabled:p,className:"bg-green-500 hover:bg-green-600 text-white",children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Y.A,{className:"w-4 h-4 mr-2"}),"Create Event"]})})]})]})})]})})}function es(e){let{isOpen:s,onClose:t,onSuccess:r,event:i}=e,[n,o]=(0,l.useState)({title:"",description:"",type:"meeting",startDate:"",startTime:"",endDate:"",endTime:"",location:"",isAllDay:!1,meetingLink:"",notes:""}),[d,c]=(0,l.useState)([]),[h,x]=(0,l.useState)(""),[u,g]=(0,l.useState)(!1),[p,{isLoading:b}]=(0,G.eV)(),y=(0,P.G)(_.mB),v=(0,P.j)(),{data:f,isLoading:j}=(0,G.TS)({search:h,status:"active"}),N=((null==f?void 0:f.data)||[]).filter(e=>!d.find(s=>s.id===e.id));(0,l.useEffect)(()=>{if(i){var e;o({title:i.title||"",description:i.description||"",type:i.type||"meeting",startDate:i.startDate?i.startDate.split("T")[0]:"",startTime:i.startTime||"",endDate:i.endDate?i.endDate.split("T")[0]:"",endTime:i.endTime||"",location:i.location||"",isAllDay:i.isAllDay||!1,meetingLink:i.meetingLink||"",notes:i.notes||""}),c((null==(e=i.attendees)?void 0:e.map(e=>({id:e.id,firstName:e.name.split(" ")[0]||"",lastName:e.name.split(" ").slice(1).join(" ")||"",email:e.email,role:"user"})))||[])}},[i]);let k=e=>{c([...d,e]),x(""),g(!1)},w=e=>{c(d.filter(s=>s.id!==e))},D=async e=>{if(e.preventDefault(),i)try{if(!n.title||!n.startDate||!n.startTime||!n.endDate||!n.endTime)return void v((0,J.$U)({type:"error",title:"Validation Error",message:"Please fill in all required fields"}));let e={title:n.title,description:n.description,type:n.type,startDate:n.startDate,startTime:n.startTime,endDate:n.endDate,endTime:n.endTime,location:n.location,isAllDay:n.isAllDay,meetingLink:n.meetingLink,notes:n.notes,attendees:d.map(e=>({id:e.id,name:"".concat(e.firstName," ").concat(e.lastName),email:e.email,type:"internal",status:"pending"}))};await p({id:i.id,...e}).unwrap(),v((0,J.$U)({type:"success",title:"Event Updated",message:"Calendar event has been updated successfully"})),v((0,J.Oo)("edit-event")),null==r||r()}catch(e){console.error("Failed to update event:",e),v((0,J.$U)({type:"error",title:"Error",message:"Failed to update event. Please try again."}))}},T=()=>{v((0,J.Oo)("edit-event")),null==t||t()};return i?(0,a.jsx)(R.lG,{open:s,onOpenChange:T,children:(0,a.jsxs)(R.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(R.c7,{children:(0,a.jsxs)(R.L3,{className:"text-xl font-semibold flex items-center gap-2 text-black",children:[(0,a.jsx)(Y.A,{className:"w-5 h-5 text-green-500"}),"Edit Event"]})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("form",{onSubmit:D,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"title",className:"text-sm font-medium text-black",children:"Event Title *"}),(0,a.jsx)(L.p,{id:"title",value:n.title,onChange:e=>o({...n,title:e.target.value}),placeholder:"Enter event title",required:!0,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"description",className:"text-sm font-medium text-black",children:"Description"}),(0,a.jsx)(z.T,{id:"description",value:n.description,onChange:e=>o({...n,description:e.target.value}),placeholder:"Describe the event",rows:3,className:"w-full bg-white text-black border-gray-300 placeholder:text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(I.J,{htmlFor:"type",className:"text-sm font-medium text-black",children:"Event Type"}),(0,a.jsxs)($.l6,{value:n.type,onValueChange:e=>o({...n,type:e}),children:[(0,a.jsx)($.bq,{className:"bg-white text-black border-gray-300",children:(0,a.jsx)($.yv,{})}),(0,a.jsxs)($.gC,{className:"bg-white border-gray-300 shadow-lg z-[60]",children:[(0,a.jsx)($.eb,{value:"meeting",className:"text-black hover:bg-sky-100",children:"\uD83E\uDD1D Meeting"}),(0,a.jsx)($.eb,{value:"call",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDE Call"}),(0,a.jsx)($.eb,{value:"demo",className:"text-black hover:bg-sky-100",children:"\uD83C\uDFAF Demo"}),(0,a.jsx)($.eb,{value:"site_visit",className:"text-black hover:bg-sky-100",children:"\uD83C\uDFD7️ Site Visit"}),(0,a.jsx)($.eb,{value:"follow_up",className:"text-black hover:bg-sky-100",children:"\uD83D\uDD04 Follow Up"}),(0,a.jsx)($.eb,{value:"other",className:"text-black hover:bg-sky-100",children:"\uD83D\uDCDD Other"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(I.J,{className:"text-sm font-medium text-black flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),"Assign to Users"]}),d.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 p-3 bg-sky-50 rounded-md border border-sky-200",children:d.map(e=>(0,a.jsxs)(Q.E,{className:"bg-sky-500 text-white hover:bg-sky-600 flex items-center gap-1 px-3 py-1",children:["\uD83D\uDC64 ",e.firstName," ",e.lastName,(0,a.jsx)("button",{type:"button",onClick:()=>w(e.id),className:"ml-1 hover:bg-sky-700 rounded-full p-0.5",children:(0,a.jsx)(W.A,{className:"w-3 h-3"})})]},e.id))}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(L.p,{value:h,onChange:e=>{x(e.target.value),g(e.target.value.length>0)},placeholder:"Search users to assign...",className:"bg-white text-black border-gray-300",onFocus:()=>g(h.length>0)}),(0,a.jsx)(U.$,{type:"button",variant:"outline",onClick:()=>g(!u),className:"border-gray-300 text-black hover:bg-sky-100",children:(0,a.jsx)(A.A,{className:"w-4 h-4"})})]}),u&&!j&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg z-[70] max-h-48 overflow-y-auto",children:N.length>0?N.map(e=>(0,a.jsxs)("button",{type:"button",onClick:()=>k(e),className:"w-full text-left p-3 hover:bg-sky-100 border-b border-gray-100 last:border-b-0 transition-colors",children:[(0,a.jsxs)("div",{className:"text-black font-medium",children:["\uD83D\uDC64 ",e.firstName," ",e.lastName]}),(0,a.jsxs)("div",{className:"text-gray-600 text-sm",children:[e.email," • ",e.role]})]},e.id)):(0,a.jsx)("div",{className:"p-3 text-gray-500 text-sm text-center",children:h?"No users found":"Start typing to search users..."})}),u&&j&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg z-[70] p-3",children:(0,a.jsx)("div",{className:"text-gray-500 text-sm text-center",children:"Loading users..."})})]})]}),(0,a.jsxs)("div",{className:"space-y-2 bg-green-50 p-3 rounded-md border border-green-200",children:[(0,a.jsx)(I.J,{className:"text-sm font-medium text-black",children:"Event Information"}),(0,a.jsxs)("div",{className:"bg-white p-2 rounded border text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Created By:"}),(0,a.jsxs)("span",{className:"text-black font-medium ml-2",children:["\uD83D\uDC64 ",i.createdByName||"".concat(null==y?void 0:y.firstName," ").concat(null==y?void 0:y.lastName)]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-300",children:[(0,a.jsx)(U.$,{type:"button",variant:"outline",onClick:T,className:"border-gray-300 text-black hover:bg-gray-100",children:"Cancel"}),(0,a.jsx)(U.$,{type:"submit",disabled:b,className:"bg-green-500 hover:bg-green-600 text-white",children:b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Updating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Y.A,{className:"w-4 h-4 mr-2"}),"Update Event"]})})]})]})})]})}):null}var et=t(2892);function ea(){var e,s,t,l,r;let i=(0,P.G)(e=>e.ui.modals),n=(0,P.j)(),o=i.find(e=>"create-task"===e.type&&e.isOpen),d=i.find(e=>"edit-task"===e.type&&e.isOpen),c=i.find(e=>"create-event"===e.type&&e.isOpen),m=i.find(e=>"edit-event"===e.type&&e.isOpen),h=i.find(e=>"property-details"===e.type&&e.isOpen),x=e=>{n((0,J.Oo)(e))};return(0,a.jsxs)(a.Fragment,{children:[o&&(0,a.jsx)(Z,{isOpen:o.isOpen,onClose:()=>x(o.id),onSuccess:()=>{window.location.reload()}}),d&&(0,a.jsx)(H,{isOpen:d.isOpen,onClose:()=>x(d.id),task:(null==(e=d.data)?void 0:e.task)||null,onSuccess:()=>{window.location.reload()}}),c&&(0,a.jsx)(ee,{isOpen:c.isOpen,onClose:()=>x(c.id),onSuccess:()=>{window.location.reload()}}),m&&(0,a.jsx)(es,{isOpen:m.isOpen,onClose:()=>x(m.id),event:(null==(s=m.data)?void 0:s.event)||null,onSuccess:()=>{window.location.reload()}}),h&&(0,a.jsx)(et.b,{property:null==(t=h.data)?void 0:t.property,isOpen:h.isOpen,onClose:()=>x(h.id),onCopyReferral:(null==(l=h.data)?void 0:l.onCopyReferral)||(()=>{}),copiedReferralId:(null==(r=h.data)?void 0:r.copiedReferralId)||null})]})}function el(e){let{children:s,title:t,showSearch:r=!0}=e,[i,n]=(0,l.useState)(!1);return(0,a.jsxs)("div",{className:"h-screen flex bg-gradient-to-br from-gray-50 to-sky-50",children:[(0,a.jsx)("div",{className:(0,o.cn)("fixed inset-y-0 left-0 z-50 w-64 transform transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",i?"translate-x-0 shadow-2xl":"-translate-x-full"),children:(0,a.jsx)(T,{})}),i&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden backdrop-blur-sm transition-all duration-300",onClick:()=>n(!1)}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(O,{onMenuClick:()=>n(!i),title:t,showSearch:r}),(0,a.jsx)("main",{className:"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-200 scrollbar-track-transparent",children:(0,a.jsx)("div",{className:"p-6",children:s})})]}),(0,a.jsx)(ea,{})]})}},6467:(e,s,t)=>{t.d(s,{T:()=>i});var a=t(9605),l=t(9585),r=t(6994);let i=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...l})});i.displayName="Textarea"},6701:(e,s,t)=>{t.d(s,{q:()=>o});var a=t(6597),l=t(2713);class r{static getCookie(e){if("undefined"==typeof document)return null;let s="; ".concat(document.cookie).split("; ".concat(e,"="));if(2===s.length){var t;let e=null==(t=s.pop())?void 0:t.split(";").shift();return e?decodeURIComponent(e):null}return null}static setCookie(e,s){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("undefined"==typeof document)return;let a="".concat(e,"=").concat(encodeURIComponent(s));t.expires&&("string"==typeof t.expires?a+="; expires=".concat(t.expires):a+="; expires=".concat(t.expires.toUTCString())),t.maxAge&&(a+="; max-age=".concat(t.maxAge)),t.path?a+="; path=".concat(t.path):a+="; path=/",t.domain&&(a+="; domain=".concat(t.domain)),t.secure&&(a+="; secure"),t.sameSite&&(a+="; samesite=".concat(t.sameSite)),document.cookie=a}static deleteCookie(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.setCookie(e,"",{...s,expires:new Date(0)})}static hasCookie(e){return null!==this.getCookie(e)}static getAllCookies(){if("undefined"==typeof document)return{};let e={};return document.cookie.split(";").forEach(s=>{let[t,a]=s.trim().split("=");t&&a&&(e[t]=decodeURIComponent(a))}),e}static getAuthToken(){return this.getCookie("accessToken")||this.getCookie("token")}static getRefreshToken(){return this.getCookie("refreshToken")}static isAuthenticated(){return this.hasCookie("accessToken")||this.hasCookie("token")}static clearAuthCookies(){this.deleteCookie("accessToken"),this.deleteCookie("refreshToken"),this.deleteCookie("token")}static getRedirectUrl(){return this.getCookie("redirectTo")}static setRedirectUrl(e){this.setCookie("redirectTo",e,{maxAge:300,sameSite:"lax"})}static clearRedirectUrl(){this.deleteCookie("redirectTo")}static parseCookieString(e){let s={};return e.split(";").forEach(e=>{let[t,a]=e.trim().split("=");t&&a&&(s[t]=decodeURIComponent(a))}),s}static setSessionCookie(e,s){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.setCookie(e,s,{...t,expires:void 0,maxAge:void 0})}static setPersistentCookie(e,s){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:7,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},l=new Date;l.setDate(l.getDate()+t),this.setCookie(e,s,{...a,expires:l})}}let i=(0,a.cw)({baseUrl:"http://localhost:5000/api",credentials:"include",prepareHeaders:(e,s)=>{let{getState:t}=s,a=null;if(!(a=r.getAuthToken()||localStorage.getItem("accessToken"))){var l,i;a=null==(i=t())||null==(l=i.auth)?void 0:l.token}return a&&e.set("authorization","Bearer ".concat(a)),e.set("content-type","application/json"),e.set("accept","application/json"),e.set("x-client-type","sales-dashboard"),e.set("x-client-version","1.0.0"),e}}),n=async(e,s,t)=>{let a=await i(e,s,t);if(a.error&&401===a.error.status){console.log("Access token expired, attempting refresh...");let l=r.getRefreshToken();if(l){let n=await i({url:"/auth/refresh",method:"POST",body:{refreshToken:l}},s,t);if(n.data){let{accessToken:l}=n.data.data;localStorage.setItem("accessToken",l),a=await i(e,s,t)}else r.clearAuthCookies(),localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/login"}else r.clearAuthCookies(),localStorage.removeItem("accessToken"),window.location.href="/login"}return a},o=(0,l.xP)({reducerPath:"api",baseQuery:n,tagTypes:["User","Lead","Customer","Property","Commission","Report","Team","Task","Calendar","Notification","Dashboard"],endpoints:()=>({})})},7555:(e,s,t)=>{t.d(s,{$U:()=>c,Ay:()=>z,Oo:()=>n,qf:()=>i});var a=t(7895);let l={modals:[],toasts:[],loading:{global:!1,components:{}},tables:{},search:{query:"",filters:[],suggestions:[],recentSearches:[],isSearching:!1},sidebar:{isOpen:!0,activeSection:null},breadcrumbs:[],pageTitle:"",pageSubtitle:void 0,quickFilters:{},bulkActions:{selectedItems:[],availableActions:[]},preferences:{density:"comfortable",animations:!0,soundEffects:!1,autoSave:!0,confirmActions:!0}},r=(0,a.Z0)({name:"ui",initialState:l,reducers:{openModal:(e,s)=>{let t=e.modals.find(e=>e.id===s.payload.id);t?(t.isOpen=!0,t.data=s.payload.data):e.modals.push({...s.payload,isOpen:!0})},closeModal:(e,s)=>{let t=e.modals.find(e=>e.id===s.payload);t&&(t.isOpen=!1)},closeAllModals:e=>{e.modals.forEach(e=>{e.isOpen=!1})},updateModalData:(e,s)=>{let t=e.modals.find(e=>e.id===s.payload.id);t&&(t.data=s.payload.data)},addToast:(e,s)=>{let t=Date.now().toString();e.toasts.push({id:t,...s.payload})},removeToast:(e,s)=>{e.toasts=e.toasts.filter(e=>e.id!==s.payload)},clearToasts:e=>{e.toasts=[]},setGlobalLoading:(e,s)=>{e.loading.global=s.payload},setComponentLoading:(e,s)=>{e.loading.components[s.payload.component]=s.payload.loading},setTableState:(e,s)=>{let{tableId:t,state:a}=s.payload;e.tables[t]={...e.tables[t],...a}},selectTableRows:(e,s)=>{let{tableId:t,rowIds:a}=s.payload;e.tables[t]||(e.tables[t]={selectedRows:[],filters:[],sort:null,pagination:{page:1,limit:10,total:0},view:"table",columns:[]}),e.tables[t].selectedRows=a},toggleTableRow:(e,s)=>{let{tableId:t,rowId:a}=s.payload;if(!e.tables[t])return;let l=e.tables[t].selectedRows,r=l.indexOf(a);r>-1?l.splice(r,1):l.push(a)},clearTableSelection:(e,s)=>{e.tables[s.payload]&&(e.tables[s.payload].selectedRows=[])},setSearchQuery:(e,s)=>{e.search.query=s.payload},setSearchFilters:(e,s)=>{e.search.filters=s.payload},addSearchFilter:(e,s)=>{let t=e.search.filters.findIndex(e=>e.id===s.payload.id);t>-1?e.search.filters[t]=s.payload:e.search.filters.push(s.payload)},removeSearchFilter:(e,s)=>{e.search.filters=e.search.filters.filter(e=>e.id!==s.payload)},setSearchSuggestions:(e,s)=>{e.search.suggestions=s.payload},addRecentSearch:(e,s)=>{let t=s.payload.trim();t&&!e.search.recentSearches.includes(t)&&(e.search.recentSearches.unshift(t),e.search.recentSearches.length>10&&(e.search.recentSearches=e.search.recentSearches.slice(0,10)))},clearRecentSearches:e=>{e.search.recentSearches=[]},setSearching:(e,s)=>{e.search.isSearching=s.payload},setSidebarOpen:(e,s)=>{e.sidebar.isOpen=s.payload},setActiveSection:(e,s)=>{e.sidebar.activeSection=s.payload},setBreadcrumbs:(e,s)=>{e.breadcrumbs=s.payload},setPageTitle:(e,s)=>{e.pageTitle=s.payload},setPageSubtitle:(e,s)=>{e.pageSubtitle=s.payload},setQuickFilter:(e,s)=>{e.quickFilters[s.payload.key]=s.payload.value},clearQuickFilters:e=>{e.quickFilters={}},setBulkSelectedItems:(e,s)=>{e.bulkActions.selectedItems=s.payload},setBulkAvailableActions:(e,s)=>{e.bulkActions.availableActions=s.payload},clearBulkSelection:e=>{e.bulkActions.selectedItems=[]},setPreferences:(e,s)=>{e.preferences={...e.preferences,...s.payload}},resetUI:e=>({...l,preferences:e.preferences})}}),{openModal:i,closeModal:n,closeAllModals:o,updateModalData:d,addToast:c,removeToast:m,clearToasts:h,setGlobalLoading:x,setComponentLoading:u,setTableState:g,selectTableRows:p,toggleTableRow:b,clearTableSelection:y,setSearchQuery:v,setSearchFilters:f,addSearchFilter:j,removeSearchFilter:N,setSearchSuggestions:k,addRecentSearch:w,clearRecentSearches:D,setSearching:T,setSidebarOpen:C,setActiveSection:A,setBreadcrumbs:S,setPageTitle:F,setPageSubtitle:E,setQuickFilter:q,clearQuickFilters:U,setBulkSelectedItems:O,setBulkAvailableActions:P,clearBulkSelection:J,setPreferences:R,resetUI:L}=r.actions,z=r.reducer},9577:(e,s,t)=>{t.d(s,{bq:()=>h,eb:()=>p,gC:()=>g,l6:()=>c,yv:()=>m});var a=t(9605),l=t(9585),r=t(8881),i=t(6277),n=t(1432),o=t(1817),d=t(6994);let c=r.bL;r.YJ;let m=r.WT,h=l.forwardRef((e,s)=>{let{className:t,children:l,...n}=e;return(0,a.jsxs)(r.l9,{ref:s,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...n,children:[l,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});h.displayName=r.l9.displayName;let x=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});x.displayName=r.PP.displayName;let u=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});u.displayName=r.wn.displayName;let g=l.forwardRef((e,s)=>{let{className:t,children:l,position:i="popper",...n}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{ref:s,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,a.jsx)(x,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,a.jsx)(u,{})]})})});g.displayName=r.UC.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...l})}).displayName=r.JU.displayName;let p=l.forwardRef((e,s)=>{let{className:t,children:l,...i}=e;return(0,a.jsxs)(r.q7,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})}),(0,a.jsx)(r.p4,{children:l})]})});p.displayName=r.q7.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...l})}).displayName=r.wv.displayName}}]);