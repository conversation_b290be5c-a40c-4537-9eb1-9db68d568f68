(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7839],{1128:(e,t,s)=>{"use strict";s.d(t,{B3:()=>l,Lv:()=>d,Ng:()=>i,_L:()=>a,ac:()=>y,ge:()=>r,nd:()=>c,pv:()=>g,tl:()=>o,uU:()=>u});let{useLoginMutation:a,useRegisterMutation:r,useLogoutMutation:i,useRefreshTokenMutation:n,useVerifyEmailMutation:u,useResendVerificationEmailMutation:l,useForgotPasswordMutation:c,useVerifyPasswordResetOTPMutation:o,useResetPasswordMutation:d,useChangePasswordMutation:h,useVerifyPhoneMutation:m,useSendPhoneOTPMutation:x,useSendEmailOTPMutation:y,useVerifyEmailOTPMutation:g,useGetCurrentUserQuery:f,useGetUserProfileQuery:p,useCheckAuthStatusQuery:v}=s(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({login:e.mutation({query:e=>({url:"/auth/login",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),register:e.mutation({query:e=>({url:"/auth/register",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),logout:e.mutation({query:()=>({url:"/auth/logout",method:"POST"}),invalidatesTags:["Auth","User"]}),refreshToken:e.mutation({query:e=>({url:"/auth/refresh",method:"POST",body:e})}),verifyEmail:e.mutation({query:e=>({url:"/auth/verify-email",method:"POST",body:e}),invalidatesTags:["User"]}),resendVerificationEmail:e.mutation({query:e=>({url:"/auth/send-verification",method:"POST",body:e})}),forgotPassword:e.mutation({query:e=>({url:"/auth/forgot-password",method:"POST",body:e})}),verifyPasswordResetOTP:e.mutation({query:e=>({url:"/auth/verify-reset-otp",method:"POST",body:e})}),resetPassword:e.mutation({query:e=>({url:"/auth/reset-password",method:"POST",body:e})}),changePassword:e.mutation({query:e=>({url:"/auth/change-password",method:"POST",body:e}),invalidatesTags:["User"]}),verifyPhone:e.mutation({query:e=>({url:"/auth/verify-phone",method:"POST",body:e}),invalidatesTags:["User"]}),sendPhoneOTP:e.mutation({query:e=>({url:"/auth/send-phone-otp",method:"POST",body:e})}),sendEmailOTP:e.mutation({query:e=>({url:"/auth/send-otp",method:"POST",body:e})}),verifyEmailOTP:e.mutation({query:e=>({url:"/auth/verify-otp",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),getCurrentUser:e.query({query:()=>"/auth/me",providesTags:["User"]}),checkAuthStatus:e.query({query:()=>"/auth/status",providesTags:["Auth"]}),getUserProfile:e.query({query:()=>"/auth/profile",providesTags:["User","Auth"]})})})},2006:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(5050).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5633:(e,t,s)=>{Promise.resolve().then(s.bind(s,8301))},5935:(e,t,s)=>{"use strict";var a=s(5383);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},6793:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(5050).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8049:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(5050).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},8301:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(9605),r=s(9585),i=s(5935),n=s(6793),u=s(2006),l=s(9922),c=s(8049),o=s(2933),d=s(8063),h=s(1128),m=s(6845);function x(){let e=(0,i.useRouter)(),t=(0,i.useSearchParams)().get("token"),[s,{isLoading:x}]=(0,h.uU)(),[y,g]=(0,r.useState)("pending"),[f,p]=(0,r.useState)("");(0,r.useEffect)(()=>{t?v(t):(g("error"),p("Invalid verification link. No token provided."))},[t]);let v=async t=>{try{(await s({token:t}).unwrap()).success&&(g("success"),p("Email verified successfully! Your account is now active."),m.toast.success("Email verified successfully!"),setTimeout(()=>{e.push("/login")},3e3))}catch(e){var a;console.error("Email verification error:",e),g("error"),(null==(a=e.data)?void 0:a.message)?p(e.data.message):p("Email verification failed. The link may be expired or invalid."),m.toast.error("Email verification failed")}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,a.jsx)(c.A,{className:"h-12 w-12 text-blue-600"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"SGM "}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Email Verification"})]}),(0,a.jsxs)(d.Zp,{className:"shadow-2xl border-0 p-2",children:[(0,a.jsxs)(d.aR,{className:"space-y-1",children:[(0,a.jsx)(d.ZB,{className:"text-2xl font-bold text-center ".concat((()=>{switch(y){case"pending":return"text-blue-600";case"success":return"text-green-600";case"error":return"text-red-600"}})()),children:(()=>{switch(y){case"pending":return"Verifying Email...";case"success":return"Email Verified!";case"error":return"Verification Failed"}})()}),(0,a.jsxs)(d.BT,{className:"text-center",children:["pending"===y&&"Please wait while we verify your email address","success"===y&&"Your account is now active and ready to use","error"===y&&"There was an issue verifying your email"]})]}),(0,a.jsxs)(d.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(()=>{switch(y){case"pending":return(0,a.jsx)(n.A,{className:"h-16 w-16 text-blue-500 mx-auto mb-4 animate-spin"});case"success":return(0,a.jsx)(u.A,{className:"h-16 w-16 text-green-500 mx-auto mb-4"});case"error":return(0,a.jsx)(l.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"})}})(),(0,a.jsx)("p",{className:"text-gray-700 mb-4",children:f})]}),(0,a.jsxs)("div",{className:"space-y-3",children:["success"===y&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.$,{onClick:()=>e.push("/login"),className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:"Go to Login"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 text-center",children:"You will be redirected to login in a few seconds..."})]}),"error"===y&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.$,{onClick:()=>e.push("/register"),className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:"Back to Registration"}),(0,a.jsx)(o.$,{onClick:()=>e.push("/login"),variant:"outline",className:"w-full",children:"Try Login Anyway"})]}),"pending"===y&&(0,a.jsx)(o.$,{onClick:()=>e.push("/login"),variant:"outline",className:"w-full",disabled:x,children:"Cancel"})]})]})]})]})})}function y(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)(c.A,{className:"w-12 h-12 text-blue-600 mx-auto"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-blue-600",children:[(0,a.jsx)(n.A,{className:"w-5 h-5 animate-spin"}),(0,a.jsx)("span",{className:"text-lg font-medium",children:"Loading..."})]})]})}),children:(0,a.jsx)(x,{})})}},9922:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(5050).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[2094,5315,1147,390,110,7358],()=>t(5633)),_N_E=e.O()}]);