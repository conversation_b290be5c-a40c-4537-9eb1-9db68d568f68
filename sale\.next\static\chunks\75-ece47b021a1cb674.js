"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[75],{2933:(e,r,t)=>{t.d(r,{$:()=>d});var a=t(9605),n=t(9585),s=t(8130),l=t(7276),i=t(6994);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,r)=>{let{className:t,variant:n,size:l,asChild:d=!1,...c}=e,u=d?s.DX:"button";return(0,a.jsx)(u,{className:(0,i.cn)(o({variant:n,size:l,className:t})),ref:r,...c})});d.displayName="Button"},3815:(e,r,t)=>{t.d(r,{Ay:()=>b,H$:()=>x,Kc:()=>m,PK:()=>o,ZB:()=>y,_v:()=>s,mB:()=>p});var a=t(7895);let n={setCurrentUser:e=>{localStorage.setItem("salesUser",JSON.stringify(e))},clearCurrentUser:()=>{localStorage.removeItem("salesUser")}},s=(0,a.zD)("auth/login",async(e,r)=>{let{rejectWithValue:t}=r;try{console.log("\uD83D\uDD10 Sales backend login attempt:",e.email);let r=await fetch("".concat("http://localhost:5000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Client-Type":"sales-dashboard","X-Client-Version":"1.0.0"},credentials:"include",body:JSON.stringify(e)}),a=await r.json();if(!r.ok)throw Error(a.message||"Login failed");console.log("✅ Sales: Backend login response:",a);let{user:s,accessToken:l,refreshToken:i}=a.data||a;if(!s||!l)return t("Invalid response format");try{return n.setCurrentUser(s),console.log("✅ Sales login successful, backend set HttpOnly cookies:",{user:s.email,role:s.role,note:"Tokens stored in HttpOnly cookies (not accessible from JS)"}),{user:s,accessToken:l,refreshToken:i}}catch(e){return console.error("Sales user storage failed:",e),t("Failed to store user data")}}catch(e){return console.error("Sales login error:",e),t(e.message||"Login failed")}}),l=(0,a.zD)("auth/logout",async()=>{try{await fetch("".concat("http://localhost:5000/api","/auth/logout"),{method:"POST",headers:{"Content-Type":"application/json","X-Client-Type":"sales-dashboard"},credentials:"include"})}catch(e){console.error("Sales logout API error:",e)}finally{n.clearCurrentUser()}}),i=(0,a.zD)("auth/refreshUser",async(e,r)=>{let{rejectWithValue:t}=r;try{let e=await fetch("".concat("http://localhost:5000/api","/auth/profile"),{method:"GET",headers:{"Content-Type":"application/json","X-Client-Type":"sales-dashboard"},credentials:"include"}),r=await e.json();if(!e.ok)throw Error(r.message||"Failed to get profile");let t=r.data||r;return n.setCurrentUser(t),t}catch(e){return t(e.message||"Failed to refresh user")}}),o=(0,a.zD)("auth/checkAuth",async(e,r)=>{let{rejectWithValue:t}=r;try{console.log("\uD83D\uDD0D Sales: Checking auth with backend (HttpOnly cookies)...");let e=await fetch("".concat("http://localhost:5000/api","/auth/check"),{method:"GET",headers:{"Content-Type":"application/json","X-Client-Type":"sales-dashboard"},credentials:"include"}),r=await e.json();if(console.log("✅ Sales: Backend auth response:",r),e.ok&&r.success&&r.data)return n.setCurrentUser(r.data),r.data;return console.log("❌ Sales: Backend auth failed:",r.message),n.clearCurrentUser(),t(r.message||"Authentication failed")}catch(e){return console.error("❌ Sales: Auth check error:",e.message),n.clearCurrentUser(),t(e.message||"Authentication check failed")}}),d=(0,a.Z0)({name:"auth",initialState:{user:null,isAuthenticated:!1,loading:!1,error:null,sessionExpiry:null},reducers:{clearError:e=>{e.error=null},setUser:(e,r)=>{e.user=r.payload,e.isAuthenticated=!0,n.setCurrentUser(r.payload)},clearAuth:e=>{e.user=null,e.isAuthenticated=!1,e.error=null,e.sessionExpiry=null,n.clearCurrentUser()},setSessionExpiry:(e,r)=>{e.sessionExpiry=r.payload},updateUserProfile:(e,r)=>{e.user&&(e.user={...e.user,...r.payload},n.setCurrentUser(e.user))}},extraReducers:e=>{e.addCase(s.pending,e=>{e.loading=!0,e.error=null}).addCase(s.fulfilled,(e,r)=>{e.loading=!1,e.user=r.payload.user||r.payload,e.isAuthenticated=!0,e.error=null}).addCase(s.rejected,(e,r)=>{e.loading=!1,e.error=r.payload,e.isAuthenticated=!1,e.user=null}),e.addCase(l.pending,e=>{e.loading=!0}).addCase(l.fulfilled,e=>{e.loading=!1,e.user=null,e.isAuthenticated=!1,e.error=null,e.sessionExpiry=null}).addCase(l.rejected,e=>{e.loading=!1,e.user=null,e.isAuthenticated=!1,e.error=null,e.sessionExpiry=null}),e.addCase(i.pending,e=>{e.loading=!0}).addCase(i.fulfilled,(e,r)=>{e.loading=!1,e.user=r.payload.user||r.payload,e.error=null}).addCase(i.rejected,(e,r)=>{e.loading=!1,e.error=r.payload}),e.addCase(o.pending,e=>{e.loading=!0}).addCase(o.fulfilled,(e,r)=>{e.loading=!1,e.user=r.payload.user||r.payload,e.isAuthenticated=!0,e.error=null}).addCase(o.rejected,(e,r)=>{e.loading=!1,e.error=r.payload,e.isAuthenticated=!1,e.user=null})}}),{clearError:c,setUser:u,clearAuth:f,setSessionExpiry:h,updateUserProfile:g}=d.actions,p=e=>e.auth.user,m=e=>e.auth.isAuthenticated,x=e=>e.auth.loading,y=e=>e.auth.error,b=d.reducer},4965:(e,r,t)=>{t.d(r,{G:()=>s,j:()=>n});var a=t(9559);let n=()=>(0,a.wA)(),s=a.d4},5097:(e,r,t)=>{t.d(r,{J:()=>d});var a=t(9605),n=t(9585),s=t(8436),l=t(7276),i=t(6994);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)(s.b,{ref:r,className:(0,i.cn)(o(),t),...n})});d.displayName=s.b.displayName},6994:(e,r,t)=>{t.d(r,{Yq:()=>o,ZV:()=>i,cn:()=>s,r6:()=>d,tP:()=>c,vv:()=>l});var a=t(8330),n=t(398);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,a.$)(r))}function l(e){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}function i(e){return new Intl.NumberFormat("en-IN").format(e)}function o(e){if(!e)return"N/A";let r=new Date(e);return isNaN(r.getTime())?"Invalid Date":new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric"}).format(r)}function d(e){if(!e)return"N/A";let r=new Date(e);return isNaN(r.getTime())?"Invalid Date":new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(r)}function c(e,r){return 0===r?100*(e>0):(e-r)/r*100}},7971:(e,r,t)=>{t.d(r,{p:()=>l});var a=t(9605),n=t(9585),s=t(6994);let l=n.forwardRef((e,r)=>{let{className:t,type:n,...l}=e;return(0,a.jsx)("input",{type:n,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...l})});l.displayName="Input"},8063:(e,r,t)=>{t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>i});var a=t(9605),n=t(9585),s=t(6994);let l=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});l.displayName="Card";let i=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",t),...n})});i.displayName="CardHeader";let o=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",t),...n})});o.displayName="CardTitle";let d=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",t),...n})});d.displayName="CardDescription";let c=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",t),...n})});c.displayName="CardContent",n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"},9421:(e,r,t)=>{t.d(r,{A:()=>n});var a=t(9605);t(9585);let n=e=>{let{size:r="md",variant:t="full",theme:n="default",className:s=""}=e,l={sm:"h-8 w-8",md:"h-12 w-12",lg:"h-16 w-16",xl:"h-24 w-24"},i={sm:"text-lg",md:"text-2xl",lg:"text-3xl",xl:"text-4xl"},o=(()=>{switch(n){case"white":return{circle:"#ffffff",circleStroke:"#0ea5e9",building:"#0ea5e9",windows:"#ffffff",door:"#fbbf24",arrow:"#fbbf24",text:"#0ea5e9",accent:"#ffffff",primary:"#f1f5f9"};case"dark":return{circle:"#1e293b",circleStroke:"#0ea5e9",building:"#ffffff",windows:"#0ea5e9",door:"#fbbf24",arrow:"#fbbf24",text:"#ffffff",accent:"#0ea5e9",primary:"#64748b"};default:return{circle:"#0ea5e9",circleStroke:"#ffffff",building:"#ffffff",windows:"#0ea5e9",door:"#fbbf24",arrow:"#fbbf24",text:"#ffffff",accent:"#0ea5e9",primary:"#64748b"}}})(),d=()=>(0,a.jsx)("div",{className:"".concat(l[r]," ").concat(s," relative"),children:(0,a.jsxs)("svg",{viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"w-full h-full",children:[(0,a.jsx)("circle",{cx:"50",cy:"50",r:"48",fill:o.circle,stroke:o.circleStroke,strokeWidth:"2"}),(0,a.jsxs)("g",{transform:"translate(20, 25)",children:[(0,a.jsx)("rect",{x:"15",y:"20",width:"30",height:"35",fill:o.building,rx:"2"}),(0,a.jsx)("rect",{x:"20",y:"25",width:"6",height:"6",fill:o.windows}),(0,a.jsx)("rect",{x:"34",y:"25",width:"6",height:"6",fill:o.windows}),(0,a.jsx)("rect",{x:"20",y:"35",width:"6",height:"6",fill:o.windows}),(0,a.jsx)("rect",{x:"34",y:"35",width:"6",height:"6",fill:o.windows}),(0,a.jsx)("rect",{x:"27",y:"45",width:"6",height:"10",fill:o.door}),(0,a.jsx)("rect",{x:"5",y:"30",width:"8",height:"25",fill:o.building,rx:"1"}),(0,a.jsx)("rect",{x:"47",y:"35",width:"8",height:"20",fill:o.building,rx:"1"}),(0,a.jsx)("path",{d:"M10 15 L20 5 L30 10 L40 2 L50 8",stroke:o.arrow,strokeWidth:"3",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("polygon",{points:"45,2 50,8 45,8",fill:o.arrow})]}),(0,a.jsx)("text",{x:"50",y:"75",textAnchor:"middle",fill:o.text,fontSize:"12",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:"SGM"})]})});switch(t){case"icon":return(0,a.jsx)(d,{});case"text":return(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"".concat(s," flex items-center"),children:[(0,a.jsx)("span",{className:"".concat(i[r]," font-bold"),style:{color:o.accent},children:"SGM"}),(0,a.jsx)("span",{className:"".concat(i[r]," font-light ml-1"),style:{color:o.primary},children:"Sales"})]}),{});default:return(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"".concat(s," flex items-center space-x-3"),children:[(0,a.jsx)(d,{}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"".concat(i[r]," font-bold leading-none"),style:{color:o.accent},children:"SGM"}),(0,a.jsx)("span",{className:"text-sm leading-none",style:{color:o.primary},children:"Sales Portal"})]})]}),{})}}}}]);