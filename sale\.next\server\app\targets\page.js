(()=>{var e={};e.id=655,e.ids=[655],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22190:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},25011:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>d.a,__next_app__:()=>h,pages:()=>o,routeModule:()=>g,tree:()=>c});var t=a(10557),r=a(68490),l=a(13172),d=a.n(l),i=a(68835),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);a.d(s,n);let c={children:["",{children:["targets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,67269)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\targets\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\targets\\page.tsx"],h={require:a,loadChunk:()=>Promise.resolve()},g=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/targets/page",pathname:"/targets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},28540:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>$});var t=a(40969),r=a(73356),l=a(88251),d=a(66949),i=a(46411),n=a(76650),c=a(2223),o=a(96727),h=a(57387),g=a(54289),m=a(12053),x=a(79201),u=a(98085);let p=(0,u.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var j=a(27470),y=a(21857),v=a(91798),N=a(83427),b=a(79123),f=a(35342),w=a(14493),T=a(77060),D=a(8713),A=a(66282),k=a(31435);let C=(0,u.A)("trophy",[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]]);var q=a(22190),L=a(92386),S=a(21764),F=a(99206);let P=e=>{switch(e){case"on_track":case"completed":return"bg-green-100 text-green-800";case"behind":return"bg-red-100 text-red-800";case"exceeded":return"bg-sky-100 text-sky-800";default:return"bg-gray-100 text-gray-800"}},E=e=>{switch(e){case"on_track":case"completed":return(0,t.jsx)(x.A,{className:"w-4 h-4 text-green-600"});case"behind":return(0,t.jsx)(p,{className:"w-4 h-4 text-red-600"});case"exceeded":return(0,t.jsx)(j.A,{className:"w-4 h-4 text-sky-600"});default:return(0,t.jsx)(y.A,{className:"w-4 h-4 text-gray-600"})}},M=e=>{switch(e){case"sales":return(0,t.jsx)(v.A,{className:"w-5 h-5"});case"revenue":return(0,t.jsx)(N.A,{className:"w-5 h-5"});case"leads":return(0,t.jsx)(b.A,{className:"w-5 h-5"});default:return(0,t.jsx)(f.A,{className:"w-5 h-5"})}};function $(){let[e,s]=(0,r.useState)("monthly"),[a,u]=(0,r.useState)(!1),[p,j]=(0,r.useState)(!1),[y,N]=(0,r.useState)(null),[$,J]=(0,r.useState)({title:"",description:"",period:"monthly",targetType:"sales",targetAmount:"",targetLeads:"",targetDeals:"",startDate:"",endDate:"",notes:""}),{data:_,isLoading:R,error:U,refetch:V}=(0,F.WD)(),[z,{isLoading:B}]=(0,F.cm)(),[G,{isLoading:O}]=(0,F.Zu)(),Z=(_?.data?.targets||[]).filter(s=>"all"===e||s.period===e),I=Z.filter(e=>e.createdBy!==e.salesRepId),W=Z.filter(e=>e.createdBy===e.salesRepId),H=()=>{J({title:"",description:"",period:"monthly",targetType:"sales",targetAmount:"",targetLeads:"",targetDeals:"",startDate:"",endDate:"",notes:""})},Q=async()=>{try{if(!$.period||!$.targetAmount||!$.startDate||!$.endDate)return void console.error("Missing required fields");let e={period:$.period,targetType:$.targetType||"sales",targetValue:parseFloat($.targetAmount)||0,targetAmount:parseFloat($.targetAmount)||0,targetLeads:parseInt($.targetLeads)||0,targetDeals:parseInt($.targetDeals)||0,startDate:$.startDate,endDate:$.endDate,notes:$.notes||"",title:$.title||"",description:$.description||""};console.log("Creating target with data:",e),await z(e).unwrap(),u(!1),H(),V()}catch(e){console.error("Failed to create target:",e)}},Y=e=>{N(e),J({title:e.title||"",description:e.description||"",period:e.period||"monthly",targetType:e.targetType||"sales",targetAmount:e.targetAmount?.toString()||"",targetLeads:e.targetLeads?.toString()||"",targetDeals:e.targetDeals?.toString()||"",startDate:e.startDate?e.startDate.split("T")[0]:"",endDate:e.endDate?e.endDate.split("T")[0]:"",notes:e.notes||""}),j(!0)},K=async()=>{if(y)try{let e={period:$.period,targetType:$.targetType,targetValue:$.targetAmount?parseFloat($.targetAmount):0,targetAmount:$.targetAmount?parseFloat($.targetAmount):0,targetLeads:$.targetLeads?parseInt($.targetLeads):0,targetDeals:$.targetDeals?parseInt($.targetDeals):0,startDate:$.startDate,endDate:$.endDate,notes:$.notes,title:$.title,description:$.description};console.log("Updating target with data:",e),await G({id:y.id||y._id,data:e}).unwrap(),j(!1),N(null),H(),V()}catch(e){console.error("Failed to update target:",e)}};return R?(0,t.jsx)(l.A,{title:"Sales Targets",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)(w.A,{className:"w-8 h-8 animate-spin text-sky-500"})})})}):U?(0,t.jsx)(l.A,{title:"Sales Targets",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(T.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Failed to load targets"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Please try refreshing the page"}),(0,t.jsx)(i.$,{onClick:()=>V(),className:"bg-sky-500 hover:bg-sky-600",children:"Try Again"})]})})})}):(0,t.jsxs)(l.A,{title:"Sales Targets",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)("div",{className:"p-2 bg-sky-500 rounded-lg",children:(0,t.jsx)(f.A,{className:"w-5 h-5 text-white"})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Sales Targets"})]}),(0,t.jsx)("p",{className:"text-gray-600",children:"Track and achieve your sales goals"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-sm text-gray-500",children:[(0,t.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,t.jsx)(D.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"all"===e?"All Time":e.charAt(0).toUpperCase()+e.slice(1)})]}),(0,t.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,t.jsx)(A.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[Z.length," targets"]})]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsxs)(c.l6,{value:e,onValueChange:s,children:[(0,t.jsx)(c.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,t.jsx)(c.yv,{})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"all",children:"All Periods"}),(0,t.jsx)(c.eb,{value:"monthly",children:"Monthly"}),(0,t.jsx)(c.eb,{value:"quarterly",children:"Quarterly"}),(0,t.jsx)(c.eb,{value:"yearly",children:"Yearly"})]})]}),(0,t.jsxs)(i.$,{className:"bg-sky-500 hover:bg-sky-600 text-white",onClick:()=>u(!0),children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"New Target"]})]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(d.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)(x.A,{className:"w-5 h-5 text-green-600"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Targets"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-900",children:Z.filter(e=>"active"===e.status).length})]})]})})}),(0,t.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(d.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-sky-100 rounded-lg",children:(0,t.jsx)(f.A,{className:"w-5 h-5 text-sky-600"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Admin Assigned"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-900",children:I.length})]})]})})}),(0,t.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(d.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,t.jsx)(A.A,{className:"w-5 h-5 text-yellow-600"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Self Created"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-900",children:W.length})]})]})})}),(0,t.jsx)(d.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(d.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)(C,{className:"w-5 h-5 text-green-600"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-900",children:Z.filter(e=>"completed"===e.status).length})]})]})})})]}),(0,t.jsxs)(d.Zp,{className:"border-gray-200 shadow-sm",children:[(0,t.jsx)(d.aR,{className:"bg-white border-b border-gray-200",children:(0,t.jsxs)(d.ZB,{className:"text-gray-900",children:["Sales Targets (",Z.length,")"]})}),(0,t.jsxs)(d.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"space-y-0",children:Z.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-100 hover:bg-sky-50 transition-colors",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,t.jsx)("div",{className:"flex-shrink-0 p-2 bg-gray-100 rounded-lg",children:M(e.type)}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,t.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:e.title||"Sales Target"}),(0,t.jsx)(n.E,{className:`text-xs ${P(e.status)}`,children:e.status.replace("_"," ").toUpperCase()}),(0,t.jsx)(n.E,{variant:"outline",className:"text-xs border-sky-200 text-sky-700",children:e.period})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Progress"}),(0,t.jsxs)("span",{className:"font-medium",children:[e.percentage||0,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${(e.percentage||0)>=100?"bg-green-500":(e.percentage||0)>=75?"bg-sky-500":(e.percentage||0)>=50?"bg-yellow-500":"bg-red-500"}`,style:{width:`${Math.min(e.percentage||0,100)}%`}})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-2 text-sm text-gray-600",children:[(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"w-3 h-3 mr-1 text-sky-500"}),"Target: ",e.targetAmount?(0,S.vv)(e.targetAmount):(0,S.ZV)(e.targetValue||0)]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(v.A,{className:"w-3 h-3 mr-1 text-green-500"}),"Achieved: ",e.achievedAmount?(0,S.vv)(e.achievedAmount):(0,S.ZV)(e.currentValue||0)]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"w-3 h-3 mr-1 text-yellow-500"}),"Leads: ",e.achievedLeads||0,"/",e.targetLeads||0]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(D.A,{className:"w-3 h-3 mr-1 text-gray-500"}),"Due: ",(0,S.Yq)(e.endDate||e.dueDate)]})]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[E(e.status),(0,t.jsx)(i.$,{size:"sm",variant:"outline",className:"border-sky-300 text-sky-600 hover:bg-sky-50",onClick:()=>Y(e),children:(0,t.jsx)(q.A,{className:"w-3 h-3"})}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",className:"border-green-300 text-green-600 hover:bg-green-50",title:"View Details",children:(0,t.jsx)(L.A,{className:"w-3 h-3"})})]})]},e.id))}),0===Z.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(f.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500",children:"No targets found"}),(0,t.jsx)(i.$,{className:"mt-4 bg-sky-500 hover:bg-sky-600 text-white",onClick:()=>V(),children:"Refresh"})]})]})]})]}),(0,t.jsx)(o.lG,{open:a,onOpenChange:e=>{u(e),e||H()},children:(0,t.jsxs)(o.Cf,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(o.c7,{children:(0,t.jsxs)(o.L3,{className:"text-xl font-semibold flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"w-5 h-5 text-sky-500"}),"Create New Target"]})}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"title",children:"Target Title"}),(0,t.jsx)(h.p,{id:"title",value:$.title,onChange:e=>J(s=>({...s,title:e.target.value})),placeholder:"Enter target title",className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"description",children:"Description"}),(0,t.jsx)(g.T,{id:"description",value:$.description,onChange:e=>J(s=>({...s,description:e.target.value})),placeholder:"Enter target description",className:"w-full",rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"period",children:"Period *"}),(0,t.jsxs)(c.l6,{value:$.period,onValueChange:e=>J(s=>({...s,period:e})),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{placeholder:"Select period"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"monthly",children:"Monthly"}),(0,t.jsx)(c.eb,{value:"quarterly",children:"Quarterly"}),(0,t.jsx)(c.eb,{value:"yearly",children:"Yearly"})]})]})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"targetType",children:"Target Type *"}),(0,t.jsxs)(c.l6,{value:$.targetType,onValueChange:e=>J(s=>({...s,targetType:e})),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{placeholder:"Select type"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"sales",children:"Sales Volume"}),(0,t.jsx)(c.eb,{value:"revenue",children:"Revenue"}),(0,t.jsx)(c.eb,{value:"leads",children:"Leads"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"targetAmount",children:"Target Amount *"}),(0,t.jsx)(h.p,{id:"targetAmount",type:"number",value:$.targetAmount,onChange:e=>J(s=>({...s,targetAmount:e.target.value})),placeholder:"Enter target amount",className:"w-full",required:!0,min:"1"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"targetLeads",children:"Target Leads"}),(0,t.jsx)(h.p,{id:"targetLeads",type:"number",value:$.targetLeads,onChange:e=>J(s=>({...s,targetLeads:e.target.value})),placeholder:"0",className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"targetDeals",children:"Target Deals"}),(0,t.jsx)(h.p,{id:"targetDeals",type:"number",value:$.targetDeals,onChange:e=>J(s=>({...s,targetDeals:e.target.value})),placeholder:"0",className:"w-full"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"startDate",children:"Start Date *"}),(0,t.jsx)(h.p,{id:"startDate",type:"date",value:$.startDate,onChange:e=>J(s=>({...s,startDate:e.target.value})),className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"endDate",children:"End Date *"}),(0,t.jsx)(h.p,{id:"endDate",type:"date",value:$.endDate,onChange:e=>J(s=>({...s,endDate:e.target.value})),className:"w-full"})]})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"notes",children:"Notes"}),(0,t.jsx)(g.T,{id:"notes",value:$.notes,onChange:e=>J(s=>({...s,notes:e.target.value})),placeholder:"Additional notes or comments",className:"w-full",rows:2})]})]}),(0,t.jsxs)(o.Es,{children:[(0,t.jsx)(i.$,{variant:"outline",onClick:()=>u(!1),children:"Cancel"}),(0,t.jsxs)(i.$,{onClick:Q,disabled:B||!$.period||!$.targetAmount||!$.startDate||!$.endDate,className:"bg-sky-500 hover:bg-sky-600",children:[B&&(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Create Target"]})]})]})}),(0,t.jsx)(o.lG,{open:p,onOpenChange:e=>{j(e),e||(N(null),H())},children:(0,t.jsxs)(o.Cf,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(o.c7,{children:(0,t.jsxs)(o.L3,{className:"text-xl font-semibold flex items-center gap-2",children:[(0,t.jsx)(q.A,{className:"w-5 h-5 text-sky-500"}),"Edit Target"]})}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-title",children:"Target Title *"}),(0,t.jsx)(h.p,{id:"edit-title",value:$.title,onChange:e=>J(s=>({...s,title:e.target.value})),placeholder:"Enter target title",className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-description",children:"Description"}),(0,t.jsx)(g.T,{id:"edit-description",value:$.description,onChange:e=>J(s=>({...s,description:e.target.value})),placeholder:"Enter target description",className:"w-full",rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-period",children:"Period *"}),(0,t.jsxs)(c.l6,{value:$.period,onValueChange:e=>J(s=>({...s,period:e})),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{placeholder:"Select period"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"monthly",children:"Monthly"}),(0,t.jsx)(c.eb,{value:"quarterly",children:"Quarterly"}),(0,t.jsx)(c.eb,{value:"yearly",children:"Yearly"})]})]})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-targetType",children:"Target Type *"}),(0,t.jsxs)(c.l6,{value:$.targetType,onValueChange:e=>J(s=>({...s,targetType:e})),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{placeholder:"Select type"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"sales",children:"Sales Volume"}),(0,t.jsx)(c.eb,{value:"revenue",children:"Revenue"}),(0,t.jsx)(c.eb,{value:"leads",children:"Leads"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-targetAmount",children:"Target Amount"}),(0,t.jsx)(h.p,{id:"edit-targetAmount",type:"number",value:$.targetAmount,onChange:e=>J(s=>({...s,targetAmount:e.target.value})),placeholder:"0",className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-targetLeads",children:"Target Leads"}),(0,t.jsx)(h.p,{id:"edit-targetLeads",type:"number",value:$.targetLeads,onChange:e=>J(s=>({...s,targetLeads:e.target.value})),placeholder:"0",className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-targetDeals",children:"Target Deals"}),(0,t.jsx)(h.p,{id:"edit-targetDeals",type:"number",value:$.targetDeals,onChange:e=>J(s=>({...s,targetDeals:e.target.value})),placeholder:"0",className:"w-full"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-startDate",children:"Start Date *"}),(0,t.jsx)(h.p,{id:"edit-startDate",type:"date",value:$.startDate,onChange:e=>J(s=>({...s,startDate:e.target.value})),className:"w-full"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-endDate",children:"End Date *"}),(0,t.jsx)(h.p,{id:"edit-endDate",type:"date",value:$.endDate,onChange:e=>J(s=>({...s,endDate:e.target.value})),className:"w-full"})]})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"edit-notes",children:"Notes"}),(0,t.jsx)(g.T,{id:"edit-notes",value:$.notes,onChange:e=>J(s=>({...s,notes:e.target.value})),placeholder:"Additional notes or comments",className:"w-full",rows:2})]})]}),(0,t.jsxs)(o.Es,{children:[(0,t.jsx)(i.$,{variant:"outline",onClick:()=>j(!1),children:"Cancel"}),(0,t.jsxs)(i.$,{onClick:K,disabled:O||!$.period||!$.targetAmount||!$.startDate||!$.endDate,className:"bg-sky-500 hover:bg-sky-600",children:[O&&(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Update Target"]})]})]})})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31435:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33873:e=>{"use strict";e.exports=require("path")},51209:(e,s,a)=>{Promise.resolve().then(a.bind(a,28540))},60937:(e,s,a)=>{Promise.resolve().then(a.bind(a,67269))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67269:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\targets\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\targets\\page.tsx","default")},77060:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},79201:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},79551:e=>{"use strict";e.exports=require("url")},92386:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},99206:(e,s,a)=>{"use strict";a.d(s,{$V:()=>i,FO:()=>c,Hu:()=>l,OT:()=>A,Pb:()=>r,WD:()=>y,Zu:()=>N,aT:()=>j,cm:()=>v,nK:()=>m,pv:()=>D,ro:()=>b});let t=a(53412).q.injectEndpoints({endpoints:e=>({getDashboardStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getDashboardActivities:e.query({query:e=>({url:"/dashboard/activities",params:e}),providesTags:["Dashboard"]}),getSalesStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getLeads:e.query({query:e=>({url:"/sales/leads",params:e}),providesTags:["Lead"]}),getLeadById:e.query({query:e=>`/sales/leads/${e}`,providesTags:(e,s,a)=>[{type:"Lead",id:a}]}),createLead:e.mutation({query:e=>({url:"/sales/leads",method:"POST",body:e}),invalidatesTags:["Lead","Dashboard"]}),updateLead:e.mutation({query:({id:e,data:s})=>({url:`/sales/leads/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:a})=>[{type:"Lead",id:a},"Lead","Dashboard"]}),deleteLead:e.mutation({query:e=>({url:`/sales/leads/${e}`,method:"DELETE"}),invalidatesTags:["Lead","Dashboard"]}),assignLead:e.mutation({query:({leadId:e,salesRepId:s})=>({url:`/sales/leads/${e}/assign`,method:"POST",body:{salesRepId:s}}),invalidatesTags:["Lead"]}),getCustomers:e.query({query:e=>({url:"/sales/customers",params:e}),providesTags:["Customer"]}),getCustomerById:e.query({query:e=>`/sales/customers/${e}`,providesTags:(e,s,a)=>[{type:"Customer",id:a}]}),createCustomer:e.mutation({query:e=>({url:"/sales/customers",method:"POST",body:e}),invalidatesTags:["Customer","Dashboard"]}),updateCustomer:e.mutation({query:({id:e,data:s})=>({url:`/sales/customers/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:a})=>[{type:"Customer",id:a},"Customer"]}),getCommissions:e.query({query:e=>({url:"/sales/commissions",params:e}),providesTags:["Commission"]}),getSalesTargets:e.query({query:()=>"/sales/targets",providesTags:["Report"]}),createSalesTarget:e.mutation({query:e=>({url:"/sales/targets",method:"POST",body:e}),invalidatesTags:["Report","Dashboard"]}),updateSalesTarget:e.mutation({query:({id:e,data:s})=>({url:`/sales/targets/${e}`,method:"PUT",body:s}),invalidatesTags:["Report","Dashboard"]}),getFollowUps:e.query({query:e=>({url:"/follow-ups",params:e}),providesTags:["Task"]}),createFollowUp:e.mutation({query:e=>({url:"/follow-ups",method:"POST",body:e}),invalidatesTags:["Task","Dashboard"]}),updateFollowUp:e.mutation({query:({id:e,data:s})=>({url:`/follow-ups/${e}`,method:"PUT",body:s}),invalidatesTags:["Task","Dashboard"]}),deleteFollowUp:e.mutation({query:e=>({url:`/follow-ups/${e}`,method:"DELETE"}),invalidatesTags:["Task","Dashboard"]})})}),{useGetDashboardStatsQuery:r,useGetDashboardActivitiesQuery:l,useGetSalesStatsQuery:d,useGetLeadsQuery:i,useGetLeadByIdQuery:n,useCreateLeadMutation:c,useUpdateLeadMutation:o,useDeleteLeadMutation:h,useAssignLeadMutation:g,useGetCustomersQuery:m,useGetCustomerByIdQuery:x,useCreateCustomerMutation:u,useUpdateCustomerMutation:p,useGetCommissionsQuery:j,useGetSalesTargetsQuery:y,useCreateSalesTargetMutation:v,useUpdateSalesTargetMutation:N,useGetFollowUpsQuery:b,useCreateFollowUpMutation:f,useUpdateFollowUpMutation:w,useDeleteFollowUpMutation:T}=t,{useGetCustomersQuery:D,useGetCommissionsQuery:A}=t}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[755,598,544,29,796,286,447,512],()=>a(25011));module.exports=t})();