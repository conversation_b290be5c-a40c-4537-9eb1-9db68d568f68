import { Types } from 'mongoose';
import { UserKYC, IUserKYC, AuditLog, User, IUser } from '../models';
import { IKYCDocument, DocumentType, DocumentCategory } from '../models/UserKYC';
import { KYCStatus } from '../types';
import { AuditAction, AuditEntity } from '../models/AuditLog';
import PDFDocument from 'pdfkit';
import fs from 'fs';
import path from 'path';
import https from 'https';
import http from 'http';

export class KYCService {
  constructor() {
    // No parent constructor needed
  }

  /**
   * Handle errors consistently
   */
  private handleError(error: any, operation: string): Error {
    console.error(`KYC Service Error in ${operation}:`, error);
    return new Error(`${operation} failed: ${error.message || error}`);
  }

  /**
   * Get user KYC data
   */
  async getUserKYC(userId: string): Promise<IUserKYC> {
    try {
      console.log('getU<PERSON><PERSON>Y<PERSON> called with userId:', userId, 'type:', typeof userId);

      // Ensure userId is properly converted to ObjectId for query
      const userObjectId = new Types.ObjectId(userId);
      let kyc = await UserKYC.findOne({ userId: userObjectId }).populate('userId', 'firstName lastName email phone dateOfBirth');

      if (!kyc) {
        // Create new KYC record if doesn't exist
        kyc = new UserKYC({
          userId,
          status: KYCStatus.NOT_STARTED,
          country: 'IN',
          riskLevel: 'low',
          personalInfo: {},
          address: {},
          documents: [],
          completedSteps: []
        });
        await kyc.save();

        // Update user's KYC status to NOT_STARTED when KYC record is created
        await User.findByIdAndUpdate(userId, {
          kycStatus: KYCStatus.NOT_STARTED,
          updatedAt: new Date()
        });
      } else {
        // Check and update completed steps for existing records
        let needsUpdate = false;

        // Check if signature step should be marked as completed
        if (!kyc.completedSteps.includes('signature')) {
          const hasSignature = kyc.documents.some(doc => doc.type === DocumentType.DIGITAL_SIGNATURE);
          const hasSelfie = kyc.documents.some(doc => doc.type === DocumentType.SELFIE);

          if (hasSignature && hasSelfie) {
            kyc.completedSteps.push('signature');
            needsUpdate = true;
            console.log('✅ Updating existing KYC: Signature step marked as completed');
          }
        }

        // Update the record if needed
        if (needsUpdate) {
          kyc.updatedAt = new Date();
          await kyc.save();
        }
      }

      return kyc;
    } catch (error) {
      throw this.handleError(error, 'getUserKYC');
    }
  }

  /**
   * Get user details for KYC auto-fill
   */
  async getUserDetailsForKYC(userId: any): Promise<any> {
    try {
      console.log('getUserDetailsForKYC called with userId:', userId, 'type:', typeof userId);

      if (!userId) {
        throw new Error('User ID is required');
      }

      // Handle case where userId might be an object with _id property
      let cleanUserId: string;
      if (typeof userId === 'object' && userId._id) {
        cleanUserId = userId._id.toString();
      } else if (typeof userId === 'string') {
        cleanUserId = userId.trim();
      } else {
        cleanUserId = userId.toString().trim();
      }

      console.log('Cleaned userId:', cleanUserId);

      const user = await User.findById(cleanUserId).select('firstName lastName email phone dateOfBirth');
      console.log('Found user:', user ? `${user.firstName} ${user.lastName}` : 'null');
      return user;
    } catch (error) {
      console.error('getUserDetailsForKYC error:', error);
      throw this.handleError(error, 'getUserDetailsForKYC');
    }
  }

  /**
   * Update KYC personal information
   */
  async updatePersonalInfo(userId: string, personalInfo: any): Promise<IUserKYC> {
    try {
      const kyc = await this.getUserKYC(userId);
      
      // Update personal info
      kyc.personalInfo = {
        ...kyc.personalInfo,
        ...personalInfo
      };

      // Update country field based on nationality
      if (personalInfo.nationality) {
        kyc.country = personalInfo.nationality;
      }

      // Add to completed steps if not already there
      if (!kyc.completedSteps.includes('personal')) {
        kyc.completedSteps.push('personal');
      }

      kyc.updatedAt = new Date();
      await kyc.save();

      // Update user's KYC status to PENDING when they start KYC
      await User.findByIdAndUpdate(userId, {
        kycStatus: KYCStatus.PENDING,
        updatedAt: new Date()
      });

      // Log audit
      await this.logAudit(userId, AuditAction.UPDATE, AuditEntity.USER_KYC, (kyc._id as Types.ObjectId).toString(), {
        step: 'personal',
        data: personalInfo
      });

      return kyc;
    } catch (error) {
      throw this.handleError(error, 'updatePersonalInfo');
    }
  }

  /**
   * Update KYC address information
   */
  async updateAddress(userId: string, address: any): Promise<IUserKYC> {
    try {
      const kyc = await this.getUserKYC(userId);
      
      // Update address
      kyc.address = {
        ...kyc.address,
        ...address
      };

      // Add to completed steps if not already there
      if (!kyc.completedSteps.includes('address')) {
        kyc.completedSteps.push('address');
      }

      kyc.updatedAt = new Date();
      await kyc.save();

      // Log audit
      await this.logAudit(userId, AuditAction.UPDATE, AuditEntity.USER_KYC, (kyc._id as Types.ObjectId).toString(), {
        step: 'address',
        data: address
      });

      return kyc;
    } catch (error) {
      throw this.handleError(error, 'updateAddress');
    }
  }

  /**
   * Upload KYC document
   */
  async uploadDocument(userId: string, documentData: {
    type: DocumentType;
    category: DocumentCategory;
    fileUrl: string;
    fileName: string;
    fileSize: number;
    documentNumber?: string;
    expiryDate?: Date;
  }): Promise<IUserKYC> {
    try {
      const kyc = await this.getUserKYC(userId);

      // Create document object
      const document: IKYCDocument = {
        type: documentData.type,
        category: documentData.category,
        documentUrl: documentData.fileUrl,
        documentNumber: documentData.documentNumber,
        expiryDate: documentData.expiryDate,
        status: KYCStatus.PENDING,
        uploadedAt: new Date()
      };

      // Add document to array
      kyc.documents.push(document);

      // Add to completed steps if not already there and has minimum documents
      if (!kyc.completedSteps.includes('documents') && kyc.documents.length >= 2) {
        kyc.completedSteps.push('documents');
      }

      // Check if signature step should be completed
      // Signature step is complete when we have both digital_signature and selfie documents
      if (!kyc.completedSteps.includes('signature')) {
        const hasSignature = kyc.documents.some(doc => doc.type === DocumentType.DIGITAL_SIGNATURE);
        const hasSelfie = kyc.documents.some(doc => doc.type === DocumentType.SELFIE);

        if (hasSignature && hasSelfie) {
          kyc.completedSteps.push('signature');
          console.log('✅ Signature step marked as completed - both signature and selfie uploaded');
        }
      }

      kyc.updatedAt = new Date();
      await kyc.save();

      // Log audit
      await this.logAudit(userId, AuditAction.CREATE, AuditEntity.USER_KYC, (kyc._id as Types.ObjectId).toString(), {
        documentType: documentData.type,
        fileUrl: documentData.fileUrl
      });

      return kyc;
    } catch (error) {
      throw this.handleError(error, 'uploadDocument');
    }
  }

  /**
   * Submit KYC for verification
   */
  async submitKYC(userId: string): Promise<IUserKYC> {
    try {
      const kyc = await this.getUserKYC(userId);

      // Validate required steps
      const requiredSteps = ['personal', 'address', 'documents'];
      const missingSteps = requiredSteps.filter(step => !kyc.completedSteps.includes(step));

      if (missingSteps.length > 0) {
        throw new Error(`Missing required steps: ${missingSteps.join(', ')}`);
      }

      // Update status
      kyc.status = KYCStatus.UNDER_REVIEW;
      kyc.submittedAt = new Date();
      kyc.updatedAt = new Date();

      // Add submission step
      if (!kyc.completedSteps.includes('submitted')) {
        kyc.completedSteps.push('submitted');
      }

      await kyc.save();

      // Update user's kycStatus to match KYC status
      await User.findByIdAndUpdate(userId, {
        kycStatus: 'under_review',
        updatedAt: new Date()
      });

      console.log('✅ Updated user kycStatus to under_review for user:', userId);

      // Log audit
      await this.logAudit(userId, AuditAction.UPDATE, AuditEntity.USER_KYC, (kyc._id as Types.ObjectId).toString(), {
        action: 'submitted_for_verification'
      });

      return kyc;
    } catch (error) {
      throw this.handleError(error, 'submitKYC');
    }
  }

  /**
   * Check if URL is an image
   */
  private isImageUrl(url: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg'];
    const urlLower = url.toLowerCase();
    return imageExtensions.some(ext => urlLower.includes(ext)) || urlLower.includes('image');
  }

  /**
   * Download image from URL and return buffer
   */
  private async downloadImage(url: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const protocol = url.startsWith('https:') ? https : http;

      protocol.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`Failed to download image: ${response.statusCode}`));
          return;
        }

        const chunks: Buffer[] = [];
        response.on('data', (chunk) => chunks.push(chunk));
        response.on('end', () => resolve(Buffer.concat(chunks)));
        response.on('error', reject);
      }).on('error', reject);
    });
  }

  /**
   * Generate KYC PDF report with all documents and information
   */
  async generateKYCPDF(user: any, kyc: IUserKYC): Promise<Buffer> {
    return new Promise(async (resolve, reject) => {
      try {
        const doc = new PDFDocument({ margin: 50, size: 'A4' });
        const chunks: Buffer[] = [];

        doc.on('data', (chunk) => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));

        // Header with logo space
        doc.fontSize(28).fillColor('#0ea5e9').text('KYC VERIFICATION REPORT', { align: 'center' });
        doc.moveDown(0.5);
        doc.fontSize(14).fillColor('#64748b').text('SGM Investment Platform', { align: 'center' });
        doc.fontSize(10).fillColor('#64748b').text(`Generated on: ${new Date().toLocaleString()}`, { align: 'center' });
        doc.moveDown(2);

        // User Information Section
        doc.fontSize(18).fillColor('#1e293b').text('PERSONAL INFORMATION', { underline: true });
        doc.moveDown(0.5);
        doc.fontSize(12).fillColor('#334155');

        const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Not provided';
        const personalData = [
          ['Full Name:', fullName],
          ['Email:', user.email || 'Not provided'],
          ['Phone:', user.phone || 'Not provided'],
          ['Date of Birth:', user.dateOfBirth ? new Date(user.dateOfBirth).toLocaleDateString() : 'Not provided'],
          ['Gender:', kyc.personalInfo?.gender || 'Not provided']
        ];

        personalData.forEach(([label, value]) => {
          doc.text(`${label} ${value}`, { continued: false });
        });

        doc.moveDown();

        // Additional Personal Details
        if (kyc.personalInfo && Object.keys(kyc.personalInfo).length > 0) {
          doc.fontSize(16).fillColor('#1e293b').text('ADDITIONAL DETAILS', { underline: true });
          doc.moveDown(0.5);
          doc.fontSize(12).fillColor('#334155');
          
          const additionalData = [
            ['Nationality:', kyc.personalInfo.nationality || 'Not provided'],
            ['Place of Birth:', kyc.personalInfo.placeOfBirth || 'Not provided'],
            ['Gender:', kyc.personalInfo.gender || 'Not provided'],
            ['Marital Status:', kyc.personalInfo.maritalStatus || 'Not provided']
          ];

          additionalData.forEach(([label, value]) => {
            doc.text(`${label} ${value}`);
          });

          doc.moveDown();
        }

        // Address Information
        if (kyc.address && Object.keys(kyc.address).length > 0) {
          doc.fontSize(16).fillColor('#1e293b').text('ADDRESS INFORMATION', { underline: true });
          doc.moveDown(0.5);
          doc.fontSize(12).fillColor('#334155');
          
          const addressData = [
            ['Street:', kyc.address.street || 'Not provided'],
            ['City:', kyc.address.city || 'Not provided'],
            ['State:', kyc.address.state || 'Not provided'],
            ['Postal Code:', kyc.address.postalCode || 'Not provided'],
            ['Country:', kyc.address.country || 'Not provided']
          ];

          addressData.forEach(([label, value]) => {
            doc.text(`${label} ${value}`);
          });

          doc.moveDown();
        }

        // KYC Status Section
        doc.fontSize(16).fillColor('#1e293b').text('VERIFICATION STATUS', { underline: true });
        doc.moveDown(0.5);
        doc.fontSize(12).fillColor('#334155');

        const statusData = [
          ['Status:', kyc.status.toUpperCase()],
          ['Submitted:', kyc.createdAt.toLocaleDateString()],
          ['Last Updated:', kyc.updatedAt.toLocaleDateString()]
        ];

        if (kyc.verifiedAt) {
          statusData.push(['Verified:', kyc.verifiedAt.toLocaleDateString()]);
        }

        // Add verifier information if available
        if (kyc.verifiedBy) {
          const verifier = kyc.verifiedBy as any; // Type assertion for populated verifier
          if (verifier.firstName && verifier.lastName) {
            statusData.push(['Verified By:', `${verifier.firstName} ${verifier.lastName}`]);
          } else if (verifier.email) {
            statusData.push(['Verified By:', verifier.email]);
          }
        }

        statusData.forEach(([label, value]) => {
          doc.text(`${label} ${value}`);
        });

        // Add verified details if KYC is approved
        if (kyc.status === KYCStatus.APPROVED && kyc.userId) {
          doc.moveDown(0.5);
          doc.fontSize(14).fillColor('#059669').text('VERIFIED DETAILS', { underline: true });
          doc.moveDown(0.3);
          doc.fontSize(12).fillColor('#334155');

          const user = kyc.userId as any; // Type assertion for populated user
          const verifiedData = [
            ['Name:', `${user.firstName} ${user.lastName}`],
            ['Email:', user.email],
            ['Phone:', user.phone || 'Not provided']
          ];

          verifiedData.forEach(([label, value]) => {
            doc.text(`${label} ${value}`);
          });
        }

        doc.moveDown();

        // Documents Section
        doc.fontSize(18).fillColor('#1e293b').text('UPLOADED DOCUMENTS', { underline: true });
        doc.moveDown(0.5);
        doc.fontSize(12).fillColor('#334155');

        if (kyc.documents && kyc.documents.length > 0) {
          for (let i = 0; i < kyc.documents.length; i++) {
            const document = kyc.documents[i];

            // Check if we need a new page
            if (doc.y > 650) {
              doc.addPage();
            }

            doc.fontSize(14).fillColor('#1e293b').text(`${i + 1}. ${document.type.toUpperCase().replace('_', ' ')} (${document.category.toUpperCase()})`);
            doc.fontSize(12).fillColor('#334155');
            doc.text(`   Status: ${document.status.toUpperCase()}`);
            doc.text(`   Uploaded: ${document.uploadedAt.toLocaleDateString()}`);

            if (document.documentNumber) {
              doc.text(`   Document Number: ${document.documentNumber}`);
            }
            if (document.expiryDate) {
              doc.text(`   Expiry Date: ${new Date(document.expiryDate).toLocaleDateString()}`);
            }
            if (document.verifiedAt) {
              doc.text(`   Verified: ${document.verifiedAt.toLocaleDateString()}`);
            }
            if (document.rejectionReason) {
              doc.fillColor('#dc2626').text(`   Rejection Reason: ${document.rejectionReason}`);
              doc.fillColor('#334155');
            }

            // Try to embed image if it's an image document
            if (document.documentUrl && this.isImageUrl(document.documentUrl)) {
              try {
                const imageBuffer = await this.downloadImage(document.documentUrl);
                const imageWidth = 200;
                const imageHeight = 150;

                // Check if image fits on current page
                if (doc.y + imageHeight > 750) {
                  doc.addPage();
                }

                doc.moveDown(0.5);
                doc.image(imageBuffer, doc.x, doc.y, {
                  width: imageWidth,
                  height: imageHeight,
                  fit: [imageWidth, imageHeight]
                });
                doc.y += imageHeight + 10;
              } catch (error) {
                console.error(`Failed to embed image for document ${i + 1}:`, error);
                doc.text(`   [Image could not be embedded: ${document.documentUrl.split('/').pop()}]`);
              }
            } else {
              doc.text(`   File: ${document.documentUrl.split('/').pop() || 'Document'}`);
            }

            doc.moveDown(1);
          }
        } else {
          doc.text('No documents uploaded');
        }

        // Verification Images Section (Selfie and Signature)
        // Note: We need to check how documents are actually stored in the controller
        // For now, let's look for documents with specific patterns in the URL
        const selfieDoc = kyc.documents.find(doc =>
          doc.documentUrl && doc.documentUrl.includes('selfie')
        );
        const signatureDoc = kyc.documents.find(doc =>
          doc.documentUrl && (
            doc.documentUrl.includes('signature') ||
            doc.documentUrl.includes('sign')
          )
        );

        if (selfieDoc || signatureDoc) {
          doc.addPage();
          doc.fontSize(18).fillColor('#1e293b').text('VERIFICATION IMAGES', { underline: true });
          doc.moveDown(0.5);

          if (selfieDoc && this.isImageUrl(selfieDoc.documentUrl)) {
            try {
              doc.fontSize(14).fillColor('#1e293b').text('SELFIE VERIFICATION');
              doc.moveDown(0.5);
              const selfieBuffer = await this.downloadImage(selfieDoc.documentUrl);
              doc.image(selfieBuffer, doc.x, doc.y, {
                width: 150,
                height: 150,
                fit: [150, 150]
              });
              doc.y += 160;
            } catch (error) {
              console.error('Failed to embed selfie:', error);
              doc.text('Selfie image could not be embedded');
            }
          }

          if (signatureDoc && this.isImageUrl(signatureDoc.documentUrl)) {
            try {
              doc.fontSize(14).fillColor('#1e293b').text('DIGITAL SIGNATURE');
              doc.moveDown(0.5);
              const signatureBuffer = await this.downloadImage(signatureDoc.documentUrl);
              doc.image(signatureBuffer, doc.x, doc.y, {
                width: 200,
                height: 100,
                fit: [200, 100]
              });
              doc.y += 110;
            } catch (error) {
              console.error('Failed to embed signature:', error);
              doc.text('Digital signature could not be embedded');
            }
          }
        }

        // Footer
        doc.moveDown(2);
        doc.fontSize(10).fillColor('#64748b');
        doc.text(`Generated on: ${new Date().toLocaleString()}`, { align: 'center' });
        doc.text('This is an official KYC verification report from SGM', { align: 'center' });
        doc.text('For verification purposes only - Not for external distribution', { align: 'center' });

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Get pending KYC verifications for admin
   */
  async getPendingVerifications(options: any = {}): Promise<IUserKYC[]> {
    try {
      const { page = 1, limit = 10, country } = options;
      const skip = (page - 1) * limit;

      const filter: any = { status: KYCStatus.PENDING };
      if (country) filter.country = country;

      return await UserKYC.find(filter)
        .populate('userId', 'firstName lastName email')
        .sort({ createdAt: 1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      throw this.handleError(error, 'getPendingVerifications');
    }
  }

  /**
   * Get KYC by ID
   */
  async getKYCById(kycId: string): Promise<IUserKYC | null> {
    try {
      const kyc = await UserKYC.findById(kycId)
        .populate('userId', 'firstName lastName email phone dateOfBirth fullName');
      return kyc;
    } catch (error) {
      throw this.handleError(error, 'getKYCById');
    }
  }

  /**
   * Approve entire KYC application
   */
  async approveKYC(userId: string, verifiedBy: string, notes?: string): Promise<IUserKYC> {
    try {
      const kyc = await this.getUserKYC(userId);

      // Update KYC status
      kyc.status = KYCStatus.APPROVED;
      kyc.verifiedAt = new Date();
      kyc.updatedAt = new Date();

      if (notes) {
        kyc.adminNotes = notes;
      }

      await kyc.save();

      // Update user's kycStatus to approved
      await User.findByIdAndUpdate(userId, {
        kycStatus: 'approved',
        updatedAt: new Date()
      });

      console.log('✅ KYC approved and user status updated for user:', userId);

      // Log audit
      await this.logAudit(userId, AuditAction.UPDATE, AuditEntity.USER_KYC, (kyc._id as Types.ObjectId).toString(), {
        action: 'kyc_approved',
        notes
      });

      return kyc;
    } catch (error) {
      throw this.handleError(error, 'approveKYC');
    }
  }

  /**
   * Reject entire KYC application
   */
  async rejectKYC(userId: string, rejectionReason: string, verifiedBy: string, notes?: string): Promise<IUserKYC> {
    try {
      const kyc = await this.getUserKYC(userId);

      // Update KYC status
      kyc.status = KYCStatus.REJECTED;
      kyc.overallRejectionReason = rejectionReason;
      kyc.updatedAt = new Date();

      if (notes) {
        kyc.adminNotes = notes;
      }

      await kyc.save();

      // Update user's kycStatus to rejected
      await User.findByIdAndUpdate(userId, {
        kycStatus: 'rejected',
        updatedAt: new Date()
      });

      console.log('✅ KYC rejected and user status updated for user:', userId);

      // Log audit
      await this.logAudit(userId, AuditAction.UPDATE, AuditEntity.USER_KYC, (kyc._id as Types.ObjectId).toString(), {
        action: 'kyc_rejected',
        rejectionReason,
        notes
      });

      return kyc;
    } catch (error) {
      throw this.handleError(error, 'rejectKYC');
    }
  }

  /**
   * Verify KYC document
   */
  async verifyDocument(userId: string, documentIndex: number, status: KYCStatus, rejectionReason?: string): Promise<IUserKYC> {
    try {
      const kyc = await this.getUserKYC(userId);

      // Find and update the document by index
      if (documentIndex < 0 || documentIndex >= kyc.documents.length) {
        throw new Error('Document not found');
      }

      const document = kyc.documents[documentIndex];
      document.status = status;
      if (rejectionReason) {
        document.rejectionReason = rejectionReason;
      }
      if (status === KYCStatus.APPROVED) {
        document.verifiedAt = new Date();
      }

      await kyc.save();

      // Log audit
      await this.logAudit(userId, AuditAction.UPDATE, AuditEntity.USER_KYC, (kyc._id as Types.ObjectId).toString(), {
        action: 'document_verification',
        documentIndex,
        status,
        rejectionReason
      });

      return kyc;
    } catch (error) {
      throw this.handleError(error, 'verifyDocument');
    }
  }

  /**
   * Get KYC statistics
   */
  async getKYCStats(): Promise<any> {
    try {
      const stats = await UserKYC.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const totalUsers = await UserKYC.countDocuments();
      const pendingCount = stats.find(s => s._id === KYCStatus.PENDING)?.count || 0;
      const approvedCount = stats.find(s => s._id === KYCStatus.APPROVED)?.count || 0;
      const rejectedCount = stats.find(s => s._id === KYCStatus.REJECTED)?.count || 0;

      return {
        total: totalUsers,
        pending: pendingCount,
        approved: approvedCount,
        rejected: rejectedCount,
        underReview: stats.find(s => s._id === KYCStatus.UNDER_REVIEW)?.count || 0
      };
    } catch (error) {
      throw this.handleError(error, 'getKYCStats');
    }
  }

  /**
   * Log audit trail
   */
  private async logAudit(userId: string, action: AuditAction, entity: AuditEntity, entityId: string, details: any) {
    try {
      await AuditLog.create({
        userId: new Types.ObjectId(userId),
        action,
        entity,
        entityId: new Types.ObjectId(entityId),
        changes: details
      });
    } catch (error) {
      console.error('Failed to log audit:', error);
    }
  }
}

export const kycService = new KYCService();
