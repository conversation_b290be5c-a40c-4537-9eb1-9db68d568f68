{"name": "user", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 1731", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.12", "@reduxjs/toolkit": "^2.0.1", "@stripe/react-stripe-js": "^3.8.0", "@stripe/stripe-js": "^7.6.1", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.303.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "react-redux": "^9.0.4", "sonner": "^1.3.1", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}