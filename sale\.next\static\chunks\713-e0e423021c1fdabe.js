"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[713],{2713:(e,t,n)=>{n.d(t,{xP:()=>S});var r=n(6597),i=n(9559),a=n(3030),u=n(7895),s=n(9585);function o(e){return e.replace(e[0],e[0].toUpperCase())}function l(e){return"infinitequery"===e.type}function c(e,...t){return Object.assign(e,...t)}n(5733);var d=Symbol();function f(e,t,n,r){let i=(0,s.useMemo)(()=>({queryArgs:e,serialized:"object"==typeof e?t({queryArgs:e,endpointDefinition:n,endpointName:r}):e}),[e,t,n,r]),a=(0,s.useRef)(i);return(0,s.useEffect)(()=>{a.current.serialized!==i.serialized&&(a.current=i)},[i]),a.current.serialized===i.serialized?a.current.queryArgs:e}function p(e){let t=(0,s.useRef)(e);return(0,s.useEffect)(()=>{(0,i.bN)(t.current,e)||(t.current=e)},[e]),(0,i.bN)(t.current,e)?t.current:e}var y="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,m="undefined"!=typeof navigator&&"ReactNative"===navigator.product,g=y||m?s.useLayoutEffect:s.useEffect,h=e=>e.isUninitialized?{...e,isUninitialized:!1,isFetching:!0,isLoading:void 0===e.data,status:r.RE.pending}:e;function v(e,...t){let n={};return t.forEach(t=>{n[t]=e[t]}),n}var b=["data","status","isLoading","isSuccess","isError","error"],w=Symbol(),S=(0,r.l0)((0,r.m7)(),(({batch:e=i.vA,hooks:t={useDispatch:i.wA,useSelector:i.d4,useStore:i.Pj},createSelector:n=a.Mz,unstable__sideEffectsInRender:y=!1,...m}={})=>({name:w,init(a,{serializeQueryArgs:m},w){let{buildQueryHooks:S,buildInfiniteQueryHooks:A,buildMutationHook:R,usePrefetch:q}=function({api:e,moduleOptions:{batch:t,hooks:{useDispatch:n,useSelector:a,useStore:o},unstable__sideEffectsInRender:c,createSelector:y},serializeQueryArgs:m,context:w}){let S=c?e=>e():s.useEffect;return{buildQueryHooks:function(i){let a=(e,t={})=>{let[n]=q(i,e,t);return O(n),(0,s.useMemo)(()=>({refetch:()=>E(n)}),[n])},u=({refetchOnReconnect:r,refetchOnFocus:a,pollingInterval:u=0,skipPollingIfUnfocused:o=!1}={})=>{let{initiate:l}=e.endpoints[i],c=n(),[f,y]=(0,s.useState)(d),m=(0,s.useRef)(void 0),g=p({refetchOnReconnect:r,refetchOnFocus:a,pollingInterval:u,skipPollingIfUnfocused:o});S(()=>{g!==m.current?.subscriptionOptions&&m.current?.updateSubscriptionOptions(g)},[g]);let h=(0,s.useRef)(g);S(()=>{h.current=g},[g]);let v=(0,s.useCallback)(function(e,n=!1){let r;return t(()=>{m.current?.unsubscribe(),m.current=r=c(l(e,{subscriptionOptions:h.current,forceRefetch:!n})),y(e)}),r},[c,l]),b=(0,s.useCallback)(()=>{m.current?.queryCacheKey&&c(e.internalActions.removeQueryResult({queryCacheKey:m.current?.queryCacheKey}))},[c]);return(0,s.useEffect)(()=>()=>{m?.current?.unsubscribe()},[]),(0,s.useEffect)(()=>{f===d||m.current||v(f,!0)},[f,v]),(0,s.useMemo)(()=>[v,f,{reset:b}],[v,f,b])},o=P(i,A);return{useQueryState:o,useQuerySubscription:a,useLazyQuerySubscription:u,useLazyQuery(e){let[t,n,{reset:r}]=u(e),i=o(n,{...e,skip:n===d}),a=(0,s.useMemo)(()=>({lastArg:n}),[n]);return(0,s.useMemo)(()=>[t,{...i,reset:r},a],[t,i,r,a])},useQuery(e,t){let n=a(e,t),i=o(e,{selectFromResult:e===r.hT||t?.skip?void 0:h,...t}),u=v(i,...b);return(0,s.useDebugValue)(u),(0,s.useMemo)(()=>({...i,...n}),[i,n])}}},buildInfiniteQueryHooks:function(e){let n=(n,i={})=>{let[a,u,o,l]=q(e,n,i),c=(0,s.useRef)(l);S(()=>{c.current=l},[l]);let d=(0,s.useCallback)(function(e,n){let r;return t(()=>{a.current?.unsubscribe(),a.current=r=u(o(e,{subscriptionOptions:c.current,direction:n}))}),r},[a,u,o]);O(a);let p=f(i.skip?r.hT:n,r.lE,w.endpointDefinitions[e],e),y=(0,s.useCallback)(()=>E(a),[a]);return(0,s.useMemo)(()=>({trigger:d,refetch:y,fetchNextPage:()=>d(p,"forward"),fetchPreviousPage:()=>d(p,"backward")}),[y,d,p])},i=P(e,R);return{useInfiniteQueryState:i,useInfiniteQuerySubscription:n,useInfiniteQuery(e,t){let{refetch:a,fetchNextPage:u,fetchPreviousPage:o}=n(e,t),l=i(e,{selectFromResult:e===r.hT||t?.skip?void 0:h,...t}),c=v(l,...b,"hasNextPage","hasPreviousPage");return(0,s.useDebugValue)(c),(0,s.useMemo)(()=>({...l,fetchNextPage:u,fetchPreviousPage:o,refetch:a}),[l,u,o,a])}}},buildMutationHook:function(r){return({selectFromResult:u,fixedCacheKey:o}={})=>{let{select:l,initiate:c}=e.endpoints[r],d=n(),[f,p]=(0,s.useState)();(0,s.useEffect)(()=>()=>{f?.arg.fixedCacheKey||f?.reset()},[f]);let m=(0,s.useCallback)(function(e){let t=d(c(e,{fixedCacheKey:o}));return p(t),t},[d,c,o]),{requestId:g}=f||{},h=(0,s.useMemo)(()=>l({fixedCacheKey:o,requestId:f?.requestId}),[o,f,l]),w=a((0,s.useMemo)(()=>u?y([h],u):h,[u,h]),i.bN),S=null==o?f?.arg.originalArgs:void 0,A=(0,s.useCallback)(()=>{t(()=>{f&&p(void 0),o&&d(e.internalActions.removeMutationResult({requestId:g,fixedCacheKey:o}))})},[d,o,f,g]),R=v(w,...b,"endpointName");(0,s.useDebugValue)(R);let q=(0,s.useMemo)(()=>({...w,originalArgs:S,reset:A}),[w,S,A]);return(0,s.useMemo)(()=>[m,q],[m,q])}},usePrefetch:function(t,r){let i=n(),a=p(r);return(0,s.useCallback)((n,r)=>i(e.util.prefetch(t,n,{...a,...r})),[t,i,a])}};function A(e,t,n){if(t?.endpointName&&e.isUninitialized){let{endpointName:e}=t,i=w.endpointDefinitions[e];n!==r.hT&&m({queryArgs:t.originalArgs,endpointDefinition:i,endpointName:e})===m({queryArgs:n,endpointDefinition:i,endpointName:e})&&(t=void 0)}let i=e.isSuccess?e.data:t?.data;void 0===i&&(i=e.data);let a=void 0!==i,u=e.isLoading,s=(!t||t.isLoading||t.isUninitialized)&&!a&&u,o=e.isSuccess||a&&(u&&!t?.isError||e.isUninitialized);return{...e,data:i,currentData:e.data,isFetching:u,isLoading:s,isSuccess:o}}function R(e,t,n){if(t?.endpointName&&e.isUninitialized){let{endpointName:e}=t,i=w.endpointDefinitions[e];n!==r.hT&&m({queryArgs:t.originalArgs,endpointDefinition:i,endpointName:e})===m({queryArgs:n,endpointDefinition:i,endpointName:e})&&(t=void 0)}let i=e.isSuccess?e.data:t?.data;void 0===i&&(i=e.data);let a=void 0!==i,u=e.isLoading,s=(!t||t.isLoading||t.isUninitialized)&&!a&&u,o=e.isSuccess||u&&a;return{...e,data:i,currentData:e.data,isFetching:u,isLoading:s,isSuccess:o}}function q(t,i,{refetchOnReconnect:a,refetchOnFocus:u,refetchOnMountOrArgChange:o,skip:c=!1,pollingInterval:d=0,skipPollingIfUnfocused:y=!1,...m}={}){let{initiate:g}=e.endpoints[t],h=n(),v=(0,s.useRef)(void 0);v.current||(v.current=h(e.internalActions.internal_getRTKQSubscriptions()));let b=f(c?r.hT:i,r.lE,w.endpointDefinitions[t],t),A=p({refetchOnReconnect:a,refetchOnFocus:u,pollingInterval:d,skipPollingIfUnfocused:y}),R=p(m.initialPageParam),P=(0,s.useRef)(void 0),{queryCacheKey:O,requestId:E}=P.current||{},j=!1;O&&E&&(j=v.current.isRequestSubscribed(O,E));let T=!j&&void 0!==P.current;return S(()=>{T&&(P.current=void 0)},[T]),S(()=>{let e=P.current;if(b===r.hT){e?.unsubscribe(),P.current=void 0;return}let n=P.current?.subscriptionOptions;e&&e.arg===b?A!==n&&e.updateSubscriptionOptions(A):(e?.unsubscribe(),P.current=h(g(b,{subscriptionOptions:A,forceRefetch:o,...l(w.endpointDefinitions[t])?{initialPageParam:R}:{}})))},[h,g,o,b,A,T,R,t]),[P,h,g,A]}function P(t,n){return(u,{skip:l=!1,selectFromResult:c}={})=>{let{select:d}=e.endpoints[t],p=f(l?r.hT:u,m,w.endpointDefinitions[t],t),h=(0,s.useRef)(void 0),v=(0,s.useMemo)(()=>y([d(p),(e,t)=>t,e=>p],n,{memoizeOptions:{resultEqualityCheck:i.bN}}),[d,p]),b=(0,s.useMemo)(()=>c?y([v],c,{devModeChecks:{identityFunctionCheck:"never"}}):v,[v,c]),S=a(e=>b(e,h.current),i.bN),A=v(o().getState(),h.current);return g(()=>{h.current=A},[A]),S}}function O(e){(0,s.useEffect)(()=>()=>{e.current?.unsubscribe?.(),e.current=void 0},[e])}function E(e){if(!e.current)throw Error((0,u.gk)(38));return e.current.refetch()}}({api:a,moduleOptions:{batch:e,hooks:t,unstable__sideEffectsInRender:y,createSelector:n},serializeQueryArgs:m,context:w});return c(a,{usePrefetch:q}),c(w,{batch:e}),{injectEndpoint(e,t){if("query"===t.type){let{useQuery:t,useLazyQuery:n,useLazyQuerySubscription:r,useQueryState:i,useQuerySubscription:u}=S(e);c(a.endpoints[e],{useQuery:t,useLazyQuery:n,useLazyQuerySubscription:r,useQueryState:i,useQuerySubscription:u}),a[`use${o(e)}Query`]=t,a[`useLazy${o(e)}Query`]=n}if("mutation"===t.type){let t=R(e);c(a.endpoints[e],{useMutation:t}),a[`use${o(e)}Mutation`]=t}else if(l(t)){let{useInfiniteQuery:t,useInfiniteQuerySubscription:n,useInfiniteQueryState:r}=A(e);c(a.endpoints[e],{useInfiniteQuery:t,useInfiniteQuerySubscription:n,useInfiniteQueryState:r}),a[`use${o(e)}InfiniteQuery`]=t}}}}}))())},3030:(e,t,n)=>{n.d(t,{Mz:()=>S,X4:()=>w});var r=e=>Array.isArray(e)?e:[e],i=0,a=null,u=class{revision=i;_value;_lastValue;_isEqual=s;constructor(e,t=s){this._value=this._lastValue=e,this._isEqual=t}get value(){return a?.add(this),this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++i)}};function s(e,t){return e===t}function o(e){return e instanceof u||console.warn("Not a valid cell! ",e),e.value}var l=(e,t)=>!1;function c(){return function(e,t=s){return new u(null,t)}(0,l)}var d=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=c()),o(t)};Symbol();var f=0,p=Object.getPrototypeOf({}),y=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,m);tag=c();tags={};children={};collectionTag=null;id=f++},m={get:(e,t)=>(function(){let{value:n}=e,r=Reflect.get(n,t);if("symbol"==typeof t||t in p)return r;if("object"==typeof r&&null!==r){let n=e.children[t];return void 0===n&&(n=e.children[t]=function(e){return Array.isArray(e)?new g(e):new y(e)}(r)),n.tag&&o(n.tag),n.proxy}{let n=e.tags[t];return void 0===n&&((n=e.tags[t]=c()).value=r),o(n),r}})(),ownKeys:e=>(d(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},g=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],h);tag=c();tags={};children={};collectionTag=null;id=f++},h={get:([e],t)=>("length"===t&&d(e),m.get(e,t)),ownKeys:([e])=>m.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>m.getOwnPropertyDescriptor(e,t),has:([e],t)=>m.has(e,t)},v="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function b(){return{s:0,v:void 0,o:null,p:null}}function w(e,t={}){let n,r=b(),{resultEqualityCheck:i}=t,a=0;function u(){let t,u=r,{length:s}=arguments;for(let e=0;e<s;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=u.o;null===e&&(u.o=e=new WeakMap);let n=e.get(t);void 0===n?(u=b(),e.set(t,u)):u=n}else{let e=u.p;null===e&&(u.p=e=new Map);let n=e.get(t);void 0===n?(u=b(),e.set(t,u)):u=n}}let o=u;if(1===u.s)t=u.v;else if(t=e.apply(null,arguments),a++,i){let e=n?.deref?.()??n;null!=e&&i(e,t)&&(t=e,0!==a&&a--),n="object"==typeof t&&null!==t||"function"==typeof t?new v(t):t}return o.s=1,o.v=t,t}return u.clearCache=()=>{r=b(),u.resetResultsCount()},u.resultsCount=()=>a,u.resetResultsCount=()=>{a=0},u}var S=function(e,...t){let n="function"==typeof e?{memoize:e,memoizeOptions:t}:e,i=(...e)=>{let t,i=0,a=0,u={},s=e.pop();"object"==typeof s&&(u=s,s=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(s,`createSelector expects an output function after the inputs, but received: [${typeof s}]`);let{memoize:o,memoizeOptions:l=[],argsMemoize:c=w,argsMemoizeOptions:d=[],devModeChecks:f={}}={...n,...u},p=r(l),y=r(d),m=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let n=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${n}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),g=o(function(){return i++,s.apply(null,arguments)},...p);return Object.assign(c(function(){a++;let e=function(e,t){let n=[],{length:r}=e;for(let i=0;i<r;i++)n.push(e[i].apply(null,t));return n}(m,arguments);return t=g.apply(null,e)},...y),{resultFunc:s,memoizedResultFunc:g,dependencies:m,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>t,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:o,argsMemoize:c})};return Object.assign(i,{withTypes:()=>i}),i}(w),A=Object.assign((e,t=S)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let n=Object.keys(e);return t(n.map(t=>e[t]),(...e)=>e.reduce((e,t,r)=>(e[n[r]]=t,e),{}))},{withTypes:()=>A})},6597:(e,t,n)=>{n.d(t,{RE:()=>o,l0:()=>ee,m7:()=>ef,lE:()=>G,cw:()=>w,$k:()=>T,hT:()=>H});var r=n(5340),i=n(7895),a=n(4605),u=n(3030),s=class extends Error{issues;constructor(e){super(e[0].message),this.name="SchemaError",this.issues=e}},o=(n(5733),(e=>(e.uninitialized="uninitialized",e.pending="pending",e.fulfilled="fulfilled",e.rejected="rejected",e))(o||{}));function l(e){return{status:e,isUninitialized:"uninitialized"===e,isLoading:"pending"===e,isSuccess:"fulfilled"===e,isError:"rejected"===e}}var c=r.Qd;function d(e){let t=0;for(let n in e)t++;return t}var f=e=>[].concat(...e);function p(e){return null!=e}var y=e=>e.replace(/\/$/,""),m=e=>e.replace(/^\//,""),g=(...e)=>fetch(...e),h=e=>e.status>=200&&e.status<=299,v=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function b(e){if(!(0,r.Qd)(e))return e;let t={...e};for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return t}function w({baseUrl:e,prepareHeaders:t=e=>e,fetchFn:n=g,paramsSerializer:i,isJsonContentType:a=v,jsonContentType:u="application/json",jsonReplacer:s,timeout:o,responseHandler:l,validateStatus:c,...d}={}){return"undefined"==typeof fetch&&n===g&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(p,g,v)=>{let w,S,{getState:A,extra:R,endpoint:q,forced:P,type:O}=g,{url:E,headers:j=new Headers(d.headers),params:T,responseHandler:C=l??"json",validateStatus:k=c??h,timeout:N=o,...M}="string"==typeof p?{url:p}:p,Q,D=g.signal;N&&(Q=new AbortController,g.signal.addEventListener("abort",Q.abort),D=Q.signal);let I={...d,signal:D,...M};j=new Headers(b(j)),I.headers=await t(j,{getState:A,arg:p,extra:R,endpoint:q,forced:P,type:O,extraOptions:v})||j;let x=e=>"object"==typeof e&&((0,r.Qd)(e)||Array.isArray(e)||"function"==typeof e.toJSON);if(!I.headers.has("content-type")&&x(I.body)&&I.headers.set("content-type",u),x(I.body)&&a(I.headers)&&(I.body=JSON.stringify(I.body,s)),T){let e=~E.indexOf("?")?"&":"?";E+=e+(i?i(T):new URLSearchParams(b(T)))}let _=new Request(E=function(e,t){var n;if(!e)return t;if(!t)return e;if(n=t,RegExp("(^|:)//").test(n))return t;let r=e.endsWith("/")||!t.startsWith("?")?"/":"";return e=y(e),t=m(t),`${e}${r}${t}`}(e,E),I);w={request:new Request(E,I)};let $,z=!1,K=Q&&setTimeout(()=>{z=!0,Q.abort()},N);try{$=await n(_)}catch(e){return{error:{status:z?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(e)},meta:w}}finally{K&&clearTimeout(K),Q?.signal.removeEventListener("abort",Q.abort)}let U=$.clone();w.response=U;let F="";try{let e;if(await Promise.all([f($,C).then(e=>S=e,t=>e=t),U.text().then(e=>F=e,()=>{})]),e)throw e}catch(e){return{error:{status:"PARSING_ERROR",originalStatus:$.status,data:F,error:String(e)},meta:w}}return k($,S)?{data:S,meta:w}:{error:{status:$.status,data:S},meta:w}};async function f(e,t){if("function"==typeof t)return t(e);if("content-type"===t&&(t=a(e.headers)?"json":"text"),"json"===t){let t=await e.text();return t.length?JSON.parse(t):null}return e.text()}}var S=class{constructor(e,t){this.value=e,this.meta=t}};async function A(e=0,t=5){let n=~~((Math.random()+.4)*(300<<Math.min(e,t)));await new Promise(e=>setTimeout(t=>e(t),n))}var R={},q=(0,i.VP)("__rtkq/focused"),P=(0,i.VP)("__rtkq/unfocused"),O=(0,i.VP)("__rtkq/online"),E=(0,i.VP)("__rtkq/offline"),j=!1;function T(e,t){return t?t(e,{onFocus:q,onFocusLost:P,onOffline:E,onOnline:O}):function(){let t=()=>e(q()),n=()=>e(P()),r=()=>e(O()),i=()=>e(E()),a=()=>{"visible"===window.document.visibilityState?t():n()};return!j&&"undefined"!=typeof window&&window.addEventListener&&(window.addEventListener("visibilitychange",a,!1),window.addEventListener("focus",t,!1),window.addEventListener("online",r,!1),window.addEventListener("offline",i,!1),j=!0),()=>{window.removeEventListener("focus",t),window.removeEventListener("visibilitychange",a),window.removeEventListener("online",r),window.removeEventListener("offline",i),j=!1}}()}function C(e){return"query"===e.type}function k(e){return"infinitequery"===e.type}function N(e){return C(e)||k(e)}function M(e,t,n,r,i,a){return"function"==typeof e?e(t,n,r,i).filter(p).map(Q).map(a):Array.isArray(e)?e.map(Q).map(a):[]}function Q(e){return"string"==typeof e?{type:e}:e}var D=Symbol("forceQueryFn"),I=e=>"function"==typeof e[D],x=class extends s{constructor(e,t,n,r){super(e),this.value=t,this.schemaName=n,this._bqMeta=r}};async function _(e,t,n,r){let i=await e["~standard"].validate(t);if(i.issues)throw new x(i.issues,t,n,r);return i.value}function $(e){return e}var z=(e={})=>({...e,[i.cN]:!0});function K(e,{pages:t,pageParams:n},r){let i=t.length-1;return e.getNextPageParam(t[i],t,n[i],n,r)}function U(e,{pages:t,pageParams:n},r){return e.getPreviousPageParam?.(t[0],t,n[0],n,r)}function F(e,t,n,r){return M(n[e.meta.arg.endpointName][t],(0,i.sf)(e)?e.payload:void 0,(0,i.WA)(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,r)}function L(e,t,n){let r=e[t];r&&n(r)}function W(e){return("arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)??e.requestId}function V(e,t,n){let r=e[W(t)];r&&n(r)}var B={},H=Symbol.for("RTKQ/skipToken"),J={status:"uninitialized"},Z=(0,a.jM)(J,()=>{}),X=(0,a.jM)(J,()=>{}),Y=WeakMap?new WeakMap:void 0,G=({endpointName:e,queryArgs:t})=>{let n="",i=Y?.get(t);if("string"==typeof i)n=i;else{let e=JSON.stringify(t,(e,t)=>(t="bigint"==typeof t?{$bigint:t.toString()}:t,t=(0,r.Qd)(t)?Object.keys(t).sort().reduce((e,n)=>(e[n]=t[n],e),{}):t));(0,r.Qd)(t)&&Y?.set(t,e),n=e}return`${e}(${n})`};function ee(...e){return function(t){let n=(0,u.X4)(e=>t.extractRehydrationInfo?.(e,{reducerPath:t.reducerPath??"api"})),r={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...t,extractRehydrationInfo:n,serializeQueryArgs(e){let n=G;if("serializeQueryArgs"in e.endpointDefinition){let t=e.endpointDefinition.serializeQueryArgs;n=e=>{let n=t(e);return"string"==typeof n?n:G({...e,queryArgs:n})}}else t.serializeQueryArgs&&(n=t.serializeQueryArgs);return n(e)},tagTypes:[...t.tagTypes||[]]},a={endpointDefinitions:{},batch(e){e()},apiUid:(0,i.Ak)(),extractRehydrationInfo:n,hasRehydrationInfo:(0,u.X4)(e=>null!=n(e))},s={injectEndpoints:function(e){for(let[t,n]of Object.entries(e.endpoints({query:e=>({...e,type:"query"}),mutation:e=>({...e,type:"mutation"}),infiniteQuery:e=>({...e,type:"infinitequery"})}))){if(!0!==e.overrideExisting&&t in a.endpointDefinitions){if("throw"===e.overrideExisting)throw Error((0,i.gk)(39));continue}for(let e of(a.endpointDefinitions[t]=n,o))e.injectEndpoint(t,n)}return s},enhanceEndpoints({addTagTypes:e,endpoints:t}){if(e)for(let t of e)r.tagTypes.includes(t)||r.tagTypes.push(t);if(t)for(let[e,n]of Object.entries(t))"function"==typeof n?n(a.endpointDefinitions[e]):Object.assign(a.endpointDefinitions[e]||{},n);return s}},o=e.map(e=>e.init(s,r,a));return s.injectEndpoints({endpoints:t.endpoints})}}function et(e,...t){return Object.assign(e,...t)}var en=({api:e,queryThunk:t,internalState:n})=>{let r=`${e.reducerPath}/subscriptions`,i=null,u=null,{updateSubscriptionOptions:s,unsubscribeQueryResult:o}=e.internalActions,l=(n,r)=>{if(s.match(r)){let{queryCacheKey:e,requestId:t,options:i}=r.payload;return n?.[e]?.[t]&&(n[e][t]=i),!0}if(o.match(r)){let{queryCacheKey:e,requestId:t}=r.payload;return n[e]&&delete n[e][t],!0}if(e.internalActions.removeQueryResult.match(r))return delete n[r.payload.queryCacheKey],!0;if(t.pending.match(r)){let{meta:{arg:e,requestId:t}}=r,i=n[e.queryCacheKey]??={};return i[`${t}_running`]={},e.subscribe&&(i[t]=e.subscriptionOptions??i[t]??{}),!0}let i=!1;if(t.fulfilled.match(r)||t.rejected.match(r)){let e=n[r.meta.arg.queryCacheKey]||{},t=`${r.meta.requestId}_running`;i||=!!e[t],delete e[t]}if(t.rejected.match(r)){let{meta:{condition:e,arg:t,requestId:a}}=r;if(e&&t.subscribe){let e=n[t.queryCacheKey]??={};e[a]=t.subscriptionOptions??e[a]??{},i=!0}}return i},c=()=>n.currentSubscriptions,f={getSubscriptions:c,getSubscriptionCount:e=>d(c()[e]??{}),isRequestSubscribed:(e,t)=>{let n=c();return!!n?.[e]?.[t]}};return(s,o)=>{if(i||(i=JSON.parse(JSON.stringify(n.currentSubscriptions))),e.util.resetApiState.match(s))return i=n.currentSubscriptions={},u=null,[!0,!1];if(e.internalActions.internal_getRTKQSubscriptions.match(s))return[!1,f];let c=l(n.currentSubscriptions,s),d=!0;if(c){u||(u=setTimeout(()=>{let t=JSON.parse(JSON.stringify(n.currentSubscriptions)),[,r]=(0,a.vI)(i,()=>t);o.next(e.internalActions.subscriptionsUpdated(r)),i=t,u=null},500));let l="string"==typeof s.type&&!!s.type.startsWith(r),c=t.rejected.match(s)&&s.meta.condition&&!!s.meta.arg.subscribe;d=!l&&!c}return[d,!1]}},er=({reducerPath:e,api:t,queryThunk:n,context:r,internalState:a,selectors:{selectQueryEntry:u,selectConfig:s}})=>{let{removeQueryResult:o,unsubscribeQueryResult:l,cacheEntriesUpserted:c}=t.internalActions,d=(0,i.i0)(l.match,n.fulfilled,n.rejected,c.match);function f(e){let t=a.currentSubscriptions[e];return!!t&&!function(e){for(let t in e)return!1;return!0}(t)}let p={};function y(e,t,n){let i=t.getState();for(let a of e){let e=u(i,a);!function(e,t,n,i){let a=r.endpointDefinitions[t],u=a?.keepUnusedDataFor??i.keepUnusedDataFor;if(u===1/0)return;let s=Math.max(0,Math.min(u,2147482.647));if(!f(e)){let t=p[e];t&&clearTimeout(t),p[e]=setTimeout(()=>{f(e)||n.dispatch(o({queryCacheKey:e})),delete p[e]},1e3*s)}}(a,e?.endpointName,t,n)}}return(e,n,i)=>{let a=s(n.getState());if(d(e)){let t;if(c.match(e))t=e.payload.map(e=>e.queryDescription.queryCacheKey);else{let{queryCacheKey:n}=l.match(e)?e.payload:e.meta.arg;t=[n]}y(t,n,a)}if(t.util.resetApiState.match(e))for(let[e,t]of Object.entries(p))t&&clearTimeout(t),delete p[e];if(r.hasRehydrationInfo(e)){let{queries:t}=r.extractRehydrationInfo(e);y(Object.keys(t),n,a)}}},ei=Error("Promise never resolved before cacheEntryRemoved."),ea=({api:e,reducerPath:t,context:n,queryThunk:r,mutationThunk:a,internalState:u,selectors:{selectQueryEntry:s,selectApiState:o}})=>{let l=(0,i.$S)(r),c=(0,i.$S)(a),d=(0,i.sf)(r,a),f={};function p(e,t,n){let r=f[e];r?.valueResolved&&(r.valueResolved({data:t,meta:n}),delete r.valueResolved)}function y(e){let t=f[e];t&&(delete f[e],t.cacheEntryRemoved())}function m(t,r,i,a,u){let s=n.endpointDefinitions[t],o=s?.onCacheEntryAdded;if(!o)return;let l={},c=new Promise(e=>{l.cacheEntryRemoved=e}),d=Promise.race([new Promise(e=>{l.valueResolved=e}),c.then(()=>{throw ei})]);d.catch(()=>{}),f[i]=l;let p=e.endpoints[t].select(N(s)?r:i),y=a.dispatch((e,t,n)=>n),m={...a,getCacheEntry:()=>p(a.getState()),requestId:u,extra:y,updateCachedData:N(s)?n=>a.dispatch(e.util.updateQueryData(t,r,n)):void 0,cacheDataLoaded:d,cacheEntryRemoved:c};Promise.resolve(o(r,m)).catch(e=>{if(e!==ei)throw e})}return(n,i,u)=>{let o=function(t){return l(t)?t.meta.arg.queryCacheKey:c(t)?t.meta.arg.fixedCacheKey??t.meta.requestId:e.internalActions.removeQueryResult.match(t)?t.payload.queryCacheKey:e.internalActions.removeMutationResult.match(t)?W(t.payload):""}(n);function g(e,t,n,r){let a=s(u,t),o=s(i.getState(),t);!a&&o&&m(e,r,t,i,n)}if(r.pending.match(n))g(n.meta.arg.endpointName,o,n.meta.requestId,n.meta.arg.originalArgs);else if(e.internalActions.cacheEntriesUpserted.match(n))for(let{queryDescription:e,value:t}of n.payload){let{endpointName:r,originalArgs:i,queryCacheKey:a}=e;g(r,a,n.meta.requestId,i),p(a,t,{})}else if(a.pending.match(n))i.getState()[t].mutations[o]&&m(n.meta.arg.endpointName,n.meta.arg.originalArgs,o,i,n.meta.requestId);else if(d(n))p(o,n.payload,n.meta.baseQueryMeta);else if(e.internalActions.removeQueryResult.match(n)||e.internalActions.removeMutationResult.match(n))y(o);else if(e.util.resetApiState.match(n))for(let e of Object.keys(f))y(e)}},eu=({api:e,context:{apiUid:t},reducerPath:n})=>(n,r)=>{e.util.resetApiState.match(n)&&r.dispatch(e.internalActions.middlewareRegistered(t))},es=({reducerPath:e,context:t,context:{endpointDefinitions:n},mutationThunk:r,queryThunk:a,api:u,assertTagType:s,refetchQuery:o,internalState:l})=>{let{removeQueryResult:c}=u.internalActions,f=(0,i.i0)((0,i.sf)(r),(0,i.WA)(r)),p=(0,i.i0)((0,i.sf)(r,a),(0,i.TK)(r,a)),y=[];function m(n,r){let i=r.getState(),a=i[e];if(y.push(...n),"delayed"===a.config.invalidationBehavior&&function(e){let{queries:t,mutations:n}=e;for(let e of[t,n])for(let t in e)if(e[t]?.status==="pending")return!0;return!1}(a))return;let s=y;if(y=[],0===s.length)return;let f=u.util.selectInvalidatedBy(i,s);t.batch(()=>{for(let{queryCacheKey:e}of Array.from(f.values())){let t=a.queries[e],n=l.currentSubscriptions[e]??{};t&&(0===d(n)?r.dispatch(c({queryCacheKey:e})):"uninitialized"!==t.status&&r.dispatch(o(t)))}})}return(e,t)=>{f(e)?m(F(e,"invalidatesTags",n,s),t):p(e)?m([],t):u.util.invalidateTags.match(e)&&m(M(e.payload,void 0,void 0,void 0,void 0,s),t)}},eo=({reducerPath:e,queryThunk:t,api:n,refetchQuery:r,internalState:i})=>{let a={};function u({queryCacheKey:t},n){let s=n.getState()[e],o=s.queries[t],c=i.currentSubscriptions[t];if(!o||"uninitialized"===o.status)return;let{lowestPollingInterval:d,skipPollingIfUnfocused:f}=l(c);if(!Number.isFinite(d))return;let p=a[t];p?.timeout&&(clearTimeout(p.timeout),p.timeout=void 0);let y=Date.now()+d;a[t]={nextPollTimestamp:y,pollingInterval:d,timeout:setTimeout(()=>{(s.config.focused||!f)&&n.dispatch(r(o)),u({queryCacheKey:t},n)},d)}}function s({queryCacheKey:t},n){let r=n.getState()[e].queries[t],s=i.currentSubscriptions[t];if(!r||"uninitialized"===r.status)return;let{lowestPollingInterval:c}=l(s);if(!Number.isFinite(c))return void o(t);let d=a[t],f=Date.now()+c;(!d||f<d.nextPollTimestamp)&&u({queryCacheKey:t},n)}function o(e){let t=a[e];t?.timeout&&clearTimeout(t.timeout),delete a[e]}function l(e={}){let t=!1,n=Number.POSITIVE_INFINITY;for(let r in e)e[r].pollingInterval&&(n=Math.min(e[r].pollingInterval,n),t=e[r].skipPollingIfUnfocused||t);return{lowestPollingInterval:n,skipPollingIfUnfocused:t}}return(e,r)=>{(n.internalActions.updateSubscriptionOptions.match(e)||n.internalActions.unsubscribeQueryResult.match(e))&&s(e.payload,r),(t.pending.match(e)||t.rejected.match(e)&&e.meta.condition)&&s(e.meta.arg,r),(t.fulfilled.match(e)||t.rejected.match(e)&&!e.meta.condition)&&u(e.meta.arg,r),n.util.resetApiState.match(e)&&function(){for(let e of Object.keys(a))o(e)}()}},el=({api:e,context:t,queryThunk:n,mutationThunk:r})=>{let a=(0,i.mm)(n,r),u=(0,i.TK)(n,r),s=(0,i.sf)(n,r),o={};return(n,r)=>{if(a(n)){let{requestId:i,arg:{endpointName:a,originalArgs:u}}=n.meta,s=t.endpointDefinitions[a],l=s?.onQueryStarted;if(l){let t={},n=new Promise((e,n)=>{t.resolve=e,t.reject=n});n.catch(()=>{}),o[i]=t;let c=e.endpoints[a].select(N(s)?u:i),d=r.dispatch((e,t,n)=>n),f={...r,getCacheEntry:()=>c(r.getState()),requestId:i,extra:d,updateCachedData:N(s)?t=>r.dispatch(e.util.updateQueryData(a,u,t)):void 0,queryFulfilled:n};l(u,f)}}else if(s(n)){let{requestId:e,baseQueryMeta:t}=n.meta;o[e]?.resolve({data:n.payload,meta:t}),delete o[e]}else if(u(n)){let{requestId:e,rejectedWithValue:t,baseQueryMeta:r}=n.meta;o[e]?.reject({error:n.payload??n.error,isUnhandledError:!t,meta:r}),delete o[e]}}},ec=({reducerPath:e,context:t,api:n,refetchQuery:r,internalState:i})=>{let{removeQueryResult:a}=n.internalActions;function u(n,u){let s=n.getState()[e],o=s.queries,l=i.currentSubscriptions;t.batch(()=>{for(let e of Object.keys(l)){let t=o[e],i=l[e];i&&t&&(Object.values(i).some(e=>!0===e[u])||Object.values(i).every(e=>void 0===e[u])&&s.config[u])&&(0===d(i)?n.dispatch(a({queryCacheKey:e})):"uninitialized"!==t.status&&n.dispatch(r(t)))}})}return(e,t)=>{q.match(e)&&u(t,"refetchOnFocus"),O.match(e)&&u(t,"refetchOnReconnect")}},ed=Symbol(),ef=({createSelector:e=u.Mz}={})=>({name:ed,init(t,{baseQuery:n,tagTypes:u,reducerPath:s,serializeQueryArgs:o,keepUnusedDataFor:y,refetchOnMountOrArgChange:m,refetchOnFocus:g,refetchOnReconnect:h,invalidationBehavior:v,onSchemaFailure:b,catchSchemaFailure:w,skipSchemaValidation:A},R){(0,a.YT)();let j=e=>e;Object.assign(t,{reducerPath:s,endpoints:{},internalActions:{onOnline:O,onOffline:E,onFocus:q,onFocusLost:P},util:{}});let T=function({serializeQueryArgs:e,reducerPath:t,createSelector:n}){let r=e=>Z,i=e=>X;return{buildQuerySelector:function(e,t){return c(e,t,a)},buildInfiniteQuerySelector:function(e,t){let{infiniteQueryOptions:n}=t;return c(e,t,function(e){var t,r,i,a,u,s;let o={...e,...l(e.status)},{isLoading:c,isError:d,direction:f}=o,p="forward"===f,y="backward"===f;return{...o,hasNextPage:(t=n,r=o.data,i=o.originalArgs,!!r&&null!=K(t,r,i)),hasPreviousPage:(a=n,u=o.data,s=o.originalArgs,!!u&&!!a.getPreviousPageParam&&null!=U(a,u,s)),isFetchingNextPage:c&&p,isFetchingPreviousPage:c&&y,isFetchNextPageError:d&&p,isFetchPreviousPageError:d&&y}})},buildMutationSelector:function(){return e=>{let r;return n((r="object"==typeof e?W(e)??H:e)===H?i:e=>(function(e){return e[t]})(e)?.mutations?.[r]??X,a)}},selectInvalidatedBy:function(e,n){let r=e[t],i=new Set;for(let e of n.filter(p).map(Q)){let t=r.provided.tags[e.type];if(t)for(let n of(void 0!==e.id?t[e.id]:f(Object.values(t)))??[])i.add(n)}return f(Array.from(i.values()).map(e=>{let t=r.queries[e];return t?[{queryCacheKey:e,endpointName:t.endpointName,originalArgs:t.originalArgs}]:[]}))},selectCachedArgsForQuery:function(e,t){return Object.values(s(e)).filter(e=>e?.endpointName===t&&"uninitialized"!==e.status).map(e=>e.originalArgs)},selectApiState:u,selectQueries:s,selectMutations:function(e){return function(e){return e[t]}(e)?.mutations},selectQueryEntry:o,selectConfig:function(e){return function(e){return e[t]}(e)?.config}};function a(e){return{...e,...l(e.status)}}function u(e){return e[t]}function s(e){return e[t]?.queries}function o(e,t){return s(e)?.[t]}function c(t,i,a){return u=>{if(u===H)return n(r,a);let s=e({queryArgs:u,endpointDefinition:i,endpointName:t});return n(e=>o(e,s)??Z,a)}}}({serializeQueryArgs:o,reducerPath:s,createSelector:e}),{selectInvalidatedBy:N,selectCachedArgsForQuery:J,buildQuerySelector:Y,buildInfiniteQuerySelector:G,buildMutationSelector:ee}=T;et(t.util,{selectInvalidatedBy:N,selectCachedArgsForQuery:J});let{queryThunk:ei,infiniteQueryThunk:ef,mutationThunk:ep,patchQueryData:ey,updateQueryData:em,upsertQueryData:eg,prefetch:eh,buildMatchThunkActions:ev}=function({reducerPath:e,baseQuery:t,context:{endpointDefinitions:n},serializeQueryArgs:r,api:u,assertTagType:s,selectors:o,onSchemaFailure:l,catchSchemaFailure:c,skipSchemaValidation:d}){function f(e,t,n=0){let r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}function p(e,t,n=0){let r=[...e,t];return n&&r.length>n?r.slice(1):r}let y=(e,t)=>e.query&&e[t]?e[t]:$,m=async(e,{signal:r,abort:i,rejectWithValue:a,fulfillWithValue:u,dispatch:s,getState:m,extra:h})=>{let v=n[e.endpointName],{metaSchema:b,skipSchemaValidation:w=d}=v;try{let n,a=y(v,"transformResponse"),l={signal:r,abort:i,dispatch:s,getState:m,extra:h,endpoint:e.endpointName,type:e.type,forced:"query"===e.type?g(e,m()):void 0,queryCacheKey:"query"===e.type?e.queryCacheKey:void 0},c="query"===e.type?e[D]:void 0,d=async(t,n,r,i)=>{if(null==n&&t.pages.length)return Promise.resolve({data:t});let a={queryArg:e.originalArgs,pageParam:n},u=await A(a),s=i?f:p;return{data:{pages:s(t.pages,u.data,r),pageParams:s(t.pageParams,n,r)},meta:u.meta}};async function A(e){let n,{extraOptions:r,argSchema:i,rawResponseSchema:u,responseSchema:s}=v;if(i&&!w&&(e=await _(i,e,"argSchema",{})),(n=c?c():v.query?await t(v.query(e),l,r):await v.queryFn(e,l,r,e=>t(e,l,r))).error)throw new S(n.error,n.meta);let{data:o}=n;u&&!w&&(o=await _(u,n.data,"rawResponseSchema",n.meta));let d=await a(o,n.meta,e);return s&&!w&&(d=await _(s,d,"responseSchema",n.meta)),{...n,data:d}}if("query"===e.type&&"infiniteQueryOptions"in v){let t,{infiniteQueryOptions:r}=v,{maxPages:i=1/0}=r,a=o.selectQueryEntry(m(),e.queryCacheKey)?.data,u=(!g(e,m())||e.direction)&&a?a:{pages:[],pageParams:[]};if("direction"in e&&e.direction&&u.pages.length){let n="backward"===e.direction,a=(n?U:K)(r,u,e.originalArgs);t=await d(u,a,i,n)}else{let{initialPageParam:n=r.initialPageParam}=e,s=a?.pageParams??[],o=s[0]??n,l=s.length;t=await d(u,o,i),c&&(t={data:t.data.pages[0]});for(let n=1;n<l;n++){let n=K(r,t.data,e.originalArgs);t=await d(t.data,n,i)}}n=t}else n=await A(e.originalArgs);return b&&!w&&n.meta&&(n.meta=await _(b,n.meta,"metaSchema",n.meta)),u(n.data,z({fulfilledTimeStamp:Date.now(),baseQueryMeta:n.meta}))}catch(n){let t=n;if(t instanceof S){let n=y(v,"transformErrorResponse"),{rawErrorResponseSchema:r,errorResponseSchema:i}=v,{value:u,meta:s}=t;try{r&&!w&&(u=await _(r,u,"rawErrorResponseSchema",s)),b&&!w&&(s=await _(b,s,"metaSchema",s));let t=await n(u,s,e.originalArgs);return i&&!w&&(t=await _(i,t,"errorResponseSchema",s)),a(t,z({baseQueryMeta:s}))}catch(e){t=e}}try{if(t instanceof x){let n={endpoint:e.endpointName,arg:e.originalArgs,type:e.type,queryCacheKey:"query"===e.type?e.queryCacheKey:void 0};v.onSchemaFailure?.(t,n),l?.(t,n);let{catchSchemaFailure:r=c}=v;if(r)return a(r(t,n),z({baseQueryMeta:t._bqMeta}))}}catch(e){t=e}throw console.error(t),t}};function g(e,t){let n=o.selectQueryEntry(t,e.queryCacheKey),r=o.selectConfig(t).refetchOnMountOrArgChange,i=n?.fulfilledTimeStamp,a=e.forceRefetch??(e.subscribe&&r);return!!a&&(!0===a||(Number(new Date)-Number(i))/1e3>=a)}let h=()=>(0,i.zD)(`${e}/executeQuery`,m,{getPendingMeta({arg:e}){let t=n[e.endpointName];return z({startedTimeStamp:Date.now(),...k(t)?{direction:e.direction}:{}})},condition(e,{getState:t}){let r=t(),i=o.selectQueryEntry(r,e.queryCacheKey),a=i?.fulfilledTimeStamp,u=e.originalArgs,s=i?.originalArgs,l=n[e.endpointName],c=e.direction;return!!I(e)||i?.status!=="pending"&&(!!(g(e,r)||C(l)&&l?.forceRefetch?.({currentArg:u,previousArg:s,endpointState:i,state:r}))||!a||!!c)},dispatchConditionRejection:!0}),v=h(),b=h(),w=(0,i.zD)(`${e}/executeMutation`,m,{getPendingMeta:()=>z({startedTimeStamp:Date.now()})}),A=e=>"force"in e,R=e=>"ifOlderThan"in e;function q(e){return t=>t?.meta?.arg?.endpointName===e}return{queryThunk:v,mutationThunk:w,infiniteQueryThunk:b,prefetch:(e,t,n)=>(r,i)=>{let a=A(n)&&n.force,s=R(n)&&n.ifOlderThan,o=(n=!0)=>u.endpoints[e].initiate(t,{forceRefetch:n,isPrefetch:!0}),l=u.endpoints[e].select(t)(i());if(a)r(o());else if(s){let e=l?.fulfilledTimeStamp;if(!e)return void r(o());(Number(new Date)-Number(new Date(e)))/1e3>=s&&r(o())}else r(o(!1))},updateQueryData:(e,t,n,r=!0)=>(i,s)=>{let o,l=u.endpoints[e].select(t)(s()),c={patches:[],inversePatches:[],undo:()=>i(u.util.patchQueryData(e,t,c.inversePatches,r))};if("uninitialized"===l.status)return c;if("data"in l)if((0,a.a6)(l.data)){let[e,t,r]=(0,a.vI)(l.data,n);c.patches.push(...t),c.inversePatches.push(...r),o=e}else o=n(l.data),c.patches.push({op:"replace",path:[],value:o}),c.inversePatches.push({op:"replace",path:[],value:l.data});return 0===c.patches.length||i(u.util.patchQueryData(e,t,c.patches,r)),c},upsertQueryData:(e,t,n)=>r=>r(u.endpoints[e].initiate(t,{subscribe:!1,forceRefetch:!0,[D]:()=>({data:n})})),patchQueryData:(e,t,i,a)=>(o,l)=>{let c=n[e],d=r({queryArgs:t,endpointDefinition:c,endpointName:e});if(o(u.internalActions.queryResultPatched({queryCacheKey:d,patches:i})),!a)return;let f=u.endpoints[e].select(t)(l()),p=M(c.providesTags,f.data,void 0,t,{},s);o(u.internalActions.updateProvidedBy([{queryCacheKey:d,providedTags:p}]))},buildMatchThunkActions:function(e,t){return{matchPending:(0,i.f$)((0,i.mm)(e),q(t)),matchFulfilled:(0,i.f$)((0,i.sf)(e),q(t)),matchRejected:(0,i.f$)((0,i.TK)(e),q(t))}}}}({baseQuery:n,reducerPath:s,context:R,api:t,serializeQueryArgs:o,assertTagType:j,selectors:T,onSchemaFailure:b,catchSchemaFailure:w,skipSchemaValidation:A}),{reducer:eb,actions:ew}=function({reducerPath:e,queryThunk:t,mutationThunk:n,serializeQueryArgs:u,context:{endpointDefinitions:s,apiUid:o,extractRehydrationInfo:l,hasRehydrationInfo:d},assertTagType:f,config:p}){let y=(0,i.VP)(`${e}/resetApiState`);function m(e,t,n,r){e[t.queryCacheKey]??={status:"uninitialized",endpointName:t.endpointName},L(e,t.queryCacheKey,e=>{e.status="pending",e.requestId=n&&e.requestId?e.requestId:r.requestId,void 0!==t.originalArgs&&(e.originalArgs=t.originalArgs),e.startedTimeStamp=r.startedTimeStamp,k(s[r.arg.endpointName])&&"direction"in t&&(e.direction=t.direction)})}function g(e,t,n,r){L(e,t.arg.queryCacheKey,e=>{if(e.requestId!==t.requestId&&!r)return;let{merge:i}=s[t.arg.endpointName];if(e.status="fulfilled",i)if(void 0!==e.data){let{fulfilledTimeStamp:r,arg:u,baseQueryMeta:s,requestId:o}=t,l=(0,a.jM)(e.data,e=>i(e,n,{arg:u.originalArgs,baseQueryMeta:s,fulfilledTimeStamp:r,requestId:o}));e.data=l}else e.data=n;else e.data=s[t.arg.endpointName].structuralSharing??!0?function e(t,n){if(t===n||!(c(t)&&c(n)||Array.isArray(t)&&Array.isArray(n)))return n;let r=Object.keys(n),i=Object.keys(t),a=r.length===i.length,u=Array.isArray(n)?[]:{};for(let i of r)u[i]=e(t[i],n[i]),a&&(a=t[i]===u[i]);return a?t:u}((0,a.Qx)(e.data)?(0,a.c2)(e.data):e.data,n):n;delete e.error,e.fulfilledTimeStamp=t.fulfilledTimeStamp})}let h=(0,i.Z0)({name:`${e}/queries`,initialState:B,reducers:{removeQueryResult:{reducer(e,{payload:{queryCacheKey:t}}){delete e[t]},prepare:(0,i.aA)()},cacheEntriesUpserted:{reducer(e,t){for(let n of t.payload){let{queryDescription:r,value:i}=n;m(e,r,!0,{arg:r,requestId:t.meta.requestId,startedTimeStamp:t.meta.timestamp}),g(e,{arg:r,requestId:t.meta.requestId,fulfilledTimeStamp:t.meta.timestamp,baseQueryMeta:{}},i,!0)}},prepare:e=>({payload:e.map(e=>{let{endpointName:t,arg:n,value:r}=e,i=s[t];return{queryDescription:{type:"query",endpointName:t,originalArgs:e.arg,queryCacheKey:u({queryArgs:n,endpointDefinition:i,endpointName:t})},value:r}}),meta:{[i.cN]:!0,requestId:(0,i.Ak)(),timestamp:Date.now()}})},queryResultPatched:{reducer(e,{payload:{queryCacheKey:t,patches:n}}){L(e,t,e=>{e.data=(0,a.$i)(e.data,n.concat())})},prepare:(0,i.aA)()}},extraReducers(e){e.addCase(t.pending,(e,{meta:t,meta:{arg:n}})=>{let r=I(n);m(e,n,r,t)}).addCase(t.fulfilled,(e,{meta:t,payload:n})=>{let r=I(t.arg);g(e,t,n,r)}).addCase(t.rejected,(e,{meta:{condition:t,arg:n,requestId:r},error:i,payload:a})=>{L(e,n.queryCacheKey,e=>{if(t);else{if(e.requestId!==r)return;e.status="rejected",e.error=a??i}})}).addMatcher(d,(e,t)=>{let{queries:n}=l(t);for(let[t,r]of Object.entries(n))(r?.status==="fulfilled"||r?.status==="rejected")&&(e[t]=r)})}}),v=(0,i.Z0)({name:`${e}/mutations`,initialState:B,reducers:{removeMutationResult:{reducer(e,{payload:t}){let n=W(t);n in e&&delete e[n]},prepare:(0,i.aA)()}},extraReducers(e){e.addCase(n.pending,(e,{meta:t,meta:{requestId:n,arg:r,startedTimeStamp:i}})=>{r.track&&(e[W(t)]={requestId:n,status:"pending",endpointName:r.endpointName,startedTimeStamp:i})}).addCase(n.fulfilled,(e,{payload:t,meta:n})=>{n.arg.track&&V(e,n,e=>{e.requestId===n.requestId&&(e.status="fulfilled",e.data=t,e.fulfilledTimeStamp=n.fulfilledTimeStamp)})}).addCase(n.rejected,(e,{payload:t,error:n,meta:r})=>{r.arg.track&&V(e,r,e=>{e.requestId===r.requestId&&(e.status="rejected",e.error=t??n)})}).addMatcher(d,(e,t)=>{let{mutations:n}=l(t);for(let[t,r]of Object.entries(n))(r?.status==="fulfilled"||r?.status==="rejected")&&t!==r?.requestId&&(e[t]=r)})}}),b=(0,i.Z0)({name:`${e}/invalidation`,initialState:{tags:{},keys:{}},reducers:{updateProvidedBy:{reducer(e,t){for(let{queryCacheKey:n,providedTags:r}of t.payload){for(let{type:t,id:i}of(w(e,n),r)){let r=(e.tags[t]??={})[i||"__internal_without_id"]??=[];r.includes(n)||r.push(n)}e.keys[n]=r}},prepare:(0,i.aA)()}},extraReducers(e){e.addCase(h.actions.removeQueryResult,(e,{payload:{queryCacheKey:t}})=>{w(e,t)}).addMatcher(d,(e,t)=>{let{provided:n}=l(t);for(let[t,r]of Object.entries(n))for(let[n,i]of Object.entries(r)){let r=(e.tags[t]??={})[n||"__internal_without_id"]??=[];for(let e of i)r.includes(e)||r.push(e)}}).addMatcher((0,i.i0)((0,i.sf)(t),(0,i.WA)(t)),(e,t)=>{S(e,[t])}).addMatcher(h.actions.cacheEntriesUpserted.match,(e,t)=>{S(e,t.payload.map(({queryDescription:e,value:t})=>({type:"UNKNOWN",payload:t,meta:{requestStatus:"fulfilled",requestId:"UNKNOWN",arg:e}})))})}});function w(e,t){for(let n of e.keys[t]??[]){let r=n.type,i=n.id??"__internal_without_id",a=e.tags[r]?.[i];a&&(e.tags[r][i]=a.filter(e=>e!==t))}delete e.keys[t]}function S(e,t){let n=t.map(e=>{let t=F(e,"providesTags",s,f),{queryCacheKey:n}=e.meta.arg;return{queryCacheKey:n,providedTags:t}});b.caseReducers.updateProvidedBy(e,b.actions.updateProvidedBy(n))}let A=(0,i.Z0)({name:`${e}/subscriptions`,initialState:B,reducers:{updateSubscriptionOptions(e,t){},unsubscribeQueryResult(e,t){},internal_getRTKQSubscriptions(){}}}),R=(0,i.Z0)({name:`${e}/internalSubscriptions`,initialState:B,reducers:{subscriptionsUpdated:{reducer:(e,t)=>(0,a.$i)(e,t.payload),prepare:(0,i.aA)()}}}),j=(0,i.Z0)({name:`${e}/config`,initialState:{online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine,focused:"undefined"==typeof document||"hidden"!==document.visibilityState,middlewareRegistered:!1,...p},reducers:{middlewareRegistered(e,{payload:t}){e.middlewareRegistered="conflict"!==e.middlewareRegistered&&o===t||"conflict"}},extraReducers:e=>{e.addCase(O,e=>{e.online=!0}).addCase(E,e=>{e.online=!1}).addCase(q,e=>{e.focused=!0}).addCase(P,e=>{e.focused=!1}).addMatcher(d,e=>({...e}))}}),T=(0,r.HY)({queries:h.reducer,mutations:v.reducer,provided:b.reducer,subscriptions:R.reducer,config:j.reducer});return{reducer:(e,t)=>T(y.match(t)?void 0:e,t),actions:{...j.actions,...h.actions,...A.actions,...R.actions,...v.actions,...b.actions,resetApiState:y}}}({context:R,queryThunk:ei,infiniteQueryThunk:ef,mutationThunk:ep,serializeQueryArgs:o,reducerPath:s,assertTagType:j,config:{refetchOnFocus:g,refetchOnReconnect:h,refetchOnMountOrArgChange:m,keepUnusedDataFor:y,reducerPath:s,invalidationBehavior:v}});et(t.util,{patchQueryData:ey,updateQueryData:em,upsertQueryData:eg,prefetch:eh,resetApiState:ew.resetApiState,upsertQueryEntries:ew.cacheEntriesUpserted}),et(t.internalActions,ew);let{middleware:eS,actions:eA}=function(e){let{reducerPath:t,queryThunk:n,api:a,context:u}=e,{apiUid:s}=u,o={invalidateTags:(0,i.VP)(`${t}/invalidateTags`)},l=e=>e.type.startsWith(`${t}/`),c=[eu,er,es,eo,ea,el];return{middleware:n=>{let i=!1,o={...e,internalState:{currentSubscriptions:{}},refetchQuery:d,isThisApiSliceAction:l},f=c.map(e=>e(o)),p=en(o),y=ec(o);return e=>o=>{let c;if(!(0,r.ve)(o))return e(o);i||(i=!0,n.dispatch(a.internalActions.middlewareRegistered(s)));let d={...n,next:e},m=n.getState(),[g,h]=p(o,d,m);if(c=g?e(o):h,n.getState()[t]&&(y(o,d,m),l(o)||u.hasRehydrationInfo(o)))for(let e of f)e(o,d,m);return c}},actions:o};function d(t){return e.api.endpoints[t.endpointName].initiate(t.originalArgs,{subscribe:!1,forceRefetch:!0})}}({reducerPath:s,context:R,queryThunk:ei,mutationThunk:ep,infiniteQueryThunk:ef,api:t,assertTagType:j,selectors:T});et(t.util,eA),et(t,{reducer:eb,middleware:eS});let{buildInitiateQuery:eR,buildInitiateInfiniteQuery:eq,buildInitiateMutation:eP,getRunningMutationThunk:eO,getRunningMutationsThunk:eE,getRunningQueriesThunk:ej,getRunningQueryThunk:eT}=function({serializeQueryArgs:e,queryThunk:t,infiniteQueryThunk:n,mutationThunk:r,api:i,context:a}){let u=new Map,s=new Map,{unsubscribeQueryResult:o,removeMutationResult:l,updateSubscriptionOptions:c}=i.internalActions;return{buildInitiateQuery:function(e,t){return y(e,t)},buildInitiateInfiniteQuery:function(e,t){return y(e,t)},buildInitiateMutation:function(e){return(t,{track:n=!0,fixedCacheKey:i}={})=>(a,u)=>{var o,c;let f=a(r({type:"mutation",endpointName:e,originalArgs:t,track:n,fixedCacheKey:i})),{requestId:p,abort:y,unwrap:m}=f,g=Object.assign((o=f.unwrap().then(e=>({data:e})),c=e=>({error:e}),o.catch(c)),{arg:f.arg,requestId:p,abort:y,unwrap:m,reset:()=>{a(l({requestId:p,fixedCacheKey:i}))}}),h=s.get(a)||{};return s.set(a,h),h[p]=g,g.then(()=>{delete h[p],d(h)||s.delete(a)}),i&&(h[i]=g,g.then(()=>{h[i]===g&&(delete h[i],d(h)||s.delete(a))})),g}},getRunningQueryThunk:function(t,n){return r=>{let i=e({queryArgs:n,endpointDefinition:a.endpointDefinitions[t],endpointName:t});return u.get(r)?.[i]}},getRunningMutationThunk:function(e,t){return e=>s.get(e)?.[t]},getRunningQueriesThunk:function(){return e=>Object.values(u.get(e)||{}).filter(p)},getRunningMutationsThunk:function(){return e=>Object.values(s.get(e)||{}).filter(p)}};function f(e){}function y(r,a){let s=(l,{subscribe:f=!0,forceRefetch:p,subscriptionOptions:y,[D]:m,...g}={})=>(h,v)=>{let b,w=e({queryArgs:l,endpointDefinition:a,endpointName:r}),S={...g,type:"query",subscribe:f,forceRefetch:p,subscriptionOptions:y,endpointName:r,originalArgs:l,queryCacheKey:w,[D]:m};if(C(a))b=t(S);else{let{direction:e,initialPageParam:t}=g;b=n({...S,direction:e,initialPageParam:t})}let A=i.endpoints[r].select(l),R=h(b),q=A(v()),{requestId:P,abort:O}=R,E=q.requestId!==P,j=u.get(h)?.[w],T=()=>A(v()),k=Object.assign(m?R.then(T):E&&!j?Promise.resolve(q):Promise.all([j,R]).then(T),{arg:l,requestId:P,subscriptionOptions:y,queryCacheKey:w,abort:O,async unwrap(){let e=await k;if(e.isError)throw e.error;return e.data},refetch:()=>h(s(l,{subscribe:!1,forceRefetch:!0})),unsubscribe(){f&&h(o({queryCacheKey:w,requestId:P}))},updateSubscriptionOptions(e){k.subscriptionOptions=e,h(c({endpointName:r,requestId:P,queryCacheKey:w,options:e}))}});if(!j&&!E&&!m){var N;let e=(N={},u.has(h)?u.get(h):u.set(h,N).get(h));e[w]=k,k.then(()=>{delete e[w],d(e)||u.delete(h)})}return k};return s}}({queryThunk:ei,mutationThunk:ep,infiniteQueryThunk:ef,api:t,serializeQueryArgs:o,context:R});return et(t.util,{getRunningMutationThunk:eO,getRunningMutationsThunk:eE,getRunningQueryThunk:eT,getRunningQueriesThunk:ej}),{name:ed,injectEndpoint(e,n){let r=t.endpoints[e]??={};C(n)&&et(r,{name:e,select:Y(e,n),initiate:eR(e,n)},ev(ei,e)),"mutation"===n.type&&et(r,{name:e,select:ee(),initiate:eP(e)},ev(ep,e)),k(n)&&et(r,{name:e,select:G(e,n),initiate:eq(e,n)},ev(ei,e))}}}});ef()}}]);