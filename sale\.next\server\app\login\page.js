(()=>{var e={};e.id=520,e.ids=[520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4942:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(62544);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},7244:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(98085).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},7409:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(73356),n=globalThis?.document?s.useLayoutEffect:()=>{}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12053:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var s=r(40969),n=r(73356),a=r(2724),i=r(52774),l=r(21764);let o=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.b,{ref:r,className:(0,l.cn)(o(),e),...t}));c.displayName=a.b.displayName},13315:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(98085).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21764:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>o,ZV:()=>l,cn:()=>a,r6:()=>c,tP:()=>d,vv:()=>i});var s=r(95534),n=r(86026);function a(...e){return(0,n.QP)((0,s.$)(e))}function i(e){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}function l(e){return new Intl.NumberFormat("en-IN").format(e)}function o(e){if(!e)return"N/A";let t=new Date(e);return isNaN(t.getTime())?"Invalid Date":new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric"}).format(t)}function c(e){if(!e)return"N/A";let t=new Date(e);return isNaN(t.getTime())?"Invalid Date":new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}function d(e,t){return 0===t?100*(e>0):(e-t)/t*100}},22195:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});var s=r(73356),n=r(24629),a=r(7409),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[n,i]=s.useState(),o=s.useRef(null),c=s.useRef(e),d=s.useRef("none"),[u,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},s.useReducer((e,t)=>r[e][t]??e,t));return s.useEffect(()=>{let e=l(o.current);d.current="mounted"===u?e:"none"},[u]),(0,a.N)(()=>{let t=o.current,r=c.current;if(r!==e){let s=d.current,n=l(t);e?f("MOUNT"):"none"===n||t?.display==="none"?f("UNMOUNT"):r&&s!==n?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,a.N)(()=>{if(n){let e,t=n.ownerDocument.defaultView??window,r=r=>{let s=l(o.current).includes(r.animationName);if(r.target===n&&s&&(f("ANIMATION_END"),!c.current)){let r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},s=e=>{e.target===n&&(d.current=l(o.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}f("ANIMATION_END")},[n,f]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:s.useCallback(e=>{o.current=e?getComputedStyle(e):null,i(e)},[])}}(t),o="function"==typeof r?r({present:i.isPresent}):s.Children.only(r),c=(0,n.s)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof r||i.isPresent?s.cloneElement(o,{ref:c}):null};function l(e){return e?.animationName||"none"}i.displayName="Presence"},26314:(e,t,r)=>{"use strict";r.d(t,{i:()=>l});var s,n=r(73356),a=r(7409),i=(s||(s=r.t(n,2)))[" useInsertionEffect ".trim().toString()]||a.N;function l({prop:e,defaultProp:t,onChange:r=()=>{},caller:s}){let[a,l,o]=function({defaultProp:e,onChange:t}){let[r,s]=n.useState(e),a=n.useRef(r),l=n.useRef(t);return i(()=>{l.current=t},[t]),n.useEffect(()=>{a.current!==r&&(l.current?.(r),a.current=r)},[r,a]),[r,s,l]}({defaultProp:t,onChange:r}),c=void 0!==e,d=c?e:a;{let t=n.useRef(void 0!==e);n.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${s} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,s])}return[d,n.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&o.current?.(r)}else l(t)},[c,e,l,o])]}Symbol("RADIX:SYNC_STATE")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35342:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(98085).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},36570:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\login\\page.tsx","default")},36577:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(40969);r(73356);let n=({size:e="md",variant:t="full",theme:r="default",className:n=""})=>{let a={sm:"h-8 w-8",md:"h-12 w-12",lg:"h-16 w-16",xl:"h-24 w-24"},i={sm:"text-lg",md:"text-2xl",lg:"text-3xl",xl:"text-4xl"},l=(()=>{switch(r){case"white":return{circle:"#ffffff",circleStroke:"#0ea5e9",building:"#0ea5e9",windows:"#ffffff",door:"#fbbf24",arrow:"#fbbf24",text:"#0ea5e9",accent:"#ffffff",primary:"#f1f5f9"};case"dark":return{circle:"#1e293b",circleStroke:"#0ea5e9",building:"#ffffff",windows:"#0ea5e9",door:"#fbbf24",arrow:"#fbbf24",text:"#ffffff",accent:"#0ea5e9",primary:"#64748b"};default:return{circle:"#0ea5e9",circleStroke:"#ffffff",building:"#ffffff",windows:"#0ea5e9",door:"#fbbf24",arrow:"#fbbf24",text:"#ffffff",accent:"#0ea5e9",primary:"#64748b"}}})(),o=()=>(0,s.jsx)("div",{className:`${a[e]} ${n} relative`,children:(0,s.jsxs)("svg",{viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"w-full h-full",children:[(0,s.jsx)("circle",{cx:"50",cy:"50",r:"48",fill:l.circle,stroke:l.circleStroke,strokeWidth:"2"}),(0,s.jsxs)("g",{transform:"translate(20, 25)",children:[(0,s.jsx)("rect",{x:"15",y:"20",width:"30",height:"35",fill:l.building,rx:"2"}),(0,s.jsx)("rect",{x:"20",y:"25",width:"6",height:"6",fill:l.windows}),(0,s.jsx)("rect",{x:"34",y:"25",width:"6",height:"6",fill:l.windows}),(0,s.jsx)("rect",{x:"20",y:"35",width:"6",height:"6",fill:l.windows}),(0,s.jsx)("rect",{x:"34",y:"35",width:"6",height:"6",fill:l.windows}),(0,s.jsx)("rect",{x:"27",y:"45",width:"6",height:"10",fill:l.door}),(0,s.jsx)("rect",{x:"5",y:"30",width:"8",height:"25",fill:l.building,rx:"1"}),(0,s.jsx)("rect",{x:"47",y:"35",width:"8",height:"20",fill:l.building,rx:"1"}),(0,s.jsx)("path",{d:"M10 15 L20 5 L30 10 L40 2 L50 8",stroke:l.arrow,strokeWidth:"3",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"}),(0,s.jsx)("polygon",{points:"45,2 50,8 45,8",fill:l.arrow})]}),(0,s.jsx)("text",{x:"50",y:"75",textAnchor:"middle",fill:l.text,fontSize:"12",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:"SGM"})]})});switch(t){case"icon":return(0,s.jsx)(o,{});case"text":return(0,s.jsx)(()=>(0,s.jsxs)("div",{className:`${n} flex items-center`,children:[(0,s.jsx)("span",{className:`${i[e]} font-bold`,style:{color:l.accent},children:"SGM"}),(0,s.jsx)("span",{className:`${i[e]} font-light ml-1`,style:{color:l.primary},children:"Sales"})]}),{});default:return(0,s.jsx)(()=>(0,s.jsxs)("div",{className:`${n} flex items-center space-x-3`,children:[(0,s.jsx)(o,{}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:`${i[e]} font-bold leading-none`,style:{color:l.accent},children:"SGM"}),(0,s.jsx)("span",{className:"text-sm leading-none",style:{color:l.primary},children:"Sales Portal"})]})]}),{})}}},39145:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>f,tree:()=>c});var s=r(10557),n=r(68490),a=r(13172),i=r.n(a),l=r(68835),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36570)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},39827:(e,t,r)=>{"use strict";r.d(t,{X:()=>a});var s=r(73356),n=r(7409);function a(e){let[t,r]=s.useState(void 0);return(0,n.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let s,n;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;s=t.inlineSize,n=t.blockSize}else s=e.offsetWidth,n=e.offsetHeight;r({width:s,height:n})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},42291:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(73356);function n(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},43379:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(40969),n=r(73356),a=r(12011),i=r(48567),l=r(61631),o=r(66949),c=r(46411),d=r(57387),u=r(12053),f=r(93541),m=r(42605),x=r(14493),h=r(70713),p=r(56252),g=r(7244);let v=(0,r(98085).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var y=r(92386),b=r(79123),w=r(35342),N=r(36577);function j(){let[e,t]=(0,n.useState)(""),[r,j]=(0,n.useState)(""),[k,A]=(0,n.useState)(!1),[S,C]=(0,n.useState)(!1),[R,M]=(0,n.useState)(!0);(0,n.useRef)(!1);let _=(0,i.j)(),P=(0,i.G)(l.H$),D=(0,i.G)(l.ZB),E=(0,a.useRouter)();if(R)return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-sky-50 via-white to-sky-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)(N.A,{size:"lg",variant:"full"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sky-600",children:[(0,s.jsx)(x.A,{className:"w-5 h-5 animate-spin"}),(0,s.jsx)("span",{className:"text-lg font-medium",children:"Checking authentication..."})]})]})});let T={sales_manager:{email:"<EMAIL>",password:"manager123",role:"Sales Manager",description:"Team management • All sales features • Reports access"},sales_agent:{email:"<EMAIL>",password:"agent123",role:"Sales Agent",description:"Lead management • Property sales • Customer follow-ups"}},I=async t=>{if(t.preventDefault(),!e||!r)return void m.o.error("Please fill in all fields");try{let t=await _((0,l._v)({email:e,password:r,rememberMe:k}));if(l._v.fulfilled.match(t))m.o.success("✅ Sales login successful!"),console.log("✅ Backend sales login successful, redirecting to dashboard..."),E.push("/dashboard");else{let e=t.payload||"Login failed";m.o.error(`❌ ${e}`),console.error("Backend sales login failed:",e)}}catch(e){m.o.error("Login failed. Please try again."),console.error("Login error:",e)}},$=async e=>{console.log("\uD83C\uDFAD Demo login clicked:",e);let r=T[e];console.log("\uD83C\uDFAD Demo account details:",{email:r.email,role:r.role}),t(r.email),j(r.password);try{console.log("\uD83C\uDFAD Dispatching demo login to backend API...");let e=await _((0,l._v)({email:r.email,password:r.password,rememberMe:!1}));console.log("\uD83C\uDFAD Demo login result:",e),l._v.fulfilled.match(e)?(m.o.success(`✅ ${r.role} login successful!`),console.log("✅ Demo login successful, redirecting to dashboard..."),setTimeout(()=>{console.log("\uD83D\uDD04 Sales: Redirecting after demo login success"),E.push("/dashboard")},500)):(console.error("❌ Demo login failed:",e),m.o.error("Demo login failed - please check server connection"))}catch(e){console.error("❌ Demo login error:",e),m.o.error("Demo login failed - please check server connection")}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-sky-50 via-white to-sky-100 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-lg space-y-8",children:[(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)(N.A,{size:"xl",variant:"full",className:"justify-center"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Welcome Back to SGM"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Sign in to access your sales dashboard and manage property leads"})]})]}),(0,s.jsxs)(o.Zp,{className:"shadow-xl border-0 bg-white/80 backdrop-blur-sm",children:[(0,s.jsxs)(o.aR,{className:"space-y-1 pb-6",children:[(0,s.jsxs)(o.ZB,{className:"flex items-center gap-3 text-xl",children:[(0,s.jsx)("div",{className:"p-2 bg-sky-100 rounded-lg",children:(0,s.jsx)(h.A,{className:"h-5 w-5 text-sky-600"})}),"Sign In to Your Account"]}),(0,s.jsx)(o.BT,{className:"text-gray-600",children:"Enter your credentials to access the sales portal"})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("form",{onSubmit:I,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(d.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:e,onChange:e=>t(e.target.value),className:"pl-10 h-12 border-gray-300 focus:border-sky-500 focus:ring-sky-500 rounded-lg",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(d.p,{id:"password",type:S?"text":"password",placeholder:"Enter your password",value:r,onChange:e=>j(e.target.value),className:"pl-10 pr-12 h-12 border-gray-300 focus:border-sky-500 focus:ring-sky-500 rounded-lg",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>C(!S),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:S?(0,s.jsx)(v,{className:"h-4 w-4"}):(0,s.jsx)(y.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(f.S,{id:"remember",checked:k,onCheckedChange:e=>A(e),className:"border-gray-300 data-[state=checked]:bg-sky-600 data-[state=checked]:border-sky-600"}),(0,s.jsx)(u.J,{htmlFor:"remember",className:"text-sm text-gray-600",children:"Remember me for 30 days"})]}),(0,s.jsx)("button",{type:"button",className:"text-sm text-sky-600 hover:text-sky-700 font-medium transition-colors",children:"Forgot password?"})]}),D&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(g.A,{className:"h-4 w-4 text-red-600"})}),(0,s.jsx)("p",{className:"text-sm text-red-700",children:D})]})}),(0,s.jsx)(c.$,{type:"submit",className:"w-full h-12 bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200",disabled:P,children:P?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 animate-spin"}),"Signing in..."]}):(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),"Sign In to Dashboard"]})})]})})]}),(0,s.jsxs)(o.Zp,{className:"shadow-xl border-0 bg-white/80 backdrop-blur-sm",children:[(0,s.jsxs)(o.aR,{className:"space-y-1 pb-4",children:[(0,s.jsxs)(o.ZB,{className:"text-lg flex items-center gap-2",children:[(0,s.jsx)("div",{className:"p-1.5 bg-yellow-100 rounded-lg",children:(0,s.jsx)(b.A,{className:"h-4 w-4 text-yellow-600"})}),"Demo Accounts"]}),(0,s.jsx)(o.BT,{children:"Quick access for testing and demonstration purposes"})]}),(0,s.jsxs)(o.Wu,{className:"space-y-3",children:[(0,s.jsx)(c.$,{onClick:()=>$("sales_manager"),className:"w-full h-16 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg",disabled:P,variant:"default",children:(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-white/20 rounded-lg",children:(0,s.jsx)(b.A,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("div",{className:"font-semibold",children:"Sales Manager"}),(0,s.jsx)("div",{className:"text-xs text-purple-100",children:"Full access & team management"})]})]}),(0,s.jsxs)("div",{className:"text-xs text-right text-purple-100",children:[(0,s.jsx)("div",{className:"font-mono",children:"<EMAIL>"}),(0,s.jsx)("div",{children:"manager123"})]})]})}),(0,s.jsx)(c.$,{onClick:()=>$("sales_agent"),className:"w-full h-16 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg",disabled:P,variant:"default",children:(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-white/20 rounded-lg",children:(0,s.jsx)(w.A,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("div",{className:"font-semibold",children:"Sales Agent"}),(0,s.jsx)("div",{className:"text-xs text-green-100",children:"Lead management & sales"})]})]}),(0,s.jsxs)("div",{className:"text-xs text-right text-green-100",children:[(0,s.jsx)("div",{className:"font-mono",children:"<EMAIL>"}),(0,s.jsx)("div",{children:"agent123"})]})]})})]})]}),(0,s.jsxs)("div",{className:"text-center text-xs text-gray-500",children:[(0,s.jsx)("p",{children:"\xa9 2024 SGM Sales Portal. All rights reserved."}),(0,s.jsx)("p",{className:"mt-1",children:"Secure authentication with backend cookie management"})]})]})})}},46411:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var s=r(40969),n=r(73356),a=r(19334),i=r(52774),l=r(21764);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...i},c)=>{let d=n?a.DX:"button";return(0,s.jsx)(d,{className:(0,l.cn)(o({variant:t,size:r,className:e})),ref:c,...i})});c.displayName="Button"},57387:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(40969),n=r(73356),a=r(21764);let i=n.forwardRef(({className:e,type:t,...r},n)=>(0,s.jsx)("input",{type:t,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...r}));i.displayName="Input"},60952:(e,t,r)=>{"use strict";function s(e,t,{checkForDefaultPrevented:r=!0}={}){return function(s){if(e?.(s),!1===r||!s.defaultPrevented)return t?.(s)}}r.d(t,{m:()=>s})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64680:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,q:()=>a});var s=r(73356),n=r(40969);function a(e,t){let r=s.createContext(t),a=e=>{let{children:t,...a}=e,i=s.useMemo(()=>a,Object.values(a));return(0,n.jsx)(r.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(n){let a=s.useContext(r);if(a)return a;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],a=()=>{let t=r.map(e=>s.createContext(e));return function(r){let n=r?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return a.scopeName=e,[function(t,a){let i=s.createContext(a),l=r.length;r=[...r,a];let o=t=>{let{scope:r,children:a,...o}=t,c=r?.[e]?.[l]||i,d=s.useMemo(()=>o,Object.values(o));return(0,n.jsx)(c.Provider,{value:d,children:a})};return o.displayName=t+"Provider",[o,function(r,n){let o=n?.[e]?.[l]||i,c=s.useContext(o);if(c)return c;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:s})=>{let n=r(e)[`__scope${s}`];return{...t,...n}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(a,...t)]}},66949:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>l});var s=r(40969),n=r(73356),a=r(21764);let i=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let l=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let o=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let c=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},70713:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(98085).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},79123:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(98085).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},79528:(e,t,r)=>{Promise.resolve().then(r.bind(r,43379))},79551:e=>{"use strict";e.exports=require("url")},92386:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(98085).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},92680:(e,t,r)=>{Promise.resolve().then(r.bind(r,36570))},93541:(e,t,r)=>{"use strict";r.d(t,{S:()=>_});var s=r(40969),n=r(73356),a=r(24629),i=r(64680),l=r(60952),o=r(26314),c=r(42291),d=r(39827),u=r(22195),f=r(81861),m="Checkbox",[x,h]=(0,i.A)(m),[p,g]=x(m);function v(e){let{__scopeCheckbox:t,checked:r,children:a,defaultChecked:i,disabled:l,form:c,name:d,onCheckedChange:u,required:f,value:x="on",internal_do_not_use_render:h}=e,[g,v]=(0,o.i)({prop:r,defaultProp:i??!1,onChange:u,caller:m}),[y,b]=n.useState(null),[w,N]=n.useState(null),j=n.useRef(!1),k=!y||!!c||!!y.closest("form"),A={checked:g,disabled:l,setChecked:v,control:y,setControl:b,name:d,form:c,value:x,hasConsumerStoppedPropagationRef:j,required:f,defaultChecked:!S(i)&&i,isFormControl:k,bubbleInput:w,setBubbleInput:N};return(0,s.jsx)(p,{scope:t,...A,children:"function"==typeof h?h(A):a})}var y="CheckboxTrigger",b=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...i},o)=>{let{control:c,value:d,disabled:u,checked:m,required:x,setControl:h,setChecked:p,hasConsumerStoppedPropagationRef:v,isFormControl:b,bubbleInput:w}=g(y,e),N=(0,a.s)(o,h),j=n.useRef(m);return n.useEffect(()=>{let e=c?.form;if(e){let t=()=>p(j.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,p]),(0,s.jsx)(f.sG.button,{type:"button",role:"checkbox","aria-checked":S(m)?"mixed":m,"aria-required":x,"data-state":C(m),"data-disabled":u?"":void 0,disabled:u,value:d,...i,ref:N,onKeyDown:(0,l.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(r,e=>{p(e=>!!S(e)||!e),w&&b&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})})});b.displayName=y;var w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:a,defaultChecked:i,required:l,disabled:o,value:c,onCheckedChange:d,form:u,...f}=e;return(0,s.jsx)(v,{__scopeCheckbox:r,checked:a,defaultChecked:i,disabled:o,required:l,onCheckedChange:d,name:n,form:u,value:c,internal_do_not_use_render:({isFormControl:e})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b,{...f,ref:t,__scopeCheckbox:r}),e&&(0,s.jsx)(A,{__scopeCheckbox:r})]})})});w.displayName=m;var N="CheckboxIndicator",j=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...a}=e,i=g(N,r);return(0,s.jsx)(u.C,{present:n||S(i.checked)||!0===i.checked,children:(0,s.jsx)(f.sG.span,{"data-state":C(i.checked),"data-disabled":i.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});j.displayName=N;var k="CheckboxBubbleInput",A=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:i,hasConsumerStoppedPropagationRef:l,checked:o,defaultChecked:u,required:m,disabled:x,name:h,value:p,form:v,bubbleInput:y,setBubbleInput:b}=g(k,e),w=(0,a.s)(r,b),N=(0,c.Z)(o),j=(0,d.X)(i);n.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(N!==o&&e){let r=new Event("click",{bubbles:t});y.indeterminate=S(o),e.call(y,!S(o)&&o),y.dispatchEvent(r)}},[y,N,o,l]);let A=n.useRef(!S(o)&&o);return(0,s.jsx)(f.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:u??A.current,required:m,disabled:x,name:h,value:p,form:v,...t,tabIndex:-1,ref:w,style:{...t.style,...j,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function S(e){return"indeterminate"===e}function C(e){return S(e)?"indeterminate":e?"checked":"unchecked"}A.displayName=k;var R=r(13315),M=r(21764);let _=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(w,{ref:r,className:(0,M.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:(0,s.jsx)(j,{className:(0,M.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(R.A,{className:"h-4 w-4"})})}));_.displayName=w.displayName}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[755,598,544,29,447],()=>r(39145));module.exports=s})();