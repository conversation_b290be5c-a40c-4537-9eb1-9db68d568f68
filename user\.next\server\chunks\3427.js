"use strict";exports.id=3427,exports.ids=[3427],exports.modules={4942:(e,t,s)=>{s.r(t),s.d(t,{default:()=>r});var a=s(62544);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},31825:(e,t,s)=>{s.d(t,{M7:()=>a,ik:()=>r});let{useGetUserProfileQuery:a,useUpdateUserProfileMutation:r,useUploadProfileImageMutation:i,useGetUserNotificationsQuery:l,useMarkNotificationAsReadMutation:o,useMarkAllNotificationsAsReadMutation:n,useDeleteNotificationMutation:d,useUpdateUserSettingsMutation:c,useGetUserActivityLogQuery:u,useDeleteUserAccountMutation:m,useExportUserDataMutation:x,useGetUserReferralCodeQuery:h,useGenerateNewReferralCodeMutation:f}=s(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getUserProfile:e.query({query:()=>"/users/profile",providesTags:["User"]}),updateUserProfile:e.mutation({query:e=>({url:"/users/profile",method:"PUT",body:e}),invalidatesTags:["User"]}),uploadProfileImage:e.mutation({query:e=>({url:"/users/upload-avatar",method:"POST",body:e}),invalidatesTags:["User"]}),getUserNotifications:e.query({query:({page:e=1,limit:t=20,unreadOnly:s=!1})=>({url:"/users/notifications",params:{page:e,limit:t,unreadOnly:s}}),providesTags:["Notification"],keepUnusedDataFor:120}),markNotificationAsRead:e.mutation({query:e=>({url:`/user/notifications/${e}/read`,method:"PUT"}),invalidatesTags:["Notification"]}),markAllNotificationsAsRead:e.mutation({query:()=>({url:"/user/notifications/mark-all-read",method:"PUT"}),invalidatesTags:["Notification"]}),deleteNotification:e.mutation({query:e=>({url:`/user/notifications/${e}`,method:"DELETE"}),invalidatesTags:["Notification"]}),updateUserSettings:e.mutation({query:e=>({url:"/user/settings",method:"PUT",body:e}),invalidatesTags:["User"]}),getUserActivityLog:e.query({query:({page:e=1,limit:t=20,type:s})=>({url:"/user/activity-log",params:{page:e,limit:t,...s&&{type:s}}}),providesTags:["User"],keepUnusedDataFor:300}),deleteUserAccount:e.mutation({query:e=>({url:"/user/delete-account",method:"DELETE",body:e}),invalidatesTags:["User"]}),exportUserData:e.mutation({query:e=>({url:"/user/export-data",method:"POST",body:e})}),getUserReferralCode:e.query({query:()=>"/user/referral-code",providesTags:["User"]}),generateNewReferralCode:e.mutation({query:()=>({url:"/user/referral-code/generate",method:"POST"}),invalidatesTags:["User"]})})})},37020:(e,t,s)=>{s.d(t,{A:()=>J});var a=s(40969),r=s(73356),i=s(21764),l=s(27092),o=s.n(l),n=s(12011),d=s(79249);let c=s(29079).d4;var u=s(61631),m=s(63122),x=s(67278),h=s(14216),f=s(45548),y=s(37124),p=s(1277),g=s(44869),b=s(28293),v=s(9041),N=s(7157),j=s(14125),w=s(29419),T=s(31745),k=s(26712),S=s(79929),q=s(60403),A=s(29501),C=s(49610),P=s(63980),U=s(83284),E=s(19854),D=s(26369),O=s(85239),Y=s(83241),K=s(86666),R=s(1077),F=s(95758),L=s(46411),I=s(75694);let M=[{title:"Dashboard",items:[{id:"dashboard",label:"Overview",icon:m.A,href:"/dashboard"},{id:"analytics",label:"Analytics",icon:x.A,href:"/dashboard/analytics"}]},{title:"Investments",items:[{id:"portfolio",label:"My Portfolio",icon:h.A,href:"/portfolio"},{id:"performance",label:"Performance",icon:f.A,href:"/portfolio/performance"},{id:"returns",label:"Returns",icon:y.A,href:"/portfolio/returns"},{id:"history",label:"History",icon:p.A,href:"/portfolio/history"}]},{title:"Properties",items:[{id:"browse",label:"Browse All",icon:g.A,href:"/properties"},{id:"featured",label:"Featured",icon:b.A,href:"/properties/featured"},{id:"wishlist",label:"Wishlist",icon:v.A,href:"/wishlist"},{id:"calculator",label:"Calculator",icon:N.A,href:"/calculator"}]},{title:"Referrals",items:[{id:"my-referrals",label:"My Network",icon:j.A,href:"/referrals"},{id:"referral-earnings",label:"Earnings",icon:w.A,href:"/referrals/earnings"},{id:"referral-analytics",label:"Analytics",icon:T.A,href:"/referrals/analytics"}]},{title:"Wallet",items:[{id:"wallet",label:"Balance",icon:k.A,href:"/wallet"},{id:"add-money",label:"Add Money",icon:S.A,href:"/wallet/add"},{id:"withdraw",label:"Withdraw",icon:q.A,href:"/wallet/withdraw"},{id:"transactions",label:"Transactions",icon:A.A,href:"/wallet/transactions"}]},{title:"Account",items:[{id:"profile",label:"My Profile",icon:C.A,href:"/profile"},{id:"kyc",label:"KYC Verification",icon:P.A,href:"/kyc"},{id:"settings",label:"Settings",icon:U.A,href:"/settings"}]},{title:"Support",items:[{id:"support-center",label:"Help Center",icon:E.A,href:"/support"},{id:"my-tickets",label:"My Tickets",icon:D.A,href:"/support/tickets"},{id:"faq",label:"FAQ",icon:O.A,href:"/support/faq"}]}];function $({className:e}){let t=(0,n.usePathname)(),[l,m]=(0,r.useState)(!1),[x,h]=(0,r.useState)(!1),f=c(u.xu),{data:y}=(0,d.xs)(),p=y?.data?.kyc?.status||f?.kycStatus,g=["/kyc","/login","/register","/dashboard","/","/support","/support/tickets","/support/faq"],b="approved"!==p&&"verified"!==p,v=()=>{h(!x)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(L.$,{variant:"ghost",size:"icon",className:"fixed top-4 left-4 z-50 md:hidden",onClick:v,children:x?(0,a.jsx)(Y.A,{className:"h-6 w-6"}):(0,a.jsx)(K.A,{className:"h-6 w-6"})}),x&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-40 md:hidden",onClick:v}),(0,a.jsxs)("aside",{className:(0,i.cn)("fixed left-0 top-0 z-50 h-screen bg-white border-r border-gray-200 shadow-lg transition-all duration-300 ease-in-out flex flex-col",l?"w-16":"w-64",x?"translate-x-0":"-translate-x-full md:translate-x-0",e),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-sky-100 flex-shrink-0",children:[(0,a.jsx)(I.A,{variant:l?"icon":"full",size:"md",className:l?"mx-auto":""}),(0,a.jsx)(L.$,{variant:"ghost",size:"icon",onClick:()=>{m(!l)},className:"hidden md:flex text-sky-600 hover:bg-sky-50",children:l?(0,a.jsx)(R.A,{className:"h-4 w-4"}):(0,a.jsx)(F.A,{className:"h-4 w-4"})})]}),b&&!l&&(0,a.jsxs)("div",{className:"mx-4 mt-4 p-4 bg-gradient-to-r from-blue-50 to-sky-50 border border-blue-200 rounded-xl",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-sm font-bold text-blue-800",children:"under_review"===p?"KYC Under Review":"KYC Required"})]}),(0,a.jsx)("p",{className:"text-xs text-blue-700 mb-3",children:"under_review"===p?"Your documents are being verified. Limited access until complete.":"Complete KYC verification to access all features."}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>window.location.href="/kyc",className:"text-xs bg-blue-600 text-white px-3 py-1 rounded-full hover:bg-blue-700 transition-colors",children:"under_review"===p?"View Status":"Start KYC"}),(0,a.jsx)("button",{onClick:()=>window.location.href="/support",className:"text-xs bg-white text-blue-600 px-3 py-1 rounded-full border border-blue-300 hover:bg-blue-50 transition-colors",children:"Support"})]})]}),(0,a.jsx)("nav",{className:"flex-1 overflow-y-auto p-6 space-y-6",children:M.map(e=>(0,a.jsxs)("div",{children:[!l&&(0,a.jsx)("h3",{className:"text-xs font-bold text-black uppercase tracking-wider mb-4 px-2",children:e.title}),(0,a.jsx)("ul",{className:"space-y-2",children:e.items.map(e=>{let r=t===e.href,n=e.icon;return b&&!g.includes(e.href)?(0,a.jsx)("li",{children:(0,a.jsxs)("div",{className:(0,i.cn)("relative flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200","text-gray-400 cursor-not-allowed opacity-50 bg-gray-50 border border-dashed border-gray-300 select-none",l&&"justify-center px-3"),title:"Complete KYC verification to access this feature",onClick:e=>{e.preventDefault(),e.stopPropagation(),Promise.resolve().then(s.bind(s,1507)).then(({toast:e})=>{e.error("KYC verification required",{description:"Complete your KYC to access this feature",action:{label:"Go to KYC",onClick:()=>window.location.href="/kyc"}})})},children:[(0,a.jsx)(n,{className:"h-5 w-5 text-gray-400"}),!l&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"flex-1",children:e.label}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"\uD83D\uDD12"})]}),l&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-gray-400 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xs text-white",children:"!"})})]})},e.id):(0,a.jsx)("li",{children:(0,a.jsxs)(o(),{href:e.href,onClick:()=>h(!1),className:(0,i.cn)("flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200",r?"bg-sky-500 text-white shadow-lg transform scale-105":"text-black hover:bg-sky-50 hover:text-sky-700 hover:shadow-md",l&&"justify-center px-3"),children:[(0,a.jsx)(n,{className:(0,i.cn)("h-5 w-5",r?"text-white":"text-sky-600")}),!l&&(0,a.jsx)("span",{children:e.label})]})},e.id)})})]},e.title))}),(0,a.jsx)("div",{className:"p-6 border-t-2 border-sky-200 bg-sky-50",children:!l&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xs font-bold text-black mb-1",children:"SGM"}),(0,a.jsx)("div",{className:"text-xs text-sky-600",children:"User Panel v1.0.0"})]})})]})]})}var z=s(71708),_=s(9343),G=s(55398),H=s(92462),V=s(40002),W=s(46173),B=s(31825),Q=s(1507);function Z({className:e}){let t=(0,n.useRouter)(),s=(0,H.GV)(u.xu),[l]=(0,V.Ng)(),{data:o}=(0,W.FZ)({}),c=o?.data?.data||[],{data:m}=(0,B.M7)(),x=m?.data||s,{data:h}=(0,d.xs)(),f=h?.data?.kyc?.status||x?.kycStatus,[y,p]=(0,r.useState)(!1),[b,v]=(0,r.useState)(!1),[N,j]=(0,r.useState)(!1),[w,T]=(0,r.useState)(""),S=c.filter(e=>!e.isRead).length,q=async()=>{try{await l().unwrap(),Q.toast.success("Logged out successfully"),t.push("/login")}catch(e){console.error("Logout error:",e),Q.toast.error("Failed to logout")}},A=e=>{switch(e){case"success":return"✅";case"warning":return"⚠️";case"error":return"❌";default:return"ℹ️"}};return(0,a.jsxs)("header",{className:(0,i.cn)("bg-white border-b border-gray-200 px-4 md:px-6 py-3 shadow-sm",e),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4 flex-1 max-w-md",children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),w.trim()&&t.push(`/search?q=${encodeURIComponent(w)}`)},className:"relative w-full",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search properties, investments...",value:w,onChange:e=>T(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white text-sm"})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-3 bg-sky-50 px-4 py-2 rounded-xl border border-sky-200",children:[(0,a.jsx)(k.A,{className:"h-5 w-5 text-sky-600"}),(0,a.jsx)("span",{className:"text-sm font-bold text-black",children:(0,i.vv)(x?.wallet?.balance||0)})]}),"approved"!==f&&(0,a.jsxs)("div",{className:(0,i.cn)("hidden md:flex items-center space-x-2 px-3 py-2 rounded-xl text-xs font-bold","under_review"===f||"pending_verification"===f?"bg-yellow-50 text-yellow-700 border border-yellow-200":"rejected"===f?"bg-red-50 text-red-700 border border-red-200":"bg-gray-50 text-gray-700 border border-gray-200"),children:[(0,a.jsx)(P.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"under_review"===f?"⏳ Under Review":"pending_verification"===f?"⏳ Pending":"rejected"===f?"❌ Rejected":"\uD83D\uDCCB KYC Required"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(L.$,{variant:"ghost",size:"icon",onClick:()=>p(!y),className:"relative h-10 w-10 rounded-xl hover:bg-sky-50 text-black",children:[(0,a.jsx)(z.A,{className:"h-5 w-5 text-sky-600"}),S>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-yellow-500 text-black text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold",children:S})]}),y&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50",children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Notifications"})}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:c.length>0?c.map(e=>(0,a.jsx)("div",{className:(0,i.cn)("p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer",!e.isRead&&"bg-blue-50"),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("span",{className:"text-lg",children:A(e.type)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:new Date(e.createdAt).toLocaleDateString()})]}),!e.isRead&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})]})},e._id)):(0,a.jsxs)("div",{className:"p-8 text-center text-gray-500",children:[(0,a.jsx)(z.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"No notifications yet"})]})}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,a.jsx)(L.$,{variant:"ghost",className:"w-full text-blue-600 hover:text-blue-700",onClick:()=>t.push("/notifications"),children:"View All Notifications"})})]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(L.$,{variant:"ghost",onClick:()=>v(!b),className:"flex items-center space-x-3 px-4 py-2 rounded-xl hover:bg-sky-50",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-sky-500 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg",children:x?.avatar?.url?(0,a.jsx)("img",{src:x.avatar.url,alt:"Profile",className:"w-10 h-10 rounded-full"}):(0,i.IM)(x?.firstName||"U",x?.lastName||"U")}),(0,a.jsxs)("div",{className:"hidden md:block text-left",children:[(0,a.jsx)("p",{className:"text-sm font-bold text-black",children:x?.firstName||"User"}),(0,a.jsx)("p",{className:"text-xs text-sky-600",children:x?.email||"<EMAIL>"})]}),(0,a.jsx)(_.A,{className:"h-4 w-4 text-sky-600"})]}),b&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50",children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white text-lg font-medium",children:s?.avatar?(0,a.jsx)("img",{src:"string"==typeof s.avatar?s.avatar:s.avatar?.url,alt:"Profile",className:"w-12 h-12 rounded-full"}):(0,i.IM)(s?.firstName||"U",s?.lastName||"U")}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-900",children:[s?.firstName," ",s?.lastName]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:s?.email})]})]})}),(0,a.jsxs)("div",{className:"py-2",children:[(0,a.jsxs)("button",{onClick:()=>t.push("/profile"),className:"flex items-center space-x-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(C.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"My Profile"})]}),(0,a.jsxs)("button",{onClick:()=>t.push("/wallet"),className:"flex items-center space-x-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"My Wallet"})]}),(0,a.jsxs)("button",{onClick:()=>t.push("/settings"),className:"flex items-center space-x-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(U.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Settings"})]}),(0,a.jsxs)("button",{onClick:()=>t.push("/support"),className:"flex items-center space-x-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(O.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Help & Support"})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 py-2",children:(0,a.jsxs)("button",{onClick:q,className:"flex items-center space-x-3 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50",children:[(0,a.jsx)(G.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Sign Out"})]})})]})]})]})]}),(y||b)&&(0,a.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>{p(!1),v(!1)}})]})}function J({children:e,className:t,restrictSidebar:s=!1}){return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[!s&&(0,a.jsx)($,{}),(0,a.jsxs)("div",{className:(0,i.cn)("transition-all duration-300",s?"ml-0":"ml-0 md:ml-64"),children:[(0,a.jsx)(Z,{className:"sticky top-0 z-30 bg-white border-b border-gray-200"}),(0,a.jsx)("main",{className:(0,i.cn)("p-4 md:p-6",t),children:e})]})]})}},40002:(e,t,s)=>{s.d(t,{B3:()=>n,Lv:()=>u,Ng:()=>i,_L:()=>a,ac:()=>f,ge:()=>r,nd:()=>d,pv:()=>y,tl:()=>c,uU:()=>o});let{useLoginMutation:a,useRegisterMutation:r,useLogoutMutation:i,useRefreshTokenMutation:l,useVerifyEmailMutation:o,useResendVerificationEmailMutation:n,useForgotPasswordMutation:d,useVerifyPasswordResetOTPMutation:c,useResetPasswordMutation:u,useChangePasswordMutation:m,useVerifyPhoneMutation:x,useSendPhoneOTPMutation:h,useSendEmailOTPMutation:f,useVerifyEmailOTPMutation:y,useGetCurrentUserQuery:p,useGetUserProfileQuery:g,useCheckAuthStatusQuery:b}=s(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({login:e.mutation({query:e=>({url:"/auth/login",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),register:e.mutation({query:e=>({url:"/auth/register",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),logout:e.mutation({query:()=>({url:"/auth/logout",method:"POST"}),invalidatesTags:["Auth","User"]}),refreshToken:e.mutation({query:e=>({url:"/auth/refresh",method:"POST",body:e})}),verifyEmail:e.mutation({query:e=>({url:"/auth/verify-email",method:"POST",body:e}),invalidatesTags:["User"]}),resendVerificationEmail:e.mutation({query:e=>({url:"/auth/send-verification",method:"POST",body:e})}),forgotPassword:e.mutation({query:e=>({url:"/auth/forgot-password",method:"POST",body:e})}),verifyPasswordResetOTP:e.mutation({query:e=>({url:"/auth/verify-reset-otp",method:"POST",body:e})}),resetPassword:e.mutation({query:e=>({url:"/auth/reset-password",method:"POST",body:e})}),changePassword:e.mutation({query:e=>({url:"/auth/change-password",method:"POST",body:e}),invalidatesTags:["User"]}),verifyPhone:e.mutation({query:e=>({url:"/auth/verify-phone",method:"POST",body:e}),invalidatesTags:["User"]}),sendPhoneOTP:e.mutation({query:e=>({url:"/auth/send-phone-otp",method:"POST",body:e})}),sendEmailOTP:e.mutation({query:e=>({url:"/auth/send-otp",method:"POST",body:e})}),verifyEmailOTP:e.mutation({query:e=>({url:"/auth/verify-otp",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),getCurrentUser:e.query({query:()=>"/auth/me",providesTags:["User"]}),checkAuthStatus:e.query({query:()=>"/auth/status",providesTags:["Auth"]}),getUserProfile:e.query({query:()=>"/auth/profile",providesTags:["User","Auth"]})})})},46173:(e,t,s)=>{s.d(t,{FZ:()=>a,b7:()=>i,d1:()=>l,nC:()=>o});let{useGetUserNotificationsQuery:a,useGetNotificationByIdQuery:r,useMarkNotificationAsReadMutation:i,useMarkAllNotificationsAsReadMutation:l,useDeleteNotificationMutation:o,useDeleteAllReadNotificationsMutation:n,useGetNotificationStatsQuery:d,useGetNotificationPreferencesQuery:c,useUpdateNotificationPreferencesMutation:u,useSubscribeToPushNotificationsMutation:m,useUnsubscribeFromPushNotificationsMutation:x,useTestNotificationMutation:h}=s(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getUserNotifications:e.query({query:e=>({url:"/users/notifications",params:{page:e.page||1,limit:e.limit||20,...void 0!==e.unreadOnly&&{unreadOnly:e.unreadOnly},...e.type&&{type:e.type},...e.category&&{category:e.category},...e.priority&&{priority:e.priority},...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate}}}),providesTags:e=>e?.data?.data?[...e.data.data.map(({_id:e})=>({type:"Notification",id:e})),{type:"Notification",id:"LIST"}]:[{type:"Notification",id:"LIST"}],keepUnusedDataFor:120}),getNotificationById:e.query({query:e=>`/users/notifications/${e}`,providesTags:(e,t,s)=>[{type:"Notification",id:s}],keepUnusedDataFor:600}),markNotificationAsRead:e.mutation({query:e=>({url:`/users/notifications/${e}/read`,method:"PATCH"}),invalidatesTags:(e,t,s)=>[{type:"Notification",id:s},{type:"Notification",id:"LIST"},{type:"Notification",id:"STATS"}]}),markAllNotificationsAsRead:e.mutation({query:()=>({url:"/users/notifications/read-all",method:"PATCH"}),invalidatesTags:[{type:"Notification",id:"LIST"},{type:"Notification",id:"STATS"}]}),deleteNotification:e.mutation({query:e=>({url:`/users/notifications/${e}`,method:"DELETE"}),invalidatesTags:(e,t,s)=>[{type:"Notification",id:s},{type:"Notification",id:"LIST"},{type:"Notification",id:"STATS"}]}),deleteAllReadNotifications:e.mutation({query:()=>({url:"/users/notifications/delete-read",method:"DELETE"}),invalidatesTags:[{type:"Notification",id:"LIST"},{type:"Notification",id:"STATS"}]}),getNotificationStats:e.query({query:()=>"/users/notifications/stats",providesTags:[{type:"Notification",id:"STATS"}],keepUnusedDataFor:300}),getNotificationPreferences:e.query({query:()=>"/users/notifications/preferences",providesTags:[{type:"Notification",id:"PREFERENCES"}],keepUnusedDataFor:1800}),updateNotificationPreferences:e.mutation({query:e=>({url:"/users/notifications/preferences",method:"PUT",body:e}),invalidatesTags:[{type:"Notification",id:"PREFERENCES"}]}),subscribeToPushNotifications:e.mutation({query:e=>({url:"/users/notifications/push/subscribe",method:"POST",body:e})}),unsubscribeFromPushNotifications:e.mutation({query:()=>({url:"/users/notifications/push/unsubscribe",method:"POST"})}),testNotification:e.mutation({query:e=>({url:"/users/notifications/test",method:"POST",body:e})})})})},75694:(e,t,s)=>{s.d(t,{A:()=>r});var a=s(40969);s(73356);let r=({size:e="md",variant:t="full",className:s=""})=>{let r={sm:"h-8 w-8",md:"h-12 w-12",lg:"h-16 w-16",xl:"h-24 w-24"},i={sm:"text-lg",md:"text-2xl",lg:"text-3xl",xl:"text-4xl"},l=()=>(0,a.jsx)("div",{className:`${r[e]} ${s} relative`,children:(0,a.jsxs)("svg",{viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"w-full h-full",children:[(0,a.jsx)("circle",{cx:"50",cy:"50",r:"48",fill:"#0ea5e9",stroke:"#ffffff",strokeWidth:"2"}),(0,a.jsxs)("g",{transform:"translate(20, 25)",children:[(0,a.jsx)("rect",{x:"15",y:"20",width:"30",height:"35",fill:"#ffffff",rx:"2"}),(0,a.jsx)("rect",{x:"20",y:"25",width:"6",height:"6",fill:"#0ea5e9"}),(0,a.jsx)("rect",{x:"34",y:"25",width:"6",height:"6",fill:"#0ea5e9"}),(0,a.jsx)("rect",{x:"20",y:"35",width:"6",height:"6",fill:"#0ea5e9"}),(0,a.jsx)("rect",{x:"34",y:"35",width:"6",height:"6",fill:"#0ea5e9"}),(0,a.jsx)("rect",{x:"27",y:"45",width:"6",height:"10",fill:"#fbbf24"}),(0,a.jsx)("rect",{x:"5",y:"30",width:"8",height:"25",fill:"#ffffff",rx:"1"}),(0,a.jsx)("rect",{x:"47",y:"35",width:"8",height:"20",fill:"#ffffff",rx:"1"}),(0,a.jsx)("path",{d:"M10 15 L20 5 L30 10 L40 2 L50 8",stroke:"#fbbf24",strokeWidth:"3",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("polygon",{points:"45,2 50,8 45,8",fill:"#fbbf24"})]}),(0,a.jsx)("text",{x:"50",y:"75",textAnchor:"middle",fill:"#ffffff",fontSize:"12",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:"SGM"})]})});switch(t){case"icon":return(0,a.jsx)(l,{});case"text":return(0,a.jsx)(()=>(0,a.jsxs)("div",{className:`${s} flex items-center`,children:[(0,a.jsx)("span",{className:`${i[e]} font-bold sgm-accent-text`,children:"SGM"}),(0,a.jsx)("span",{className:`${i[e]} font-light sgm-primary-text ml-1`,children:"Investments"})]}),{});default:return(0,a.jsx)(()=>(0,a.jsxs)("div",{className:`${s} flex items-center space-x-3`,children:[(0,a.jsx)(l,{}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:`${i[e]} font-bold sgm-accent-text leading-none`,children:"SGM"}),(0,a.jsx)("span",{className:"text-sm sgm-primary-text leading-none",children:"Investments"})]})]}),{})}}},79249:(e,t,s)=>{s.d(t,{Gy:()=>n,VI:()=>o,Yx:()=>x,cs:()=>m,et:()=>p,fW:()=>i,fx:()=>u,i6:()=>d,jY:()=>l,xs:()=>a});let{useGetKYCStatusQuery:a,useGetKYCRequirementsQuery:r,useGetKYCDocumentsQuery:i,useGetKYCPersonalInfoQuery:l,useGetKYCAddressInfoQuery:o,useUploadKYCDocumentMutation:n,useGetKYCPresignedUrlMutation:d,useDeleteKYCDocumentMutation:c,useSubmitKYCVerificationMutation:u,useUpdatePersonalInfoMutation:m,useUpdateAddressInfoMutation:x,useGetKYCHistoryQuery:h,useRequestKYCUpgradeMutation:f,useGetKYCVerificationErrorsQuery:y,useDownloadKYCPDFMutation:p}=s(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getKYCStatus:e.query({query:()=>"/kyc",providesTags:[{type:"KYC",id:"STATUS"}],keepUnusedDataFor:300}),getKYCRequirements:e.query({query:()=>"/kyc/requirements",providesTags:[{type:"KYC",id:"REQUIREMENTS"}],keepUnusedDataFor:3600}),getKYCDocuments:e.query({query:()=>"/kyc/documents",providesTags:[{type:"KYC",id:"DOCUMENTS"}],keepUnusedDataFor:600}),getKYCPersonalInfo:e.query({query:()=>"/kyc/personal-info",providesTags:[{type:"KYC",id:"PERSONAL_INFO"}],keepUnusedDataFor:600}),getKYCAddressInfo:e.query({query:()=>"/kyc/address-info",providesTags:[{type:"KYC",id:"ADDRESS_INFO"}],keepUnusedDataFor:600}),uploadKYCDocument:e.mutation({query:e=>{if(e.file){let t=new FormData;return Object.keys(e).forEach(s=>{"file"===s?t.append("document",e.file):void 0!==e[s]&&t.append(s,String(e[s]))}),{url:"/kyc/document",method:"POST",body:t,formData:!0}}{let{file:t,...s}=e;return{url:"/kyc/document",method:"POST",body:s}}},invalidatesTags:[{type:"KYC",id:"DOCUMENTS"},{type:"KYC",id:"STATUS"}]}),getKYCPresignedUrl:e.mutation({query:e=>({url:"/s3/presigned-url",method:"POST",body:e})}),deleteKYCDocument:e.mutation({query:e=>({url:`/kyc/documents/${e}`,method:"DELETE"}),invalidatesTags:[{type:"KYC",id:"DOCUMENTS"},{type:"KYC",id:"STATUS"}]}),submitKYCVerification:e.mutation({query:()=>({url:"/kyc/submit",method:"POST"}),invalidatesTags:[{type:"KYC",id:"STATUS"}]}),updatePersonalInfo:e.mutation({query:e=>({url:"/kyc",method:"PUT",body:e}),invalidatesTags:[{type:"KYC",id:"STATUS"}]}),updateAddressInfo:e.mutation({query:e=>({url:"/kyc/address",method:"PUT",body:e}),invalidatesTags:[{type:"KYC",id:"STATUS"}]}),getKYCHistory:e.query({query:()=>"/kyc/history",providesTags:[{type:"KYC",id:"HISTORY"}],keepUnusedDataFor:900}),requestKYCUpgrade:e.mutation({query:e=>({url:"/kyc/upgrade",method:"POST",body:e}),invalidatesTags:[{type:"KYC",id:"STATUS"}]}),getKYCVerificationErrors:e.query({query:()=>"/kyc/verification-errors",providesTags:[{type:"KYC",id:"ERRORS"}],keepUnusedDataFor:600}),downloadKYCPDF:e.mutation({query:()=>({url:"/kyc/download-pdf",method:"GET",responseHandler:e=>e.blob()})})})})}};