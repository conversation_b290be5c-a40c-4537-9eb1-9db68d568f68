"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[366],{783:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(6501).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1480:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(6501).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},1817:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(6501).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},2266:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(6501).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},3480:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(6501).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4281:(e,t,r)=>{r.d(t,{X:()=>u});var n=r(9585),o=r(6921);function u(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let u=t[0];if("borderBoxSize"in u){let e=u.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},4761:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},4842:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(6501).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},4853:(e,t,r)=>{r.d(t,{C:()=>i});var n=r(9585),o=r(4455),u=r(6921),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),a=n.useRef(null),c=n.useRef(e),s=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=l(a.current);s.current="mounted"===d?e:"none"},[d]),(0,u.N)(()=>{let t=a.current,r=c.current;if(r!==e){let n=s.current,o=l(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,u.N)(()=>{if(o){var e;let t,r=null!=(e=o.ownerDocument.defaultView)?e:window,n=e=>{let n=l(a.current).includes(e.animationName);if(e.target===o&&n&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},u=e=>{e.target===o&&(s.current=l(a.current))};return o.addEventListener("animationstart",u),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",u),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(t),a="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),c=(0,o.s)(i.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||i.isPresent?n.cloneElement(a,{ref:c}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},5509:(e,t,r)=>{r.d(t,{C1:()=>A,bL:()=>g});var n=r(9585),o=r(4455),u=r(8972),i=r(4761),l=r(9728),a=r(8069),c=r(4281),s=r(4853),d=r(7905),f=r(9605),p="Checkbox",[m,v]=(0,u.A)(p),[y,h]=m(p);function k(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:u,disabled:i,form:a,name:c,onCheckedChange:s,required:d,value:m="on",internal_do_not_use_render:v}=e,[h,k]=(0,l.i)({prop:r,defaultProp:null!=u&&u,onChange:s,caller:p}),[N,b]=n.useState(null),[g,x]=n.useState(null),A=n.useRef(!1),w=!N||!!a||!!N.closest("form"),M={checked:h,disabled:i,setChecked:k,control:N,setControl:b,name:c,form:a,value:m,hasConsumerStoppedPropagationRef:A,required:d,defaultChecked:!E(u)&&u,isFormControl:w,bubbleInput:g,setBubbleInput:x};return(0,f.jsx)(y,{scope:t,...M,children:"function"==typeof v?v(M):o})}var N="CheckboxTrigger",b=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:u,onClick:l,...a}=e,{control:c,value:s,disabled:p,checked:m,required:v,setControl:y,setChecked:k,hasConsumerStoppedPropagationRef:b,isFormControl:g,bubbleInput:x}=h(N,r),A=(0,o.s)(t,y),w=n.useRef(m);return n.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let t=()=>k(w.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,k]),(0,f.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":E(m)?"mixed":m,"aria-required":v,"data-state":C(m),"data-disabled":p?"":void 0,disabled:p,value:s,...a,ref:A,onKeyDown:(0,i.m)(u,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(l,e=>{k(e=>!!E(e)||!e),x&&g&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});b.displayName=N;var g=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:u,required:i,disabled:l,value:a,onCheckedChange:c,form:s,...d}=e;return(0,f.jsx)(k,{__scopeCheckbox:r,checked:o,defaultChecked:u,disabled:l,required:i,onCheckedChange:c,name:n,form:s,value:a,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(b,{...d,ref:t,__scopeCheckbox:r}),n&&(0,f.jsx)(M,{__scopeCheckbox:r})]})}})});g.displayName=p;var x="CheckboxIndicator",A=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,u=h(x,r);return(0,f.jsx)(s.C,{present:n||E(u.checked)||!0===u.checked,children:(0,f.jsx)(d.sG.span,{"data-state":C(u.checked),"data-disabled":u.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});A.displayName=x;var w="CheckboxBubbleInput",M=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...u}=e,{control:i,hasConsumerStoppedPropagationRef:l,checked:s,defaultChecked:p,required:m,disabled:v,name:y,value:k,form:N,bubbleInput:b,setBubbleInput:g}=h(w,r),x=(0,o.s)(t,g),A=(0,a.Z)(s),M=(0,c.X)(i);n.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(A!==s&&e){let r=new Event("click",{bubbles:t});b.indeterminate=E(s),e.call(b,!E(s)&&s),b.dispatchEvent(r)}},[b,A,s,l]);let C=n.useRef(!E(s)&&s);return(0,f.jsx)(d.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:C.current,required:m,disabled:v,name:y,value:k,form:N,...u,tabIndex:-1,ref:x,style:{...u.style,...M,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function E(e){return"indeterminate"===e}function C(e){return E(e)?"indeterminate":e?"checked":"unchecked"}M.displayName=w},6901:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(6501).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},6921:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(9585),o=globalThis?.document?n.useLayoutEffect:()=>{}},8069:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(9585);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},8972:(e,t,r)=>{r.d(t,{A:()=>i,q:()=>u});var n=r(9585),o=r(9605);function u(e,t){let r=n.createContext(t),u=e=>{let{children:t,...u}=e,i=n.useMemo(()=>u,Object.values(u));return(0,o.jsx)(r.Provider,{value:i,children:t})};return u.displayName=e+"Provider",[u,function(o){let u=n.useContext(r);if(u)return u;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],u=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return u.scopeName=e,[function(t,u){let i=n.createContext(u),l=r.length;r=[...r,u];let a=t=>{let{scope:r,children:u,...a}=t,c=r?.[e]?.[l]||i,s=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(c.Provider,{value:s,children:u})};return a.displayName=t+"Provider",[a,function(r,o){let a=o?.[e]?.[l]||i,c=n.useContext(a);if(c)return c;if(void 0!==u)return u;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(u,...t)]}},9192:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(6501).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9728:(e,t,r)=>{r.d(t,{i:()=>l});var n,o=r(9585),u=r(6921),i=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.N;function l({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[u,l,a]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),u=o.useRef(r),l=o.useRef(t);return i(()=>{l.current=t},[t]),o.useEffect(()=>{u.current!==r&&(l.current?.(r),u.current=r)},[r,u]),[r,n,l]}({defaultProp:t,onChange:r}),c=void 0!==e,s=c?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,n])}return[s,o.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&a.current?.(r)}else l(t)},[c,e,l,a])]}Symbol("RADIX:SYNC_STATE")}}]);