(()=>{var e={};e.id=1720,e.ids=[1720],e.modules={2036:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(99024).A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},2132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});let i=(0,t(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\wishlist\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\wishlist\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5568:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(99024).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24689:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(99024).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},28149:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(99024).A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47364:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(99024).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},54829:(e,s,t)=>{"use strict";t.d(s,{EF:()=>i,U0:()=>r,e6:()=>a});let{useGetUserWishlistQuery:i,useAddToWishlistMutation:r,useRemoveFromWishlistMutation:a,useCheckWishlistStatusQuery:l,useSetPriceAlertMutation:d,useRemovePriceAlertMutation:n,useGetPopularPropertiesQuery:c,useGetWishlistStatsQuery:o,useClearWishlistMutation:h,useExportWishlistMutation:x}=t(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getUserWishlist:e.query({query:({page:e=1,limit:s=12})=>({url:"/wishlist",params:{page:e,limit:s}}),providesTags:[{type:"Wishlist",id:"LIST"}],keepUnusedDataFor:300}),addToWishlist:e.mutation({query:e=>({url:"/wishlist",method:"POST",body:{propertyId:e}}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),removeFromWishlist:e.mutation({query:e=>({url:`/wishlist/${e}`,method:"DELETE"}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),checkWishlistStatus:e.query({query:e=>`/wishlist/check/${e}`,providesTags:(e,s,t)=>[{type:"Wishlist",id:`check-${t}`}],keepUnusedDataFor:600}),setPriceAlert:e.mutation({query:({propertyId:e,...s})=>({url:`/wishlist/${e}/price-alert`,method:"POST",body:s}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),removePriceAlert:e.mutation({query:e=>({url:`/wishlist/${e}/price-alert`,method:"DELETE"}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),getPopularProperties:e.query({query:({limit:e=10})=>({url:"/wishlist/popular",params:{limit:e}}),providesTags:[{type:"Wishlist",id:"POPULAR"}],keepUnusedDataFor:1800}),getWishlistStats:e.query({query:()=>"/wishlist/stats",providesTags:[{type:"Wishlist",id:"STATS"}],keepUnusedDataFor:600}),clearWishlist:e.mutation({query:()=>({url:"/wishlist/clear",method:"DELETE"}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),exportWishlist:e.mutation({query:e=>({url:"/wishlist/export",method:"POST",body:e})})})})},56286:(e,s,t)=>{Promise.resolve().then(t.bind(t,69498))},61014:(e,s,t)=>{Promise.resolve().then(t.bind(t,2132))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69498:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var i=t(40969),r=t(73356),a=t(37020),l=t(66949),d=t(46411),n=t(2036),c=t(91473),o=t(47364),h=t(24689),x=t(28293),p=t(9041),m=t(58573),u=t(5568),y=t(28149);let g=(0,t(99024).A)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);var j=t(45548),v=t(21764),f=t(54829),w=t(1507);function b(){let[e,s]=(0,r.useState)("grid"),[t,b]=(0,r.useState)("all"),[N,k]=(0,r.useState)([]),{data:A,isLoading:T,refetch:P}=(0,f.EF)({limit:50}),[q]=(0,f.e6)(),S=A?.data?.data||[],M=async e=>{try{await q(e).unwrap(),w.toast.success("Property removed from wishlist"),P()}catch(e){w.toast.error("Failed to remove property from wishlist")}},E=async()=>{try{await Promise.all(N.map(e=>q(e).unwrap())),w.toast.success(`${N.length} properties removed from wishlist`),k([]),P()}catch(e){w.toast.error("Failed to remove properties from wishlist")}},W=e=>{k(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},C=S.filter(e=>"all"===t||("featured"===t?e.featured:e.type===t));return T?(0,i.jsx)(a.A,{children:(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"animate-pulse",children:[(0,i.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(e=>(0,i.jsx)("div",{className:"h-80 bg-gray-200 rounded-lg"},e))})]})})}):(0,i.jsx)(a.A,{children:(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Wishlist"}),(0,i.jsx)("p",{className:"text-gray-600 mt-1",children:"Properties you've saved for later"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(d.$,{variant:"grid"===e?"default":"outline",size:"sm",onClick:()=>s("grid"),children:(0,i.jsx)(n.A,{className:"h-4 w-4"})}),(0,i.jsx)(d.$,{variant:"list"===e?"default":"outline",size:"sm",onClick:()=>s("list"),children:(0,i.jsx)(c.A,{className:"h-4 w-4"})})]})]}),(0,i.jsx)(l.Zp,{children:(0,i.jsx)(l.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("select",{value:t,onChange:e=>b(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[{value:"all",label:"All Properties"},{value:"residential",label:"Residential"},{value:"commercial",label:"Commercial"},{value:"mixed",label:"Mixed Use"},{value:"featured",label:"Featured Only"}].map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))}),S.length>0&&(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:N.length===S.length,onChange:()=>{N.length===S.length?k([]):k(S.map(e=>e._id))},className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,i.jsxs)("span",{className:"text-sm text-gray-600",children:["Select All (",N.length," selected)"]})]})]}),N.length>0&&(0,i.jsx)("div",{className:"flex items-center space-x-3",children:(0,i.jsxs)(d.$,{variant:"outline",size:"sm",onClick:E,className:"flex items-center space-x-2",children:[(0,i.jsx)(o.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{children:["Remove Selected (",N.length,")"]})]})})]})})}),(0,i.jsx)("div",{className:"flex items-center justify-between",children:(0,i.jsxs)("p",{className:"text-gray-600",children:[C.length," properties in your wishlist"]})}),C.length>0?(0,i.jsx)("div",{className:"grid"===e?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:C.map(e=>(0,i.jsxs)(l.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("div",{className:"absolute top-3 left-3 z-10",children:(0,i.jsx)("input",{type:"checkbox",checked:N.includes(e._id),onChange:()=>W(e._id),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded bg-white"})}),e.images&&e.images.length>0?(0,i.jsx)("img",{src:e.images[0].url,alt:e.name,className:"w-full h-48 object-cover rounded-t-lg"}):(0,i.jsx)("div",{className:"w-full h-48 bg-gray-200 rounded-t-lg flex items-center justify-center",children:(0,i.jsx)(h.A,{className:"h-12 w-12 text-gray-400"})}),e.featured&&(0,i.jsxs)("div",{className:"absolute top-3 right-12 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center",children:[(0,i.jsx)(x.A,{className:"h-3 w-3 mr-1"}),"Featured"]}),(0,i.jsx)("button",{onClick:()=>M(e._id),className:"absolute top-3 right-3 p-2 bg-white rounded-full shadow-md hover:bg-red-50 text-red-600",children:(0,i.jsx)(p.A,{className:"h-4 w-4 fill-current"})})]}),(0,i.jsx)(l.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-xl font-semibold",children:e.name}),(0,i.jsxs)("div",{className:"flex items-center text-gray-600 text-sm mt-1",children:[(0,i.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"string"==typeof e.location?e.location:e.location?.address||e.location?.city||"Location not specified"]})]}),(0,i.jsx)("p",{className:"text-gray-600 text-sm line-clamp-2",children:e.description}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Stock Price"}),(0,i.jsx)("div",{className:"font-semibold",children:(0,v.vv)(e.pricePerStock)})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Expected Returns"}),(0,i.jsx)("div",{className:"font-semibold text-green-600",children:(0,v.Ee)(e.roi)})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Available"}),(0,i.jsxs)("div",{className:"font-semibold",children:[e.availableStocks," stocks"]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Type"}),(0,i.jsx)("div",{className:"font-semibold capitalize",children:e.type})]})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t space-x-2",children:[(0,i.jsxs)(d.$,{variant:"outline",size:"sm",className:"flex-1 flex items-center justify-center space-x-1",children:[(0,i.jsx)(u.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:"View"})]}),(0,i.jsx)(d.$,{variant:"outline",size:"sm",className:"flex items-center justify-center",children:(0,i.jsx)(y.A,{className:"h-4 w-4"})}),(0,i.jsxs)(d.$,{size:"sm",className:"flex-1 flex items-center justify-center space-x-1",children:[(0,i.jsx)(g,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:"Invest"})]})]})]})})]},e._id))}):(0,i.jsx)(l.Zp,{children:(0,i.jsxs)(l.Wu,{className:"text-center py-12",children:[(0,i.jsx)(p.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-300"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Your wishlist is empty"}),(0,i.jsx)("p",{className:"text-gray-600 mb-6",children:"all"===t?"You haven't saved any properties yet. Browse properties and add them to your wishlist.":`No ${t} properties in your wishlist. Try changing the filter or browse more properties.`}),(0,i.jsx)(d.$,{children:"Browse Properties"})]})}),S.length>0&&(0,i.jsxs)(l.Zp,{children:[(0,i.jsx)(l.aR,{children:(0,i.jsx)(l.ZB,{className:"text-lg",children:"Wishlist Tips"})}),(0,i.jsx)(l.Wu,{children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,i.jsx)(p.A,{className:"h-4 w-4 text-blue-600"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium",children:"Save for Later"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Keep track of properties you're interested in"})]})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,i.jsx)(j.A,{className:"h-4 w-4 text-green-600"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium",children:"Compare Options"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Easily compare different investment opportunities"})]})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,i.jsx)(y.A,{className:"h-4 w-4 text-purple-600"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium",children:"Share with Others"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Get opinions from friends and family"})]})]})]})})]})]})})}},78057:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>o,routeModule:()=>x,tree:()=>c});var i=t(10557),r=t(68490),a=t(13172),l=t.n(a),d=t(68835),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(s,n);let c={children:["",{children:["wishlist",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2132)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\wishlist\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\wishlist\\page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},x=new i.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/wishlist/page",pathname:"/wishlist",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79551:e=>{"use strict";e.exports=require("url")},91473:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(99024).A)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),i=s.X(0,[755,3777,2544,7092,7555,2487,3427],()=>t(78057));module.exports=i})();