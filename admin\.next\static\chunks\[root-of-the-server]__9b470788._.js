(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[next]/internal/font/google/geist_e531dabc.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "geist_e531dabc-module__QGiZLq__className",
  "variable": "geist_e531dabc-module__QGiZLq__variable",
});
}}),
"[next]/internal/font/google/geist_e531dabc.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/geist_e531dabc.module.css [app-client] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Geist', 'Geist Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "geist_mono_68a01160-module__YLcDdW__className",
  "variable": "geist_mono_68a01160-module__YLcDdW__variable",
});
}}),
"[next]/internal/font/google/geist_mono_68a01160.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Geist Mono', 'Geist Mono Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[project]/src/utils/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "apiClient": (()=>apiClient),
    "authAPI": (()=>authAPI),
    "dashboardAPI": (()=>dashboardAPI),
    "default": (()=>__TURBOPACK__default__export__),
    "leadsAPI": (()=>leadsAPI),
    "propertiesAPI": (()=>propertiesAPI),
    "transactionsAPI": (()=>transactionsAPI),
    "usersAPI": (()=>usersAPI)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$axios$40$1$2e$10$2e$0$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/js-cookie@3.0.5/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$6_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
;
;
// API Configuration
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:5000/api") || 'http://localhost:5000/api';
// Create axios instance with cookies support
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$axios$40$1$2e$10$2e$0$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    timeout: 30000,
    withCredentials: true,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});
// Request interceptor
api.interceptors.request.use((config)=>{
    // Add auth token from cookies or localStorage
    const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('accessToken') || localStorage.getItem('accessToken');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Add request timestamp
    config.headers['X-Request-Time'] = new Date().toISOString();
    // Log request in development
    if ("TURBOPACK compile-time truthy", 1) {
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
            data: config.data,
            params: config.params
        });
    }
    return config;
}, (error)=>{
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
});
// Response interceptor
api.interceptors.response.use((response)=>{
    // Log response in development
    if ("TURBOPACK compile-time truthy", 1) {
        console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
            status: response.status,
            data: response.data
        });
    }
    return response;
}, async (error)=>{
    const originalRequest = error.config;
    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        try {
            // Try to refresh token
            const refreshToken = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('refreshToken');
            if (refreshToken) {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$axios$40$1$2e$10$2e$0$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${API_BASE_URL}/auth/refresh`, {
                    refreshToken
                }, {
                    withCredentials: true
                });
                if (response.data.success) {
                    const newToken = response.data.data.accessToken;
                    // Update token in cookies and localStorage
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set('accessToken', newToken);
                    localStorage.setItem('accessToken', newToken);
                    // Retry original request with new token
                    originalRequest.headers.Authorization = `Bearer ${newToken}`;
                    return api(originalRequest);
                }
            }
        } catch (refreshError) {
            console.error('Token refresh failed:', refreshError);
        }
        // If refresh fails, redirect to login
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('accessToken');
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('refreshToken');
        localStorage.removeItem('accessToken');
        if ("TURBOPACK compile-time truthy", 1) {
            window.location.href = '/login';
        }
    }
    // Handle other errors
    const errorMessage = error.response?.data?.message || error.message || 'An error occurred';
    // Show error toast for non-401 errors
    if (error.response?.status !== 401) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$6_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
    }
    // Log error in development
    if ("TURBOPACK compile-time truthy", 1) {
        console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
            status: error.response?.status,
            message: errorMessage,
            data: error.response?.data
        });
    }
    return Promise.reject(error);
});
const apiClient = {
    // GET request
    get: async (url, config)=>{
        const response = await api.get(url, config);
        return response.data;
    },
    // POST request
    post: async (url, data, config)=>{
        const response = await api.post(url, data, config);
        return response.data;
    },
    // PUT request
    put: async (url, data, config)=>{
        const response = await api.put(url, data, config);
        return response.data;
    },
    // PATCH request
    patch: async (url, data, config)=>{
        const response = await api.patch(url, data, config);
        return response.data;
    },
    // DELETE request
    delete: async (url, config)=>{
        const response = await api.delete(url, config);
        return response.data;
    },
    // Upload file
    upload: async (url, formData, onProgress)=>{
        const response = await api.post(url, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent)=>{
                if (onProgress && progressEvent.total) {
                    const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);
                    onProgress(progress);
                }
            }
        });
        return response.data;
    }
};
const authAPI = {
    login: async (credentials)=>{
        console.log('🔐 Attempting backend login:', credentials.email);
        try {
            // Call real backend API
            const response = await apiClient.post('/auth/login', credentials);
            console.log('✅ Backend login response:', response);
            return response;
        } catch (backendError) {
            console.error('❌ Backend login failed:', backendError.response?.data || backendError.message);
            // Re-throw the error so the UI can handle it
            throw new Error(backendError.response?.data?.message || backendError.message || 'Login failed - please check your credentials');
        }
    },
    // Check if user is authenticated (reads HttpOnly cookies)
    checkAuth: async ()=>{
        console.log('🔍 Backend auth check started');
        try {
            // Backend will check HttpOnly cookies and return user data
            const response = await apiClient.get('/auth/me');
            console.log('✅ Backend auth check successful:', response);
            return response;
        } catch (backendError) {
            console.error('❌ Backend auth check failed:', backendError.response?.data || backendError.message);
            throw new Error(backendError.response?.data?.message || backendError.message || 'Authentication check failed');
        }
    },
    logout: ()=>apiClient.post('/auth/logout'),
    refreshToken: ()=>apiClient.post('/auth/refresh'),
    getProfile: ()=>apiClient.get('/auth/me'),
    updateProfile: (data)=>apiClient.put('/auth/profile', data),
    changePassword: (data)=>apiClient.post('/auth/change-password', data)
};
const usersAPI = {
    getUsers: (params)=>apiClient.get('/users', {
            params
        }),
    getUser: (id)=>apiClient.get(`/users/${id}`),
    createUser: (data)=>apiClient.post('/users', data),
    updateUser: (id, data)=>apiClient.put(`/users/${id}`, data),
    deleteUser: (id)=>apiClient.delete(`/users/${id}`),
    getUserStats: ()=>apiClient.get('/users/stats')
};
const propertiesAPI = {
    getProperties: (params)=>apiClient.get('/properties', {
            params
        }),
    getProperty: (id)=>apiClient.get(`/properties/${id}`),
    createProperty: (data)=>apiClient.post('/properties', data),
    updateProperty: (id, data)=>apiClient.put(`/properties/${id}`, data),
    deleteProperty: (id)=>apiClient.delete(`/properties/${id}`),
    getPropertyStats: ()=>apiClient.get('/properties/stats')
};
const leadsAPI = {
    getLeads: (params)=>apiClient.get('/leads', {
            params
        }),
    getLead: (id)=>apiClient.get(`/leads/${id}`),
    createLead: (data)=>apiClient.post('/leads', data),
    updateLead: (id, data)=>apiClient.put(`/leads/${id}`, data),
    deleteLead: (id)=>apiClient.delete(`/leads/${id}`),
    assignLead: (id, data)=>apiClient.post(`/leads/${id}/assign`, data),
    getLeadStats: ()=>apiClient.get('/leads/dashboard/stats')
};
const transactionsAPI = {
    getTransactions: (params)=>apiClient.get('/transactions', {
            params
        }),
    getTransaction: (id)=>apiClient.get(`/transactions/${id}`),
    getTransactionStats: ()=>apiClient.get('/transactions/stats')
};
const dashboardAPI = {
    getStats: ()=>apiClient.get('/dashboard/stats'),
    getChartData: (type, period)=>apiClient.get(`/dashboard/charts/${type}`, {
            params: {
                period
            }
        }),
    getRecentActivity: ()=>apiClient.get('/dashboard/activity')
};
const __TURBOPACK__default__export__ = api;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/types/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// User Types
__turbopack_context__.s({
    "ConstructionStatus": (()=>ConstructionStatus),
    "KYCStatus": (()=>KYCStatus),
    "LeadPriority": (()=>LeadPriority),
    "LeadSource": (()=>LeadSource),
    "LeadStatus": (()=>LeadStatus),
    "PropertyStatus": (()=>PropertyStatus),
    "TransactionStatus": (()=>TransactionStatus),
    "TransactionType": (()=>TransactionType),
    "UserRole": (()=>UserRole),
    "UserStatus": (()=>UserStatus)
});
var UserRole = /*#__PURE__*/ function(UserRole) {
    UserRole["ADMIN"] = "admin";
    UserRole["SUBADMIN"] = "subadmin";
    UserRole["USER"] = "user";
    UserRole["SALES"] = "sales";
    return UserRole;
}({});
var UserStatus = /*#__PURE__*/ function(UserStatus) {
    UserStatus["ACTIVE"] = "active";
    UserStatus["INACTIVE"] = "inactive";
    UserStatus["SUSPENDED"] = "suspended";
    UserStatus["PENDING"] = "pending";
    return UserStatus;
}({});
var KYCStatus = /*#__PURE__*/ function(KYCStatus) {
    KYCStatus["NOT_STARTED"] = "not_started";
    KYCStatus["PENDING"] = "pending";
    KYCStatus["UNDER_REVIEW"] = "under_review";
    KYCStatus["APPROVED"] = "approved";
    KYCStatus["REJECTED"] = "rejected";
    return KYCStatus;
}({});
var PropertyStatus = /*#__PURE__*/ function(PropertyStatus) {
    PropertyStatus["ACTIVE"] = "active";
    PropertyStatus["INACTIVE"] = "inactive";
    PropertyStatus["SOLD_OUT"] = "sold_out";
    PropertyStatus["COMING_SOON"] = "coming_soon";
    return PropertyStatus;
}({});
var ConstructionStatus = /*#__PURE__*/ function(ConstructionStatus) {
    ConstructionStatus["PLANNING"] = "planning";
    ConstructionStatus["FOUNDATION"] = "foundation";
    ConstructionStatus["STRUCTURE"] = "structure";
    ConstructionStatus["FINISHING"] = "finishing";
    ConstructionStatus["COMPLETED"] = "completed";
    return ConstructionStatus;
}({});
var LeadStatus = /*#__PURE__*/ function(LeadStatus) {
    LeadStatus["NEW"] = "new";
    LeadStatus["CONTACTED"] = "contacted";
    LeadStatus["QUALIFIED"] = "qualified";
    LeadStatus["PROPOSAL_SENT"] = "proposal_sent";
    LeadStatus["NEGOTIATION"] = "negotiation";
    LeadStatus["CONVERTED"] = "converted";
    LeadStatus["LOST"] = "lost";
    LeadStatus["ON_HOLD"] = "on_hold";
    return LeadStatus;
}({});
var LeadSource = /*#__PURE__*/ function(LeadSource) {
    LeadSource["WEBSITE"] = "website";
    LeadSource["SOCIAL_MEDIA"] = "social_media";
    LeadSource["REFERRAL"] = "referral";
    LeadSource["COLD_CALL"] = "cold_call";
    LeadSource["EMAIL_CAMPAIGN"] = "email_campaign";
    LeadSource["ADVERTISEMENT"] = "advertisement";
    LeadSource["WALK_IN"] = "walk_in";
    LeadSource["OTHER"] = "other";
    return LeadSource;
}({});
var LeadPriority = /*#__PURE__*/ function(LeadPriority) {
    LeadPriority["LOW"] = "low";
    LeadPriority["MEDIUM"] = "medium";
    LeadPriority["HIGH"] = "high";
    LeadPriority["URGENT"] = "urgent";
    return LeadPriority;
}({});
var TransactionType = /*#__PURE__*/ function(TransactionType) {
    TransactionType["INVESTMENT"] = "investment";
    TransactionType["RETURN"] = "return";
    TransactionType["WITHDRAWAL"] = "withdrawal";
    TransactionType["REFUND"] = "refund";
    return TransactionType;
}({});
var TransactionStatus = /*#__PURE__*/ function(TransactionStatus) {
    TransactionStatus["PENDING"] = "pending";
    TransactionStatus["COMPLETED"] = "completed";
    TransactionStatus["FAILED"] = "failed";
    TransactionStatus["CANCELLED"] = "cancelled";
    return TransactionStatus;
}({});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/auth.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "authUtils": (()=>authUtils),
    "default": (()=>__TURBOPACK__default__export__),
    "jwtUtils": (()=>jwtUtils),
    "sessionManager": (()=>sessionManager),
    "tokenManager": (()=>tokenManager),
    "userManager": (()=>userManager)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/js-cookie@3.0.5/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-client] (ecmascript)");
;
;
const tokenManager = {
    // Get access token
    getAccessToken: ()=>{
        // For HttpOnly cookies, we cannot access them from JavaScript
        // This function will return null for HttpOnly cookies (which is expected)
        // Try to get from regular cookies (non-HttpOnly) - for development/testing
        const cookieToken = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('accessToken');
        if (cookieToken) {
            console.log('📝 Found non-HttpOnly cookie token');
            return cookieToken;
        }
        // Try localStorage as fallback (for development/testing)
        if ("TURBOPACK compile-time truthy", 1) {
            const localToken = localStorage.getItem('accessToken');
            if (localToken) {
                console.log('📝 Found localStorage token');
                return localToken;
            }
        }
        // For production with HttpOnly cookies, this will always return null
        // Auth check should be done via backend API calls
        console.log('📝 No accessible token found (HttpOnly cookies are not accessible)');
        return null;
    },
    // Set access token
    setAccessToken: (token, rememberMe = false)=>{
        const isProduction = ("TURBOPACK compile-time value", "development") === 'production';
        if (rememberMe) {
            // Set cookie for 30 days
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set('accessToken', token, {
                expires: 30,
                secure: isProduction,
                sameSite: 'lax',
                path: '/' // Ensure cookie is available site-wide
            });
        } else {
            // Session cookie
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set('accessToken', token, {
                secure: isProduction,
                sameSite: 'lax',
                path: '/' // Ensure cookie is available site-wide
            });
        }
        // Only set localStorage on client side
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.setItem('accessToken', token);
        }
        // Debug: Verify cookie was set
        if ("TURBOPACK compile-time truthy", 1) {
            setTimeout(()=>{
                const cookieCheck = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('accessToken');
                console.log('🍪 Cookie set verification:', {
                    tokenLength: token.length,
                    cookieExists: !!cookieCheck,
                    cookieValue: cookieCheck ? `${cookieCheck.substring(0, 20)}...` : 'none'
                });
            }, 100);
        }
    },
    // Get refresh token
    getRefreshToken: ()=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('refreshToken') || null;
    },
    // Set refresh token
    setRefreshToken: (token, rememberMe = false)=>{
        if (rememberMe) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set('refreshToken', token, {
                expires: 30,
                secure: true,
                sameSite: 'strict'
            });
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set('refreshToken', token, {
                secure: true,
                sameSite: 'strict'
            });
        }
    },
    // Verify token is properly stored and accessible
    verifyTokenStorage: ()=>{
        const cookieToken = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('accessToken');
        const localToken = ("TURBOPACK compile-time truthy", 1) ? localStorage.getItem('accessToken') : ("TURBOPACK unreachable", undefined);
        console.log('🔍 Token verification:', {
            cookieToken: cookieToken ? `${cookieToken.substring(0, 20)}...` : 'none',
            localToken: localToken ? `${localToken.substring(0, 20)}...` : 'none',
            cookieExists: !!cookieToken,
            localExists: !!localToken
        });
        return !!(cookieToken || localToken);
    },
    // Clear all tokens
    clearTokens: ()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('accessToken');
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$js$2d$cookie$40$3$2e$0$2e$5$2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('refreshToken');
        // Only clear localStorage on client side
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.removeItem('accessToken');
            localStorage.removeItem('user');
        }
    },
    // Check if user is authenticated
    isAuthenticated: ()=>{
        const token = tokenManager.getAccessToken();
        return !!token;
    }
};
const userManager = {
    // Get current user
    getCurrentUser: ()=>{
        try {
            const userStr = localStorage.getItem('user');
            return userStr ? JSON.parse(userStr) : null;
        } catch (error) {
            console.error('Error parsing user data:', error);
            return null;
        }
    },
    // Set current user
    setCurrentUser: (user)=>{
        localStorage.setItem('user', JSON.stringify(user));
    },
    // Clear current user
    clearCurrentUser: ()=>{
        localStorage.removeItem('user');
    },
    // Check user role
    hasRole: (requiredRole)=>{
        const user = userManager.getCurrentUser();
        if (!user) return false;
        const roleHierarchy = {
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].ADMIN]: 4,
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].SUBADMIN]: 3,
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].SALES]: 2,
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].USER]: 1
        };
        const userLevel = roleHierarchy[user.role];
        const requiredLevel = roleHierarchy[requiredRole];
        return userLevel >= requiredLevel;
    },
    // Check if user has any of the required roles
    hasAnyRole: (roles)=>{
        return roles.some((role)=>userManager.hasRole(role));
    },
    // Get user display name
    getDisplayName: ()=>{
        const user = userManager.getCurrentUser();
        if (!user) return 'Guest';
        return `${user.firstName} ${user.lastName}`.trim() || user.email;
    },
    // Get user initials
    getInitials: ()=>{
        const user = userManager.getCurrentUser();
        if (!user) return 'G';
        const firstName = user.firstName || '';
        const lastName = user.lastName || '';
        if (firstName && lastName) {
            return `${firstName[0]}${lastName[0]}`.toUpperCase();
        } else if (firstName) {
            return firstName.substring(0, 2).toUpperCase();
        } else if (user.email) {
            return user.email.substring(0, 2).toUpperCase();
        }
        return 'U';
    }
};
const authUtils = {
    // Login user
    login: async (user, tokens, rememberMe = false)=>{
        tokenManager.setAccessToken(tokens.accessToken, rememberMe);
        tokenManager.setRefreshToken(tokens.refreshToken, rememberMe);
        userManager.setCurrentUser(user);
    },
    // Logout user
    logout: ()=>{
        tokenManager.clearTokens();
        userManager.clearCurrentUser();
        // Redirect to login page
        if ("TURBOPACK compile-time truthy", 1) {
            window.location.href = '/login';
        }
    },
    // Check if user is admin
    isAdmin: ()=>{
        return userManager.hasRole(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].ADMIN);
    },
    // Check if user is sub-admin
    isSubAdmin: ()=>{
        return userManager.hasRole(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].SUBADMIN);
    },
    // Check if user is sales
    isSales: ()=>{
        return userManager.hasRole(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].SALES);
    },
    // Check if user can manage users
    canManageUsers: ()=>{
        return userManager.hasAnyRole([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].ADMIN,
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].SUBADMIN
        ]);
    },
    // Check if user can manage properties
    canManageProperties: ()=>{
        return userManager.hasAnyRole([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].ADMIN,
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].SUBADMIN
        ]);
    },
    // Check if user can manage leads
    canManageLeads: ()=>{
        return userManager.hasAnyRole([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].ADMIN,
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].SUBADMIN,
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].SALES
        ]);
    },
    // Check if user can view analytics
    canViewAnalytics: ()=>{
        return userManager.hasAnyRole([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].ADMIN,
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].SUBADMIN
        ]);
    },
    // Get allowed routes for user
    getAllowedRoutes: ()=>{
        const user = userManager.getCurrentUser();
        if (!user) return [
            '/login'
        ];
        const baseRoutes = [
            '/dashboard',
            '/profile',
            '/settings'
        ];
        switch(user.role){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].ADMIN:
                return [
                    ...baseRoutes,
                    '/users',
                    '/properties',
                    '/leads',
                    '/transactions',
                    '/analytics',
                    '/system'
                ];
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].SUBADMIN:
                return [
                    ...baseRoutes,
                    '/users',
                    '/properties',
                    '/leads',
                    '/transactions',
                    '/analytics'
                ];
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserRole"].SALES:
                return [
                    ...baseRoutes,
                    '/leads',
                    '/customers'
                ];
            default:
                return baseRoutes;
        }
    },
    // Check if route is allowed for user
    isRouteAllowed: (route)=>{
        const allowedRoutes = authUtils.getAllowedRoutes();
        return allowedRoutes.some((allowedRoute)=>route.startsWith(allowedRoute));
    }
};
const jwtUtils = {
    // Decode JWT token (without verification)
    decode: (token)=>{
        try {
            const base64Url = token.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(atob(base64).split('').map((c)=>'%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)).join(''));
            return JSON.parse(jsonPayload);
        } catch (error) {
            console.error('Error decoding JWT:', error);
            return null;
        }
    },
    // Check if token is expired
    isExpired: (token)=>{
        try {
            const decoded = jwtUtils.decode(token);
            if (!decoded || !decoded.exp) return true;
            const currentTime = Date.now() / 1000;
            return decoded.exp < currentTime;
        } catch (error) {
            return true;
        }
    },
    // Get token expiration time
    getExpirationTime: (token)=>{
        try {
            const decoded = jwtUtils.decode(token);
            if (!decoded || !decoded.exp) return null;
            return new Date(decoded.exp * 1000);
        } catch (error) {
            return null;
        }
    },
    // Get time until token expires (in minutes)
    getTimeUntilExpiry: (token)=>{
        try {
            const expirationTime = jwtUtils.getExpirationTime(token);
            if (!expirationTime) return 0;
            const currentTime = new Date();
            const timeDiff = expirationTime.getTime() - currentTime.getTime();
            return Math.max(0, Math.floor(timeDiff / (1000 * 60))); // Convert to minutes
        } catch (error) {
            return 0;
        }
    }
};
const sessionManager = {
    // Check session validity
    isSessionValid: ()=>{
        const token = tokenManager.getAccessToken();
        if (!token) return false;
        return !jwtUtils.isExpired(token);
    },
    // Get session info
    getSessionInfo: ()=>{
        const token = tokenManager.getAccessToken();
        const user = userManager.getCurrentUser();
        if (!token || !user) {
            return {
                isValid: false,
                user: null,
                expiresAt: null,
                timeUntilExpiry: 0
            };
        }
        const expiresAt = jwtUtils.getExpirationTime(token);
        const timeUntilExpiry = jwtUtils.getTimeUntilExpiry(token);
        return {
            isValid: !jwtUtils.isExpired(token),
            user,
            expiresAt,
            timeUntilExpiry
        };
    },
    // Extend session (refresh token)
    extendSession: async ()=>{
        try {
            const refreshToken = tokenManager.getRefreshToken();
            if (!refreshToken) return false;
            // This would typically call the refresh endpoint
            // Implementation depends on your API
            return true;
        } catch (error) {
            console.error('Error extending session:', error);
            return false;
        }
    }
};
const __TURBOPACK__default__export__ = {
    tokenManager,
    userManager,
    authUtils,
    jwtUtils,
    sessionManager
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/slices/authSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkAuthAsync": (()=>checkAuthAsync),
    "clearAuth": (()=>clearAuth),
    "clearError": (()=>clearError),
    "clearUser": (()=>clearUser),
    "default": (()=>__TURBOPACK__default__export__),
    "loginAsync": (()=>loginAsync),
    "logoutAsync": (()=>logoutAsync),
    "refreshUserAsync": (()=>refreshUserAsync),
    "selectAuth": (()=>selectAuth),
    "selectAuthError": (()=>selectAuthError),
    "selectAuthLoading": (()=>selectAuthLoading),
    "selectIsAuthenticated": (()=>selectIsAuthenticated),
    "selectUser": (()=>selectUser),
    "setLoading": (()=>setLoading),
    "setSessionExpiry": (()=>setSessionExpiry),
    "setUser": (()=>setUser),
    "updateUserProfile": (()=>updateUserProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_7c11fc0195c3cadab20264a1be66ea7e/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/auth.ts [app-client] (ecmascript)");
;
;
;
const initialState = {
    user: null,
    isAuthenticated: false,
    loading: false,
    error: null,
    sessionExpiry: null
};
const loginAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/login', async (credentials, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].login(credentials);
        // Handle both backend and mock responses
        let responseData;
        if (response.success && response.data) {
            responseData = response.data;
        } else if (response.data && response.data.user) {
            // Handle direct response format
            responseData = response.data;
        } else {
            return rejectWithValue(response.message || 'Login failed');
        }
        const { user, accessToken, refreshToken } = responseData;
        if (!user || !accessToken) {
            return rejectWithValue('Invalid response format');
        }
        // Backend sets HttpOnly cookies automatically
        // We only store user data locally (tokens are in HttpOnly cookies)
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userManager"].setCurrentUser(user);
            console.log('✅ Login successful, backend set HttpOnly cookies:', {
                user: user.email,
                role: user.role,
                note: 'Tokens stored in HttpOnly cookies (not accessible from JS)'
            });
            return {
                user,
                accessToken,
                refreshToken
            };
        } catch (storageError) {
            console.error('User storage failed:', storageError);
            return rejectWithValue('Failed to store user data');
        }
    } catch (error) {
        console.error('Login error:', error);
        return rejectWithValue(error.response?.data?.message || error.message || 'Login failed');
    }
});
const logoutAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/logout', async (_, { rejectWithValue })=>{
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].logout();
    } catch (error) {
        console.error('Logout API error:', error);
    } finally{
        // Always clear local state
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tokenManager"].clearTokens();
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userManager"].clearCurrentUser();
    }
});
const refreshUserAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/refreshUser', async (_, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].getProfile();
        if (response.success) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userManager"].setCurrentUser(response.data);
            return response.data;
        } else {
            return rejectWithValue(response.message);
        }
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to refresh user');
    }
});
const checkAuthAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/checkAuth', async (_, { rejectWithValue })=>{
    try {
        console.log('🔍 Checking auth with backend (HttpOnly cookies)...');
        // Direct backend call - backend will check HttpOnly cookies
        // No need to check localStorage/cookies from frontend
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].getProfile();
        console.log('✅ Backend auth response:', response);
        if (response.success && response.data) {
            // Store user data (not tokens, as they're in HttpOnly cookies)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userManager"].setCurrentUser(response.data);
            return response.data;
        } else {
            console.log('❌ Backend auth failed:', response.message);
            // Clear any local user data (tokens are HttpOnly, can't clear from frontend)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userManager"].clearCurrentUser();
            return rejectWithValue(response.message || 'Authentication failed');
        }
    } catch (error) {
        console.error('❌ Auth check error:', error.response?.data || error.message);
        // Clear local user data on error
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userManager"].clearCurrentUser();
        // Return appropriate error message
        const errorMessage = error.response?.data?.message || error.message || 'Authentication check failed';
        return rejectWithValue(errorMessage);
    }
});
// Auth slice
const authSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'auth',
    initialState,
    reducers: {
        clearError: (state)=>{
            state.error = null;
        },
        setUser: (state, action)=>{
            state.user = action.payload;
            state.isAuthenticated = true;
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userManager"].setCurrentUser(action.payload);
        },
        clearAuth: (state)=>{
            state.user = null;
            state.isAuthenticated = false;
            state.error = null;
            state.sessionExpiry = null;
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tokenManager"].clearTokens();
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userManager"].clearCurrentUser();
        },
        setSessionExpiry: (state, action)=>{
            state.sessionExpiry = action.payload;
        },
        updateUserProfile: (state, action)=>{
            if (state.user) {
                state.user = {
                    ...state.user,
                    ...action.payload
                };
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userManager"].setCurrentUser(state.user);
            }
        }
    },
    extraReducers: (builder)=>{
        // Login
        builder.addCase(loginAsync.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(loginAsync.fulfilled, (state, action)=>{
            state.loading = false;
            // Handle nested user structure from backend
            state.user = action.payload.user || action.payload;
            state.isAuthenticated = true;
            state.error = null;
        }).addCase(loginAsync.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
            state.isAuthenticated = false;
            state.user = null;
        });
        // Logout
        builder.addCase(logoutAsync.pending, (state)=>{
            state.loading = true;
        }).addCase(logoutAsync.fulfilled, (state)=>{
            state.loading = false;
            state.user = null;
            state.isAuthenticated = false;
            state.error = null;
            state.sessionExpiry = null;
        }).addCase(logoutAsync.rejected, (state)=>{
            state.loading = false;
            state.user = null;
            state.isAuthenticated = false;
            state.error = null;
            state.sessionExpiry = null;
        });
        // Refresh user
        builder.addCase(refreshUserAsync.pending, (state)=>{
            state.loading = true;
        }).addCase(refreshUserAsync.fulfilled, (state, action)=>{
            state.loading = false;
            // Handle nested user structure from backend
            state.user = action.payload.user || action.payload;
            state.error = null;
        }).addCase(refreshUserAsync.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
        // Check auth
        builder.addCase(checkAuthAsync.pending, (state)=>{
            state.loading = true;
        }).addCase(checkAuthAsync.fulfilled, (state, action)=>{
            state.loading = false;
            // Handle nested user structure from backend
            state.user = action.payload.user || action.payload;
            state.isAuthenticated = true;
            state.error = null;
        }).addCase(checkAuthAsync.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
            state.isAuthenticated = false;
            state.user = null;
        });
    }
});
const { clearError, setUser, clearAuth, setSessionExpiry, updateUserProfile } = authSlice.actions;
const clearUser = clearAuth;
const setLoading = (loading)=>(dispatch)=>{
    // This is handled by async thunks, but we can add a simple action if needed
    };
const selectAuth = (state)=>state.auth;
const selectUser = (state)=>state.auth.user;
const selectIsAuthenticated = (state)=>state.auth.isAuthenticated;
const selectAuthLoading = (state)=>state.auth.loading;
const selectAuthError = (state)=>state.auth.error;
const __TURBOPACK__default__export__ = authSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/slices/uiSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addBreadcrumb": (()=>addBreadcrumb),
    "addNotification": (()=>addNotification),
    "clearBreadcrumbs": (()=>clearBreadcrumbs),
    "clearLoading": (()=>clearLoading),
    "clearNotifications": (()=>clearNotifications),
    "closeAllModals": (()=>closeAllModals),
    "closeModal": (()=>closeModal),
    "default": (()=>__TURBOPACK__default__export__),
    "markAllNotificationsRead": (()=>markAllNotificationsRead),
    "markNotificationRead": (()=>markNotificationRead),
    "openModal": (()=>openModal),
    "removeNotification": (()=>removeNotification),
    "resetUI": (()=>resetUI),
    "selectBreadcrumbs": (()=>selectBreadcrumbs),
    "selectGlobalLoading": (()=>selectGlobalLoading),
    "selectLoading": (()=>selectLoading),
    "selectModal": (()=>selectModal),
    "selectNotifications": (()=>selectNotifications),
    "selectPageTitle": (()=>selectPageTitle),
    "selectSidebarCollapsed": (()=>selectSidebarCollapsed),
    "selectSidebarOpen": (()=>selectSidebarOpen),
    "selectTheme": (()=>selectTheme),
    "selectUI": (()=>selectUI),
    "selectUnreadNotifications": (()=>selectUnreadNotifications),
    "setBreadcrumbs": (()=>setBreadcrumbs),
    "setGlobalLoading": (()=>setGlobalLoading),
    "setLoading": (()=>setLoading),
    "setPageTitle": (()=>setPageTitle),
    "setSidebarCollapsed": (()=>setSidebarCollapsed),
    "setSidebarOpen": (()=>setSidebarOpen),
    "setTheme": (()=>setTheme),
    "toggleModal": (()=>toggleModal),
    "toggleSidebar": (()=>toggleSidebar),
    "toggleSidebarCollapsed": (()=>toggleSidebarCollapsed)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_7c11fc0195c3cadab20264a1be66ea7e/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    sidebarOpen: true,
    sidebarCollapsed: false,
    theme: 'light',
    notifications: [],
    loading: {
        global: false
    },
    modals: {},
    breadcrumbs: [],
    pageTitle: 'Dashboard'
};
const uiSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'ui',
    initialState,
    reducers: {
        // Sidebar
        toggleSidebar: (state)=>{
            state.sidebarOpen = !state.sidebarOpen;
        },
        setSidebarOpen: (state, action)=>{
            state.sidebarOpen = action.payload;
        },
        toggleSidebarCollapsed: (state)=>{
            state.sidebarCollapsed = !state.sidebarCollapsed;
        },
        setSidebarCollapsed: (state, action)=>{
            state.sidebarCollapsed = action.payload;
        },
        // Theme
        setTheme: (state, action)=>{
            state.theme = action.payload;
        },
        // Notifications
        addNotification: (state, action)=>{
            const notification = {
                ...action.payload,
                id: Date.now().toString(),
                timestamp: new Date().toISOString(),
                read: false
            };
            state.notifications.unshift(notification);
            // Keep only last 50 notifications
            if (state.notifications.length > 50) {
                state.notifications = state.notifications.slice(0, 50);
            }
        },
        markNotificationRead: (state, action)=>{
            const notification = state.notifications.find((n)=>n.id === action.payload);
            if (notification) {
                notification.read = true;
            }
        },
        markAllNotificationsRead: (state)=>{
            state.notifications.forEach((notification)=>{
                notification.read = true;
            });
        },
        removeNotification: (state, action)=>{
            state.notifications = state.notifications.filter((n)=>n.id !== action.payload);
        },
        clearNotifications: (state)=>{
            state.notifications = [];
        },
        // Loading
        setGlobalLoading: (state, action)=>{
            state.loading.global = action.payload;
        },
        setLoading: (state, action)=>{
            state.loading[action.payload.key] = action.payload.loading;
        },
        clearLoading: (state, action)=>{
            delete state.loading[action.payload];
        },
        // Modals
        openModal: (state, action)=>{
            state.modals[action.payload] = true;
        },
        closeModal: (state, action)=>{
            state.modals[action.payload] = false;
        },
        toggleModal: (state, action)=>{
            state.modals[action.payload] = !state.modals[action.payload];
        },
        closeAllModals: (state)=>{
            Object.keys(state.modals).forEach((key)=>{
                state.modals[key] = false;
            });
        },
        // Breadcrumbs
        setBreadcrumbs: (state, action)=>{
            state.breadcrumbs = action.payload;
        },
        addBreadcrumb: (state, action)=>{
            // Mark previous breadcrumbs as not current
            state.breadcrumbs.forEach((item)=>{
                item.current = false;
            });
            // Add new breadcrumb
            state.breadcrumbs.push({
                ...action.payload,
                current: true
            });
        },
        clearBreadcrumbs: (state)=>{
            state.breadcrumbs = [];
        },
        // Page title
        setPageTitle: (state, action)=>{
            state.pageTitle = action.payload;
        },
        // Reset UI state
        resetUI: (state)=>{
            return {
                ...initialState,
                theme: state.theme,
                sidebarCollapsed: state.sidebarCollapsed
            };
        }
    }
});
const { toggleSidebar, setSidebarOpen, toggleSidebarCollapsed, setSidebarCollapsed, setTheme, addNotification, markNotificationRead, markAllNotificationsRead, removeNotification, clearNotifications, setGlobalLoading, setLoading, clearLoading, openModal, closeModal, toggleModal, closeAllModals, setBreadcrumbs, addBreadcrumb, clearBreadcrumbs, setPageTitle, resetUI } = uiSlice.actions;
const selectUI = (state)=>state.ui;
const selectSidebarOpen = (state)=>state.ui.sidebarOpen;
const selectSidebarCollapsed = (state)=>state.ui.sidebarCollapsed;
const selectTheme = (state)=>state.ui.theme;
const selectNotifications = (state)=>state.ui.notifications;
const selectUnreadNotifications = (state)=>state.ui.notifications.filter((n)=>!n.read);
const selectGlobalLoading = (state)=>state.ui.loading.global;
const selectLoading = (key)=>(state)=>state.ui.loading[key] || false;
const selectModal = (key)=>(state)=>state.ui.modals[key] || false;
const selectBreadcrumbs = (state)=>state.ui.breadcrumbs;
const selectPageTitle = (state)=>state.ui.pageTitle;
const __TURBOPACK__default__export__ = uiSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/slices/usersSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearError": (()=>clearError),
    "createUserAsync": (()=>createUserAsync),
    "default": (()=>__TURBOPACK__default__export__),
    "deleteUserAsync": (()=>deleteUserAsync),
    "fetchUsersAsync": (()=>fetchUsersAsync),
    "setCurrentUser": (()=>setCurrentUser),
    "updateUserAsync": (()=>updateUserAsync)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_7c11fc0195c3cadab20264a1be66ea7e/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/api.ts [app-client] (ecmascript)");
;
;
const initialState = {
    users: [],
    currentUser: null,
    loading: false,
    error: null,
    pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 10
    }
};
const fetchUsersAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('users/fetchUsers', async (params, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersAPI"].getUsers(params);
        return response;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to fetch users');
    }
});
const createUserAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('users/createUser', async (userData, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersAPI"].createUser(userData);
        return response.data;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to create user');
    }
});
const updateUserAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('users/updateUser', async ({ id, userData }, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersAPI"].updateUser(id, userData);
        return response.data;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to update user');
    }
});
const deleteUserAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('users/deleteUser', async (id, { rejectWithValue })=>{
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersAPI"].deleteUser(id);
        return id;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to delete user');
    }
});
const usersSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'users',
    initialState,
    reducers: {
        clearError: (state)=>{
            state.error = null;
        },
        setCurrentUser: (state, action)=>{
            state.currentUser = action.payload;
        }
    },
    extraReducers: (builder)=>{
        builder.addCase(fetchUsersAsync.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(fetchUsersAsync.fulfilled, (state, action)=>{
            state.loading = false;
            state.users = action.payload.data;
            if (action.payload.meta?.pagination) {
                state.pagination = action.payload.meta.pagination;
            }
        }).addCase(fetchUsersAsync.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        }).addCase(createUserAsync.fulfilled, (state, action)=>{
            state.users.unshift(action.payload);
        }).addCase(updateUserAsync.fulfilled, (state, action)=>{
            const index = state.users.findIndex((user)=>user.id === action.payload.id);
            if (index !== -1) {
                state.users[index] = action.payload;
            }
        }).addCase(deleteUserAsync.fulfilled, (state, action)=>{
            state.users = state.users.filter((user)=>user.id !== action.payload);
        });
    }
});
const { clearError, setCurrentUser } = usersSlice.actions;
const __TURBOPACK__default__export__ = usersSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/slices/propertiesSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearError": (()=>clearError),
    "createPropertyAsync": (()=>createPropertyAsync),
    "default": (()=>__TURBOPACK__default__export__),
    "deletePropertyAsync": (()=>deletePropertyAsync),
    "fetchPropertiesAsync": (()=>fetchPropertiesAsync),
    "setCurrentProperty": (()=>setCurrentProperty),
    "updatePropertyAsync": (()=>updatePropertyAsync)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_7c11fc0195c3cadab20264a1be66ea7e/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/api.ts [app-client] (ecmascript)");
;
;
const initialState = {
    properties: [],
    currentProperty: null,
    loading: false,
    error: null,
    pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 10
    }
};
const fetchPropertiesAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('properties/fetchProperties', async (params, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["propertiesAPI"].getProperties(params);
        return response;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to fetch properties');
    }
});
const createPropertyAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('properties/createProperty', async (propertyData, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["propertiesAPI"].createProperty(propertyData);
        return response.data;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to create property');
    }
});
const updatePropertyAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('properties/updateProperty', async ({ id, propertyData }, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["propertiesAPI"].updateProperty(id, propertyData);
        return response.data;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to update property');
    }
});
const deletePropertyAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('properties/deleteProperty', async (id, { rejectWithValue })=>{
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["propertiesAPI"].deleteProperty(id);
        return id;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to delete property');
    }
});
const propertiesSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'properties',
    initialState,
    reducers: {
        clearError: (state)=>{
            state.error = null;
        },
        setCurrentProperty: (state, action)=>{
            state.currentProperty = action.payload;
        }
    },
    extraReducers: (builder)=>{
        builder.addCase(fetchPropertiesAsync.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(fetchPropertiesAsync.fulfilled, (state, action)=>{
            state.loading = false;
            state.properties = action.payload.data;
            if (action.payload.meta?.pagination) {
                state.pagination = action.payload.meta.pagination;
            }
        }).addCase(fetchPropertiesAsync.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        }).addCase(createPropertyAsync.fulfilled, (state, action)=>{
            state.properties.unshift(action.payload);
        }).addCase(updatePropertyAsync.fulfilled, (state, action)=>{
            const index = state.properties.findIndex((property)=>property.id === action.payload.id);
            if (index !== -1) {
                state.properties[index] = action.payload;
            }
        }).addCase(deletePropertyAsync.fulfilled, (state, action)=>{
            state.properties = state.properties.filter((property)=>property.id !== action.payload);
        });
    }
});
const { clearError, setCurrentProperty } = propertiesSlice.actions;
const __TURBOPACK__default__export__ = propertiesSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/slices/leadsSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearError": (()=>clearError),
    "createLeadAsync": (()=>createLeadAsync),
    "default": (()=>__TURBOPACK__default__export__),
    "deleteLeadAsync": (()=>deleteLeadAsync),
    "fetchLeadsAsync": (()=>fetchLeadsAsync),
    "setCurrentLead": (()=>setCurrentLead),
    "updateLeadAsync": (()=>updateLeadAsync)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_7c11fc0195c3cadab20264a1be66ea7e/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/api.ts [app-client] (ecmascript)");
;
;
const initialState = {
    leads: [],
    currentLead: null,
    loading: false,
    error: null,
    pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 10
    }
};
const fetchLeadsAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('leads/fetchLeads', async (params, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["leadsAPI"].getLeads(params);
        return response;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to fetch leads');
    }
});
const createLeadAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('leads/createLead', async (leadData, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["leadsAPI"].createLead(leadData);
        return response.data;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to create lead');
    }
});
const updateLeadAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('leads/updateLead', async ({ id, leadData }, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["leadsAPI"].updateLead(id, leadData);
        return response.data;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to update lead');
    }
});
const deleteLeadAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('leads/deleteLead', async (id, { rejectWithValue })=>{
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["leadsAPI"].deleteLead(id);
        return id;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to delete lead');
    }
});
const leadsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'leads',
    initialState,
    reducers: {
        clearError: (state)=>{
            state.error = null;
        },
        setCurrentLead: (state, action)=>{
            state.currentLead = action.payload;
        }
    },
    extraReducers: (builder)=>{
        builder.addCase(fetchLeadsAsync.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(fetchLeadsAsync.fulfilled, (state, action)=>{
            state.loading = false;
            state.leads = action.payload.data;
            if (action.payload.meta?.pagination) {
                state.pagination = action.payload.meta.pagination;
            }
        }).addCase(fetchLeadsAsync.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        }).addCase(createLeadAsync.fulfilled, (state, action)=>{
            state.leads.unshift(action.payload);
        }).addCase(updateLeadAsync.fulfilled, (state, action)=>{
            const index = state.leads.findIndex((lead)=>lead.id === action.payload.id);
            if (index !== -1) {
                state.leads[index] = action.payload;
            }
        }).addCase(deleteLeadAsync.fulfilled, (state, action)=>{
            state.leads = state.leads.filter((lead)=>lead.id !== action.payload);
        });
    }
});
const { clearError, setCurrentLead } = leadsSlice.actions;
const __TURBOPACK__default__export__ = leadsSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/slices/dashboardSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearError": (()=>clearError),
    "default": (()=>__TURBOPACK__default__export__),
    "fetchChartDataAsync": (()=>fetchChartDataAsync),
    "fetchDashboardStatsAsync": (()=>fetchDashboardStatsAsync)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_7c11fc0195c3cadab20264a1be66ea7e/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/api.ts [app-client] (ecmascript)");
;
;
const initialState = {
    stats: null,
    chartData: {},
    recentActivity: [],
    loading: false,
    error: null
};
const fetchDashboardStatsAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('dashboard/fetchStats', async (_, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dashboardAPI"].getStats();
        return response.data;
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard stats');
    }
});
const fetchChartDataAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('dashboard/fetchChartData', async ({ type, period }, { rejectWithValue })=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dashboardAPI"].getChartData(type, period);
        return {
            type,
            data: response.data
        };
    } catch (error) {
        return rejectWithValue(error.response?.data?.message || 'Failed to fetch chart data');
    }
});
const dashboardSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'dashboard',
    initialState,
    reducers: {
        clearError: (state)=>{
            state.error = null;
        }
    },
    extraReducers: (builder)=>{
        builder.addCase(fetchDashboardStatsAsync.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(fetchDashboardStatsAsync.fulfilled, (state, action)=>{
            state.loading = false;
            state.stats = action.payload;
        }).addCase(fetchDashboardStatsAsync.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        }).addCase(fetchChartDataAsync.fulfilled, (state, action)=>{
            state.chartData[action.payload.type] = action.payload.data;
        });
    }
});
const { clearError } = dashboardSlice.actions;
const __TURBOPACK__default__export__ = dashboardSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/cookies.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Cookie utility functions for client-side cookie management
 */ __turbopack_context__.s({
    "CookieUtils": (()=>CookieUtils)
});
class CookieUtils {
    /**
   * Get a cookie value by name
   */ static getCookie(name) {
        if (typeof document === 'undefined') return null;
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) {
            const cookieValue = parts.pop()?.split(';').shift();
            return cookieValue ? decodeURIComponent(cookieValue) : null;
        }
        return null;
    }
    /**
   * Set a cookie
   */ static setCookie(name, value, options = {}) {
        if (typeof document === 'undefined') return;
        let cookieString = `${name}=${encodeURIComponent(value)}`;
        if (options.expires) {
            if (typeof options.expires === 'string') {
                cookieString += `; expires=${options.expires}`;
            } else {
                cookieString += `; expires=${options.expires.toUTCString()}`;
            }
        }
        if (options.maxAge) {
            cookieString += `; max-age=${options.maxAge}`;
        }
        if (options.path) {
            cookieString += `; path=${options.path}`;
        } else {
            cookieString += `; path=/`;
        }
        if (options.domain) {
            cookieString += `; domain=${options.domain}`;
        }
        if (options.secure) {
            cookieString += `; secure`;
        }
        if (options.sameSite) {
            cookieString += `; samesite=${options.sameSite}`;
        }
        document.cookie = cookieString;
    }
    /**
   * Delete a cookie
   */ static deleteCookie(name, options = {}) {
        this.setCookie(name, '', {
            ...options,
            expires: new Date(0)
        });
    }
    /**
   * Check if a cookie exists
   */ static hasCookie(name) {
        return this.getCookie(name) !== null;
    }
    /**
   * Get all cookies as an object
   */ static getAllCookies() {
        if (typeof document === 'undefined') return {};
        const cookies = {};
        document.cookie.split(';').forEach((cookie)=>{
            const [name, value] = cookie.trim().split('=');
            if (name && value) {
                cookies[name] = decodeURIComponent(value);
            }
        });
        return cookies;
    }
    /**
   * Get authentication token from cookies
   */ static getAuthToken() {
        // Try accessToken first (new format), then token (legacy)
        return this.getCookie('accessToken') || this.getCookie('token');
    }
    /**
   * Get refresh token from cookies
   */ static getRefreshToken() {
        return this.getCookie('refreshToken');
    }
    /**
   * Check if user is authenticated based on cookies
   */ static isAuthenticated() {
        return this.hasCookie('accessToken') || this.hasCookie('token');
    }
    /**
   * Clear all authentication cookies
   */ static clearAuthCookies() {
        this.deleteCookie('accessToken');
        this.deleteCookie('refreshToken');
        this.deleteCookie('token') // Legacy cleanup
        ;
    }
    /**
   * Get redirect URL from cookies
   */ static getRedirectUrl() {
        return this.getCookie('redirectTo');
    }
    /**
   * Set redirect URL in cookies
   */ static setRedirectUrl(url) {
        this.setCookie('redirectTo', url, {
            maxAge: 300,
            sameSite: 'lax'
        });
    }
    /**
   * Clear redirect URL from cookies
   */ static clearRedirectUrl() {
        this.deleteCookie('redirectTo');
    }
    /**
   * Parse cookie string (useful for server-side)
   */ static parseCookieString(cookieString) {
        const cookies = {};
        cookieString.split(';').forEach((cookie)=>{
            const [name, value] = cookie.trim().split('=');
            if (name && value) {
                cookies[name] = decodeURIComponent(value);
            }
        });
        return cookies;
    }
    /**
   * Set session cookie (expires when browser closes)
   */ static setSessionCookie(name, value, options = {}) {
        this.setCookie(name, value, {
            ...options,
            // Don't set expires or maxAge for session cookies
            expires: undefined,
            maxAge: undefined
        });
    }
    /**
   * Set persistent cookie (with expiration)
   */ static setPersistentCookie(name, value, days = 7, options = {}) {
        const expires = new Date();
        expires.setDate(expires.getDate() + days);
        this.setCookie(name, value, {
            ...options,
            expires
        });
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/baseApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "baseApi": (()=>baseApi),
    "createFormDataQuery": (()=>createFormDataQuery),
    "createQueryParams": (()=>createQueryParams)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_7c11fc0195c3cadab20264a1be66ea7e/node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_7c11fc0195c3cadab20264a1be66ea7e/node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/cookies.ts [app-client] (ecmascript)");
;
;
// Base query configuration with authentication and error handling
const baseQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchBaseQuery"])({
    baseUrl: ("TURBOPACK compile-time value", "http://localhost:5000/api") || 'http://localhost:5000/api',
    credentials: 'include',
    prepareHeaders: (headers, { getState })=>{
        // Get token from cookies first, then localStorage, then Redux state
        let token = null;
        if ("TURBOPACK compile-time truthy", 1) {
            token = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CookieUtils"].getAuthToken() || localStorage.getItem('accessToken');
        }
        if (!token) {
            token = getState()?.auth?.user?.accessToken;
        }
        if (token) {
            headers.set('authorization', `Bearer ${token}`);
        }
        // Set common headers
        headers.set('content-type', 'application/json');
        headers.set('accept', 'application/json');
        // Add client type for backend identification
        headers.set('x-client-type', 'admin-dashboard');
        headers.set('x-client-version', '1.0.0');
        return headers;
    }
});
// Enhanced base query with automatic token refresh and error handling
const baseQueryWithReauth = async (args, api, extraOptions)=>{
    let result = await baseQuery(args, api, extraOptions);
    // Log API errors for debugging
    if (result.error) {
        console.error('API Error:', result.error);
    }
    // Handle network errors or API server not available
    if (result.error && (result.error.status === 'FETCH_ERROR' || result.error.status === 'PARSING_ERROR')) {
        console.error('API server not available. Please ensure the backend server is running.');
    // Don't return mock data - let the error propagate to the UI
    }
    // Handle 401 Unauthorized - attempt token refresh
    if (result.error && result.error.status === 401) {
        console.log('Token expired, attempting refresh...');
        // Try to refresh token
        const refreshResult = await baseQuery({
            url: '/auth/refresh',
            method: 'POST',
            body: {
                refreshToken: ("TURBOPACK compile-time truthy", 1) ? localStorage.getItem('refreshToken') : ("TURBOPACK unreachable", undefined)
            }
        }, api, extraOptions);
        if (refreshResult.data) {
            // Store new token
            const newTokenData = refreshResult.data;
            if (newTokenData.success && newTokenData.data?.accessToken) {
                if ("TURBOPACK compile-time truthy", 1) {
                    localStorage.setItem('accessToken', newTokenData.data.accessToken);
                }
                // Retry original request with new token
                result = await baseQuery(args, api, extraOptions);
            }
        } else {
            // Refresh failed - redirect to login
            if ("TURBOPACK compile-time truthy", 1) {
                localStorage.removeItem('accessToken');
                localStorage.removeItem('refreshToken');
            }
            // Dispatch logout action if available
            if ("TURBOPACK compile-time truthy", 1) {
                window.location.href = '/login';
            }
        }
    }
    // Handle other common errors
    if (result.error) {
        console.error('API Error:', result.error);
        // Handle network errors
        if (result.error.status === 'FETCH_ERROR') {
            console.error('Network error - check if backend is running');
        }
        // Handle server errors
        if (result.error.status === 500) {
            console.error('Server error - check backend logs');
        }
    }
    return result;
};
const baseApi = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createApi"])({
    reducerPath: 'baseApi',
    baseQuery: baseQueryWithReauth,
    tagTypes: [
        'User',
        'UserStats',
        'Property',
        'PropertyStats',
        'Lead',
        'LeadStats',
        'Transaction',
        'TransactionStats',
        'Dashboard',
        'DashboardStats',
        'PropertyOwner',
        'PropertyOwnerStats',
        'Support',
        'SupportStats',
        'Finance',
        'FinanceStats',
        'Stock',
        'StockStats',
        'Settings',
        'SystemSettings',
        'Notification',
        'Activity',
        'KYC',
        'Analytics',
        'System',
        'AuditLog',
        'Backup',
        'Communication',
        'Campaign',
        'NotificationLog',
        'PaymentMethod',
        'Dispute',
        'Sales',
        'SalesStats',
        'SalesTeam',
        'SalesTask',
        'SalesTarget',
        'Commission',
        'Calendar',
        'Investment',
        'Portfolio',
        'Wallet'
    ],
    endpoints: ()=>({})
});
const createQueryParams = (params = {})=>({
        page: params.page || 1,
        limit: params.limit || 10,
        search: params.search,
        sortBy: params.sortBy,
        sortOrder: params.sortOrder,
        startDate: params.dateRange?.start,
        endDate: params.dateRange?.end
    });
const createFormDataQuery = (data)=>{
    const formData = new FormData();
    Object.entries(data).forEach(([key, value])=>{
        if (value instanceof File || value instanceof Blob) {
            formData.append(key, value);
        } else if (Array.isArray(value)) {
            value.forEach((item, index)=>{
                if (item instanceof File || item instanceof Blob) {
                    formData.append(`${key}[${index}]`, item);
                } else {
                    formData.append(`${key}[${index}]`, JSON.stringify(item));
                }
            });
        } else if (typeof value === 'object' && value !== null) {
            formData.append(key, JSON.stringify(value));
        } else {
            formData.append(key, String(value));
        }
    });
    return formData;
} // Export common types and utilities
 // Types exported in individual API files
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/usersApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useBulkUpdateUsersMutation": (()=>useBulkUpdateUsersMutation),
    "useConfirmUserFileUploadMutation": (()=>useConfirmUserFileUploadMutation),
    "useCreateUserMutation": (()=>useCreateUserMutation),
    "useDeleteUserDocumentMutation": (()=>useDeleteUserDocumentMutation),
    "useDeleteUserMutation": (()=>useDeleteUserMutation),
    "useExportUsersMutation": (()=>useExportUsersMutation),
    "useGetUserByIdQuery": (()=>useGetUserByIdQuery),
    "useGetUserPresignedUrlMutation": (()=>useGetUserPresignedUrlMutation),
    "useGetUserPropertiesQuery": (()=>useGetUserPropertiesQuery),
    "useGetUserStockHoldingsQuery": (()=>useGetUserStockHoldingsQuery),
    "useGetUsersQuery": (()=>useGetUsersQuery),
    "useGetUsersStatsQuery": (()=>useGetUsersStatsQuery),
    "useLazyGetUserByIdQuery": (()=>useLazyGetUserByIdQuery),
    "useLinkUserToPropertyMutation": (()=>useLinkUserToPropertyMutation),
    "useUnlinkUserFromPropertyMutation": (()=>useUnlinkUserFromPropertyMutation),
    "useUpdateUserMutation": (()=>useUpdateUserMutation),
    "useUpdateUserRoleMutation": (()=>useUpdateUserRoleMutation),
    "useVerifyUserEmailMutation": (()=>useVerifyUserEmailMutation),
    "useVerifyUserPhoneMutation": (()=>useVerifyUserPhoneMutation),
    "usersApi": (()=>usersApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const usersApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    endpoints: (builder)=>({
            // Get users with pagination and filters
            getUsers: builder.query({
                query: (params = {})=>{
                    const queryParams = {
                        page: params.page || 1,
                        limit: params.limit || 10,
                        search: params.search,
                        sort: params.sort || params.sortBy || 'createdAt',
                        order: params.order || params.sortOrder || 'desc'
                    };
                    // Add optional filters
                    if (params.role && params.role !== 'all') queryParams.role = params.role;
                    if (params.status && params.status !== 'all') queryParams.status = params.status;
                    if (params.kycStatus && params.kycStatus !== 'all') queryParams.kycStatus = params.kycStatus;
                    if (params.isEmailVerified !== undefined) queryParams.isEmailVerified = params.isEmailVerified;
                    if (params.isPhoneVerified !== undefined) queryParams.isPhoneVerified = params.isPhoneVerified;
                    if (params.dateFrom) queryParams.dateFrom = params.dateFrom;
                    if (params.dateTo) queryParams.dateTo = params.dateTo;
                    return {
                        url: '/users',
                        params: queryParams
                    };
                },
                providesTags: [
                    'User'
                ]
            }),
            // Get user by ID
            getUserById: builder.query({
                query: (id)=>`/users/${id}`,
                providesTags: (_, __, id)=>[
                        {
                            type: 'User',
                            id
                        }
                    ]
            }),
            // Get users statistics
            getUsersStats: builder.query({
                query: ()=>'/users/stats',
                providesTags: [
                    'UserStats'
                ]
            }),
            // Create user
            createUser: builder.mutation({
                query: (userData)=>({
                        url: '/users',
                        method: 'POST',
                        body: userData
                    }),
                invalidatesTags: [
                    'User',
                    'UserStats'
                ]
            }),
            // Update user
            updateUser: builder.mutation({
                query: ({ id, userData })=>({
                        url: `/users/${id}`,
                        method: 'PUT',
                        body: userData
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'User',
                            id
                        },
                        'User',
                        'UserStats'
                    ]
            }),
            // Delete user
            deleteUser: builder.mutation({
                query: (id)=>({
                        url: `/users/${id}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'User',
                    'UserStats'
                ]
            }),
            // Bulk operations
            bulkUpdateUsers: builder.mutation({
                query: ({ userIds, updates })=>({
                        url: '/users/bulk-update',
                        method: 'PUT',
                        body: {
                            userIds,
                            updates
                        }
                    }),
                invalidatesTags: [
                    'User',
                    'UserStats'
                ]
            }),
            // Export users
            exportUsers: builder.mutation({
                query: (filters)=>({
                        url: '/users/export',
                        method: 'POST',
                        body: filters,
                        responseHandler: (response)=>response.blob()
                    })
            }),
            // Get presigned URL for user document upload
            getUserPresignedUrl: builder.mutation({
                query: (uploadData)=>({
                        url: '/s3/presigned-url',
                        method: 'POST',
                        body: uploadData
                    })
            }),
            // Confirm user document upload
            confirmUserFileUpload: builder.mutation({
                query: (confirmData)=>({
                        url: '/s3/confirm-upload',
                        method: 'POST',
                        body: confirmData
                    }),
                invalidatesTags: (_, __, { userId })=>userId ? [
                        {
                            type: 'User',
                            id: userId
                        }
                    ] : []
            }),
            // Delete user document
            deleteUserDocument: builder.mutation({
                query: ({ userId, fileKey })=>({
                        url: `/users/${userId}/documents/${encodeURIComponent(fileKey)}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: (_, __, { userId })=>[
                        {
                            type: 'User',
                            id: userId
                        }
                    ]
            }),
            // Role Management
            updateUserRole: builder.mutation({
                query: ({ userId, ...data })=>({
                        url: `/users/${userId}/role`,
                        method: 'PUT',
                        body: data
                    }),
                invalidatesTags: (_, __, { userId })=>[
                        {
                            type: 'User',
                            id: userId
                        },
                        'User',
                        'UserStats'
                    ]
            }),
            // Property Linking
            linkUserToProperty: builder.mutation({
                query: ({ userId, ...data })=>({
                        url: `/users/${userId}/properties/link`,
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: (_, __, { userId })=>[
                        {
                            type: 'User',
                            id: userId
                        },
                        'User',
                        'Property'
                    ]
            }),
            unlinkUserFromProperty: builder.mutation({
                query: ({ userId, propertyId })=>({
                        url: `/users/${userId}/properties/${propertyId}/unlink`,
                        method: 'DELETE'
                    }),
                invalidatesTags: (_, __, { userId })=>[
                        {
                            type: 'User',
                            id: userId
                        },
                        'User',
                        'Property'
                    ]
            }),
            // Get user's properties
            getUserProperties: builder.query({
                query: (userId)=>`/users/${userId}/properties`,
                providesTags: (_, __, userId)=>[
                        {
                            type: 'User',
                            id: userId
                        }
                    ]
            }),
            // Stock Management for Users
            getUserStockHoldings: builder.query({
                query: (userId)=>`/users/${userId}/stocks`,
                providesTags: (_, __, userId)=>[
                        {
                            type: 'User',
                            id: userId
                        }
                    ]
            }),
            // User Verification Actions
            verifyUserEmail: builder.mutation({
                query: (userId)=>({
                        url: `/users/${userId}/verify-email`,
                        method: 'POST'
                    }),
                invalidatesTags: (_, __, userId)=>[
                        {
                            type: 'User',
                            id: userId
                        },
                        'User'
                    ]
            }),
            verifyUserPhone: builder.mutation({
                query: (userId)=>({
                        url: `/users/${userId}/verify-phone`,
                        method: 'POST'
                    }),
                invalidatesTags: (_, __, userId)=>[
                        {
                            type: 'User',
                            id: userId
                        },
                        'User'
                    ]
            })
        }),
    overrideExisting: true
});
const { useGetUsersQuery, useGetUserByIdQuery, useLazyGetUserByIdQuery, useGetUsersStatsQuery, useCreateUserMutation, useUpdateUserMutation, useDeleteUserMutation, useBulkUpdateUsersMutation, useExportUsersMutation, useGetUserPresignedUrlMutation, useConfirmUserFileUploadMutation, useDeleteUserDocumentMutation, // New hooks for enhanced functionality
useUpdateUserRoleMutation, useLinkUserToPropertyMutation, useUnlinkUserFromPropertyMutation, useGetUserPropertiesQuery, useGetUserStockHoldingsQuery, useVerifyUserEmailMutation, useVerifyUserPhoneMutation } = usersApi;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/propertiesApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "propertiesApi": (()=>propertiesApi),
    "useAddPropertyOwnerMutation": (()=>useAddPropertyOwnerMutation),
    "useBulkUpdatePropertiesMutation": (()=>useBulkUpdatePropertiesMutation),
    "useConfirmFileUploadMutation": (()=>useConfirmFileUploadMutation),
    "useCreatePropertyMutation": (()=>useCreatePropertyMutation),
    "useCreatePropertyStocksMutation": (()=>useCreatePropertyStocksMutation),
    "useDeletePropertyFileMutation": (()=>useDeletePropertyFileMutation),
    "useDeletePropertyMutation": (()=>useDeletePropertyMutation),
    "useExportPropertiesMutation": (()=>useExportPropertiesMutation),
    "useFeaturePropertyMutation": (()=>useFeaturePropertyMutation),
    "useGetPresignedUrlMutation": (()=>useGetPresignedUrlMutation),
    "useGetPropertiesQuery": (()=>useGetPropertiesQuery),
    "useGetPropertiesStatsQuery": (()=>useGetPropertiesStatsQuery),
    "useGetPropertyByIdQuery": (()=>useGetPropertyByIdQuery),
    "useGetPropertyOwnersQuery": (()=>useGetPropertyOwnersQuery),
    "useGetPropertyStocksQuery": (()=>useGetPropertyStocksQuery),
    "useLazyGetPropertyByIdQuery": (()=>useLazyGetPropertyByIdQuery),
    "useRemovePropertyOwnerMutation": (()=>useRemovePropertyOwnerMutation),
    "useUpdatePropertyMutation": (()=>useUpdatePropertyMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const propertiesApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    overrideExisting: true,
    endpoints: (builder)=>({
            // Get properties with pagination and filters
            getProperties: builder.query({
                query: (params = {})=>({
                        url: '/properties',
                        params: {
                            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createQueryParams"])(params),
                            status: params.status,
                            location: params.location,
                            minPrice: params.priceRange?.min,
                            maxPrice: params.priceRange?.max,
                            constructionStatus: params.constructionStatus,
                            featured: params.featured
                        }
                    }),
                providesTags: [
                    'Property'
                ]
            }),
            // Get property by ID
            getPropertyById: builder.query({
                query: (id)=>`/properties/${id}`,
                providesTags: (_, __, id)=>[
                        {
                            type: 'Property',
                            id
                        }
                    ]
            }),
            // Get properties statistics
            getPropertiesStats: builder.query({
                query: ()=>'/properties/stats',
                providesTags: [
                    'PropertyStats'
                ]
            }),
            // Create property
            createProperty: builder.mutation({
                query: (propertyData)=>({
                        url: '/properties',
                        method: 'POST',
                        body: propertyData
                    }),
                invalidatesTags: [
                    'Property',
                    'PropertyStats'
                ]
            }),
            // Update property
            updateProperty: builder.mutation({
                query: ({ id, propertyData })=>({
                        url: `/properties/${id}`,
                        method: 'PUT',
                        body: propertyData
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Property',
                            id
                        },
                        'Property',
                        'PropertyStats'
                    ]
            }),
            // Delete property
            deleteProperty: builder.mutation({
                query: (id)=>({
                        url: `/properties/${id}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'Property',
                    'PropertyStats'
                ]
            }),
            // Feature property
            featureProperty: builder.mutation({
                query: ({ id, featured })=>({
                        url: `/properties/${id}/feature`,
                        method: 'PUT',
                        body: {
                            featured
                        }
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Property',
                            id
                        },
                        'Property',
                        'PropertyStats'
                    ]
            }),
            // Bulk operations
            bulkUpdateProperties: builder.mutation({
                query: ({ ids, updates })=>({
                        url: '/properties/bulk-update',
                        method: 'PUT',
                        body: {
                            ids,
                            updates
                        }
                    }),
                invalidatesTags: [
                    'Property',
                    'PropertyStats'
                ]
            }),
            // Export properties
            exportProperties: builder.mutation({
                query: (filters)=>({
                        url: '/properties/export',
                        method: 'POST',
                        body: filters,
                        responseHandler: (response)=>response.blob()
                    })
            }),
            // Get presigned URL for file upload
            getPresignedUrl: builder.mutation({
                query: (uploadData)=>({
                        url: '/s3/presigned-url',
                        method: 'POST',
                        body: uploadData
                    })
            }),
            // Confirm file upload after S3 upload
            confirmFileUpload: builder.mutation({
                query: (confirmData)=>({
                        url: '/s3/confirm-upload',
                        method: 'POST',
                        body: confirmData
                    }),
                invalidatesTags: (_, __, { propertyId })=>propertyId ? [
                        {
                            type: 'Property',
                            id: propertyId
                        }
                    ] : []
            }),
            // Delete uploaded file
            deletePropertyFile: builder.mutation({
                query: ({ propertyId, fileKey })=>({
                        url: `/properties/${propertyId}/files/${encodeURIComponent(fileKey)}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: (_, __, { propertyId })=>[
                        {
                            type: 'Property',
                            id: propertyId
                        }
                    ]
            }),
            // Owner Management
            addPropertyOwner: builder.mutation({
                query: ({ propertyId, ...data })=>({
                        url: `/properties/${propertyId}/owners`,
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: (_, __, { propertyId })=>[
                        {
                            type: 'Property',
                            id: propertyId
                        },
                        'Property',
                        'User'
                    ]
            }),
            removePropertyOwner: builder.mutation({
                query: ({ propertyId, userId })=>({
                        url: `/properties/${propertyId}/owners/${userId}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: (_, __, { propertyId })=>[
                        {
                            type: 'Property',
                            id: propertyId
                        },
                        'Property',
                        'User'
                    ]
            }),
            // Get property owners
            getPropertyOwners: builder.query({
                query: (propertyId)=>`/properties/${propertyId}/owners`,
                providesTags: (_, __, propertyId)=>[
                        {
                            type: 'Property',
                            id: propertyId
                        }
                    ]
            }),
            // Stock Management
            createPropertyStocks: builder.mutation({
                query: ({ propertyId, ...data })=>({
                        url: `/properties/${propertyId}/stocks`,
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: (_, __, { propertyId })=>[
                        {
                            type: 'Property',
                            id: propertyId
                        },
                        'Property',
                        'Stock'
                    ]
            }),
            getPropertyStocks: builder.query({
                query: (propertyId)=>`/properties/${propertyId}/stocks`,
                providesTags: (_, __, propertyId)=>[
                        {
                            type: 'Property',
                            id: propertyId
                        }
                    ]
            })
        })
});
const { useGetPropertiesQuery, useGetPropertyByIdQuery, useLazyGetPropertyByIdQuery, useGetPropertiesStatsQuery, useCreatePropertyMutation, useUpdatePropertyMutation, useDeletePropertyMutation, useFeaturePropertyMutation, useBulkUpdatePropertiesMutation, useExportPropertiesMutation, useGetPresignedUrlMutation, useConfirmFileUploadMutation, useDeletePropertyFileMutation, // New hooks for enhanced functionality
useAddPropertyOwnerMutation, useRemovePropertyOwnerMutation, useGetPropertyOwnersQuery, useCreatePropertyStocksMutation, useGetPropertyStocksQuery } = propertiesApi;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/leadsApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "leadsApi": (()=>leadsApi),
    "useAddLeadNoteMutation": (()=>useAddLeadNoteMutation),
    "useAssignLeadMutation": (()=>useAssignLeadMutation),
    "useBulkUpdateLeadsMutation": (()=>useBulkUpdateLeadsMutation),
    "useCreateLeadMutation": (()=>useCreateLeadMutation),
    "useDeleteLeadMutation": (()=>useDeleteLeadMutation),
    "useExportLeadsMutation": (()=>useExportLeadsMutation),
    "useGetLeadActivityQuery": (()=>useGetLeadActivityQuery),
    "useGetLeadByIdQuery": (()=>useGetLeadByIdQuery),
    "useGetLeadsQuery": (()=>useGetLeadsQuery),
    "useGetLeadsStatsQuery": (()=>useGetLeadsStatsQuery),
    "useLazyGetLeadByIdQuery": (()=>useLazyGetLeadByIdQuery),
    "useUpdateLeadMutation": (()=>useUpdateLeadMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const leadsApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    endpoints: (builder)=>({
            // Get leads with pagination and filters
            getLeads: builder.query({
                query: (params = {})=>({
                        url: '/leads',
                        params: {
                            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createQueryParams"])(params),
                            status: params.status,
                            priority: params.priority,
                            assignedTo: params.assignedTo,
                            source: params.source,
                            city: params.city,
                            country: params.country
                        }
                    }),
                providesTags: [
                    'Lead'
                ]
            }),
            // Get lead by ID
            getLeadById: builder.query({
                query: (id)=>`/leads/${id}`,
                providesTags: (_, __, id)=>[
                        {
                            type: 'Lead',
                            id
                        }
                    ]
            }),
            // Get leads statistics
            getLeadsStats: builder.query({
                query: ()=>'/leads/stats',
                providesTags: [
                    'LeadStats'
                ]
            }),
            // Create lead
            createLead: builder.mutation({
                query: (leadData)=>({
                        url: '/leads',
                        method: 'POST',
                        body: leadData
                    }),
                invalidatesTags: [
                    'Lead',
                    'LeadStats'
                ]
            }),
            // Update lead
            updateLead: builder.mutation({
                query: ({ id, leadData })=>({
                        url: `/leads/${id}`,
                        method: 'PUT',
                        body: leadData
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Lead',
                            id
                        },
                        'Lead',
                        'LeadStats'
                    ]
            }),
            // Delete lead
            deleteLead: builder.mutation({
                query: (id)=>({
                        url: `/leads/${id}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'Lead',
                    'LeadStats'
                ]
            }),
            // Assign lead
            assignLead: builder.mutation({
                query: ({ id, assignedTo })=>({
                        url: `/leads/${id}/assign`,
                        method: 'PUT',
                        body: {
                            assignedTo
                        }
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Lead',
                            id
                        },
                        'Lead',
                        'LeadStats'
                    ]
            }),
            // Bulk operations
            bulkUpdateLeads: builder.mutation({
                query: ({ ids, updates })=>({
                        url: '/leads/bulk-update',
                        method: 'PUT',
                        body: {
                            ids,
                            updates
                        }
                    }),
                invalidatesTags: [
                    'Lead',
                    'LeadStats'
                ]
            }),
            // Export leads
            exportLeads: builder.mutation({
                query: (filters)=>({
                        url: '/leads/export',
                        method: 'POST',
                        body: filters,
                        responseHandler: (response)=>response.blob()
                    })
            }),
            // Get lead activity
            getLeadActivity: builder.query({
                query: (id)=>`/leads/${id}/activity`,
                providesTags: (_, __, id)=>[
                        {
                            type: 'Lead',
                            id
                        }
                    ]
            }),
            // Add lead note
            addLeadNote: builder.mutation({
                query: ({ id, note })=>({
                        url: `/leads/${id}/notes`,
                        method: 'POST',
                        body: {
                            note
                        }
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Lead',
                            id
                        }
                    ]
            })
        }),
    overrideExisting: true
});
const { useGetLeadsQuery, useGetLeadByIdQuery, useLazyGetLeadByIdQuery, useGetLeadsStatsQuery, useCreateLeadMutation, useUpdateLeadMutation, useDeleteLeadMutation, useAssignLeadMutation, useBulkUpdateLeadsMutation, useExportLeadsMutation, useGetLeadActivityQuery, useAddLeadNoteMutation } = leadsApi;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/dashboardApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "dashboardApi": (()=>dashboardApi),
    "useExportDashboardReportMutation": (()=>useExportDashboardReportMutation),
    "useGetDashboardStatsQuery": (()=>useGetDashboardStatsQuery),
    "useGetLeadConversionChartQuery": (()=>useGetLeadConversionChartQuery),
    "useGetNotificationsQuery": (()=>useGetNotificationsQuery),
    "useGetPropertyPerformanceChartQuery": (()=>useGetPropertyPerformanceChartQuery),
    "useGetQuickActionsQuery": (()=>useGetQuickActionsQuery),
    "useGetRecentActivitiesQuery": (()=>useGetRecentActivitiesQuery),
    "useGetRecentTransactionsQuery": (()=>useGetRecentTransactionsQuery),
    "useGetRevenueChartQuery": (()=>useGetRevenueChartQuery),
    "useGetSystemHealthQuery": (()=>useGetSystemHealthQuery),
    "useGetTopPropertiesQuery": (()=>useGetTopPropertiesQuery),
    "useGetUserGrowthChartQuery": (()=>useGetUserGrowthChartQuery),
    "useMarkAllNotificationsReadMutation": (()=>useMarkAllNotificationsReadMutation),
    "useMarkNotificationReadMutation": (()=>useMarkNotificationReadMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const dashboardApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    endpoints: (builder)=>({
            // Get dashboard statistics
            getDashboardStats: builder.query({
                query: (params = {})=>({
                        url: '/dashboard/stats',
                        params
                    }),
                providesTags: [
                    'DashboardStats'
                ]
            }),
            // Get recent activities
            getRecentActivities: builder.query({
                query: (params = {})=>({
                        url: '/dashboard/activities',
                        params: {
                            limit: params.limit || 10
                        }
                    }),
                providesTags: [
                    'Activity'
                ]
            }),
            // Get revenue chart data
            getRevenueChart: builder.query({
                query: (params = {})=>({
                        url: '/dashboard/charts',
                        params: {
                            type: 'revenue',
                            period: params.period || '7d'
                        }
                    }),
                providesTags: [
                    'DashboardStats'
                ]
            }),
            // Get user growth chart data
            getUserGrowthChart: builder.query({
                query: (params = {})=>({
                        url: '/dashboard/charts',
                        params: {
                            type: 'userGrowth',
                            period: params.period || '7d'
                        }
                    }),
                providesTags: [
                    'DashboardStats'
                ]
            }),
            // Get property performance chart data
            getPropertyPerformanceChart: builder.query({
                query: (params = {})=>({
                        url: '/dashboard/charts',
                        params: {
                            type: 'propertyPerformance',
                            period: params.period || '7d'
                        }
                    }),
                providesTags: [
                    'DashboardStats'
                ]
            }),
            // Get lead conversion chart data
            getLeadConversionChart: builder.query({
                query: (params = {})=>({
                        url: '/dashboard/charts',
                        params: {
                            type: 'leadConversion',
                            period: params.period || '7d'
                        }
                    }),
                providesTags: [
                    'DashboardStats'
                ]
            }),
            // Get top performing properties
            getTopProperties: builder.query({
                query: (params = {})=>({
                        url: '/dashboard/top-properties',
                        params: {
                            limit: params.limit || 5
                        }
                    }),
                providesTags: [
                    'DashboardStats'
                ]
            }),
            // Get recent transactions
            getRecentTransactions: builder.query({
                query: (params = {})=>({
                        url: '/dashboard/recent-transactions',
                        params: {
                            limit: params.limit || 10
                        }
                    }),
                providesTags: [
                    'DashboardStats'
                ]
            }),
            // Get system health
            getSystemHealth: builder.query({
                query: ()=>'/dashboard/system-health',
                providesTags: [
                    'DashboardStats'
                ]
            }),
            // Get notifications
            getNotifications: builder.query({
                query: (params = {})=>({
                        url: '/dashboard/notifications',
                        params: {
                            limit: params.limit || 10,
                            unreadOnly: params.unreadOnly || false
                        }
                    }),
                providesTags: [
                    'Notification'
                ]
            }),
            // Mark notification as read
            markNotificationRead: builder.mutation({
                query: (id)=>({
                        url: `/dashboard/notifications/${id}/read`,
                        method: 'PUT'
                    }),
                invalidatesTags: [
                    'Notification'
                ]
            }),
            // Mark all notifications as read
            markAllNotificationsRead: builder.mutation({
                query: ()=>({
                        url: '/dashboard/notifications/mark-all-read',
                        method: 'PUT'
                    }),
                invalidatesTags: [
                    'Notification'
                ]
            }),
            // Get quick actions data
            getQuickActions: builder.query({
                query: ()=>'/dashboard/quick-actions',
                providesTags: [
                    'DashboardStats'
                ]
            }),
            // Export dashboard report
            exportDashboardReport: builder.mutation({
                query: ({ format, period })=>({
                        url: '/dashboard/export',
                        method: 'POST',
                        body: {
                            format,
                            period
                        },
                        responseHandler: (response)=>response.blob()
                    })
            })
        }),
    overrideExisting: true
});
const { useGetDashboardStatsQuery, useGetRecentActivitiesQuery, useGetRevenueChartQuery, useGetUserGrowthChartQuery, useGetPropertyPerformanceChartQuery, useGetLeadConversionChartQuery, useGetTopPropertiesQuery, useGetRecentTransactionsQuery, useGetSystemHealthQuery, useGetNotificationsQuery, useMarkNotificationReadMutation, useMarkAllNotificationsReadMutation, useGetQuickActionsQuery, useExportDashboardReportMutation } = dashboardApi // Export types
 // Types exported in types/index.ts to avoid conflicts
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/financeApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "financeApi": (()=>financeApi),
    "useBulkProcessTransactionsMutation": (()=>useBulkProcessTransactionsMutation),
    "useCreateTransactionMutation": (()=>useCreateTransactionMutation),
    "useExportTransactionsMutation": (()=>useExportTransactionsMutation),
    "useGenerateFinancialReportMutation": (()=>useGenerateFinancialReportMutation),
    "useGetCommissionDataQuery": (()=>useGetCommissionDataQuery),
    "useGetFinanceStatsQuery": (()=>useGetFinanceStatsQuery),
    "useGetPaymentMethodsQuery": (()=>useGetPaymentMethodsQuery),
    "useGetPendingApprovalsQuery": (()=>useGetPendingApprovalsQuery),
    "useGetRevenueAnalyticsQuery": (()=>useGetRevenueAnalyticsQuery),
    "useGetTransactionAnalyticsQuery": (()=>useGetTransactionAnalyticsQuery),
    "useGetTransactionByIdQuery": (()=>useGetTransactionByIdQuery),
    "useGetTransactionsQuery": (()=>useGetTransactionsQuery),
    "useProcessTransactionMutation": (()=>useProcessTransactionMutation),
    "useUpdatePaymentMethodMutation": (()=>useUpdatePaymentMethodMutation),
    "useUpdateTransactionMutation": (()=>useUpdateTransactionMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const financeApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    overrideExisting: true,
    endpoints: (builder)=>({
            // Get transactions with pagination and filters
            getTransactions: builder.query({
                query: (params = {})=>({
                        url: '/finance/transactions',
                        params: {
                            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createQueryParams"])(params),
                            type: params.type,
                            status: params.status,
                            userId: params.userId,
                            propertyId: params.propertyId,
                            minAmount: params.amountRange?.min,
                            maxAmount: params.amountRange?.max,
                            paymentMethod: params.paymentMethod
                        }
                    }),
                providesTags: [
                    'Transaction'
                ]
            }),
            // Get transaction by ID
            getTransactionById: builder.query({
                query: (id)=>`/finance/transactions/${id}`,
                providesTags: (_, __, id)=>[
                        {
                            type: 'Transaction',
                            id
                        }
                    ]
            }),
            // Get finance statistics
            getFinanceStats: builder.query({
                query: ()=>'/finance/stats',
                providesTags: [
                    'FinanceStats'
                ]
            }),
            // Create transaction
            createTransaction: builder.mutation({
                query: (transactionData)=>({
                        url: '/finance/transactions',
                        method: 'POST',
                        body: transactionData
                    }),
                invalidatesTags: [
                    'Transaction',
                    'FinanceStats'
                ]
            }),
            // Update transaction
            updateTransaction: builder.mutation({
                query: ({ id, transactionData })=>({
                        url: `/finance/transactions/${id}`,
                        method: 'PUT',
                        body: transactionData
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Transaction',
                            id
                        },
                        'Transaction',
                        'FinanceStats'
                    ]
            }),
            // Process transaction
            processTransaction: builder.mutation({
                query: ({ id, action, reason })=>({
                        url: `/finance/transactions/${id}/process`,
                        method: 'POST',
                        body: {
                            action,
                            reason
                        }
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Transaction',
                            id
                        },
                        'Transaction',
                        'FinanceStats'
                    ]
            }),
            // Get payment methods
            getPaymentMethods: builder.query({
                query: ()=>'/finance/payment-methods',
                providesTags: [
                    'FinanceStats'
                ]
            }),
            // Update payment method
            updatePaymentMethod: builder.mutation({
                query: ({ id, methodData })=>({
                        url: `/finance/payment-methods/${id}`,
                        method: 'PUT',
                        body: methodData
                    }),
                invalidatesTags: [
                    'FinanceStats'
                ]
            }),
            // Get revenue analytics
            getRevenueAnalytics: builder.query({
                query: (params = {})=>({
                        url: '/finance/analytics/revenue',
                        params: {
                            period: params.period || '30d'
                        }
                    }),
                providesTags: [
                    'FinanceStats'
                ]
            }),
            // Get transaction analytics
            getTransactionAnalytics: builder.query({
                query: (params = {})=>({
                        url: '/finance/analytics/transactions',
                        params: {
                            period: params.period || '30d'
                        }
                    }),
                providesTags: [
                    'FinanceStats'
                ]
            }),
            // Export transactions
            exportTransactions: builder.mutation({
                query: ({ format, ...filters })=>({
                        url: '/finance/transactions/export',
                        method: 'POST',
                        body: {
                            format,
                            filters
                        },
                        responseHandler: (response)=>response.blob()
                    })
            }),
            // Generate financial report
            generateFinancialReport: builder.mutation({
                query: ({ type, period, format })=>({
                        url: '/finance/reports/generate',
                        method: 'POST',
                        body: {
                            type,
                            period,
                            format
                        },
                        responseHandler: (response)=>response.blob()
                    })
            }),
            // Bulk process transactions
            bulkProcessTransactions: builder.mutation({
                query: ({ ids, action, reason })=>({
                        url: '/finance/transactions/bulk-process',
                        method: 'POST',
                        body: {
                            ids,
                            action,
                            reason
                        }
                    }),
                invalidatesTags: [
                    'Transaction',
                    'FinanceStats'
                ]
            }),
            // Get pending approvals
            getPendingApprovals: builder.query({
                query: ()=>'/finance/pending-approvals',
                providesTags: [
                    'Transaction'
                ]
            }),
            // Get commission data
            getCommissionData: builder.query({
                query: (params = {})=>({
                        url: '/finance/commissions',
                        params: {
                            period: params.period || '30d'
                        }
                    }),
                providesTags: [
                    'FinanceStats'
                ]
            })
        })
});
const { useGetTransactionsQuery, useGetTransactionByIdQuery, useGetFinanceStatsQuery, useCreateTransactionMutation, useUpdateTransactionMutation, useProcessTransactionMutation, useGetPaymentMethodsQuery, useUpdatePaymentMethodMutation, useGetRevenueAnalyticsQuery, useGetTransactionAnalyticsQuery, useExportTransactionsMutation, useGenerateFinancialReportMutation, useBulkProcessTransactionsMutation, useGetPendingApprovalsQuery, useGetCommissionDataQuery } = financeApi // Export types
 // Types exported in types/index.ts to avoid conflicts
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/stocksApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "stocksApi": (()=>stocksApi),
    "useBulkUpdateReturnsMutation": (()=>useBulkUpdateReturnsMutation),
    "useCreateStockPurchaseMutation": (()=>useCreateStockPurchaseMutation),
    "useExportStocksMutation": (()=>useExportStocksMutation),
    "useGenerateStockCertificateMutation": (()=>useGenerateStockCertificateMutation),
    "useGetDividendHistoryQuery": (()=>useGetDividendHistoryQuery),
    "useGetPropertyStocksQuery": (()=>useGetPropertyStocksQuery),
    "useGetStockByIdQuery": (()=>useGetStockByIdQuery),
    "useGetStockPerformanceQuery": (()=>useGetStockPerformanceQuery),
    "useGetStockTransactionsQuery": (()=>useGetStockTransactionsQuery),
    "useGetStocksQuery": (()=>useGetStocksQuery),
    "useGetStocksStatsQuery": (()=>useGetStocksStatsQuery),
    "useGetUserStocksQuery": (()=>useGetUserStocksQuery),
    "useProcessDividendPaymentMutation": (()=>useProcessDividendPaymentMutation),
    "useSellStockMutation": (()=>useSellStockMutation),
    "useTransferStockMutation": (()=>useTransferStockMutation),
    "useUpdateStockReturnsMutation": (()=>useUpdateStockReturnsMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const stocksApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    overrideExisting: true,
    endpoints: (builder)=>({
            // Get stocks with pagination and filters
            getStocks: builder.query({
                query: (params = {})=>({
                        url: '/stocks',
                        params: {
                            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createQueryParams"])(params),
                            propertyId: params.propertyId,
                            userId: params.userId,
                            status: params.status,
                            minPrice: params.priceRange?.min,
                            maxPrice: params.priceRange?.max,
                            minReturn: params.returnRange?.min,
                            maxReturn: params.returnRange?.max
                        }
                    }),
                providesTags: [
                    'Stock'
                ]
            }),
            // Get stock by ID
            getStockById: builder.query({
                query: (id)=>`/stocks/${id}`,
                providesTags: (_, __, id)=>[
                        {
                            type: 'Stock',
                            id
                        }
                    ]
            }),
            // Get stocks statistics
            getStocksStats: builder.query({
                query: ()=>'/stocks/stats',
                providesTags: [
                    'StockStats'
                ]
            }),
            // Get stock transactions
            getStockTransactions: builder.query({
                query: (params = {})=>({
                        url: '/stocks/transactions',
                        params: {
                            stockId: params.stockId,
                            userId: params.userId,
                            type: params.type,
                            status: params.status,
                            page: params.page || 1,
                            limit: params.limit || 10
                        }
                    }),
                providesTags: [
                    'Stock'
                ]
            }),
            // Create stock purchase
            createStockPurchase: builder.mutation({
                query: (purchaseData)=>({
                        url: '/stocks/purchase',
                        method: 'POST',
                        body: purchaseData
                    }),
                invalidatesTags: [
                    'Stock',
                    'StockStats'
                ]
            }),
            // Transfer stock
            transferStock: builder.mutation({
                query: (transferData)=>({
                        url: '/stocks/transfer',
                        method: 'POST',
                        body: transferData
                    }),
                invalidatesTags: [
                    'Stock',
                    'StockStats'
                ]
            }),
            // Sell stock
            sellStock: builder.mutation({
                query: (saleData)=>({
                        url: '/stocks/sell',
                        method: 'POST',
                        body: saleData
                    }),
                invalidatesTags: [
                    'Stock',
                    'StockStats'
                ]
            }),
            // Update stock returns
            updateStockReturns: builder.mutation({
                query: ({ stockId, returns, dividends })=>({
                        url: `/stocks/${stockId}/returns`,
                        method: 'PUT',
                        body: {
                            returns,
                            dividends
                        }
                    }),
                invalidatesTags: (_, __, { stockId })=>[
                        {
                            type: 'Stock',
                            id: stockId
                        },
                        'Stock',
                        'StockStats'
                    ]
            }),
            // Get user stocks
            getUserStocks: builder.query({
                query: ({ userId, status, page = 1, limit = 10 })=>({
                        url: `/stocks/user/${userId}`,
                        params: {
                            status,
                            page,
                            limit
                        }
                    }),
                providesTags: [
                    'Stock'
                ]
            }),
            // Get property stocks
            getPropertyStocks: builder.query({
                query: ({ propertyId, status, page = 1, limit = 10 })=>({
                        url: `/stocks/property/${propertyId}`,
                        params: {
                            status,
                            page,
                            limit
                        }
                    }),
                providesTags: [
                    'Stock'
                ]
            }),
            // Generate stock certificate
            generateStockCertificate: builder.mutation({
                query: ({ stockId, format })=>({
                        url: `/stocks/${stockId}/certificate`,
                        method: 'POST',
                        body: {
                            format
                        },
                        responseHandler: (response)=>response.blob()
                    })
            }),
            // Get stock performance analytics
            getStockPerformance: builder.query({
                query: (params = {})=>({
                        url: '/stocks/analytics/performance',
                        params: {
                            period: params.period || '12m'
                        }
                    }),
                providesTags: [
                    'StockStats'
                ]
            }),
            // Bulk update stock returns
            bulkUpdateReturns: builder.mutation({
                query: ({ updates })=>({
                        url: '/stocks/bulk-update-returns',
                        method: 'PUT',
                        body: {
                            updates
                        }
                    }),
                invalidatesTags: [
                    'Stock',
                    'StockStats'
                ]
            }),
            // Export stocks data
            exportStocks: builder.mutation({
                query: ({ format, ...filters })=>({
                        url: '/stocks/export',
                        method: 'POST',
                        body: {
                            format,
                            filters
                        },
                        responseHandler: (response)=>response.blob()
                    })
            }),
            // Get dividend history
            getDividendHistory: builder.query({
                query: (params = {})=>({
                        url: '/stocks/dividends',
                        params: {
                            propertyId: params.propertyId,
                            status: params.status,
                            page: params.page || 1,
                            limit: params.limit || 10
                        }
                    }),
                providesTags: [
                    'Stock'
                ]
            }),
            // Process dividend payment
            processDividendPayment: builder.mutation({
                query: (dividendData)=>({
                        url: '/stocks/dividends/process',
                        method: 'POST',
                        body: dividendData
                    }),
                invalidatesTags: [
                    'Stock',
                    'StockStats'
                ]
            })
        })
});
const { useGetStocksQuery, useGetStockByIdQuery, useGetStocksStatsQuery, useGetStockTransactionsQuery, useCreateStockPurchaseMutation, useTransferStockMutation, useSellStockMutation, useUpdateStockReturnsMutation, useGetUserStocksQuery, useGetPropertyStocksQuery, useGenerateStockCertificateMutation, useGetStockPerformanceQuery, useBulkUpdateReturnsMutation, useExportStocksMutation, useGetDividendHistoryQuery, useProcessDividendPaymentMutation } = stocksApi // Export types
 // Types exported in types/index.ts to avoid conflicts
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/propertyStocksApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "propertyStocksApi": (()=>propertyStocksApi),
    "useCreateStockMutation": (()=>useCreateStockMutation),
    "useDeletePropertyStockMutation": (()=>useDeletePropertyStockMutation),
    "useGetPropertyStockByIdQuery": (()=>useGetPropertyStockByIdQuery),
    "useGetPropertyStocksQuery": (()=>useGetPropertyStocksQuery),
    "useGetStockInvestorsQuery": (()=>useGetStockInvestorsQuery),
    "useGetStockTransactionsQuery": (()=>useGetStockTransactionsQuery),
    "useUpdatePropertyStockMutation": (()=>useUpdatePropertyStockMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const propertyStocksApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    overrideExisting: true,
    endpoints: (builder)=>({
            // Create a new stock
            createStock: builder.mutation({
                query: (stockData)=>({
                        url: '/stocks',
                        method: 'POST',
                        body: stockData
                    }),
                invalidatesTags: [
                    'Stock'
                ]
            }),
            // Get all stocks with pagination and filtering
            getPropertyStocks: builder.query({
                query: (params = {})=>({
                        url: '/stocks',
                        params: {
                            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createQueryParams"])(params),
                            status: params.status,
                            riskLevel: params.riskLevel
                        }
                    }),
                providesTags: [
                    'Stock'
                ]
            }),
            // Get stock by ID
            getPropertyStockById: builder.query({
                query: (id)=>`/stocks/${id}`,
                providesTags: (_, __, id)=>[
                        {
                            type: 'Stock',
                            id
                        }
                    ]
            }),
            // Update stock
            updatePropertyStock: builder.mutation({
                query: ({ id, updates })=>({
                        url: `/stocks/${id}`,
                        method: 'PUT',
                        body: updates
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Stock',
                            id
                        },
                        'Stock'
                    ]
            }),
            // Delete stock
            deletePropertyStock: builder.mutation({
                query: (id)=>({
                        url: `/stocks/${id}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'Stock'
                ]
            }),
            // Get stock transactions
            getStockTransactions: builder.query({
                query: (stockId)=>`/stocks/${stockId}/transactions`,
                providesTags: (_, __, stockId)=>[
                        {
                            type: 'Stock',
                            id: stockId
                        }
                    ]
            }),
            // Get stock investors
            getStockInvestors: builder.query({
                query: (stockId)=>`/stocks/${stockId}/investors`,
                providesTags: (_, __, stockId)=>[
                        {
                            type: 'Stock',
                            id: stockId
                        }
                    ]
            })
        })
});
const { useCreateStockMutation, useGetPropertyStocksQuery, useGetPropertyStockByIdQuery, useUpdatePropertyStockMutation, useDeletePropertyStockMutation, useGetStockTransactionsQuery, useGetStockInvestorsQuery } = propertyStocksApi;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/propertyOwnersApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "propertyOwnersApi": (()=>propertyOwnersApi),
    "useBulkUpdatePropertyOwnersMutation": (()=>useBulkUpdatePropertyOwnersMutation),
    "useCreatePropertyOwnerMutation": (()=>useCreatePropertyOwnerMutation),
    "useDeletePropertyOwnerMutation": (()=>useDeletePropertyOwnerMutation),
    "useExportPropertyOwnersMutation": (()=>useExportPropertyOwnersMutation),
    "useGetOwnerAnalyticsQuery": (()=>useGetOwnerAnalyticsQuery),
    "useGetOwnerPerformanceQuery": (()=>useGetOwnerPerformanceQuery),
    "useGetOwnerPropertiesQuery": (()=>useGetOwnerPropertiesQuery),
    "useGetOwnerTransactionsQuery": (()=>useGetOwnerTransactionsQuery),
    "useGetPendingVerificationsQuery": (()=>useGetPendingVerificationsQuery),
    "useGetPropertyOwnerByIdQuery": (()=>useGetPropertyOwnerByIdQuery),
    "useGetPropertyOwnersQuery": (()=>useGetPropertyOwnersQuery),
    "useGetPropertyOwnersStatsQuery": (()=>useGetPropertyOwnersStatsQuery),
    "useLinkPropertyToOwnerMutation": (()=>useLinkPropertyToOwnerMutation),
    "useSearchPropertyOwnersQuery": (()=>useSearchPropertyOwnersQuery),
    "useSendOwnerNotificationMutation": (()=>useSendOwnerNotificationMutation),
    "useUnlinkPropertyFromOwnerMutation": (()=>useUnlinkPropertyFromOwnerMutation),
    "useUpdateOwnerBankDetailsMutation": (()=>useUpdateOwnerBankDetailsMutation),
    "useUpdatePropertyOwnerMutation": (()=>useUpdatePropertyOwnerMutation),
    "useUploadOwnerDocumentMutation": (()=>useUploadOwnerDocumentMutation),
    "useVerifyPropertyOwnerMutation": (()=>useVerifyPropertyOwnerMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const propertyOwnersApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    endpoints: (builder)=>({
            // Get property owners with pagination and filters
            getPropertyOwners: builder.query({
                query: (params = {})=>({
                        url: '/property-owners',
                        params: {
                            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createQueryParams"])(params),
                            status: params.status,
                            verificationStatus: params.verificationStatus,
                            city: params.city,
                            state: params.state,
                            country: params.country,
                            hasProperties: params.hasProperties,
                            company: params.company,
                            includeProperties: 'true'
                        }
                    }),
                providesTags: [
                    'PropertyOwner'
                ]
            }),
            // Get property owner by ID
            getPropertyOwnerById: builder.query({
                query: (id)=>`/property-owners/${id}`,
                providesTags: (_, __, id)=>[
                        {
                            type: 'PropertyOwner',
                            id
                        }
                    ]
            }),
            // Get property owners statistics
            getPropertyOwnersStats: builder.query({
                query: ()=>'/property-owners/stats',
                providesTags: [
                    'PropertyOwnerStats'
                ]
            }),
            // Create property owner
            createPropertyOwner: builder.mutation({
                query: (ownerData)=>({
                        url: '/property-owners',
                        method: 'POST',
                        body: ownerData
                    }),
                invalidatesTags: [
                    'PropertyOwner',
                    'PropertyOwnerStats'
                ]
            }),
            // Update property owner
            updatePropertyOwner: builder.mutation({
                query: ({ id, ownerData })=>({
                        url: `/property-owners/${id}`,
                        method: 'PUT',
                        body: ownerData
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'PropertyOwner',
                            id
                        },
                        'PropertyOwner',
                        'PropertyOwnerStats'
                    ]
            }),
            // Delete property owner
            deletePropertyOwner: builder.mutation({
                query: (id)=>({
                        url: `/property-owners/${id}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'PropertyOwner',
                    'PropertyOwnerStats'
                ]
            }),
            // Verify property owner
            verifyPropertyOwner: builder.mutation({
                query: ({ id, status, reason })=>({
                        url: `/property-owners/${id}/verify`,
                        method: 'PUT',
                        body: {
                            status,
                            reason
                        }
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'PropertyOwner',
                            id
                        },
                        'PropertyOwner',
                        'PropertyOwnerStats'
                    ]
            }),
            // Upload property owner document
            uploadOwnerDocument: builder.mutation({
                query: ({ id, documentType, file })=>{
                    const formData = new FormData();
                    formData.append('document', file);
                    formData.append('type', documentType);
                    return {
                        url: `/property-owners/${id}/documents`,
                        method: 'POST',
                        body: formData
                    };
                },
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'PropertyOwner',
                            id
                        }
                    ]
            }),
            // Get property owner properties
            getOwnerProperties: builder.query({
                query: ({ ownerId, status, page = 1, limit = 10 })=>({
                        url: `/property-owners/${ownerId}/properties`,
                        params: {
                            status,
                            page,
                            limit
                        }
                    }),
                providesTags: [
                    'PropertyOwner'
                ]
            }),
            // Get property owner transactions
            getOwnerTransactions: builder.query({
                query: ({ ownerId, type, status, page = 1, limit = 10 })=>({
                        url: `/property-owners/${ownerId}/transactions`,
                        params: {
                            type,
                            status,
                            page,
                            limit
                        }
                    }),
                providesTags: [
                    'PropertyOwner'
                ]
            }),
            // Search property owners
            searchPropertyOwners: builder.query({
                query: ({ query, type = 'all', country, verified, limit = 20 })=>({
                        url: '/property-owners/search',
                        params: {
                            q: query,
                            type,
                            country,
                            verified,
                            limit
                        }
                    }),
                providesTags: [
                    'PropertyOwner'
                ]
            }),
            // Link property to owner
            linkPropertyToOwner: builder.mutation({
                query: ({ ownerId, propertyId })=>({
                        url: `/property-owners/${ownerId}/properties/${propertyId}`,
                        method: 'POST'
                    }),
                invalidatesTags: [
                    'PropertyOwner',
                    'Property'
                ]
            }),
            // Unlink property from owner
            unlinkPropertyFromOwner: builder.mutation({
                query: ({ ownerId, propertyId })=>({
                        url: `/property-owners/${ownerId}/properties/${propertyId}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'PropertyOwner',
                    'Property'
                ]
            }),
            // Get property owner analytics
            getOwnerAnalytics: builder.query({
                query: (ownerId)=>`/property-owners/${ownerId}/analytics`,
                providesTags: [
                    'PropertyOwner'
                ]
            }),
            // Bulk update property owners
            bulkUpdatePropertyOwners: builder.mutation({
                query: ({ ids, updates })=>({
                        url: '/property-owners/bulk-update',
                        method: 'PUT',
                        body: {
                            ids,
                            updates
                        }
                    }),
                invalidatesTags: [
                    'PropertyOwner',
                    'PropertyOwnerStats'
                ]
            }),
            // Export property owners
            exportPropertyOwners: builder.mutation({
                query: ({ format, ...filters })=>({
                        url: '/property-owners/export',
                        method: 'POST',
                        body: {
                            format,
                            filters
                        },
                        responseHandler: (response)=>response.blob()
                    })
            }),
            // Get pending verifications
            getPendingVerifications: builder.query({
                query: ()=>'/property-owners/pending-verifications',
                providesTags: [
                    'PropertyOwner'
                ]
            }),
            // Send notification to property owner
            sendOwnerNotification: builder.mutation({
                query: ({ ownerId, type, subject, message })=>({
                        url: `/property-owners/${ownerId}/notify`,
                        method: 'POST',
                        body: {
                            type,
                            subject,
                            message
                        }
                    })
            }),
            // Get property owner performance
            getOwnerPerformance: builder.query({
                query: (params = {})=>({
                        url: '/property-owners/performance',
                        params: {
                            period: params.period || '12m'
                        }
                    }),
                providesTags: [
                    'PropertyOwnerStats'
                ]
            }),
            // Update owner bank details
            updateOwnerBankDetails: builder.mutation({
                query: ({ id, bankDetails })=>({
                        url: `/property-owners/${id}/bank-details`,
                        method: 'PUT',
                        body: bankDetails
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'PropertyOwner',
                            id
                        }
                    ]
            })
        }),
    overrideExisting: true
});
const { useGetPropertyOwnersQuery, useGetPropertyOwnerByIdQuery, useGetPropertyOwnersStatsQuery, useCreatePropertyOwnerMutation, useUpdatePropertyOwnerMutation, useDeletePropertyOwnerMutation, useVerifyPropertyOwnerMutation, useUploadOwnerDocumentMutation, useGetOwnerPropertiesQuery, useGetOwnerTransactionsQuery, useGetOwnerAnalyticsQuery, useBulkUpdatePropertyOwnersMutation, useExportPropertyOwnersMutation, useGetPendingVerificationsQuery, useSendOwnerNotificationMutation, useGetOwnerPerformanceQuery, useUpdateOwnerBankDetailsMutation, useSearchPropertyOwnersQuery, useLinkPropertyToOwnerMutation, useUnlinkPropertyFromOwnerMutation } = propertyOwnersApi // Export types
 // Types exported in types/index.ts to avoid conflicts
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/supportApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "supportApi": (()=>supportApi),
    "useAddTicketMessageMutation": (()=>useAddTicketMessageMutation),
    "useAssignTicketMutation": (()=>useAssignTicketMutation),
    "useCloseTicketMutation": (()=>useCloseTicketMutation),
    "useConfirmSupportFileUploadMutation": (()=>useConfirmSupportFileUploadMutation),
    "useCreateKnowledgeBaseArticleMutation": (()=>useCreateKnowledgeBaseArticleMutation),
    "useCreateSupportTicketMutation": (()=>useCreateSupportTicketMutation),
    "useDeleteKnowledgeBaseArticleMutation": (()=>useDeleteKnowledgeBaseArticleMutation),
    "useExportSupportTicketsMutation": (()=>useExportSupportTicketsMutation),
    "useGetKnowledgeBaseArticlesQuery": (()=>useGetKnowledgeBaseArticlesQuery),
    "useGetSupportAgentsQuery": (()=>useGetSupportAgentsQuery),
    "useGetSupportPresignedUrlMutation": (()=>useGetSupportPresignedUrlMutation),
    "useGetSupportStatsQuery": (()=>useGetSupportStatsQuery),
    "useGetSupportTicketByIdQuery": (()=>useGetSupportTicketByIdQuery),
    "useGetSupportTicketsQuery": (()=>useGetSupportTicketsQuery),
    "useGetTicketAnalyticsQuery": (()=>useGetTicketAnalyticsQuery),
    "useReopenTicketMutation": (()=>useReopenTicketMutation),
    "useResolveTicketMutation": (()=>useResolveTicketMutation),
    "useSubmitSatisfactionRatingMutation": (()=>useSubmitSatisfactionRatingMutation),
    "useUpdateKnowledgeBaseArticleMutation": (()=>useUpdateKnowledgeBaseArticleMutation),
    "useUpdateSupportTicketMutation": (()=>useUpdateSupportTicketMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const supportApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    overrideExisting: true,
    endpoints: (builder)=>({
            // Get support tickets with pagination and filters
            getSupportTickets: builder.query({
                query: (params = {})=>({
                        url: '/support/tickets',
                        params: {
                            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createQueryParams"])(params),
                            status: params.status,
                            priority: params.priority,
                            category: params.category,
                            assignedTo: params.assignedTo,
                            userId: params.userId
                        }
                    }),
                providesTags: [
                    'Support'
                ]
            }),
            // Get support ticket by ID
            getSupportTicketById: builder.query({
                query: (id)=>`/support/tickets/${id}`,
                providesTags: (_, __, id)=>[
                        {
                            type: 'Support',
                            id
                        }
                    ]
            }),
            // Get support statistics
            getSupportStats: builder.query({
                query: ()=>'/support/stats',
                providesTags: [
                    'SupportStats'
                ]
            }),
            // Create support ticket
            createSupportTicket: builder.mutation({
                query: (ticketData)=>({
                        url: '/support/tickets',
                        method: 'POST',
                        body: ticketData
                    }),
                invalidatesTags: [
                    'Support',
                    'SupportStats'
                ]
            }),
            // Get presigned URL for support attachment upload
            getSupportPresignedUrl: builder.mutation({
                query: (uploadData)=>({
                        url: '/s3/presigned-url',
                        method: 'POST',
                        body: uploadData
                    })
            }),
            // Confirm support attachment upload
            confirmSupportFileUpload: builder.mutation({
                query: (confirmData)=>({
                        url: '/support/upload/confirm',
                        method: 'POST',
                        body: confirmData
                    }),
                invalidatesTags: (_, __, { ticketId })=>ticketId ? [
                        {
                            type: 'Support',
                            id: ticketId
                        }
                    ] : [
                        'Support'
                    ]
            }),
            // Update support ticket
            updateSupportTicket: builder.mutation({
                query: ({ id, ticketData })=>({
                        url: `/support/tickets/${id}`,
                        method: 'PUT',
                        body: ticketData
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Support',
                            id
                        },
                        'Support',
                        'SupportStats'
                    ]
            }),
            // Assign ticket
            assignTicket: builder.mutation({
                query: ({ ticketId, assignedTo })=>({
                        url: `/support/tickets/${ticketId}/assign`,
                        method: 'POST',
                        body: {
                            assignedTo
                        }
                    }),
                invalidatesTags: (_, __, { ticketId })=>[
                        {
                            type: 'Support',
                            id: ticketId
                        },
                        'Support',
                        'SupportStats'
                    ]
            }),
            // Add message to ticket
            addTicketMessage: builder.mutation({
                query: ({ ticketId, ...messageData })=>({
                        url: `/support/tickets/${ticketId}/messages`,
                        method: 'POST',
                        body: messageData
                    }),
                invalidatesTags: (_, __, { ticketId })=>[
                        {
                            type: 'Support',
                            id: ticketId
                        },
                        'Support',
                        'SupportStats'
                    ]
            }),
            // Resolve ticket
            resolveTicket: builder.mutation({
                query: ({ ticketId, resolutionNotes })=>({
                        url: `/support/tickets/${ticketId}/resolve`,
                        method: 'POST',
                        body: {
                            resolutionNotes
                        }
                    }),
                invalidatesTags: (_, __, { ticketId })=>[
                        {
                            type: 'Support',
                            id: ticketId
                        },
                        'Support',
                        'SupportStats'
                    ]
            }),
            // Close ticket
            closeTicket: builder.mutation({
                query: ({ id, resolution })=>({
                        url: `/support/tickets/${id}/close`,
                        method: 'PUT',
                        body: {
                            resolution
                        }
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Support',
                            id
                        },
                        'Support',
                        'SupportStats'
                    ]
            }),
            // Reopen ticket
            reopenTicket: builder.mutation({
                query: ({ id, reason })=>({
                        url: `/support/tickets/${id}/reopen`,
                        method: 'PUT',
                        body: {
                            reason
                        }
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Support',
                            id
                        },
                        'Support',
                        'SupportStats'
                    ]
            }),
            // Get knowledge base articles
            getKnowledgeBaseArticles: builder.query({
                query: (params = {})=>({
                        url: '/support/knowledge-base',
                        params: {
                            category: params.category,
                            search: params.search,
                            published: params.published,
                            page: params.page || 1,
                            limit: params.limit || 10
                        }
                    }),
                providesTags: [
                    'Support'
                ]
            }),
            // Create knowledge base article
            createKnowledgeBaseArticle: builder.mutation({
                query: (articleData)=>({
                        url: '/support/knowledge-base',
                        method: 'POST',
                        body: articleData
                    }),
                invalidatesTags: [
                    'Support'
                ]
            }),
            // Update knowledge base article
            updateKnowledgeBaseArticle: builder.mutation({
                query: ({ id, articleData })=>({
                        url: `/support/knowledge-base/${id}`,
                        method: 'PUT',
                        body: articleData
                    }),
                invalidatesTags: [
                    'Support'
                ]
            }),
            // Delete knowledge base article
            deleteKnowledgeBaseArticle: builder.mutation({
                query: (id)=>({
                        url: `/support/knowledge-base/${id}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'Support'
                ]
            }),
            // Get support agents
            getSupportAgents: builder.query({
                query: ()=>'/support/agents',
                providesTags: [
                    'Support'
                ]
            }),
            // Export support tickets
            exportSupportTickets: builder.mutation({
                query: ({ format, ...filters })=>({
                        url: '/support/tickets/export',
                        method: 'POST',
                        body: {
                            format,
                            filters
                        },
                        responseHandler: (response)=>response.blob()
                    })
            }),
            // Submit satisfaction rating
            submitSatisfactionRating: builder.mutation({
                query: ({ ticketId, rating, feedback })=>({
                        url: `/support/tickets/${ticketId}/satisfaction`,
                        method: 'POST',
                        body: {
                            rating,
                            feedback
                        }
                    }),
                invalidatesTags: (_, __, { ticketId })=>[
                        {
                            type: 'Support',
                            id: ticketId
                        },
                        'SupportStats'
                    ]
            }),
            // Get ticket analytics
            getTicketAnalytics: builder.query({
                query: (params = {})=>({
                        url: '/support/analytics',
                        params: {
                            period: params.period || '30d'
                        }
                    }),
                providesTags: [
                    'SupportStats'
                ]
            })
        })
});
const { useGetSupportTicketsQuery, useGetSupportTicketByIdQuery, useGetSupportStatsQuery, useCreateSupportTicketMutation, useUpdateSupportTicketMutation, useAssignTicketMutation, useAddTicketMessageMutation, useResolveTicketMutation, useCloseTicketMutation, useReopenTicketMutation, useGetKnowledgeBaseArticlesQuery, useCreateKnowledgeBaseArticleMutation, useUpdateKnowledgeBaseArticleMutation, useDeleteKnowledgeBaseArticleMutation, useGetSupportAgentsQuery, useExportSupportTicketsMutation, useSubmitSatisfactionRatingMutation, useGetTicketAnalyticsQuery, useGetSupportPresignedUrlMutation, useConfirmSupportFileUploadMutation } = supportApi // Export types
 // Types exported in types/index.ts to avoid conflicts
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/settingsApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "settingsApi": (()=>settingsApi),
    "useApplyAdminSettingsPresetMutation": (()=>useApplyAdminSettingsPresetMutation),
    "useBackupSettingsMutation": (()=>useBackupSettingsMutation),
    "useBulkUpdateSystemSettingsMutation": (()=>useBulkUpdateSystemSettingsMutation),
    "useClearSystemLogsMutation": (()=>useClearSystemLogsMutation),
    "useCreateAdminSettingMutation": (()=>useCreateAdminSettingMutation),
    "useCreateApiKeyMutation": (()=>useCreateApiKeyMutation),
    "useCreateEmailTemplateMutation": (()=>useCreateEmailTemplateMutation),
    "useDeleteAdminSettingMutation": (()=>useDeleteAdminSettingMutation),
    "useDeleteEmailTemplateMutation": (()=>useDeleteEmailTemplateMutation),
    "useExportAdminSettingsMutation": (()=>useExportAdminSettingsMutation),
    "useGetAdminSettingByIdQuery": (()=>useGetAdminSettingByIdQuery),
    "useGetAdminSettingsByCategoryQuery": (()=>useGetAdminSettingsByCategoryQuery),
    "useGetAdminSettingsPresetsQuery": (()=>useGetAdminSettingsPresetsQuery),
    "useGetAdminSettingsQuery": (()=>useGetAdminSettingsQuery),
    "useGetAdminSettingsRecommendationsQuery": (()=>useGetAdminSettingsRecommendationsQuery),
    "useGetApiKeysQuery": (()=>useGetApiKeysQuery),
    "useGetEmailTemplateByIdQuery": (()=>useGetEmailTemplateByIdQuery),
    "useGetEmailTemplatesQuery": (()=>useGetEmailTemplatesQuery),
    "useGetNotificationSettingsQuery": (()=>useGetNotificationSettingsQuery),
    "useGetPaymentSettingsQuery": (()=>useGetPaymentSettingsQuery),
    "useGetSecuritySettingsQuery": (()=>useGetSecuritySettingsQuery),
    "useGetSettingByKeyQuery": (()=>useGetSettingByKeyQuery),
    "useGetSystemLogsQuery": (()=>useGetSystemLogsQuery),
    "useGetSystemSettingsQuery": (()=>useGetSystemSettingsQuery),
    "useImportAdminSettingsMutation": (()=>useImportAdminSettingsMutation),
    "useResetAdminSettingsToDefaultsMutation": (()=>useResetAdminSettingsToDefaultsMutation),
    "useRestoreSettingsMutation": (()=>useRestoreSettingsMutation),
    "useRevokeApiKeyMutation": (()=>useRevokeApiKeyMutation),
    "useTestEmailConfigurationMutation": (()=>useTestEmailConfigurationMutation),
    "useToggleAdminSettingStatusMutation": (()=>useToggleAdminSettingStatusMutation),
    "useUpdateAdminSettingMutation": (()=>useUpdateAdminSettingMutation),
    "useUpdateEmailTemplateMutation": (()=>useUpdateEmailTemplateMutation),
    "useUpdateNotificationSettingsMutation": (()=>useUpdateNotificationSettingsMutation),
    "useUpdatePaymentSettingsMutation": (()=>useUpdatePaymentSettingsMutation),
    "useUpdateSecuritySettingsMutation": (()=>useUpdateSecuritySettingsMutation),
    "useUpdateSystemSettingMutation": (()=>useUpdateSystemSettingMutation),
    "useValidateAdminSettingsConfigurationQuery": (()=>useValidateAdminSettingsConfigurationQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const settingsApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    overrideExisting: true,
    endpoints: (builder)=>({
            // Get all system settings
            getSystemSettings: builder.query({
                query: (params = {})=>({
                        url: '/settings/system',
                        params: {
                            category: params.category,
                            publicOnly: params.publicOnly
                        }
                    }),
                providesTags: [
                    'SystemSettings'
                ]
            }),
            // Get setting by key
            getSettingByKey: builder.query({
                query: (key)=>`/settings/system/${key}`,
                providesTags: (_, __, key)=>[
                        {
                            type: 'SystemSettings',
                            id: key
                        }
                    ]
            }),
            // Update system setting
            updateSystemSetting: builder.mutation({
                query: ({ key, value })=>({
                        url: `/settings/system/${key}`,
                        method: 'PUT',
                        body: {
                            value
                        }
                    }),
                invalidatesTags: (_, __, { key })=>[
                        {
                            type: 'SystemSettings',
                            id: key
                        },
                        'SystemSettings'
                    ]
            }),
            // Bulk update system settings
            bulkUpdateSystemSettings: builder.mutation({
                query: ({ settings })=>({
                        url: '/settings/system/bulk-update',
                        method: 'PUT',
                        body: {
                            settings
                        }
                    }),
                invalidatesTags: [
                    'SystemSettings'
                ]
            }),
            // Get email templates
            getEmailTemplates: builder.query({
                query: (params = {})=>({
                        url: '/settings/email-templates',
                        params: {
                            type: params.type,
                            active: params.active
                        }
                    }),
                providesTags: [
                    'SystemSettings'
                ]
            }),
            // Get email template by ID
            getEmailTemplateById: builder.query({
                query: (id)=>`/settings/email-templates/${id}`,
                providesTags: (_, __, id)=>[
                        {
                            type: 'SystemSettings',
                            id
                        }
                    ]
            }),
            // Create email template
            createEmailTemplate: builder.mutation({
                query: (templateData)=>({
                        url: '/settings/email-templates',
                        method: 'POST',
                        body: templateData
                    }),
                invalidatesTags: [
                    'SystemSettings'
                ]
            }),
            // Update email template
            updateEmailTemplate: builder.mutation({
                query: ({ id, templateData })=>({
                        url: `/settings/email-templates/${id}`,
                        method: 'PUT',
                        body: templateData
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'SystemSettings',
                            id
                        },
                        'SystemSettings'
                    ]
            }),
            // Delete email template
            deleteEmailTemplate: builder.mutation({
                query: (id)=>({
                        url: `/settings/email-templates/${id}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'SystemSettings'
                ]
            }),
            // Get notification settings
            getNotificationSettings: builder.query({
                query: (params = {})=>({
                        url: '/settings/notifications',
                        params: {
                            userId: params.userId
                        }
                    }),
                providesTags: [
                    'SystemSettings'
                ]
            }),
            // Update notification settings
            updateNotificationSettings: builder.mutation({
                query: ({ userId, settings })=>({
                        url: '/settings/notifications',
                        method: 'PUT',
                        body: {
                            userId,
                            ...settings
                        }
                    }),
                invalidatesTags: [
                    'SystemSettings'
                ]
            }),
            // Get security settings
            getSecuritySettings: builder.query({
                query: ()=>'/settings/security',
                providesTags: [
                    'SystemSettings'
                ]
            }),
            // Update security settings
            updateSecuritySettings: builder.mutation({
                query: (securityData)=>({
                        url: '/settings/security',
                        method: 'PUT',
                        body: securityData
                    }),
                invalidatesTags: [
                    'SystemSettings'
                ]
            }),
            // Get payment settings
            getPaymentSettings: builder.query({
                query: ()=>'/settings/payment',
                providesTags: [
                    'SystemSettings'
                ]
            }),
            // Update payment settings
            updatePaymentSettings: builder.mutation({
                query: (paymentData)=>({
                        url: '/settings/payment',
                        method: 'PUT',
                        body: paymentData
                    }),
                invalidatesTags: [
                    'SystemSettings'
                ]
            }),
            // Test email configuration
            testEmailConfiguration: builder.mutation({
                query: (emailData)=>({
                        url: '/settings/test-email',
                        method: 'POST',
                        body: emailData
                    })
            }),
            // Backup settings
            backupSettings: builder.mutation({
                query: ()=>({
                        url: '/settings/backup',
                        method: 'POST',
                        responseHandler: (response)=>response.blob()
                    })
            }),
            // Restore settings
            restoreSettings: builder.mutation({
                query: ({ file, overwrite = false })=>{
                    const formData = new FormData();
                    formData.append('backup', file);
                    formData.append('overwrite', String(overwrite));
                    return {
                        url: '/settings/restore',
                        method: 'POST',
                        body: formData
                    };
                },
                invalidatesTags: [
                    'SystemSettings'
                ]
            }),
            // Get system logs
            getSystemLogs: builder.query({
                query: (params = {})=>({
                        url: '/settings/logs',
                        params: {
                            level: params.level,
                            module: params.module,
                            startDate: params.startDate,
                            endDate: params.endDate,
                            page: params.page || 1,
                            limit: params.limit || 50
                        }
                    }),
                providesTags: [
                    'SystemSettings'
                ]
            }),
            // Clear system logs
            clearSystemLogs: builder.mutation({
                query: (params)=>({
                        url: '/settings/logs/clear',
                        method: 'DELETE',
                        body: params
                    }),
                invalidatesTags: [
                    'SystemSettings'
                ]
            }),
            // Get API keys
            getApiKeys: builder.query({
                query: ()=>'/settings/api-keys',
                providesTags: [
                    'SystemSettings'
                ]
            }),
            // Create API key
            createApiKey: builder.mutation({
                query: (keyData)=>({
                        url: '/settings/api-keys',
                        method: 'POST',
                        body: keyData
                    }),
                invalidatesTags: [
                    'SystemSettings'
                ]
            }),
            // Revoke API key
            revokeApiKey: builder.mutation({
                query: (id)=>({
                        url: `/settings/api-keys/${id}/revoke`,
                        method: 'PUT'
                    }),
                invalidatesTags: [
                    'SystemSettings'
                ]
            }),
            // Admin Settings Endpoints (matching current page structure)
            // Get all admin settings
            getAdminSettings: builder.query({
                query: (params = {})=>({
                        url: '/admin/settings',
                        params
                    }),
                providesTags: [
                    'Settings'
                ]
            }),
            // Get admin setting by ID
            getAdminSettingById: builder.query({
                query: (id)=>`/admin/settings/${id}`,
                providesTags: (_, __, id)=>[
                        {
                            type: 'Settings',
                            id
                        }
                    ]
            }),
            // Create admin setting
            createAdminSetting: builder.mutation({
                query: (settingData)=>({
                        url: '/admin/settings',
                        method: 'POST',
                        body: settingData
                    }),
                invalidatesTags: [
                    'Settings'
                ]
            }),
            // Update admin setting
            updateAdminSetting: builder.mutation({
                query: ({ id, settingData, value })=>({
                        url: `/admin/settings/${id}`,
                        method: 'PUT',
                        body: settingData || {
                            value
                        }
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Settings',
                            id
                        },
                        'Settings'
                    ]
            }),
            // Toggle admin setting status
            toggleAdminSettingStatus: builder.mutation({
                query: ({ id, isActive })=>({
                        url: `/admin/settings/${id}/toggle`,
                        method: 'PUT',
                        body: {
                            isActive
                        }
                    }),
                invalidatesTags: (_, __, { id })=>[
                        {
                            type: 'Settings',
                            id
                        },
                        'Settings'
                    ]
            }),
            // Delete admin setting
            deleteAdminSetting: builder.mutation({
                query: (id)=>({
                        url: `/admin/settings/${id}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: (_, __, id)=>[
                        {
                            type: 'Settings',
                            id
                        },
                        'Settings'
                    ]
            }),
            // Get admin settings by category
            getAdminSettingsByCategory: builder.query({
                query: (category)=>`/admin/settings/category/${category}`,
                providesTags: (_, __, category)=>[
                        {
                            type: 'Settings',
                            id: `category-${category}`
                        },
                        'Settings'
                    ]
            }),
            // Reset all settings to defaults
            resetAdminSettingsToDefaults: builder.mutation({
                query: ()=>({
                        url: '/admin/settings/reset',
                        method: 'POST'
                    }),
                invalidatesTags: [
                    'Settings'
                ]
            }),
            // Export settings configuration
            exportAdminSettings: builder.mutation({
                query: ()=>({
                        url: '/admin/settings/export',
                        method: 'GET',
                        responseHandler: (response)=>response.blob()
                    })
            }),
            // Import settings configuration
            importAdminSettings: builder.mutation({
                query: ({ settings, overwrite = false })=>({
                        url: '/admin/settings/import',
                        method: 'POST',
                        body: {
                            settings,
                            overwrite
                        }
                    }),
                invalidatesTags: [
                    'Settings'
                ]
            }),
            // Get available configuration presets
            getAdminSettingsPresets: builder.query({
                query: ()=>'/admin/settings/presets',
                providesTags: [
                    'Settings'
                ]
            }),
            // Apply configuration preset
            applyAdminSettingsPreset: builder.mutation({
                query: (presetName)=>({
                        url: `/admin/settings/presets/${presetName}`,
                        method: 'POST'
                    }),
                invalidatesTags: [
                    'Settings'
                ]
            }),
            // Validate current configuration
            validateAdminSettingsConfiguration: builder.query({
                query: ()=>'/admin/settings/validate',
                providesTags: [
                    'Settings'
                ]
            }),
            // Get configuration recommendations
            getAdminSettingsRecommendations: builder.query({
                query: ()=>'/admin/settings/recommendations',
                providesTags: [
                    'Settings'
                ]
            })
        })
});
const { useGetSystemSettingsQuery, useGetSettingByKeyQuery, useUpdateSystemSettingMutation, useBulkUpdateSystemSettingsMutation, useGetEmailTemplatesQuery, useGetEmailTemplateByIdQuery, useCreateEmailTemplateMutation, useUpdateEmailTemplateMutation, useDeleteEmailTemplateMutation, useGetNotificationSettingsQuery, useUpdateNotificationSettingsMutation, useGetSecuritySettingsQuery, useUpdateSecuritySettingsMutation, useGetPaymentSettingsQuery, useUpdatePaymentSettingsMutation, useTestEmailConfigurationMutation, useBackupSettingsMutation, useRestoreSettingsMutation, useGetSystemLogsQuery, useClearSystemLogsMutation, useGetApiKeysQuery, useCreateApiKeyMutation, useRevokeApiKeyMutation, // Admin Settings hooks
useGetAdminSettingsQuery, useGetAdminSettingByIdQuery, useCreateAdminSettingMutation, useUpdateAdminSettingMutation, useToggleAdminSettingStatusMutation, useDeleteAdminSettingMutation, useGetAdminSettingsByCategoryQuery, useResetAdminSettingsToDefaultsMutation, useExportAdminSettingsMutation, useImportAdminSettingsMutation, useGetAdminSettingsPresetsQuery, useApplyAdminSettingsPresetMutation, useValidateAdminSettingsConfigurationQuery, useGetAdminSettingsRecommendationsQuery } = settingsApi // Export types
 // Types exported in types/index.ts to avoid conflicts
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/adminPaymentApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "adminPaymentApi": (()=>adminPaymentApi),
    "useAddFundsToUserMutation": (()=>useAddFundsToUserMutation),
    "useApproveWithdrawalMutation": (()=>useApproveWithdrawalMutation),
    "useDeductFundsFromUserMutation": (()=>useDeductFundsFromUserMutation),
    "useGetAllTransactionsQuery": (()=>useGetAllTransactionsQuery),
    "useGetPendingWithdrawalsQuery": (()=>useGetPendingWithdrawalsQuery),
    "useRejectWithdrawalMutation": (()=>useRejectWithdrawalMutation),
    "useSearchUsersQuery": (()=>useSearchUsersQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const adminPaymentApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    endpoints: (builder)=>({
            // Add funds to user
            addFundsToUser: builder.mutation({
                query: (data)=>({
                        url: '/admin/payments/add-funds',
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Transaction',
                    'User'
                ]
            }),
            // Deduct funds from user
            deductFundsFromUser: builder.mutation({
                query: (data)=>({
                        url: '/admin/payments/deduct-funds',
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Transaction',
                    'User'
                ]
            }),
            // Get all transactions
            getAllTransactions: builder.query({
                query: (filters = {})=>{
                    const params = new URLSearchParams();
                    Object.entries(filters).forEach(([key, value])=>{
                        if (value !== undefined && value !== '') {
                            params.append(key, value.toString());
                        }
                    });
                    return `/admin/payments/transactions?${params.toString()}`;
                },
                transformResponse: (response)=>{
                    // Handle the API response structure
                    if (response.success && response.data) {
                        return response.data;
                    }
                    return {
                        transactions: [],
                        pagination: {
                            page: 1,
                            limit: 50,
                            total: 0,
                            pages: 0
                        },
                        statistics: {
                            totalAmount: 0,
                            totalCredit: 0,
                            totalDebit: 0,
                            totalTransactions: 0
                        }
                    };
                },
                providesTags: [
                    'Transaction'
                ]
            }),
            // Get pending withdrawals
            getPendingWithdrawals: builder.query({
                query: ({ page = 1, limit = 20 } = {})=>`/admin/payments/withdrawals/pending?page=${page}&limit=${limit}`,
                transformResponse: (response)=>{
                    // Handle the API response structure
                    if (response.success && response.data) {
                        return response.data;
                    }
                    return {
                        withdrawals: [],
                        pagination: {
                            page: 1,
                            limit: 20,
                            total: 0,
                            pages: 0
                        }
                    };
                },
                providesTags: [
                    'Transaction'
                ]
            }),
            // Approve withdrawal
            approveWithdrawal: builder.mutation({
                query: (data)=>({
                        url: '/admin/payments/withdrawals/approve',
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Transaction'
                ]
            }),
            // Reject withdrawal
            rejectWithdrawal: builder.mutation({
                query: (data)=>({
                        url: '/admin/payments/withdrawals/reject',
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Transaction'
                ]
            }),
            // Search users
            searchUsers: builder.query({
                query: ({ query, limit = 10 })=>`/admin/payments/users/search?query=${encodeURIComponent(query)}&limit=${limit}`,
                transformResponse: (response)=>{
                    // Debug logging
                    console.log('Search Users API Response:', response);
                    // Handle the API response structure
                    if (response.success && response.data) {
                        return response.data;
                    }
                    return {
                        users: [],
                        total: 0
                    };
                },
                providesTags: [
                    'User'
                ]
            })
        }),
    overrideExisting: false
});
const { useAddFundsToUserMutation, useDeductFundsFromUserMutation, useGetAllTransactionsQuery, useGetPendingWithdrawalsQuery, useApproveWithdrawalMutation, useRejectWithdrawalMutation, useSearchUsersQuery } = adminPaymentApi;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/adminUserApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "adminUserApi": (()=>adminUserApi),
    "useAdminResetUserPasswordMutation": (()=>useAdminResetUserPasswordMutation),
    "useGetAllUsersQuery": (()=>useGetAllUsersQuery),
    "useGetUserDetailsQuery": (()=>useGetUserDetailsQuery),
    "useGetUserKYCQuery": (()=>useGetUserKYCQuery),
    "useSearchUsersForPaymentsQuery": (()=>useSearchUsersForPaymentsQuery),
    "useUpdateUserStatusMutation": (()=>useUpdateUserStatusMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const adminUserApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    endpoints: (builder)=>({
            // Get all users
            getAllUsers: builder.query({
                query: (filters = {})=>{
                    const params = new URLSearchParams();
                    Object.entries(filters).forEach(([key, value])=>{
                        if (value !== undefined && value !== '') {
                            params.append(key, value.toString());
                        }
                    });
                    return `/admin/users?${params.toString()}`;
                },
                transformResponse: (response)=>{
                    // Debug logging
                    console.log('Get All Users API Response:', response);
                    // Handle the API response structure
                    if (response.success && response.data) {
                        return response.data;
                    }
                    return {
                        users: [],
                        pagination: {
                            page: 1,
                            limit: 20,
                            total: 0,
                            pages: 0
                        },
                        statistics: {
                            totalUsers: 0,
                            activeUsers: 0,
                            inactiveUsers: 0,
                            blockedUsers: 0
                        }
                    };
                },
                providesTags: [
                    'User'
                ]
            }),
            // Get user details
            getUserDetails: builder.query({
                query: (userId)=>`/admin/users/${userId}`,
                transformResponse: (response)=>{
                    // Debug logging
                    console.log('Get User Details API Response:', response);
                    // Handle the API response structure
                    if (response.success && response.data) {
                        return response.data;
                    }
                    return null;
                },
                providesTags: (result, error, userId)=>[
                        {
                            type: 'User',
                            id: userId
                        }
                    ]
            }),
            // Get user KYC details
            getUserKYC: builder.query({
                query: (userId)=>`/admin/users/${userId}/kyc`,
                transformResponse: (response)=>{
                    // Debug logging
                    console.log('Get User KYC API Response:', response);
                    // Handle the API response structure
                    if (response.success && response.data) {
                        return response.data;
                    }
                    return null;
                },
                providesTags: (result, error, userId)=>[
                        {
                            type: 'User',
                            id: userId
                        }
                    ]
            }),
            // Update user status
            updateUserStatus: builder.mutation({
                query: ({ userId, ...data })=>({
                        url: `/admin/users/${userId}/status`,
                        method: 'PUT',
                        body: data
                    }),
                transformResponse: (response)=>{
                    // Debug logging
                    console.log('Update User Status API Response:', response);
                    // Handle the API response structure
                    if (response.success && response.data) {
                        return response.data;
                    }
                    return response;
                },
                invalidatesTags: (result, error, { userId })=>[
                        'User',
                        {
                            type: 'User',
                            id: userId
                        }
                    ]
            }),
            // Search users (for payment management)
            searchUsersForPayments: builder.query({
                query: ({ query, limit = 10 })=>`/admin/payments/users/search?query=${encodeURIComponent(query)}&limit=${limit}`,
                transformResponse: (response)=>{
                    // Debug logging
                    console.log('Search Users for Payments API Response:', response);
                    // Handle the API response structure
                    if (response.success && response.data) {
                        return response.data;
                    }
                    return {
                        users: [],
                        total: 0
                    };
                },
                providesTags: [
                    'User'
                ]
            }),
            // Admin Password Reset for User
            adminResetUserPassword: builder.mutation({
                query: ({ userId, sendEmail = true })=>({
                        url: `/admin/users/${userId}/reset-password`,
                        method: 'POST',
                        body: {
                            sendEmail
                        }
                    }),
                invalidatesTags: [
                    'User'
                ]
            })
        }),
    overrideExisting: false
});
const { useGetAllUsersQuery, useGetUserDetailsQuery, useGetUserKYCQuery, useUpdateUserStatusMutation, useSearchUsersForPaymentsQuery, useAdminResetUserPasswordMutation } = adminUserApi;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/analyticsApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "analyticsApi": (()=>analyticsApi),
    "useExportAnalyticsDataMutation": (()=>useExportAnalyticsDataMutation),
    "useGenerateCustomReportMutation": (()=>useGenerateCustomReportMutation),
    "useGetComparativeAnalyticsQuery": (()=>useGetComparativeAnalyticsQuery),
    "useGetDashboardMetricsQuery": (()=>useGetDashboardMetricsQuery),
    "useGetFinancialAnalyticsQuery": (()=>useGetFinancialAnalyticsQuery),
    "useGetPredictiveAnalyticsQuery": (()=>useGetPredictiveAnalyticsQuery),
    "useGetPropertyAnalyticsQuery": (()=>useGetPropertyAnalyticsQuery),
    "useGetSystemAnalyticsQuery": (()=>useGetSystemAnalyticsQuery),
    "useGetUserAnalyticsQuery": (()=>useGetUserAnalyticsQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
;
const analyticsApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    overrideExisting: true,
    endpoints: (builder)=>({
            // System Analytics
            getSystemAnalytics: builder.query({
                query: (params)=>({
                        url: '/admin/analytics/system',
                        params
                    }),
                providesTags: [
                    'Analytics',
                    'Dashboard'
                ]
            }),
            // User Analytics
            getUserAnalytics: builder.query({
                query: (params)=>({
                        url: '/admin/analytics/users',
                        params
                    }),
                providesTags: [
                    'Analytics',
                    'User'
                ]
            }),
            // Property Analytics
            getPropertyAnalytics: builder.query({
                query: (params)=>({
                        url: '/admin/analytics/properties',
                        params
                    }),
                providesTags: [
                    'Analytics',
                    'Property'
                ]
            }),
            // Financial Analytics
            getFinancialAnalytics: builder.query({
                query: (params)=>({
                        url: '/admin/analytics/financial',
                        params
                    }),
                providesTags: [
                    'Analytics',
                    'Finance'
                ]
            }),
            // Real-time Dashboard Metrics
            getDashboardMetrics: builder.query({
                query: ()=>'/admin/analytics/dashboard-metrics',
                providesTags: [
                    'Analytics',
                    'Dashboard'
                ]
            }),
            // Custom Reports
            generateCustomReport: builder.mutation({
                query: (data)=>({
                        url: '/admin/analytics/generate-report',
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Analytics'
                ]
            }),
            // Export Analytics Data
            exportAnalyticsData: builder.mutation({
                query: (params)=>({
                        url: '/admin/analytics/export',
                        method: 'POST',
                        body: params,
                        responseHandler: (response)=>response.blob()
                    })
            }),
            // Comparative Analytics
            getComparativeAnalytics: builder.query({
                query: (params)=>({
                        url: '/admin/analytics/comparative',
                        params
                    }),
                providesTags: [
                    'Analytics'
                ]
            }),
            // Predictive Analytics
            getPredictiveAnalytics: builder.query({
                query: (params)=>({
                        url: '/admin/analytics/predictive',
                        params
                    }),
                providesTags: [
                    'Analytics'
                ]
            })
        })
});
const { useGetSystemAnalyticsQuery, useGetUserAnalyticsQuery, useGetPropertyAnalyticsQuery, useGetFinancialAnalyticsQuery, useGetDashboardMetricsQuery, useGenerateCustomReportMutation, useExportAnalyticsDataMutation, useGetComparativeAnalyticsQuery, useGetPredictiveAnalyticsQuery } = analyticsApi;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "persistor": (()=>persistor),
    "store": (()=>store),
    "useAppDispatch": (()=>useAppDispatch),
    "useAppSelector": (()=>useAppSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_7c11fc0195c3cadab20264a1be66ea7e/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$redux$40$5$2e$0$2e$1$2f$node_modules$2f$redux$2f$dist$2f$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/redux@5.0.1/node_modules/redux/dist/redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$redux$2d$persist$40$6$2e$0$2e$0_react$40$19$2e$1$2e$0_redux$40$5$2e$0$2e$1$2f$node_modules$2f$redux$2d$persist$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/redux-persist@6.0.0_react@19.1.0_redux@5.0.1/node_modules/redux-persist/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$redux$2d$persist$40$6$2e$0$2e$0_react$40$19$2e$1$2e$0_redux$40$5$2e$0$2e$1$2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/redux-persist@6.0.0_react@19.1.0_redux@5.0.1/node_modules/redux-persist/es/persistStore.js [app-client] (ecmascript) <export default as persistStore>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$redux$2d$persist$40$6$2e$0$2e$0_react$40$19$2e$1$2e$0_redux$40$5$2e$0$2e$1$2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/redux-persist@6.0.0_react@19.1.0_redux@5.0.1/node_modules/redux-persist/es/persistReducer.js [app-client] (ecmascript) <export default as persistReducer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$redux$2d$persist$40$6$2e$0$2e$0_react$40$19$2e$1$2e$0_redux$40$5$2e$0$2e$1$2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/redux-persist@6.0.0_react@19.1.0_redux@5.0.1/node_modules/redux-persist/lib/storage/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_7c11fc0195c3cadab20264a1be66ea7e/node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs [app-client] (ecmascript)");
// Import slices
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/authSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$uiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/uiSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$usersSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/usersSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$propertiesSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/propertiesSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$leadsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/leadsSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$dashboardSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/dashboardSlice.ts [app-client] (ecmascript)");
// Import RTK Query API service
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/baseApi.ts [app-client] (ecmascript)");
// Import all API slices to ensure they are injected into baseApi
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$usersApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/usersApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$propertiesApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/propertiesApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$leadsApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/leadsApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$dashboardApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/dashboardApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$financeApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/financeApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$stocksApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/stocksApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$propertyStocksApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/propertyStocksApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$propertyOwnersApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/propertyOwnersApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$supportApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/supportApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$settingsApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/settingsApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$adminPaymentApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/adminPaymentApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$adminUserApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/adminUserApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$analyticsApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/analyticsApi.ts [app-client] (ecmascript)");
// Typed hooks
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$redux$40$9$2e$2$2e$0_$40$types$2b$re_7a0ba9d40341b33400f3d407a4f8522c$2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-redux@9.2.0_@types+re_7a0ba9d40341b33400f3d407a4f8522c/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Create noop storage for server-side rendering
const createNoopStorage = ()=>{
    return {
        getItem (_key) {
            return Promise.resolve(null);
        },
        setItem (_key, value) {
            return Promise.resolve(value);
        },
        removeItem (_key) {
            return Promise.resolve();
        }
    };
};
// Use proper storage based on environment
const storageEngine = ("TURBOPACK compile-time truthy", 1) ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$redux$2d$persist$40$6$2e$0$2e$0_react$40$19$2e$1$2e$0_redux$40$5$2e$0$2e$1$2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] : ("TURBOPACK unreachable", undefined);
// Persist configuration
const persistConfig = {
    key: 'sgm-admin',
    storage: storageEngine,
    whitelist: [
        'auth',
        'ui'
    ],
    blacklist: [
        'users',
        'properties',
        'leads',
        'dashboard',
        'baseApi'
    ]
};
// Root reducer
const rootReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$redux$40$5$2e$0$2e$1$2f$node_modules$2f$redux$2f$dist$2f$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["combineReducers"])({
    auth: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ui: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$uiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    users: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$usersSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    properties: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$propertiesSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    leads: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$leadsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    dashboard: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$dashboardSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].reducerPath]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].reducer
});
// Persisted reducer
const persistedReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$redux$2d$persist$40$6$2e$0$2e$0_react$40$19$2e$1$2e$0_redux$40$5$2e$0$2e$1$2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(persistConfig, rootReducer);
const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: [
                    'persist/PERSIST',
                    'persist/REHYDRATE'
                ],
                ignoredPaths: [
                    'register'
                ]
            }
        }).concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$baseApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseApi"].middleware),
    devTools: ("TURBOPACK compile-time value", "development") !== 'production'
});
// Setup listeners for RTK Query
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$reduxjs$2b$toolkit$40$2$2e$8$2e$2_reac_7c11fc0195c3cadab20264a1be66ea7e$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setupListeners"])(store.dispatch);
const persistor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$redux$2d$persist$40$6$2e$0$2e$0_react$40$19$2e$1$2e$0_redux$40$5$2e$0$2e$1$2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__["persistStore"])(store);
;
const useAppDispatch = ()=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$redux$40$9$2e$2$2e$0_$40$types$2b$re_7a0ba9d40341b33400f3d407a4f8522c$2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"])();
};
_s(useAppDispatch, "jI3HA1r1Cumjdbu14H7G+TUj798=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$redux$40$9$2e$2$2e$0_$40$types$2b$re_7a0ba9d40341b33400f3d407a4f8522c$2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"]
    ];
});
const useAppSelector = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$redux$40$9$2e$2$2e$0_$40$types$2b$re_7a0ba9d40341b33400f3d407a4f8522c$2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"];
const __TURBOPACK__default__export__ = store;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "default": (()=>__TURBOPACK__default__export__),
    "useAuthContext": (()=>useAuthContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/index.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/authSlice.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    _s();
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppDispatch"])();
    const user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppSelector"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["selectUser"]);
    const isAuthenticated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppSelector"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["selectIsAuthenticated"]);
    const loading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppSelector"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["selectAuthLoading"]);
    const checkAuth = ()=>{
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["checkAuthAsync"])());
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            // Only check auth if we don't already have user data and we have a token
            const hasToken = "object" !== 'undefined' && (document.cookie.includes('accessToken=') || document.cookie.includes('token=') || localStorage.getItem('token'));
            // Only check auth if we have a token but no user data
            if (hasToken && !user && !loading) {
                console.log('🔍 AuthContext: Checking auth on mount');
                checkAuth();
            } else {
                console.log('🔍 AuthContext: Skipping auth check - hasToken:', hasToken, 'user:', !!user, 'loading:', loading);
            }
        }
    }["AuthProvider.useEffect"], []) // Empty dependency array - only run once on mount
    ;
    const value = {
        user,
        isAuthenticated,
        loading,
        checkAuth
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 61,
        columnNumber: 5
    }, this);
}
_s(AuthProvider, "Q6DaKdrLnVdnKqp6DmlsCk+zhlE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppDispatch"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppSelector"]
    ];
});
_c = AuthProvider;
function useAuthContext() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuthContext must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuthContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const __TURBOPACK__default__export__ = AuthContext;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RootLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/geist_e531dabc.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/geist_mono_68a01160.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$redux$40$9$2e$2$2e$0_$40$types$2b$re_7a0ba9d40341b33400f3d407a4f8522c$2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-redux@9.2.0_@types+re_7a0ba9d40341b33400f3d407a4f8522c/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$redux$2d$persist$40$6$2e$0$2e$0_react$40$19$2e$1$2e$0_redux$40$5$2e$0$2e$1$2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/redux-persist@6.0.0_react@19.1.0_redux@5.0.1/node_modules/redux-persist/es/integration/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$6_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/index.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
function RootLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "en",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("head", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("title", {
                        children: "SGM Admin Dashboard"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 29,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "description",
                        content: "SGM Real Estate Investment Platform Admin Dashboard"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 30,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/layout.tsx",
                lineNumber: 28,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                className: `${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].variable} ${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].variable} antialiased`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$redux$40$9$2e$2$2e$0_$40$types$2b$re_7a0ba9d40341b33400f3d407a4f8522c$2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Provider"], {
                    store: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["store"],
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$redux$2d$persist$40$6$2e$0$2e$0_react$40$19$2e$1$2e$0_redux$40$5$2e$0$2e$1$2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PersistGate"], {
                        loading: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: "Loading..."
                        }, void 0, false, {
                            fileName: "[project]/src/app/layout.tsx",
                            lineNumber: 36,
                            columnNumber: 33
                        }, void 0),
                        persistor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persistor"],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthProvider"], {
                            children: [
                                children,
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$6_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {
                                    position: "top-right",
                                    richColors: true
                                }, void 0, false, {
                                    fileName: "[project]/src/app/layout.tsx",
                                    lineNumber: 39,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/layout.tsx",
                            lineNumber: 37,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 36,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/layout.tsx",
                    lineNumber: 35,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/layout.tsx",
                lineNumber: 32,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/layout.tsx",
        lineNumber: 27,
        columnNumber: 5
    }, this);
}
_c = RootLayout;
var _c;
__turbopack_context__.k.register(_c, "RootLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__9b470788._.js.map