(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4700],{1128:(e,s,t)=>{"use strict";t.d(s,{B3:()=>o,Lv:()=>h,Ng:()=>l,_L:()=>r,ac:()=>y,ge:()=>a,nd:()=>d,pv:()=>g,tl:()=>c,uU:()=>i});let{useLoginMutation:r,useRegisterMutation:a,useLogoutMutation:l,useRefreshTokenMutation:n,useVerifyEmailMutation:i,useResendVerificationEmailMutation:o,useForgotPasswordMutation:d,useVerifyPasswordResetOTPMutation:c,useResetPasswordMutation:h,useChangePasswordMutation:u,useVerifyPhoneMutation:m,useSendPhoneOTPMutation:x,useSendEmailOTPMutation:y,useVerifyEmailOTPMutation:g,useGetCurrentUserQuery:p,useGetUserProfileQuery:w,useCheckAuthStatusQuery:f}=t(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({login:e.mutation({query:e=>({url:"/auth/login",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),register:e.mutation({query:e=>({url:"/auth/register",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),logout:e.mutation({query:()=>({url:"/auth/logout",method:"POST"}),invalidatesTags:["Auth","User"]}),refreshToken:e.mutation({query:e=>({url:"/auth/refresh",method:"POST",body:e})}),verifyEmail:e.mutation({query:e=>({url:"/auth/verify-email",method:"POST",body:e}),invalidatesTags:["User"]}),resendVerificationEmail:e.mutation({query:e=>({url:"/auth/send-verification",method:"POST",body:e})}),forgotPassword:e.mutation({query:e=>({url:"/auth/forgot-password",method:"POST",body:e})}),verifyPasswordResetOTP:e.mutation({query:e=>({url:"/auth/verify-reset-otp",method:"POST",body:e})}),resetPassword:e.mutation({query:e=>({url:"/auth/reset-password",method:"POST",body:e})}),changePassword:e.mutation({query:e=>({url:"/auth/change-password",method:"POST",body:e}),invalidatesTags:["User"]}),verifyPhone:e.mutation({query:e=>({url:"/auth/verify-phone",method:"POST",body:e}),invalidatesTags:["User"]}),sendPhoneOTP:e.mutation({query:e=>({url:"/auth/send-phone-otp",method:"POST",body:e})}),sendEmailOTP:e.mutation({query:e=>({url:"/auth/send-otp",method:"POST",body:e})}),verifyEmailOTP:e.mutation({query:e=>({url:"/auth/verify-otp",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),getCurrentUser:e.query({query:()=>"/auth/me",providesTags:["User"]}),checkAuthStatus:e.query({query:()=>"/auth/status",providesTags:["Auth"]}),getUserProfile:e.query({query:()=>"/auth/profile",providesTags:["User","Auth"]})})})},1470:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2006:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4454:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4502:(e,s,t)=>{Promise.resolve().then(t.bind(t,8502))},5828:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7530:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},8049:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},8502:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var r=t(9605),a=t(9585),l=t(5935),n=t(6762),i=t.n(n),o=t(2407),d=t(7730),c=t(5706),h=t(5828),u=t(8049),m=t(2006),x=t(4454),y=t(7530),g=t(1470),p=t(6793),w=t(2933),f=t(8063),j=t(1128),b=t(6845);let N=c.Ik({password:c.Yj().min(8,"Password must be at least 8 characters"),confirmPassword:c.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function v(){let e=(0,l.useRouter)(),[s,t]=(0,a.useState)(null),[n,c]=(0,a.useState)(!1),[v,k]=(0,a.useState)(!1),[P,A]=(0,a.useState)(!1),[T,{isLoading:q}]=(0,j.Lv)(),{register:S,handleSubmit:O,formState:{errors:M},watch:E}=(0,o.mN)({resolver:(0,d.u)(N)}),C=E("password");(0,a.useEffect)(()=>{let s=sessionStorage.getItem("resetToken");s?t(s):(b.toast.error("Invalid or missing reset token. Please request a new password reset."),e.push("/forgot-password"))},[e]);let R=async t=>{if(s)try{(await T({resetToken:s,newPassword:t.password,confirmPassword:t.confirmPassword}).unwrap()).success&&(A(!0),b.toast.success("Password reset successfully!"),sessionStorage.removeItem("resetToken"),setTimeout(()=>{e.push("/login")},3e3))}catch(s){var r,a,l;(null==(r=s.data)?void 0:r.error)==="INVALID_TOKEN"||(null==(a=s.data)?void 0:a.error)==="TOKEN_INVALIDATED"?(b.toast.error("Reset token has expired. Please request a new password reset."),e.push("/forgot-password")):b.toast.error((null==(l=s.data)?void 0:l.message)||"Failed to reset password")}},L=(e=>{if(!e)return{strength:0,label:"",color:""};let s=0;return e.length>=8&&s++,/[A-Z]/.test(e)&&s++,/[a-z]/.test(e)&&s++,/[0-9]/.test(e)&&s++,/[^A-Za-z0-9]/.test(e)&&s++,{strength:s,label:["Very Weak","Weak","Fair","Good","Strong"][s-1]||"",color:["bg-red-500","bg-orange-500","bg-yellow-500","bg-blue-500","bg-green-500"][s-1]||"bg-gray-300"}})(C||"");return s?P?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,r.jsx)(u.A,{className:"h-12 w-12 text-blue-600"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"SGM "})]}),(0,r.jsxs)(f.Zp,{className:"shadow-xl border-0",children:[(0,r.jsxs)(f.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(m.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsx)(f.ZB,{className:"text-2xl font-bold",children:"Password Reset Successful"}),(0,r.jsx)(f.BT,{children:"Your password has been successfully reset"})]}),(0,r.jsx)(f.Wu,{className:"space-y-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"You can now sign in with your new password."}),(0,r.jsx)(i(),{href:"/login",children:(0,r.jsx)(w.$,{className:"w-full",children:"Continue to Login"})})]})})]})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,r.jsx)(u.A,{className:"h-12 w-12 text-blue-600"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"SGM "}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Create your new password"})]}),(0,r.jsxs)(f.Zp,{className:"shadow-xl border-0",children:[(0,r.jsxs)(f.aR,{className:"space-y-1",children:[(0,r.jsx)(f.ZB,{className:"text-2xl font-bold text-center",children:"Reset Password"}),(0,r.jsx)(f.BT,{className:"text-center",children:"Enter your new password below"})]}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsxs)("form",{onSubmit:O(R),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...S("password"),type:n?"text":"password",id:"password",placeholder:"Enter your new password",className:"w-full pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)("button",{type:"button",onClick:()=>c(!n),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:n?(0,r.jsx)(y.A,{className:"h-4 w-4"}):(0,r.jsx)(g.A,{className:"h-4 w-4"})})]}),M.password&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:M.password.message}),C&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Password strength:"}),(0,r.jsx)("span",{className:"font-medium ".concat(L.strength>=4?"text-green-600":L.strength>=3?"text-blue-600":L.strength>=2?"text-yellow-600":"text-red-600"),children:L.label})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full transition-all ".concat(L.color),style:{width:"".concat(L.strength/5*100,"%")}})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"text-sm font-medium text-gray-700",children:"Confirm New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...S("confirmPassword"),type:v?"text":"password",id:"confirmPassword",placeholder:"Confirm your new password",className:"w-full pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)("button",{type:"button",onClick:()=>k(!v),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:v?(0,r.jsx)(y.A,{className:"h-4 w-4"}):(0,r.jsx)(g.A,{className:"h-4 w-4"})})]}),M.confirmPassword&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:M.confirmPassword.message})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Password Requirements:"}),(0,r.jsxs)("ul",{className:"text-xs text-gray-600 space-y-1",children:[(0,r.jsx)("li",{className:C&&C.length>=8?"text-green-600":"",children:"• At least 8 characters long"}),(0,r.jsx)("li",{className:C&&/[A-Z]/.test(C)?"text-green-600":"",children:"• Contains uppercase letter"}),(0,r.jsx)("li",{className:C&&/[a-z]/.test(C)?"text-green-600":"",children:"• Contains lowercase letter"}),(0,r.jsx)("li",{className:C&&/[0-9]/.test(C)?"text-green-600":"",children:"• Contains number"}),(0,r.jsx)("li",{className:C&&/[^A-Za-z0-9]/.test(C)?"text-green-600":"",children:"• Contains special character"})]})]}),(0,r.jsx)(w.$,{type:"submit",disabled:q,className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors",children:q?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Resetting password..."]}):"Reset Password"})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsx)(i(),{href:"/login",className:"text-blue-600 hover:text-blue-500 font-medium",children:"Back to Login"})})]})]})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsx)(f.Zp,{className:"w-full max-w-md",children:(0,r.jsxs)(f.Wu,{className:"text-center py-12",children:[(0,r.jsx)(h.A,{className:"h-16 w-16 mx-auto mb-4 text-red-500"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Invalid Reset Link"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"This password reset link is invalid or has expired."}),(0,r.jsx)(i(),{href:"/forgot-password",children:(0,r.jsx)(w.$,{children:"Request New Reset Link"})})]})})})}function k(){return(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(v,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2094,5315,7436,3403,1147,390,110,7358],()=>s(4502)),_N_E=e.O()}]);