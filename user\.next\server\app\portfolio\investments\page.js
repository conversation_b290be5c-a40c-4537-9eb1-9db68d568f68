(()=>{var e={};e.id=1524,e.ids=[1524],e.modules={252:(e,t,r)=>{"use strict";r.d(t,{tU:()=>W,av:()=>X,j7:()=>z,Xi:()=>V});var s=r(40969),a=r(73356),n=r(60952),i=r(64680),o=r(60308),l=r(24629),c=r(59705),d=r(81861),u=r(71060),p=r(26314),m=r(7957),x="rovingFocusGroup.onEntryFocus",f={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[v,g,b]=(0,o.N)(h),[y,j]=(0,i.A)(h,[b]),[w,N]=y(h),k=a.forwardRef((e,t)=>(0,s.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,s.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,s.jsx)(R,{...e,ref:t})})}));k.displayName=h;var R=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:o=!1,dir:c,currentTabStopId:v,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:y,onEntryFocus:j,preventScrollOnEntryFocus:N=!1,...k}=e,R=a.useRef(null),T=(0,l.s)(t,R),A=(0,m.jH)(c),[C,D]=(0,p.i)({prop:v,defaultProp:b??null,onChange:y,caller:h}),[F,P]=a.useState(!1),E=(0,u.c)(j),S=g(r),_=a.useRef(!1),[G,q]=a.useState(0);return a.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(x,E),()=>e.removeEventListener(x,E)},[E]),(0,s.jsx)(w,{scope:r,orientation:i,dir:A,loop:o,currentTabStopId:C,onItemFocus:a.useCallback(e=>D(e),[D]),onItemShiftTab:a.useCallback(()=>P(!0),[]),onFocusableItemAdd:a.useCallback(()=>q(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>q(e=>e-1),[]),children:(0,s.jsx)(d.sG.div,{tabIndex:F||0===G?-1:0,"data-orientation":i,...k,ref:T,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{_.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let t=!_.current;if(e.target===e.currentTarget&&t&&!F){let t=new CustomEvent(x,f);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=S().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),N)}}_.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>P(!1))})})}),T="RovingFocusGroupItem",A=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:o=!1,tabStopId:l,children:u,...p}=e,m=(0,c.B)(),x=l||m,f=N(T,r),h=f.currentTabStopId===x,b=g(r),{onFocusableItemAdd:y,onFocusableItemRemove:j,currentTabStopId:w}=f;return a.useEffect(()=>{if(i)return y(),()=>j()},[i,y,j]),(0,s.jsx)(v.ItemSlot,{scope:r,id:x,focusable:i,active:o,children:(0,s.jsx)(d.sG.span,{tabIndex:h?0:-1,"data-orientation":f.orientation,...p,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{i?f.onItemFocus(x):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>f.onItemFocus(x)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var s;let a=(s=e.key,"rtl"!==r?s:"ArrowLeft"===s?"ArrowRight":"ArrowRight"===s?"ArrowLeft":s);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return C[a]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let s=r.indexOf(e.currentTarget);r=f.loop?function(e,t){return e.map((r,s)=>e[(t+s)%e.length])}(r,s+1):r.slice(s+1)}setTimeout(()=>I(r))}}),children:"function"==typeof u?u({isCurrentTabStop:h,hasTabStop:null!=w}):u})})});A.displayName=T;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e,t=!1){let r=document.activeElement;for(let s of e)if(s===r||(s.focus({preventScroll:t}),document.activeElement!==r))return}var D=r(22195),F="Tabs",[P,E]=(0,i.A)(F,[j]),S=j(),[_,G]=P(F),q=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:i,orientation:o="horizontal",dir:l,activationMode:u="automatic",...x}=e,f=(0,m.jH)(l),[h,v]=(0,p.i)({prop:a,onChange:n,defaultProp:i??"",caller:F});return(0,s.jsx)(_,{scope:r,baseId:(0,c.B)(),value:h,onValueChange:v,orientation:o,dir:f,activationMode:u,children:(0,s.jsx)(d.sG.div,{dir:f,"data-orientation":o,...x,ref:t})})});q.displayName=F;var U="TabsList",L=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,i=G(U,r),o=S(r);return(0,s.jsx)(k,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:a,children:(0,s.jsx)(d.sG.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});L.displayName=U;var B="TabsTrigger",M=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:i=!1,...o}=e,l=G(B,r),c=S(r),u=$(l.baseId,a),p=Z(l.baseId,a),m=a===l.value;return(0,s.jsx)(A,{asChild:!0,...c,focusable:!i,active:m,children:(0,s.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":p,"data-state":m?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...o,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;m||i||!e||l.onValueChange(a)})})})});M.displayName=B;var H="TabsContent",K=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:i,children:o,...l}=e,c=G(H,r),u=$(c.baseId,n),p=Z(c.baseId,n),m=n===c.value,x=a.useRef(m);return a.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.jsx)(D.C,{present:i||m,children:({present:r})=>(0,s.jsx)(d.sG.div,{"data-state":m?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:p,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:r&&o})})});function $(e,t){return`${e}-trigger-${t}`}function Z(e,t){return`${e}-content-${t}`}K.displayName=H;var O=r(21764);let W=q,z=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(L,{ref:r,className:(0,O.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));z.displayName=L.displayName;let V=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(M,{ref:r,className:(0,O.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));V.displayName=M.displayName;let X=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(K,{ref:r,className:(0,O.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));X.displayName=K.displayName},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4942:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(62544);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20239:(e,t,r)=>{Promise.resolve().then(r.bind(r,99525))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31498:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\portfolio\\\\investments\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\portfolio\\investments\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},45548:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(99024).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},56525:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(99024).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67095:(e,t,r)=>{Promise.resolve().then(r.bind(r,31498))},70557:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(10557),a=r(68490),n=r(13172),i=r.n(n),o=r(68835),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["portfolio",{children:["investments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,31498)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\portfolio\\investments\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\portfolio\\investments\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/portfolio/investments/page",pathname:"/portfolio/investments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},76650:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var s=r(40969);r(73356);var a=r(52774),n=r(21764);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,s.jsx)("div",{className:(0,n.cn)(i({variant:t}),e),...r})}},79551:e=>{"use strict";e.exports=require("url")},99525:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(40969),a=r(73356),n=r(66949),i=r(46411),o=r(76650),l=r(252),c=r(45548),d=r(91447),u=r(58573),p=r(63407),m=r(56525);let{useGetUserStockHoldingsQuery:x,useDownloadStockCertificateMutation:f,useGetStockTransactionHistoryQuery:h}=r(88559).q.injectEndpoints({endpoints:e=>({getUserStockHoldings:e.query({query:()=>"/stocks/my-holdings",providesTags:["StockHoldings"]}),downloadStockCertificate:e.mutation({query:({transactionId:e})=>({url:`/stocks/certificate/${e}`,method:"GET",responseHandler:e=>e.blob()}),invalidatesTags:["StockCertificate"]}),getStockTransactionHistory:e.query({query:({page:e=1,limit:t=10}={})=>({url:"/transactions/my-transactions",params:{page:e,limit:t,type:"stock"}}),providesTags:["StockTransaction"]})})});var v=r(1507);let g=e=>new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e);function b(){let[e,t]=(0,a.useState)("holdings"),{data:r,isLoading:h,error:b}=x(),[y,{isLoading:j}]=f(),w=r?.data||[],N=async(e,t)=>{try{let r=await y({transactionId:e}).unwrap(),s=window.URL.createObjectURL(r),a=document.createElement("a");a.href=s,a.download=`stock-certificate-${t.replace(/\s+/g,"-")}-${e}.pdf`,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(s),v.toast.success("Certificate downloaded successfully!")}catch(e){console.error("Download error:",e),v.toast.error("Failed to download certificate")}};return h?(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,s.jsx)("div",{className:"h-32 bg-gray-200 rounded"},e))})]})}):b?(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6 text-center",children:(0,s.jsx)("p",{className:"text-red-600",children:"Failed to load investment data"})})})}):(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Investment Portfolio"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Track your property investments and download certificates"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Investment"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g(w.reduce((e,t)=>e+t.totalInvestment,0))})]}),(0,s.jsx)(c.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Shares"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.reduce((e,t)=>e+t.totalShares,0)})]}),(0,s.jsx)(d.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Expected Annual Returns"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g(w.reduce((e,t)=>e+t.totalInvestment*t.stock.expectedROI/100,0))})]}),(0,s.jsx)(c.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,s.jsxs)(l.tU,{value:e,onValueChange:t,children:[(0,s.jsxs)(l.j7,{className:"grid w-full grid-cols-2",children:[(0,s.jsx)(l.Xi,{value:"holdings",children:"Stock Holdings"}),(0,s.jsx)(l.Xi,{value:"transactions",children:"Transaction History"})]}),(0,s.jsx)(l.av,{value:"holdings",className:"space-y-6",children:0===w.length?(0,s.jsx)(n.Zp,{children:(0,s.jsxs)(n.Wu,{className:"p-8 text-center",children:[(0,s.jsx)(d.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Investments Yet"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Start investing in properties to see your portfolio here"}),(0,s.jsx)(i.$,{onClick:()=>window.location.href="/properties",children:"Browse Properties"})]})}):(0,s.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:w.map(e=>(0,s.jsxs)(n.Zp,{className:"overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative h-48",children:[(0,s.jsx)("img",{src:e.property.images[0]||"/placeholder-property.jpg",alt:e.property.title,className:"w-full h-full object-cover"}),(0,s.jsx)("div",{className:"absolute top-4 right-4",children:(0,s.jsxs)(o.E,{variant:"secondary",className:"bg-white/90",children:[e.totalShares," shares"]})})]}),(0,s.jsxs)(n.Wu,{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.property.title}),(0,s.jsxs)("div",{className:"flex items-center text-gray-600 mb-4",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-1"}),(0,s.jsx)("span",{className:"text-sm",children:e.property.location})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Total Investment"}),(0,s.jsx)("p",{className:"font-semibold",children:g(e.totalInvestment)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Expected ROI"}),(0,s.jsxs)("p",{className:"font-semibold text-green-600",children:[e.stock.expectedROI,"%"]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Recent Transactions"}),e.transactions.slice(0,2).map(t=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-sm font-medium",children:[t.stockQuantity," shares"]}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:new Date(t.createdAt).toLocaleDateString()})]})]}),(0,s.jsxs)(i.$,{size:"sm",variant:"outline",onClick:()=>N(t._id,e.property.title),disabled:j,children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"Certificate"]})]},t._id))]})]})]},e.property._id))})}),(0,s.jsx)(l.av,{value:"transactions",children:(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)(n.ZB,{children:"Transaction History"})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("p",{className:"text-gray-600",children:"Transaction history will be implemented here"})})]})})]})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[755,3777,2544,3319,2487],()=>r(70557));module.exports=s})();