(()=>{var e={};e.id=662,e.ids=[662],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8232:(e,s,r)=>{Promise.resolve().then(r.bind(r,25814))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21384:(e,s,r)=>{Promise.resolve().then(r.bind(r,72010))},24001:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>t.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>c});var l=r(10557),i=r(68490),a=r(13172),t=r.n(a),d=r(68835),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(s,n);let c={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72010)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\settings\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\settings\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},h=new l.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},25814:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var l=r(40969),i=r(73356),a=r(88251),t=r(66949),d=r(46411),n=r(57387),c=r(12053),o=r(76650),x=r(2223),h=r(77272),m=r(93094),u=r(70713),j=r(98085);let p=(0,j.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]);var f=r(35342);let y=(0,j.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),v=(0,j.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);function b(){let[e,s]=(0,i.useState)("profile"),r=[{id:"profile",label:"Profile",icon:h.A},{id:"notifications",label:"Notifications",icon:m.A},{id:"security",label:"Security",icon:u.A},{id:"preferences",label:"Preferences",icon:p},{id:"targets",label:"Targets",icon:f.A}];return(0,l.jsx)(a.A,{title:"Settings",children:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Settings"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Manage your account settings and preferences"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,l.jsx)("div",{className:"lg:col-span-1",children:(0,l.jsx)(t.Zp,{children:(0,l.jsx)(t.Wu,{className:"p-4",children:(0,l.jsx)("nav",{className:"space-y-1",children:r.map(r=>(0,l.jsxs)("button",{onClick:()=>s(r.id),className:`w-full flex items-center space-x-3 px-3 py-2 text-left rounded-lg transition-colors ${e===r.id?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-50"}`,children:[(0,l.jsx)(r.icon,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:r.label})]},r.id))})})})}),(0,l.jsxs)("div",{className:"lg:col-span-3",children:["profile"===e&&(0,l.jsxs)(t.Zp,{children:[(0,l.jsx)(t.aR,{children:(0,l.jsx)(t.ZB,{children:"Profile Information"})}),(0,l.jsxs)(t.Wu,{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("div",{className:"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(h.A,{className:"w-8 h-8 text-blue-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,l.jsx)(y,{className:"w-4 h-4 mr-2"}),"Change Photo"]}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"JPG, PNG up to 2MB"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"firstName",children:"First Name"}),(0,l.jsx)(n.p,{id:"firstName",defaultValue:"John"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"lastName",children:"Last Name"}),(0,l.jsx)(n.p,{id:"lastName",defaultValue:"Doe"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"email",children:"Email"}),(0,l.jsx)(n.p,{id:"email",type:"email",defaultValue:"<EMAIL>"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"phone",children:"Phone"}),(0,l.jsx)(n.p,{id:"phone",defaultValue:"+91 98765 43210"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"department",children:"Department"}),(0,l.jsxs)(x.l6,{defaultValue:"sales",children:[(0,l.jsx)(x.bq,{children:(0,l.jsx)(x.yv,{})}),(0,l.jsxs)(x.gC,{children:[(0,l.jsx)(x.eb,{value:"sales",children:"Sales"}),(0,l.jsx)(x.eb,{value:"marketing",children:"Marketing"}),(0,l.jsx)(x.eb,{value:"support",children:"Support"})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"territory",children:"Territory"}),(0,l.jsxs)(x.l6,{defaultValue:"mumbai",children:[(0,l.jsx)(x.bq,{children:(0,l.jsx)(x.yv,{})}),(0,l.jsxs)(x.gC,{children:[(0,l.jsx)(x.eb,{value:"mumbai",children:"Mumbai"}),(0,l.jsx)(x.eb,{value:"delhi",children:"Delhi"}),(0,l.jsx)(x.eb,{value:"bangalore",children:"Bangalore"})]})]})]})]}),(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsxs)(d.$,{children:[(0,l.jsx)(v,{className:"w-4 h-4 mr-2"}),"Save Changes"]})})]})]}),"notifications"===e&&(0,l.jsxs)(t.Zp,{children:[(0,l.jsx)(t.aR,{children:(0,l.jsx)(t.ZB,{children:"Notification Preferences"})}),(0,l.jsxs)(t.Wu,{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium",children:"Email Notifications"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Receive notifications via email"})]}),(0,l.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium",children:"Push Notifications"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Receive push notifications in browser"})]}),(0,l.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium",children:"SMS Notifications"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Receive important updates via SMS"})]}),(0,l.jsx)("input",{type:"checkbox",className:"rounded"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium",children:"Lead Assignments"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Get notified when new leads are assigned"})]}),(0,l.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium",children:"Follow-up Reminders"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Reminders for scheduled follow-ups"})]}),(0,l.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]})]}),(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsxs)(d.$,{children:[(0,l.jsx)(v,{className:"w-4 h-4 mr-2"}),"Save Preferences"]})})]})]}),"security"===e&&(0,l.jsxs)(t.Zp,{children:[(0,l.jsx)(t.aR,{children:(0,l.jsx)(t.ZB,{children:"Security Settings"})}),(0,l.jsxs)(t.Wu,{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,l.jsx)(n.p,{id:"currentPassword",type:"password"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"newPassword",children:"New Password"}),(0,l.jsx)(n.p,{id:"newPassword",type:"password"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,l.jsx)(n.p,{id:"confirmPassword",type:"password"})]})]}),(0,l.jsxs)("div",{className:"border-t pt-6",children:[(0,l.jsx)("h4",{className:"font-medium mb-4",children:"Two-Factor Authentication"}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h5",{className:"font-medium",children:"Enable 2FA"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Add an extra layer of security to your account"})]}),(0,l.jsx)(o.E,{variant:"outline",children:"Disabled"})]})]}),(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsxs)(d.$,{children:[(0,l.jsx)(v,{className:"w-4 h-4 mr-2"}),"Update Security"]})})]})]}),"preferences"===e&&(0,l.jsxs)(t.Zp,{children:[(0,l.jsx)(t.aR,{children:(0,l.jsx)(t.ZB,{children:"Preferences"})}),(0,l.jsxs)(t.Wu,{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"language",children:"Language"}),(0,l.jsxs)(x.l6,{defaultValue:"en",children:[(0,l.jsx)(x.bq,{children:(0,l.jsx)(x.yv,{})}),(0,l.jsxs)(x.gC,{children:[(0,l.jsx)(x.eb,{value:"en",children:"English"}),(0,l.jsx)(x.eb,{value:"hi",children:"Hindi"}),(0,l.jsx)(x.eb,{value:"mr",children:"Marathi"})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"timezone",children:"Timezone"}),(0,l.jsxs)(x.l6,{defaultValue:"ist",children:[(0,l.jsx)(x.bq,{children:(0,l.jsx)(x.yv,{})}),(0,l.jsxs)(x.gC,{children:[(0,l.jsx)(x.eb,{value:"ist",children:"IST (UTC+5:30)"}),(0,l.jsx)(x.eb,{value:"utc",children:"UTC"}),(0,l.jsx)(x.eb,{value:"est",children:"EST (UTC-5)"})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"currency",children:"Currency"}),(0,l.jsxs)(x.l6,{defaultValue:"inr",children:[(0,l.jsx)(x.bq,{children:(0,l.jsx)(x.yv,{})}),(0,l.jsxs)(x.gC,{children:[(0,l.jsx)(x.eb,{value:"inr",children:"INR (₹)"}),(0,l.jsx)(x.eb,{value:"usd",children:"USD ($)"}),(0,l.jsx)(x.eb,{value:"eur",children:"EUR (€)"})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"dateFormat",children:"Date Format"}),(0,l.jsxs)(x.l6,{defaultValue:"dd/mm/yyyy",children:[(0,l.jsx)(x.bq,{children:(0,l.jsx)(x.yv,{})}),(0,l.jsxs)(x.gC,{children:[(0,l.jsx)(x.eb,{value:"dd/mm/yyyy",children:"DD/MM/YYYY"}),(0,l.jsx)(x.eb,{value:"mm/dd/yyyy",children:"MM/DD/YYYY"}),(0,l.jsx)(x.eb,{value:"yyyy-mm-dd",children:"YYYY-MM-DD"})]})]})]})]}),(0,l.jsxs)("div",{className:"border-t pt-6",children:[(0,l.jsx)("h4",{className:"font-medium mb-4",children:"Dashboard Preferences"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm",children:"Show welcome message"}),(0,l.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm",children:"Auto-refresh dashboard"}),(0,l.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm",children:"Compact view"}),(0,l.jsx)("input",{type:"checkbox",className:"rounded"})]})]})]}),(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsxs)(d.$,{children:[(0,l.jsx)(v,{className:"w-4 h-4 mr-2"}),"Save Preferences"]})})]})]}),"targets"===e&&(0,l.jsxs)(t.Zp,{children:[(0,l.jsx)(t.aR,{children:(0,l.jsx)(t.ZB,{children:"Target Settings"})}),(0,l.jsxs)(t.Wu,{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"monthlySalesTarget",children:"Monthly Sales Target"}),(0,l.jsx)(n.p,{id:"monthlySalesTarget",type:"number",defaultValue:"25"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"monthlyRevenueTarget",children:"Monthly Revenue Target"}),(0,l.jsx)(n.p,{id:"monthlyRevenueTarget",type:"number",defaultValue:"3000000"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"monthlyLeadsTarget",children:"Monthly Leads Target"}),(0,l.jsx)(n.p,{id:"monthlyLeadsTarget",type:"number",defaultValue:"100"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.J,{htmlFor:"commissionRate",children:"Commission Rate (%)"}),(0,l.jsx)(n.p,{id:"commissionRate",type:"number",defaultValue:"5",step:"0.1"})]})]}),(0,l.jsxs)("div",{className:"border-t pt-6",children:[(0,l.jsx)("h4",{className:"font-medium mb-4",children:"Reminder Settings"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm",children:"Daily target reminders"}),(0,l.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm",children:"Weekly progress reports"}),(0,l.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm",children:"Monthly target alerts"}),(0,l.jsx)("input",{type:"checkbox",className:"rounded"})]})]})]}),(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsxs)(d.$,{children:[(0,l.jsx)(v,{className:"w-4 h-4 mr-2"}),"Save Target Settings"]})})]})]})]})]})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70713:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(98085).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},72010:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});let l=(0,r(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\settings\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),l=s.X(0,[755,598,544,29,796,286,447,512],()=>r(24001));module.exports=l})();