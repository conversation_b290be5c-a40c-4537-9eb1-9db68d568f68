(()=>{var e={};e.id=1790,e.ids=[1790],e.modules={2963:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5506:(e,t,s)=>{"use strict";s.d(t,{oR:()=>E});var r,a=s(73356);let i={data:""},o=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||i,n=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,l=/\n+/g,c=(e,t)=>{let s="",r="",a="";for(let i in e){let o=e[i];"@"==i[0]?"i"==i[1]?s=i+" "+o+";":r+="f"==i[1]?c(o,i):i+"{"+c(o,"k"==i[1]?"":t)+"}":"object"==typeof o?r+=c(o,t?t.replace(/([^,])+/g,e=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):i):null!=o&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=c.p?c.p(i,o):i+":"+o+";")}return s+(t&&a?t+"{"+a+"}":a)+r},p={},u=e=>{if("object"==typeof e){let t="";for(let s in e)t+=s+u(e[s]);return t}return e},m=(e,t,s,r,a)=>{let i=u(e),o=p[i]||(p[i]=(e=>{let t=0,s=11;for(;t<e.length;)s=101*s+e.charCodeAt(t++)>>>0;return"go"+s})(i));if(!p[o]){let t=i!==e?e:(e=>{let t,s,r=[{}];for(;t=n.exec(e.replace(d,""));)t[4]?r.shift():t[3]?(s=t[3].replace(l," ").trim(),r.unshift(r[0][s]=r[0][s]||{})):r[0][t[1]]=t[2].replace(l," ").trim();return r[0]})(e);p[o]=c(a?{["@keyframes "+o]:t}:t,s?"":"."+o)}let m=s&&p.g?p.g:null;return s&&(p.g=p[o]),((e,t,s,r)=>{r?t.data=t.data.replace(r,e):-1===t.data.indexOf(e)&&(t.data=s?e+t.data:t.data+e)})(p[o],t,r,m),o},x=(e,t,s)=>e.reduce((e,r,a)=>{let i=t[a];if(i&&i.call){let e=i(s),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;i=t?"."+t:e&&"object"==typeof e?e.props?"":c(e,""):!1===e?"":e}return e+r+(null==i?"":i)},"");function h(e){let t=this||{},s=e.call?e(t.p):e;return m(s.unshift?s.raw?x(s,[].slice.call(arguments,1),t.p):s.reduce((e,s)=>Object.assign(e,s&&s.call?s(t.p):s),{}):s,o(t.target),t.g,t.o,t.k)}h.bind({g:1});let g,y,f,b=h.bind({k:1});function v(e,t){let s=this||{};return function(){let r=arguments;function a(i,o){let n=Object.assign({},i),d=n.className||a.className;s.p=Object.assign({theme:y&&y()},n),s.o=/ *go\d+/.test(d),n.className=h.apply(s,r)+(d?" "+d:""),t&&(n.ref=o);let l=e;return e[0]&&(l=n.as||e,delete n.as),f&&l[0]&&f(n),g(l,n)}return t?t(a):a}}var w=e=>"function"==typeof e,k=(e,t)=>w(e)?e(t):e,N=(()=>{let e=0;return()=>(++e).toString()})(),T=(()=>{let e;return()=>e})(),S=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:s}=t;return S(e,{type:+!!e.toasts.find(e=>e.id===s.id),toast:s});case 3:let{toastId:r}=t;return{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},A=[],q={toasts:[],pausedAt:void 0},D=e=>{q=S(q,e),A.forEach(e=>{e(q)})},C={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},I=(e={})=>{let[t,s]=j(q),r=Q(q);H(()=>(r.current!==q&&s(q),A.push(s),()=>{let e=A.indexOf(s);e>-1&&A.splice(e,1)}),[]);let a=t.toasts.map(t=>{var s,r,a;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(s=e[t.type])?void 0:s.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(r=e[t.type])?void 0:r.duration)||(null==e?void 0:e.duration)||C[t.type],style:{...e.style,...null==(a=e[t.type])?void 0:a.style,...t.style}}});return{...t,toasts:a}},P=(e,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...s,id:(null==s?void 0:s.id)||N()}),$=e=>(t,s)=>{let r=P(t,e,s);return D({type:2,toast:r}),r.id},E=(e,t)=>$("blank")(e,t);E.error=$("error"),E.success=$("success"),E.loading=$("loading"),E.custom=$("custom"),E.dismiss=e=>{D({type:3,toastId:e})},E.remove=e=>D({type:4,toastId:e}),E.promise=(e,t,s)=>{let r=E.loading(t.loading,{...s,...null==s?void 0:s.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=t.success?k(t.success,e):void 0;return a?E.success(a,{id:r,...s,...null==s?void 0:s.success}):E.dismiss(r),e}).catch(e=>{let a=t.error?k(t.error,e):void 0;a?E.error(a,{id:r,...s,...null==s?void 0:s.error}):E.dismiss(r)}),e};var _=(e,t)=>{D({type:1,toast:{id:e,height:t}})},R=()=>{D({type:5,time:Date.now()})},F=new Map,O=1e3,U=(e,t=O)=>{if(F.has(e))return;let s=setTimeout(()=>{F.delete(e),D({type:4,toastId:e})},t);F.set(e,s)},L=b`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,M=b`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,B=b`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,z=(v("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${L} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${M} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${B} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,b`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`),Z=(v("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${z} 1s linear infinite;
`,b`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`),G=b`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,W=(v("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Z} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${G} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,v("div")`
  position: absolute;
`,v("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,b`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`);v("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${W} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,v("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,v("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,r=a.createElement,c.p=void 0,g=r,y=void 0,f=void 0,h`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28496:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31220:(e,t,s)=>{Promise.resolve().then(s.bind(s,73664))},33873:e=>{"use strict";e.exports=require("path")},38814:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\support\\\\tickets\\\\[ticketId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\support\\tickets\\[ticketId]\\page.tsx","default")},40719:(e,t,s)=>{"use strict";s.d(t,{Rc:()=>l,g1:()=>r,iH:()=>h,ot:()=>a,u8:()=>i});let{useGetUserTicketsQuery:r,useGetTicketByIdQuery:a,useCreateSupportTicketMutation:i,useAddTicketResponseMutation:o,useCloseTicketMutation:n,useReopenTicketMutation:d,useAddTicketMessageMutation:l,useGetFAQCategoriesQuery:c,useGetFAQQuestionsQuery:p,useSearchFAQQuery:u,useRateFAQAnswerMutation:m,useGetSupportStatsQuery:x,useGetLiveChatStatusQuery:h,useStartLiveChatMutation:g,useSendChatMessageMutation:y,useEndLiveChatMutation:f,useGetChatHistoryQuery:b,useSubmitFeedbackMutation:v,useGetSupportContactsQuery:w}=s(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getUserTickets:e.query({query:e=>({url:"/support/tickets/my-tickets",params:{page:e.page||1,limit:e.limit||20,...e.status&&{status:e.status},...e.category&&{category:e.category},...e.priority&&{priority:e.priority},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),providesTags:e=>e?.data?.data?[...e.data.data.map(({_id:e})=>({type:"SupportTicket",id:e})),{type:"SupportTicket",id:"LIST"}]:[{type:"SupportTicket",id:"LIST"}],keepUnusedDataFor:120}),getTicketById:e.query({query:e=>`/support/tickets/${e}`,providesTags:(e,t,s)=>[{type:"SupportTicket",id:s}],keepUnusedDataFor:300}),createSupportTicket:e.mutation({query:e=>({url:"/support/tickets",method:"POST",body:e}),invalidatesTags:[{type:"SupportTicket",id:"LIST"}]}),addTicketResponse:e.mutation({query:({ticketId:e,...t})=>({url:`/support/tickets/${e}/responses`,method:"POST",body:t}),invalidatesTags:(e,t,{ticketId:s})=>[{type:"SupportTicket",id:s},{type:"SupportTicket",id:"LIST"}]}),closeTicket:e.mutation({query:({ticketId:e,...t})=>({url:`/support/tickets/${e}/close`,method:"PUT",body:t}),invalidatesTags:(e,t,{ticketId:s})=>[{type:"SupportTicket",id:s},{type:"SupportTicket",id:"LIST"}]}),reopenTicket:e.mutation({query:({ticketId:e,...t})=>({url:`/support/tickets/${e}/reopen`,method:"PUT",body:t}),invalidatesTags:(e,t,{ticketId:s})=>[{type:"SupportTicket",id:s},{type:"SupportTicket",id:"LIST"}]}),addTicketMessage:e.mutation({query:({ticketId:e,message:t,attachments:s})=>({url:`/support/tickets/${e}/messages`,method:"POST",body:{content:t,attachments:s}}),invalidatesTags:(e,t,{ticketId:s})=>[{type:"SupportTicket",id:s},{type:"SupportTicket",id:"LIST"}]}),getFAQCategories:e.query({query:()=>"/support/faq/categories",providesTags:[{type:"SupportTicket",id:"FAQ_CATEGORIES"}],keepUnusedDataFor:3600}),getFAQQuestions:e.query({query:e=>({url:"/support/faq/questions",params:{page:e.page||1,limit:e.limit||20,...e.categoryId&&{categoryId:e.categoryId},...e.search&&{search:e.search}}}),providesTags:[{type:"SupportTicket",id:"FAQ_QUESTIONS"}],keepUnusedDataFor:1800}),searchFAQ:e.query({query:({query:e,limit:t=10})=>({url:"/support/faq/search",params:{q:e,limit:t}}),keepUnusedDataFor:0}),rateFAQAnswer:e.mutation({query:e=>({url:"/support/faq/rate",method:"POST",body:e})}),getSupportStats:e.query({query:()=>"/support/stats",providesTags:[{type:"SupportTicket",id:"STATS"}],keepUnusedDataFor:600}),getLiveChatStatus:e.query({query:()=>"/support/live-chat/status",keepUnusedDataFor:60}),startLiveChat:e.mutation({query:e=>({url:"/support/live-chat/start",method:"POST",body:e})}),sendChatMessage:e.mutation({query:({chatId:e,...t})=>({url:`/support/live-chat/${e}/messages`,method:"POST",body:t})}),endLiveChat:e.mutation({query:({chatId:e,...t})=>({url:`/support/live-chat/${e}/end`,method:"PUT",body:t})}),getChatHistory:e.query({query:e=>({url:"/support/live-chat/history",params:{page:e.page||1,limit:e.limit||20,...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate}}}),providesTags:[{type:"SupportTicket",id:"CHAT_HISTORY"}],keepUnusedDataFor:600}),submitFeedback:e.mutation({query:e=>({url:"/support/feedback",method:"POST",body:e})}),getSupportContacts:e.query({query:()=>"/support/contacts",providesTags:[{type:"SupportTicket",id:"CONTACTS"}],keepUnusedDataFor:86400})})})},54289:(e,t,s)=>{"use strict";s.d(t,{T:()=>o});var r=s(40969),a=s(73356),i=s(21764);let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));o.displayName="Textarea"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65827:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},73664:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var r=s(40969),a=s(73356),i=s(12011),o=s(66949),n=s(46411),d=s(76650),l=s(54289),c=s(2408),p=s(15423),u=s(1110),m=s(28496),x=s(65827),h=s(49610),g=s(2963),y=s(40719),f=s(92462),b=s(61631),v=s(5506),w=s(37020);function k(){let e=(0,i.useParams)(),t=(0,i.useRouter)(),s=e.ticketId,k=(0,a.useRef)(null),[N,T]=(0,a.useState)(""),S=(0,f.GV)(b.xu),{data:A,isLoading:q,error:D,refetch:C}=(0,y.ot)(s),[I,{isLoading:P}]=(0,y.Rc)(),$=A?.data?.ticket,E=A?.data?.messages||[],_=async()=>{if(!N.trim())return void v.oR.error("Please enter a message");try{await I({ticketId:s,message:N.trim()}).unwrap(),T(""),C(),v.oR.success("Message sent successfully!")}catch(e){console.error("Error sending message:",e),v.oR.error(e?.data?.message||"Failed to send message")}};if(q)return(0,r.jsx)(w.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mb-4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/3"})]})]})})});if(D){let e=D?.data?.message||"Unknown error",s=D?.status,a=e.includes("authentication")||e.includes("unauthorized")||e.includes("Access token is required")||401===s,i=e.includes("Access denied")||403===s;return a?(t.push("/login?redirect="+encodeURIComponent(window.location.pathname)),null):i?(0,r.jsx)(w.A,{children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)(n.$,{variant:"ghost",onClick:()=>t.push("/support"),className:"mb-6",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Back to Support"]}),(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.Wu,{className:"p-6 text-center",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You can only view your own support tickets."}),(0,r.jsx)(n.$,{onClick:()=>t.push("/support"),children:"View My Tickets"})]})})]})}):(0,r.jsx)(w.A,{children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)(n.$,{variant:"ghost",onClick:()=>t.push("/support"),className:"mb-6",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Back to Support"]}),(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.Wu,{className:"p-6 text-center",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Ticket Not Found"}),(0,r.jsx)("p",{className:"text-gray-600",children:"The support ticket you're looking for doesn't exist or you don't have permission to view it."})]})})]})})}return(0,r.jsx)(w.A,{children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,r.jsxs)(n.$,{variant:"ghost",onClick:()=>t.back(),children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Back to Support"]})}),$&&(0,r.jsxs)(o.Zp,{className:"mb-6",children:[(0,r.jsx)(o.aR,{children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(o.ZB,{className:"text-xl mb-2",children:$.subject}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,r.jsxs)("span",{children:["Ticket #",$._id.slice(-8)]}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-1"}),new Date($.createdAt).toLocaleDateString()]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(e=>{switch(e){case"high":return(0,r.jsx)(d.E,{className:"bg-red-100 text-red-800",children:"High"});case"medium":return(0,r.jsx)(d.E,{className:"bg-yellow-100 text-yellow-800",children:"Medium"});case"low":return(0,r.jsx)(d.E,{className:"bg-green-100 text-green-800",children:"Low"});default:return(0,r.jsx)(d.E,{variant:"outline",children:e})}})($.priority),(e=>{switch(e){case"open":return(0,r.jsxs)(d.E,{className:"bg-red-100 text-red-800",children:[(0,r.jsx)(c.A,{className:"h-3 w-3 mr-1"}),"Open"]});case"in_progress":return(0,r.jsxs)(d.E,{className:"bg-yellow-100 text-yellow-800",children:[(0,r.jsx)(p.A,{className:"h-3 w-3 mr-1"}),"In Progress"]});case"resolved":return(0,r.jsxs)(d.E,{className:"bg-green-100 text-green-800",children:[(0,r.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"Resolved"]});case"closed":return(0,r.jsx)(d.E,{className:"bg-gray-100 text-gray-800",children:"Closed"});default:return(0,r.jsx)(d.E,{variant:"outline",children:e})}})($.status)]})]})}),(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Category"}),(0,r.jsx)("p",{className:"text-gray-600",children:$.category})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Description"}),(0,r.jsx)("p",{className:"text-gray-600 whitespace-pre-wrap",children:$.description})]})]})})]}),(0,r.jsxs)(o.Zp,{className:"mb-6",children:[(0,r.jsx)(o.aR,{children:(0,r.jsxs)(o.ZB,{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-5 w-5 mr-2"}),"Chat Messages"]}),(0,r.jsxs)(d.E,{variant:"outline",className:"text-xs",children:[E?.length||0," messages"]})]})}),(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("div",{className:"border rounded-lg bg-gray-50 p-4 max-h-96 overflow-y-auto space-y-3",children:[(0,r.jsx)("div",{className:"flex justify-end mb-4",children:(0,r.jsxs)("div",{className:"max-w-xs lg:max-w-md",children:[(0,r.jsxs)("div",{className:"bg-blue-500 text-white px-4 py-3 rounded-lg rounded-br-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)(h.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{className:"text-xs font-medium",children:"You"}),(0,r.jsxs)("span",{className:"text-xs opacity-75",children:[$&&new Date($.createdAt).toLocaleDateString()," ",$&&new Date($.createdAt).toLocaleTimeString()]})]}),(0,r.jsx)("p",{className:"text-sm font-medium mb-1",children:$?.subject}),(0,r.jsx)("p",{className:"text-sm",children:$?.description})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1 text-right",children:["Ticket #",$?._id.slice(-8)]})]})}),E&&E.length>0?E.map((e,t)=>{let s=(e.senderId?._id||e.senderId?.id||e.senderId)===S?._id,a=e.senderId?.role||e.senderType,i="admin"===a||"subadmin"===a||"support"===a;return(0,r.jsx)("div",{className:`flex mb-4 ${s?"justify-end":"justify-start"}`,children:(0,r.jsxs)("div",{className:"max-w-xs lg:max-w-md",children:[(0,r.jsxs)("div",{className:`px-4 py-3 shadow-sm ${s?"bg-blue-500 text-white rounded-lg rounded-br-sm":i?"bg-green-100 text-green-900 border border-green-200 rounded-lg rounded-bl-sm":"bg-white text-gray-900 border rounded-lg rounded-bl-sm"}`,children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)(h.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{className:"text-xs font-medium",children:s?"You":i?"Support Team":e.senderId?.firstName+" "+e.senderId?.lastName}),(0,r.jsx)("span",{className:"text-xs opacity-75",children:new Date(e.createdAt).toLocaleTimeString()})]}),(0,r.jsx)("p",{className:"text-sm whitespace-pre-wrap",children:e.content||e.message})]}),(0,r.jsxs)("div",{className:`text-xs text-gray-500 mt-1 ${s?"text-right":"text-left"}`,children:["sent"===e.status&&"✓","delivered"===e.status&&"✓✓","read"===e.status&&(0,r.jsx)("span",{className:"text-blue-500",children:"✓✓"})]})]})},e._id||t)}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No messages yet. Start the conversation!"})]}),(0,r.jsx)("div",{ref:k})]})})]}),$&&"closed"!==$.status&&(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.Wu,{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(l.T,{value:N,onChange:e=>T(e.target.value),placeholder:"Type your message here...",rows:2,className:"resize-none flex-1",onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),_())}}),(0,r.jsx)(n.$,{onClick:_,disabled:P||!N.trim(),className:"self-end",children:P?(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):(0,r.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Press Enter to send, Shift+Enter for new line"})]})})]})})}},76650:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var r=s(40969);s(73356);var a=s(52774),i=s(21764);let o=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,i.cn)(o({variant:t}),e),...s})}},78484:(e,t,s)=>{Promise.resolve().then(s.bind(s,38814))},79551:e=>{"use strict";e.exports=require("url")},82065:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var r=s(10557),a=s(68490),i=s(13172),o=s.n(i),n=s(68835),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let l={children:["",{children:["support",{children:["tickets",{children:["[ticketId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,38814)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\support\\tickets\\[ticketId]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\support\\tickets\\[ticketId]\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/support/tickets/[ticketId]/page",pathname:"/support/tickets/[ticketId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[755,3777,2544,7092,7555,2487,3427],()=>s(82065));module.exports=r})();