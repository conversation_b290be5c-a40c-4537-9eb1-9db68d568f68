(()=>{var e={};e.id=7839,e.ids=[7839],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4942:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(62544);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},5825:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(99024).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},6026:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(99024).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24689:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(99024).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40002:(e,t,r)=>{"use strict";r.d(t,{B3:()=>l,Lv:()=>c,Ng:()=>i,_L:()=>s,ac:()=>x,ge:()=>a,nd:()=>u,pv:()=>y,tl:()=>d,uU:()=>o});let{useLoginMutation:s,useRegisterMutation:a,useLogoutMutation:i,useRefreshTokenMutation:n,useVerifyEmailMutation:o,useResendVerificationEmailMutation:l,useForgotPasswordMutation:u,useVerifyPasswordResetOTPMutation:d,useResetPasswordMutation:c,useChangePasswordMutation:h,useVerifyPhoneMutation:m,useSendPhoneOTPMutation:p,useSendEmailOTPMutation:x,useVerifyEmailOTPMutation:y,useGetCurrentUserQuery:g,useGetUserProfileQuery:f,useCheckAuthStatusQuery:v}=r(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({login:e.mutation({query:e=>({url:"/auth/login",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),register:e.mutation({query:e=>({url:"/auth/register",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),logout:e.mutation({query:()=>({url:"/auth/logout",method:"POST"}),invalidatesTags:["Auth","User"]}),refreshToken:e.mutation({query:e=>({url:"/auth/refresh",method:"POST",body:e})}),verifyEmail:e.mutation({query:e=>({url:"/auth/verify-email",method:"POST",body:e}),invalidatesTags:["User"]}),resendVerificationEmail:e.mutation({query:e=>({url:"/auth/send-verification",method:"POST",body:e})}),forgotPassword:e.mutation({query:e=>({url:"/auth/forgot-password",method:"POST",body:e})}),verifyPasswordResetOTP:e.mutation({query:e=>({url:"/auth/verify-reset-otp",method:"POST",body:e})}),resetPassword:e.mutation({query:e=>({url:"/auth/reset-password",method:"POST",body:e})}),changePassword:e.mutation({query:e=>({url:"/auth/change-password",method:"POST",body:e}),invalidatesTags:["User"]}),verifyPhone:e.mutation({query:e=>({url:"/auth/verify-phone",method:"POST",body:e}),invalidatesTags:["User"]}),sendPhoneOTP:e.mutation({query:e=>({url:"/auth/send-phone-otp",method:"POST",body:e})}),sendEmailOTP:e.mutation({query:e=>({url:"/auth/send-otp",method:"POST",body:e})}),verifyEmailOTP:e.mutation({query:e=>({url:"/auth/verify-otp",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),getCurrentUser:e.query({query:()=>"/auth/me",providesTags:["User"]}),checkAuthStatus:e.query({query:()=>"/auth/status",providesTags:["Auth"]}),getUserProfile:e.query({query:()=>"/auth/profile",providesTags:["User","Auth"]})})})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66743:(e,t,r)=>{Promise.resolve().then(r.bind(r,67089))},67089:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(40969),a=r(73356),i=r(12011),n=r(5825),o=r(1110),l=r(6026),u=r(24689),d=r(46411),c=r(66949),h=r(40002);function m(){let e=(0,i.useRouter)();(0,i.useSearchParams)().get("token");let[t,{isLoading:r}]=(0,h.uU)(),[m,p]=(0,a.useState)("pending"),[x,y]=(0,a.useState)("");return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,s.jsx)(u.A,{className:"h-12 w-12 text-blue-600"})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"SGM "}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Email Verification"})]}),(0,s.jsxs)(c.Zp,{className:"shadow-2xl border-0 p-2",children:[(0,s.jsxs)(c.aR,{className:"space-y-1",children:[(0,s.jsx)(c.ZB,{className:`text-2xl font-bold text-center ${(()=>{switch(m){case"pending":return"text-blue-600";case"success":return"text-green-600";case"error":return"text-red-600"}})()}`,children:(()=>{switch(m){case"pending":return"Verifying Email...";case"success":return"Email Verified!";case"error":return"Verification Failed"}})()}),(0,s.jsxs)(c.BT,{className:"text-center",children:["pending"===m&&"Please wait while we verify your email address","success"===m&&"Your account is now active and ready to use","error"===m&&"There was an issue verifying your email"]})]}),(0,s.jsxs)(c.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(()=>{switch(m){case"pending":return(0,s.jsx)(n.A,{className:"h-16 w-16 text-blue-500 mx-auto mb-4 animate-spin"});case"success":return(0,s.jsx)(o.A,{className:"h-16 w-16 text-green-500 mx-auto mb-4"});case"error":return(0,s.jsx)(l.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"})}})(),(0,s.jsx)("p",{className:"text-gray-700 mb-4",children:x})]}),(0,s.jsxs)("div",{className:"space-y-3",children:["success"===m&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.$,{onClick:()=>e.push("/login"),className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:"Go to Login"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 text-center",children:"You will be redirected to login in a few seconds..."})]}),"error"===m&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.$,{onClick:()=>e.push("/register"),className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:"Back to Registration"}),(0,s.jsx)(d.$,{onClick:()=>e.push("/login"),variant:"outline",className:"w-full",children:"Try Login Anyway"})]}),"pending"===m&&(0,s.jsx)(d.$,{onClick:()=>e.push("/login"),variant:"outline",className:"w-full",disabled:r,children:"Cancel"})]})]})]})]})})}function p(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)(u.A,{className:"w-12 h-12 text-blue-600 mx-auto"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-blue-600",children:[(0,s.jsx)(n.A,{className:"w-5 h-5 animate-spin"}),(0,s.jsx)("span",{className:"text-lg font-medium",children:"Loading..."})]})]})}),children:(0,s.jsx)(m,{})})}r(1507)},72319:(e,t,r)=>{Promise.resolve().then(r.bind(r,93719))},79551:e=>{"use strict";e.exports=require("url")},86739:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>h,tree:()=>u});var s=r(10557),a=r(68490),i=r(13172),n=r.n(i),o=r(68835),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let u={children:["",{children:["verify-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,93719)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\verify-email\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\verify-email\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/verify-email/page",pathname:"/verify-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},93719:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\verify-email\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\verify-email\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[755,3777,2544,2487],()=>r(86739));module.exports=s})();