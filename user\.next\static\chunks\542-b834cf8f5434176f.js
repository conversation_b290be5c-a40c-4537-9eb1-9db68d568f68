"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[542],{95:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(5050).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},153:(e,t,n)=>{n.d(t,{jH:()=>u});var r=n(9585);n(9605);var o=r.createContext(void 0);function u(e){let t=r.useContext(o);return e||t||"ltr"}},4321:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function o(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function u(e,t,n){var o=r(e,t,"set");if(o.set)o.set.call(e,n);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=n}return n}n.d(t,{N:()=>d});var i,l=n(9585),a=n(8972),c=n(4455),s=n(8130),f=n(9605);function d(e){let t=e+"CollectionProvider",[n,r]=(0,a.A)(t),[o,u]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{let{scope:t,children:n}=e,r=l.useRef(null),u=l.useRef(new Map).current;return(0,f.jsx)(o,{scope:t,itemMap:u,collectionRef:r,children:n})};i.displayName=t;let d=e+"CollectionSlot",m=(0,s.TL)(d),p=l.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=u(d,n),i=(0,c.s)(t,o.collectionRef);return(0,f.jsx)(m,{ref:i,children:r})});p.displayName=d;let v=e+"CollectionItemSlot",y="data-radix-collection-item",N=(0,s.TL)(v),w=l.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,i=l.useRef(null),a=(0,c.s)(t,i),s=u(v,n);return l.useEffect(()=>(s.itemMap.set(i,{ref:i,...o}),()=>void s.itemMap.delete(i))),(0,f.jsx)(N,{...{[y]:""},ref:a,children:r})});return w.displayName=v,[{Provider:i,Slot:p,ItemSlot:w},function(t){let n=u(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(y,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var m=new WeakMap;function p(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=v(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function v(e){return e!=e||0===e?0:Math.trunc(e)}i=new WeakMap},4761:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},4853:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(9585),o=n(4455),u=n(6921),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),a=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(a.current);s.current="mounted"===f?e:"none"},[f]),(0,u.N)(()=>{let t=a.current,n=c.current;if(n!==e){let r=s.current,o=l(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),c.current=e}},[e,d]),(0,u.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=l(a.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},u=e=>{e.target===o&&(s.current=l(a.current))};return o.addEventListener("animationstart",u),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",u),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(t),a="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),c=(0,o.s)(i.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||i.isPresent?r.cloneElement(a,{ref:c}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},6921:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(9585),o=globalThis?.document?r.useLayoutEffect:()=>{}},7090:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(9585);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},7421:(e,t,n)=>{n.d(t,{B:()=>a});var r,o=n(9585),u=n(6921),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function a(e){let[t,n]=o.useState(i());return(0,u.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},7905:(e,t,n)=>{n.d(t,{hO:()=>a,sG:()=>l});var r=n(9585),o=n(3220),u=n(8130),i=n(9605),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,u.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...u}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?n:t,{...u,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function a(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},8972:(e,t,n)=>{n.d(t,{A:()=>i,q:()=>u});var r=n(9585),o=n(9605);function u(e,t){let n=r.createContext(t),u=e=>{let{children:t,...u}=e,i=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(n.Provider,{value:i,children:t})};return u.displayName=e+"Provider",[u,function(o){let u=r.useContext(n);if(u)return u;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return u.scopeName=e,[function(t,u){let i=r.createContext(u),l=n.length;n=[...n,u];let a=t=>{let{scope:n,children:u,...a}=t,c=n?.[e]?.[l]||i,s=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(c.Provider,{value:s,children:u})};return a.displayName=t+"Provider",[a,function(n,o){let a=o?.[e]?.[l]||i,c=r.useContext(a);if(c)return c;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(u,...t)]}},9728:(e,t,n)=>{n.d(t,{i:()=>l});var r,o=n(9585),u=n(6921),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[u,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),u=o.useRef(n),l=o.useRef(t);return i(()=>{l.current=t},[t]),o.useEffect(()=>{u.current!==n&&(l.current?.(n),u.current=n)},[n,u]),[n,r,l]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[c,e,l,a])]}Symbol("RADIX:SYNC_STATE")}}]);