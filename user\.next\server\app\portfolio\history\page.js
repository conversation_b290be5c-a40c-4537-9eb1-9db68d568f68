(()=>{var e={};e.id=9781,e.ids=[9781],e.modules={1393:(e,t,s)=>{Promise.resolve().then(s.bind(s,74056))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5568:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28496:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29081:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=s(10557),r=s(68490),l=s(13172),n=s.n(l),i=s(68835),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let o={children:["",{children:["portfolio",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,74056)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\portfolio\\history\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\portfolio\\history\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/portfolio/history/page",pathname:"/portfolio/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29655:(e,t,s)=>{"use strict";s.d(t,{AR:()=>r,rx:()=>a});let{useGetWalletBalanceQuery:a,useGetWalletTransactionsQuery:r,useAddMoneyToWalletMutation:l,useWithdrawMoneyMutation:n,useGetPaymentMethodsQuery:i,useAddPaymentMethodMutation:d,useUpdatePaymentMethodMutation:o,useDeletePaymentMethodMutation:c,useVerifyPaymentMethodMutation:m,useGetTransactionByIdQuery:x,useGetWalletAnalyticsQuery:u,useExportWalletStatementMutation:p,useSetTransactionAlertsMutation:h,useGetWalletLimitsQuery:y,useRequestLimitIncreaseMutation:g}=s(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getWalletBalance:e.query({query:()=>"/wallet",providesTags:[{type:"Wallet",id:"BALANCE"}],keepUnusedDataFor:120}),getWalletTransactions:e.query({query:e=>({url:"/wallet/transactions",params:{page:e.page||1,limit:e.limit||20,...e.type&&{type:e.type},...e.status&&{status:e.status},...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),transformResponse:e=>e?.success&&e?.data?.data?.length?e:{success:!0,message:"Transactions retrieved successfully",data:{data:[{_id:"demo-txn-1",type:"stock_purchase",amount:25e3,status:"completed",description:"Property Investment - Luxury Apartments",createdAt:new Date().toISOString(),reference:"TXN001"},{_id:"demo-txn-2",type:"deposit",amount:5e4,status:"completed",description:"Wallet Deposit",createdAt:new Date(Date.now()-864e5).toISOString(),reference:"TXN002"},{_id:"demo-txn-3",type:"investment",amount:1e5,status:"completed",description:"Property Investment - Commercial Complex",createdAt:new Date(Date.now()-1728e5).toISOString(),reference:"TXN003"}],pagination:{page:1,limit:20,total:3,pages:1}}},providesTags:e=>e?.data?.data?[...e.data.data.map(({_id:e})=>({type:"Transaction",id:e})),{type:"Transaction",id:"LIST"}]:[{type:"Transaction",id:"LIST"}],keepUnusedDataFor:300}),addMoneyToWallet:e.mutation({query:e=>({url:"/wallet/add-funds",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),withdrawMoney:e.mutation({query:e=>({url:"/wallet/withdraw",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),getPaymentMethods:e.query({query:()=>"/wallet/payment-methods",providesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}],keepUnusedDataFor:600}),addPaymentMethod:e.mutation({query:e=>({url:"/wallet/payment-methods",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),updatePaymentMethod:e.mutation({query:({id:e,...t})=>({url:`/wallet/payment-methods/${e}`,method:"PUT",body:t}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),deletePaymentMethod:e.mutation({query:e=>({url:`/wallet/payment-methods/${e}`,method:"DELETE"}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),verifyPaymentMethod:e.mutation({query:({id:e,verificationData:t})=>({url:`/wallet/payment-methods/${e}/verify`,method:"POST",body:t}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),getTransactionById:e.query({query:e=>`/wallet/transactions/${e}`,providesTags:(e,t,s)=>[{type:"Transaction",id:s}],keepUnusedDataFor:1800}),getWalletAnalytics:e.query({query:({period:e="1Y"})=>({url:"/wallet/analytics",params:{period:e}}),providesTags:[{type:"Wallet",id:"ANALYTICS"}],keepUnusedDataFor:900}),exportWalletStatement:e.mutation({query:e=>({url:"/wallet/export-statement",method:"POST",body:e})}),setTransactionAlerts:e.mutation({query:e=>({url:"/wallet/alerts",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"}]}),getWalletLimits:e.query({query:()=>"/wallet/limits",providesTags:[{type:"Wallet",id:"LIMITS"}],keepUnusedDataFor:3600}),requestLimitIncrease:e.mutation({query:e=>({url:"/wallet/request-limit-increase",method:"POST",body:e})})})})},33873:e=>{"use strict";e.exports=require("path")},37079:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},56021:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},56525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},60350:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(40969),r=s(73356),l=s(37020),n=s(85306),i=s(66949),d=s(76650),o=s(46411),c=s(1277),m=s(37079),x=s(56525),u=s(63407),p=s(56021),h=s(5568),y=s(29655),g=s(21764);function v(){let[e,t]=(0,r.useState)("all"),[s,v]=(0,r.useState)("createdAt"),[b,f]=(0,r.useState)("desc"),{data:j,isLoading:N,error:w}=(0,y.AR)({type:"all"!==e?e:void 0,sortBy:s,sortOrder:b,limit:100}),T=Array.isArray(j?.data)?j.data:j?.data?.data||[],k=e=>{switch(e){case"investment":return"bg-blue-100 text-blue-800";case"return":return"bg-green-100 text-green-800";case"withdrawal":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},A=e=>{switch(e){case"investment":return"↗️";case"return":return"\uD83D\uDCB0";case"withdrawal":return"↙️";default:return"\uD83D\uDCC4"}},P=T.reduce((e,t)=>e+(t.amount||0),0),q=T.filter(e=>{let t=new Date(e.createdAt),s=new Date;return t.getMonth()===s.getMonth()&&t.getFullYear()===s.getFullYear()}).length,S=T.length>0?Math.round(T.filter(e=>"completed"===e.status).length/T.length*100):0,D=[{label:"Total Transactions",value:T.length.toString(),trend:"neutral"},{label:"This Month",value:q.toString(),trend:"neutral"},{label:"Total Volume",value:(0,g.vv)(P),trend:"neutral"},{label:"Success Rate",value:`${S}%`,trend:"neutral"}];return N?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[1,2,3,4].map(e=>(0,a.jsx)("div",{className:"h-24 bg-gray-200 rounded-lg"},e))}),(0,a.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg"})]})})}):(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(n.Ay,{title:"Transaction History",description:"View all your investment transactions and activities with detailed tracking",icon:c.A,gradient:!0,breadcrumbs:[{label:"Portfolio",href:"/portfolio"},{label:"History"}],actions:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(n.lX.Secondary,{children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Filter"]}),(0,a.jsxs)(n.lX.Primary,{children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Export History"]})]})}),(0,a.jsx)(n.T0,{stats:D}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(i.Zp,{className:"border-0 shadow-lg",children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Transactions"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:T.length})]}),(0,a.jsx)("div",{className:"p-3 rounded-full bg-blue-50 text-blue-600",children:(0,a.jsx)(c.A,{className:"h-6 w-6"})})]})})}),(0,a.jsx)(i.Zp,{className:"border-0 shadow-lg",children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"This Month"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:"3"})]}),(0,a.jsx)("div",{className:"p-3 rounded-full bg-green-50 text-green-600",children:(0,a.jsx)(u.A,{className:"h-6 w-6"})})]})})}),(0,a.jsx)(i.Zp,{className:"border-0 shadow-lg",children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Volume"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:"₹4,48,500"})]}),(0,a.jsx)("div",{className:"p-3 rounded-full bg-sky-50 text-sky-600",children:(0,a.jsx)(p.A,{className:"h-6 w-6"})})]})})})]}),(0,a.jsxs)(i.Zp,{className:"border-0 shadow-lg",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 text-sky-600"}),(0,a.jsx)("span",{children:"Recent Transactions"})]})}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"space-y-4",children:T.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"text-2xl",children:A(e.type)}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.description||"Transaction"}),(0,a.jsx)(d.E,{className:k(e.type),children:e.type})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(0,g.Yq)(e.createdAt)," • ID: ",e._id.slice(-8)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:`font-bold ${"withdrawal"===e.type?"text-red-600":"text-green-600"}`,children:["withdrawal"===e.type?"-":"+","₹",e.amount.toLocaleString()]}),(0,a.jsx)(d.E,{variant:"secondary",className:"bg-green-100 text-green-800 text-xs",children:e.status})]}),(0,a.jsx)(o.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]})]},e._id))}),(0,a.jsx)("div",{className:"flex justify-center mt-6",children:(0,a.jsx)(o.$,{variant:"outline",className:"border-sky-200 text-sky-600 hover:bg-sky-50",children:"Load More Transactions"})})]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63407:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},66545:(e,t,s)=>{Promise.resolve().then(s.bind(s,60350))},74056:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\portfolio\\\\history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\portfolio\\history\\page.tsx","default")},76650:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(40969);s(73356);var r=s(52774),l=s(21764);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...s}){return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:t}),e),...s})}},79551:e=>{"use strict";e.exports=require("url")},85306:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,T0:()=>c,lX:()=>m});var a=s(40969);s(73356);var r=s(46411),l=s(76650),n=s(21764),i=s(28496),d=s(12011);function o({title:e,description:t,icon:s,badge:o,actions:c,breadcrumbs:m,showBackButton:x=!1,className:u,gradient:p=!1}){let h=(0,d.useRouter)();return(0,a.jsxs)("div",{className:(0,n.cn)("relative overflow-hidden",p&&"bg-gradient-to-r from-sky-50 via-blue-50 to-indigo-50",!p&&"bg-white",u),children:[p&&(0,a.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,a.jsx)("div",{className:"relative px-4 sm:px-6 lg:px-8 py-6 sm:py-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[m&&m.length>0&&(0,a.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,a.jsx)("ol",{className:"flex items-center space-x-2 text-sm",children:m.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,a.jsx)("span",{className:"mx-2 text-gray-400",children:"/"}),e.href?(0,a.jsx)("button",{onClick:()=>h.push(e.href),className:"text-gray-600 hover:text-sky-600 transition-colors",children:e.label}):(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:e.label})]},t))})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[x&&(0,a.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>h.back(),className:"flex-shrink-0 mt-1",children:(0,a.jsx)(i.A,{className:"h-4 w-4"})}),s&&(0,a.jsx)("div",{className:(0,n.cn)("flex-shrink-0 p-3 rounded-xl",p?"bg-white/80 backdrop-blur-sm shadow-lg":"bg-sky-50","text-sky-600"),children:(0,a.jsx)(s,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center flex-wrap gap-3 mb-2",children:[(0,a.jsx)("h1",{className:(0,n.cn)("text-2xl sm:text-3xl font-bold text-gray-900 leading-tight",p&&"bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent"),children:e}),o&&(0,a.jsx)(l.E,{variant:o.variant||"default",className:(0,n.cn)("text-xs font-medium",o.className),children:o.text})]}),t&&(0,a.jsx)("p",{className:"text-gray-600 text-sm sm:text-base max-w-2xl leading-relaxed",children:t})]})]}),c&&(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"flex items-center space-x-3",children:c})})]})]})}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"})]})}function c({stats:e,className:t}){return(0,a.jsx)("div",{className:(0,n.cn)("grid grid-cols-2 sm:grid-cols-4 gap-4 mt-6",t),children:e.map((e,t)=>(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide",children:e.label}),(0,a.jsx)("p",{className:"text-lg font-bold text-gray-900 mt-1",children:e.value}),e.change&&(0,a.jsx)("p",{className:(0,n.cn)("text-xs font-medium mt-1","up"===e.trend&&"text-green-600","down"===e.trend&&"text-red-600","neutral"===e.trend&&"text-gray-600"),children:e.change})]},t))})}let m={Primary:({children:e,...t})=>(0,a.jsx)(r.$,{className:"bg-sky-600 hover:bg-sky-700 text-white shadow-lg",...t,children:e}),Secondary:({children:e,...t})=>(0,a.jsx)(r.$,{variant:"outline",className:"border-sky-200 text-sky-700 hover:bg-sky-50",...t,children:e}),Ghost:({children:e,...t})=>(0,a.jsx)(r.$,{variant:"ghost",className:"text-gray-600 hover:text-sky-600 hover:bg-sky-50",...t,children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[755,3777,2544,7092,7555,2487,3427],()=>s(29081));module.exports=a})();