(()=>{var e={};e.id=362,e.ids=[362],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20571:(e,s,r)=>{"use strict";r.d(s,{Cl:()=>p,af:()=>b,dy:()=>m,zq:()=>t});let{useGetPropertiesQuery:t,useGetPropertyByIdQuery:a,useGetMyPropertiesQuery:l,useAssignPropertyToSalesRepMutation:i,useGetPropertyInquiriesQuery:o,useCreatePropertyInquiryMutation:d,useUpdatePropertyInquiryMutation:n,useGetPropertyAnalyticsQuery:c,useSearchPropertiesQuery:u,useGetPropertyTypesQuery:p,useGetPropertyLocationsQuery:m,useAddToWatchlistMutation:x,useRemoveFromWatchlistMutation:h,useGetWatchlistQuery:y,useComparePropertiesQuery:g,useGetPropertyStockQuery:v,useGetPropertiesWithStocksQuery:b}=r(53412).q.injectEndpoints({endpoints:e=>({getProperties:e.query({query:e=>({url:"/sales/properties",params:e}),providesTags:["Property"]}),getPropertyById:e.query({query:e=>`/sales/properties/${e}`,providesTags:(e,s,r)=>[{type:"Property",id:r}]}),getMyProperties:e.query({query:()=>"/sales/properties/my-properties",providesTags:["Property"]}),assignPropertyToSalesRep:e.mutation({query:({propertyId:e,salesRepId:s})=>({url:`/sales/properties/${e}/assign`,method:"POST",body:{salesRepId:s}}),invalidatesTags:["Property"]}),getPropertyInquiries:e.query({query:e=>({url:"/sales/property-inquiries",params:e}),providesTags:["Property"]}),createPropertyInquiry:e.mutation({query:e=>({url:"/sales/property-inquiries",method:"POST",body:e}),invalidatesTags:["Property","Lead"]}),updatePropertyInquiry:e.mutation({query:({id:e,data:s})=>({url:`/sales/property-inquiries/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:r})=>[{type:"Property",id:r},"Property"]}),getPropertyAnalytics:e.query({query:e=>({url:"/sales/properties/analytics",params:e}),providesTags:["Property","Dashboard"]}),searchProperties:e.query({query:({query:e,filters:s})=>({url:"/sales/properties/search",params:{query:e,...s}}),providesTags:["Property"]}),getPropertyTypes:e.query({query:()=>"/sales/properties/types",providesTags:["Property"]}),getPropertyLocations:e.query({query:()=>"/sales/properties/locations",providesTags:["Property"]}),addToWatchlist:e.mutation({query:e=>({url:`/sales/properties/${e}/watchlist`,method:"POST"}),invalidatesTags:["Property"]}),removeFromWatchlist:e.mutation({query:e=>({url:`/sales/properties/${e}/watchlist`,method:"DELETE"}),invalidatesTags:["Property"]}),getWatchlist:e.query({query:()=>"/sales/properties/watchlist",providesTags:["Property"]}),compareProperties:e.query({query:e=>({url:"/sales/properties/compare",method:"POST",body:{propertyIds:e}}),providesTags:["Property"]}),getPropertyStock:e.query({query:e=>`/sales/properties/${e}/stock`,providesTags:(e,s,r)=>[{type:"Property",id:`${r}-stock`}]}),getPropertiesWithStocks:e.query({query:e=>({url:"/sales/properties/with-stocks",params:e}),providesTags:["Property"]})})})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31435:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(98085).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33873:e=>{"use strict";e.exports=require("path")},51902:(e,s,r)=>{Promise.resolve().then(r.bind(r,99126))},53168:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(98085).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},56630:(e,s,r)=>{Promise.resolve().then(r.bind(r,98272))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77060:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(98085).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},79551:e=>{"use strict";e.exports=require("url")},82583:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(98085).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},83113:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>n});var t=r(10557),a=r(68490),l=r(13172),i=r.n(l),o=r(68835),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(s,d);let n={children:["",{children:["leads",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,98272)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\leads\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\leads\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/leads/page",pathname:"/leads",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},92386:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(98085).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},93541:(e,s,r)=>{"use strict";r.d(s,{S:()=>D});var t=r(40969),a=r(73356),l=r(24629),i=r(64680),o=r(60952),d=r(26314),n=r(42291),c=r(39827),u=r(22195),p=r(81861),m="Checkbox",[x,h]=(0,i.A)(m),[y,g]=x(m);function v(e){let{__scopeCheckbox:s,checked:r,children:l,defaultChecked:i,disabled:o,form:n,name:c,onCheckedChange:u,required:p,value:x="on",internal_do_not_use_render:h}=e,[g,v]=(0,d.i)({prop:r,defaultProp:i??!1,onChange:u,caller:m}),[b,j]=a.useState(null),[f,N]=a.useState(null),w=a.useRef(!1),P=!b||!!n||!!b.closest("form"),q={checked:g,disabled:o,setChecked:v,control:b,setControl:j,name:c,form:n,value:x,hasConsumerStoppedPropagationRef:w,required:p,defaultChecked:!k(i)&&i,isFormControl:P,bubbleInput:f,setBubbleInput:N};return(0,t.jsx)(y,{scope:s,...q,children:"function"==typeof h?h(q):l})}var b="CheckboxTrigger",j=a.forwardRef(({__scopeCheckbox:e,onKeyDown:s,onClick:r,...i},d)=>{let{control:n,value:c,disabled:u,checked:m,required:x,setControl:h,setChecked:y,hasConsumerStoppedPropagationRef:v,isFormControl:j,bubbleInput:f}=g(b,e),N=(0,l.s)(d,h),w=a.useRef(m);return a.useEffect(()=>{let e=n?.form;if(e){let s=()=>y(w.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[n,y]),(0,t.jsx)(p.sG.button,{type:"button",role:"checkbox","aria-checked":k(m)?"mixed":m,"aria-required":x,"data-state":C(m),"data-disabled":u?"":void 0,disabled:u,value:c,...i,ref:N,onKeyDown:(0,o.m)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.m)(r,e=>{y(e=>!!k(e)||!e),f&&j&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})})});j.displayName=b;var f=a.forwardRef((e,s)=>{let{__scopeCheckbox:r,name:a,checked:l,defaultChecked:i,required:o,disabled:d,value:n,onCheckedChange:c,form:u,...p}=e;return(0,t.jsx)(v,{__scopeCheckbox:r,checked:l,defaultChecked:i,disabled:d,required:o,onCheckedChange:c,name:a,form:u,value:n,internal_do_not_use_render:({isFormControl:e})=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j,{...p,ref:s,__scopeCheckbox:r}),e&&(0,t.jsx)(q,{__scopeCheckbox:r})]})})});f.displayName=m;var N="CheckboxIndicator",w=a.forwardRef((e,s)=>{let{__scopeCheckbox:r,forceMount:a,...l}=e,i=g(N,r);return(0,t.jsx)(u.C,{present:a||k(i.checked)||!0===i.checked,children:(0,t.jsx)(p.sG.span,{"data-state":C(i.checked),"data-disabled":i.disabled?"":void 0,...l,ref:s,style:{pointerEvents:"none",...e.style}})})});w.displayName=N;var P="CheckboxBubbleInput",q=a.forwardRef(({__scopeCheckbox:e,...s},r)=>{let{control:i,hasConsumerStoppedPropagationRef:o,checked:d,defaultChecked:u,required:m,disabled:x,name:h,value:y,form:v,bubbleInput:b,setBubbleInput:j}=g(P,e),f=(0,l.s)(r,j),N=(0,n.Z)(d),w=(0,c.X)(i);a.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!o.current;if(N!==d&&e){let r=new Event("click",{bubbles:s});b.indeterminate=k(d),e.call(b,!k(d)&&d),b.dispatchEvent(r)}},[b,N,d,o]);let q=a.useRef(!k(d)&&d);return(0,t.jsx)(p.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:u??q.current,required:m,disabled:x,name:h,value:y,form:v,...s,tabIndex:-1,ref:f,style:{...s.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function k(e){return"indeterminate"===e}function C(e){return k(e)?"indeterminate":e?"checked":"unchecked"}q.displayName=P;var T=r(13315),A=r(21764);let D=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)(f,{ref:r,className:(0,A.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:(0,t.jsx)(w,{className:(0,A.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(T.A,{className:"h-4 w-4"})})}));D.displayName=f.displayName},98272:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\leads\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\leads\\page.tsx","default")},99126:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>E});var t=r(40969),a=r(73356),l=r(88251),i=r(66949),o=r(46411),d=r(57387),n=r(76650),c=r(2223),u=r(96727),p=r(12053),m=r(54289),x=r(93541),h=r(14493),y=r(77060),g=r(31435),v=r(71727),b=r(82583),j=r(77272),f=r(56252),N=r(82039),w=r(61115),P=r(83427),q=r(8713),k=r(92386),C=r(53168),T=r(21764),A=r(99206),D=r(20571);let F=e=>{switch(e){case"new":default:return"bg-gray-100 text-gray-800";case"contacted":return"bg-sky-100 text-sky-800";case"qualified":case"closed_won":return"bg-green-100 text-green-800";case"proposal":case"negotiation":return"bg-yellow-100 text-yellow-800";case"closed_lost":return"bg-red-100 text-red-800"}},S=e=>e>=80?"text-red-600 bg-red-100":e>=60?"text-yellow-600 bg-yellow-100":e>=40?"text-blue-600 bg-blue-100":"text-gray-600 bg-gray-100";function E(){let[e,s]=(0,a.useState)(""),[r,E]=(0,a.useState)("all"),[L,$]=(0,a.useState)("all"),[_,U]=(0,a.useState)(1),[R,M]=(0,a.useState)(!1),[I,O]=(0,a.useState)({fullName:"",email:"",phone:"",city:"",country:"India",source:"website",priority:"medium",interestedProperties:[],budget:{min:"",max:""},notes:"",tags:[],nextFollowUpDate:"",expectedCloseDate:""}),{data:B,isLoading:z,error:J,refetch:G}=(0,A.$V)({page:_,limit:20,search:e||void 0,status:"all"!==r?r:void 0,source:"all"!==L?L:void 0,sortBy:"createdAt",sortOrder:"desc"}),{data:W,isLoading:V}=(0,D.zq)({limit:100,status:"active"}),[Z,{isLoading:H}]=(0,A.FO)(),K=B?.data?.leads||[],Q=B?.data?.pagination,X=W?.data?.properties||[],Y=()=>{O({fullName:"",email:"",phone:"",city:"",country:"India",source:"website",priority:"medium",interestedProperties:[],budget:{min:"",max:""},notes:"",tags:[],nextFollowUpDate:"",expectedCloseDate:""})},ee=async e=>{e.preventDefault();try{let e={fullName:I.fullName,email:I.email,phone:I.phone,city:I.city,country:I.country,source:I.source,priority:I.priority,interestedProperties:I.interestedProperties,budget:I.budget.min||I.budget.max?{min:parseFloat(I.budget.min)||0,max:parseFloat(I.budget.max)||0}:void 0,notes:I.notes,tags:I.tags,nextFollowUpDate:I.nextFollowUpDate||void 0,expectedCloseDate:I.expectedCloseDate||void 0};await Z(e).unwrap(),M(!1),Y(),G()}catch(e){console.error("Failed to create lead:",e)}},es=e=>{O(s=>({...s,interestedProperties:s.interestedProperties.includes(e)?s.interestedProperties.filter(s=>s!==e):[...s.interestedProperties,e]}))};return z?(0,t.jsx)(l.A,{title:"Leads Management",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)(h.A,{className:"w-8 h-8 animate-spin text-sky-500"})})})}):J?(0,t.jsx)(l.A,{title:"Leads Management",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(y.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Failed to load leads"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Please try refreshing the page"}),(0,t.jsx)(o.$,{onClick:()=>G(),className:"bg-sky-500 hover:bg-sky-600",children:"Try Again"})]})})})}):(0,t.jsxs)(l.A,{title:"Leads Management",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Leads"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage and track your sales leads"})]}),(0,t.jsxs)(o.$,{className:"bg-sky-500 hover:bg-sky-600 text-white",onClick:()=>M(!0),children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Add New Lead"]})]}),(0,t.jsx)(i.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(d.p,{placeholder:"Search leads by name, email, or phone...",value:e,onChange:e=>s(e.target.value),className:"pl-10 border-gray-300 focus:border-sky-500 focus:ring-sky-500"})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(c.l6,{value:r,onValueChange:E,children:[(0,t.jsx)(c.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,t.jsx)(c.yv,{placeholder:"Status"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"all",children:"All Status"}),(0,t.jsx)(c.eb,{value:"new",children:"New"}),(0,t.jsx)(c.eb,{value:"contacted",children:"Contacted"}),(0,t.jsx)(c.eb,{value:"qualified",children:"Qualified"}),(0,t.jsx)(c.eb,{value:"proposal",children:"Proposal"}),(0,t.jsx)(c.eb,{value:"negotiation",children:"Negotiation"}),(0,t.jsx)(c.eb,{value:"closed_won",children:"Closed Won"}),(0,t.jsx)(c.eb,{value:"closed_lost",children:"Closed Lost"})]})]}),(0,t.jsxs)(c.l6,{value:L,onValueChange:$,children:[(0,t.jsx)(c.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,t.jsx)(c.yv,{placeholder:"Source"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"all",children:"All Sources"}),(0,t.jsx)(c.eb,{value:"website",children:"Website"}),(0,t.jsx)(c.eb,{value:"referral",children:"Referral"}),(0,t.jsx)(c.eb,{value:"social_media",children:"Social Media"}),(0,t.jsx)(c.eb,{value:"advertisement",children:"Advertisement"}),(0,t.jsx)(c.eb,{value:"cold_call",children:"Cold Call"})]})]}),(0,t.jsxs)(o.$,{variant:"outline",className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:[(0,t.jsx)(b.A,{className:"w-4 h-4 mr-2"}),"More Filters"]})]})]})})}),(0,t.jsxs)(i.Zp,{className:"border-gray-200 shadow-sm",children:[(0,t.jsx)(i.aR,{className:"bg-white border-b border-gray-200",children:(0,t.jsxs)(i.ZB,{className:"text-gray-900",children:["Leads (",K.length,")",Q&&(0,t.jsxs)("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["Page ",Q.currentPage," of ",Q.totalPages,"(",Q.totalItems," total)"]})]})}),(0,t.jsxs)(i.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"space-y-0",children:K.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-100 hover:bg-sky-50 transition-colors cursor-pointer",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-sky-100 rounded-full flex items-center justify-center flex-shrink-0",children:(0,t.jsx)(j.A,{className:"w-6 h-6 text-sky-600"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsxs)("h4",{className:"text-lg font-medium text-gray-900 truncate",children:[e.firstName," ",e.lastName]}),(0,t.jsx)(n.E,{className:`text-xs ${F(e.status)}`,children:e.status.replace("_"," ").toUpperCase()}),e.score&&(0,t.jsx)("div",{className:`px-2 py-1 rounded-full text-xs font-medium ${S(e.score)}`,children:e.score})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 text-sm text-gray-600",children:[(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"w-3 h-3 mr-1 text-sky-500"}),e.email]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"w-3 h-3 mr-1 text-green-500"}),e.phone]}),(e.interestedProperty||e.propertyInterest)&&(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(w.A,{className:"w-3 h-3 mr-1 text-yellow-500"}),e.interestedProperty||e.propertyInterest]}),e.budget&&(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(P.A,{className:"w-3 h-3 mr-1 text-green-600"}),(0,T.vv)(e.budget)]})]}),e.tags&&e.tags.length>0&&(0,t.jsx)("div",{className:"flex items-center space-x-1 mt-2",children:e.tags.map(e=>(0,t.jsx)(n.E,{variant:"outline",className:"text-xs border-sky-200 text-sky-700",children:e},e))})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 flex-shrink-0",children:[(e.nextFollowUp||e.followUpDate)&&(0,t.jsxs)("div",{className:"text-right hidden sm:block",children:[(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,t.jsx)(q.A,{className:"w-3 h-3 mr-1 text-yellow-500"}),"Next follow-up"]}),(0,t.jsx)("div",{className:"text-xs font-medium text-gray-900",children:(0,T.Yq)(e.nextFollowUp||e.followUpDate)})]}),(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)(o.$,{size:"sm",variant:"outline",className:"h-8 w-8 p-0 border-green-300 text-green-600 hover:bg-green-50",children:(0,t.jsx)(N.A,{className:"w-3 h-3"})}),(0,t.jsx)(o.$,{size:"sm",variant:"outline",className:"h-8 w-8 p-0 border-sky-300 text-sky-600 hover:bg-sky-50",children:(0,t.jsx)(f.A,{className:"w-3 h-3"})}),(0,t.jsx)(o.$,{size:"sm",variant:"outline",className:"h-8 w-8 p-0 border-yellow-300 text-yellow-600 hover:bg-yellow-50",children:(0,t.jsx)(k.A,{className:"w-3 h-3"})}),(0,t.jsx)(o.$,{size:"sm",variant:"outline",className:"h-8 w-8 p-0 border-gray-300 text-gray-600 hover:bg-gray-50",children:(0,t.jsx)(C.A,{className:"w-3 h-3"})})]})]})]},e.id))}),0===K.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(j.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500",children:"No leads found matching your criteria"}),(0,t.jsx)(o.$,{className:"mt-4 bg-sky-500 hover:bg-sky-600 text-white",onClick:()=>G(),children:"Refresh"})]}),Q&&Q.totalPages>1&&(0,t.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(Q.currentPage-1)*Q.itemsPerPage+1," to"," ",Math.min(Q.currentPage*Q.itemsPerPage,Q.totalItems)," of"," ",Q.totalItems," results"]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>U(Q.currentPage-1),disabled:1===Q.currentPage,className:"border-gray-300",children:"Previous"}),(0,t.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>U(Q.currentPage+1),disabled:Q.currentPage===Q.totalPages,className:"border-gray-300",children:"Next"})]})]})]})]})]}),(0,t.jsx)(u.lG,{open:R,onOpenChange:e=>{M(e),e||Y()},children:(0,t.jsxs)(u.Cf,{className:"sm:max-w-[700px] max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(u.c7,{children:(0,t.jsxs)(u.L3,{className:"text-xl font-semibold flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"w-5 h-5 text-sky-500"}),"Create New Lead"]})}),(0,t.jsxs)("form",{onSubmit:ee,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Basic Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"fullName",children:"Full Name *"}),(0,t.jsx)(d.p,{id:"fullName",value:I.fullName,onChange:e=>O(s=>({...s,fullName:e.target.value})),placeholder:"Enter full name",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"email",children:"Email *"}),(0,t.jsx)(d.p,{id:"email",type:"email",value:I.email,onChange:e=>O(s=>({...s,email:e.target.value})),placeholder:"Enter email address",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"phone",children:"Phone *"}),(0,t.jsx)(d.p,{id:"phone",value:I.phone,onChange:e=>O(s=>({...s,phone:e.target.value})),placeholder:"Enter phone number",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"city",children:"City *"}),(0,t.jsx)(d.p,{id:"city",value:I.city,onChange:e=>O(s=>({...s,city:e.target.value})),placeholder:"Enter city",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"country",children:"Country *"}),(0,t.jsx)(d.p,{id:"country",value:I.country,onChange:e=>O(s=>({...s,country:e.target.value})),placeholder:"Enter country",required:!0})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Lead Details"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"source",children:"Source *"}),(0,t.jsxs)(c.l6,{value:I.source,onValueChange:e=>O(s=>({...s,source:e})),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"website",children:"Website"}),(0,t.jsx)(c.eb,{value:"social_media",children:"Social Media"}),(0,t.jsx)(c.eb,{value:"referral",children:"Referral"}),(0,t.jsx)(c.eb,{value:"cold_call",children:"Cold Call"}),(0,t.jsx)(c.eb,{value:"email_campaign",children:"Email Campaign"}),(0,t.jsx)(c.eb,{value:"advertisement",children:"Advertisement"}),(0,t.jsx)(c.eb,{value:"walk_in",children:"Walk In"}),(0,t.jsx)(c.eb,{value:"other",children:"Other"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"priority",children:"Priority"}),(0,t.jsxs)(c.l6,{value:I.priority,onValueChange:e=>O(s=>({...s,priority:e})),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"low",children:"Low"}),(0,t.jsx)(c.eb,{value:"medium",children:"Medium"}),(0,t.jsx)(c.eb,{value:"high",children:"High"}),(0,t.jsx)(c.eb,{value:"urgent",children:"Urgent"})]})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Budget Range"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"budgetMin",children:"Minimum Budget"}),(0,t.jsx)(d.p,{id:"budgetMin",type:"number",value:I.budget.min,onChange:e=>O(s=>({...s,budget:{...s.budget,min:e.target.value}})),placeholder:"Enter minimum budget"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"budgetMax",children:"Maximum Budget"}),(0,t.jsx)(d.p,{id:"budgetMax",type:"number",value:I.budget.max,onChange:e=>O(s=>({...s,budget:{...s.budget,max:e.target.value}})),placeholder:"Enter maximum budget"})]})]})]}),X.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Interested Properties"}),(0,t.jsx)("div",{className:"max-h-40 overflow-y-auto space-y-2 border rounded-lg p-3",children:X.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.S,{id:`property-${e.id}`,checked:I.interestedProperties.includes(e.id),onCheckedChange:()=>es(e.id)}),(0,t.jsxs)(p.J,{htmlFor:`property-${e.id}`,className:"text-sm cursor-pointer flex-1",children:[e.title||e.name," - ",(0,T.vv)(e.pricePerStock)]})]},e.id))})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Follow-up Schedule"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"nextFollowUp",children:"Next Follow-up Date"}),(0,t.jsx)(d.p,{id:"nextFollowUp",type:"datetime-local",value:I.nextFollowUpDate,onChange:e=>O(s=>({...s,nextFollowUpDate:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"expectedClose",children:"Expected Close Date"}),(0,t.jsx)(d.p,{id:"expectedClose",type:"date",value:I.expectedCloseDate,onChange:e=>O(s=>({...s,expectedCloseDate:e.target.value}))})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Additional Information"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"notes",children:"Notes"}),(0,t.jsx)(m.T,{id:"notes",value:I.notes,onChange:e=>O(s=>({...s,notes:e.target.value})),placeholder:"Enter any additional notes about the lead...",rows:3})]})]}),(0,t.jsxs)(u.Es,{className:"flex space-x-2",children:[(0,t.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>M(!1),disabled:H,children:"Cancel"}),(0,t.jsx)(o.$,{type:"submit",className:"bg-sky-500 hover:bg-sky-600 text-white",disabled:H,children:H?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Creating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Create Lead"]})})]})]})]})})]})}},99206:(e,s,r)=>{"use strict";r.d(s,{$V:()=>o,FO:()=>n,Hu:()=>l,OT:()=>k,Pb:()=>a,WD:()=>v,Zu:()=>j,aT:()=>g,cm:()=>b,nK:()=>m,pv:()=>q,ro:()=>f});let t=r(53412).q.injectEndpoints({endpoints:e=>({getDashboardStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getDashboardActivities:e.query({query:e=>({url:"/dashboard/activities",params:e}),providesTags:["Dashboard"]}),getSalesStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getLeads:e.query({query:e=>({url:"/sales/leads",params:e}),providesTags:["Lead"]}),getLeadById:e.query({query:e=>`/sales/leads/${e}`,providesTags:(e,s,r)=>[{type:"Lead",id:r}]}),createLead:e.mutation({query:e=>({url:"/sales/leads",method:"POST",body:e}),invalidatesTags:["Lead","Dashboard"]}),updateLead:e.mutation({query:({id:e,data:s})=>({url:`/sales/leads/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:r})=>[{type:"Lead",id:r},"Lead","Dashboard"]}),deleteLead:e.mutation({query:e=>({url:`/sales/leads/${e}`,method:"DELETE"}),invalidatesTags:["Lead","Dashboard"]}),assignLead:e.mutation({query:({leadId:e,salesRepId:s})=>({url:`/sales/leads/${e}/assign`,method:"POST",body:{salesRepId:s}}),invalidatesTags:["Lead"]}),getCustomers:e.query({query:e=>({url:"/sales/customers",params:e}),providesTags:["Customer"]}),getCustomerById:e.query({query:e=>`/sales/customers/${e}`,providesTags:(e,s,r)=>[{type:"Customer",id:r}]}),createCustomer:e.mutation({query:e=>({url:"/sales/customers",method:"POST",body:e}),invalidatesTags:["Customer","Dashboard"]}),updateCustomer:e.mutation({query:({id:e,data:s})=>({url:`/sales/customers/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:r})=>[{type:"Customer",id:r},"Customer"]}),getCommissions:e.query({query:e=>({url:"/sales/commissions",params:e}),providesTags:["Commission"]}),getSalesTargets:e.query({query:()=>"/sales/targets",providesTags:["Report"]}),createSalesTarget:e.mutation({query:e=>({url:"/sales/targets",method:"POST",body:e}),invalidatesTags:["Report","Dashboard"]}),updateSalesTarget:e.mutation({query:({id:e,data:s})=>({url:`/sales/targets/${e}`,method:"PUT",body:s}),invalidatesTags:["Report","Dashboard"]}),getFollowUps:e.query({query:e=>({url:"/follow-ups",params:e}),providesTags:["Task"]}),createFollowUp:e.mutation({query:e=>({url:"/follow-ups",method:"POST",body:e}),invalidatesTags:["Task","Dashboard"]}),updateFollowUp:e.mutation({query:({id:e,data:s})=>({url:`/follow-ups/${e}`,method:"PUT",body:s}),invalidatesTags:["Task","Dashboard"]}),deleteFollowUp:e.mutation({query:e=>({url:`/follow-ups/${e}`,method:"DELETE"}),invalidatesTags:["Task","Dashboard"]})})}),{useGetDashboardStatsQuery:a,useGetDashboardActivitiesQuery:l,useGetSalesStatsQuery:i,useGetLeadsQuery:o,useGetLeadByIdQuery:d,useCreateLeadMutation:n,useUpdateLeadMutation:c,useDeleteLeadMutation:u,useAssignLeadMutation:p,useGetCustomersQuery:m,useGetCustomerByIdQuery:x,useCreateCustomerMutation:h,useUpdateCustomerMutation:y,useGetCommissionsQuery:g,useGetSalesTargetsQuery:v,useCreateSalesTargetMutation:b,useUpdateSalesTargetMutation:j,useGetFollowUpsQuery:f,useCreateFollowUpMutation:N,useUpdateFollowUpMutation:w,useDeleteFollowUpMutation:P}=t,{useGetCustomersQuery:q,useGetCommissionsQuery:k}=t}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[755,598,544,29,796,286,447,512],()=>r(83113));module.exports=t})();