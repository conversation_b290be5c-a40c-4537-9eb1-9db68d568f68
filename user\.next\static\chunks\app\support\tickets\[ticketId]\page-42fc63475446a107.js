(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1790],{1856:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var r=t(9605),a=t(9585),i=t(5935),l=t(8063),n=t(2933),d=t(2790),c=t(6467),o=t(5460),u=t(3089),p=t(2006),m=t(9644),x=t(8415),h=t(498),g=t(8163),y=t(7379),v=t(9311),j=t(3815),b=t(6073),N=t(3005);function f(){var e,s,t;let f=(0,i.useParams)(),k=(0,i.useRouter)(),T=f.ticketId,w=(0,a.useRef)(null),[S,A]=(0,a.useState)(""),q=(0,v.GV)(j.xu),{data:C,isLoading:D,error:E,refetch:I}=(0,y.ot)(T),[F,{isLoading:L}]=(0,y.Rc)(),O=null==C||null==(e=C.data)?void 0:e.ticket,U=(null==C||null==(s=C.data)?void 0:s.messages)||[];(0,a.useEffect)(()=>{P()},[U]);let P=()=>{var e;null==(e=w.current)||e.scrollIntoView({behavior:"smooth"})},R=async()=>{if(!S.trim())return void b.oR.error("Please enter a message");try{await F({ticketId:T,message:S.trim()}).unwrap(),A(""),I(),b.oR.success("Message sent successfully!")}catch(s){var e;console.error("Error sending message:",s),b.oR.error((null==s||null==(e=s.data)?void 0:e.message)||"Failed to send message")}};if(D)return(0,r.jsx)(N.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mb-4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/3"})]})]})})});if(E){let e=(null==E||null==(t=E.data)?void 0:t.message)||"Unknown error",s=null==E?void 0:E.status,a=e.includes("authentication")||e.includes("unauthorized")||e.includes("Access token is required")||401===s,i=e.includes("Access denied")||403===s;return a?(k.push("/login?redirect="+encodeURIComponent(window.location.pathname)),null):i?(0,r.jsx)(N.A,{children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)(n.$,{variant:"ghost",onClick:()=>k.push("/support"),className:"mb-6",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Back to Support"]}),(0,r.jsx)(l.Zp,{children:(0,r.jsxs)(l.Wu,{className:"p-6 text-center",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You can only view your own support tickets."}),(0,r.jsx)(n.$,{onClick:()=>k.push("/support"),children:"View My Tickets"})]})})]})}):(0,r.jsx)(N.A,{children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)(n.$,{variant:"ghost",onClick:()=>k.push("/support"),className:"mb-6",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Back to Support"]}),(0,r.jsx)(l.Zp,{children:(0,r.jsxs)(l.Wu,{className:"p-6 text-center",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Ticket Not Found"}),(0,r.jsx)("p",{className:"text-gray-600",children:"The support ticket you're looking for doesn't exist or you don't have permission to view it."})]})})]})})}return(0,r.jsx)(N.A,{children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,r.jsxs)(n.$,{variant:"ghost",onClick:()=>k.back(),children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Back to Support"]})}),O&&(0,r.jsxs)(l.Zp,{className:"mb-6",children:[(0,r.jsx)(l.aR,{children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(l.ZB,{className:"text-xl mb-2",children:O.subject}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,r.jsxs)("span",{children:["Ticket #",O._id.slice(-8)]}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-1"}),new Date(O.createdAt).toLocaleDateString()]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(e=>{switch(e){case"high":return(0,r.jsx)(d.E,{className:"bg-red-100 text-red-800",children:"High"});case"medium":return(0,r.jsx)(d.E,{className:"bg-yellow-100 text-yellow-800",children:"Medium"});case"low":return(0,r.jsx)(d.E,{className:"bg-green-100 text-green-800",children:"Low"});default:return(0,r.jsx)(d.E,{variant:"outline",children:e})}})(O.priority),(e=>{switch(e){case"open":return(0,r.jsxs)(d.E,{className:"bg-red-100 text-red-800",children:[(0,r.jsx)(o.A,{className:"h-3 w-3 mr-1"}),"Open"]});case"in_progress":return(0,r.jsxs)(d.E,{className:"bg-yellow-100 text-yellow-800",children:[(0,r.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"In Progress"]});case"resolved":return(0,r.jsxs)(d.E,{className:"bg-green-100 text-green-800",children:[(0,r.jsx)(p.A,{className:"h-3 w-3 mr-1"}),"Resolved"]});case"closed":return(0,r.jsx)(d.E,{className:"bg-gray-100 text-gray-800",children:"Closed"});default:return(0,r.jsx)(d.E,{variant:"outline",children:e})}})(O.status)]})]})}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Category"}),(0,r.jsx)("p",{className:"text-gray-600",children:O.category})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Description"}),(0,r.jsx)("p",{className:"text-gray-600 whitespace-pre-wrap",children:O.description})]})]})})]}),(0,r.jsxs)(l.Zp,{className:"mb-6",children:[(0,r.jsx)(l.aR,{children:(0,r.jsxs)(l.ZB,{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-5 w-5 mr-2"}),"Chat Messages"]}),(0,r.jsxs)(d.E,{variant:"outline",className:"text-xs",children:[(null==U?void 0:U.length)||0," messages"]})]})}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"border rounded-lg bg-gray-50 p-4 max-h-96 overflow-y-auto space-y-3",children:[(0,r.jsx)("div",{className:"flex justify-end mb-4",children:(0,r.jsxs)("div",{className:"max-w-xs lg:max-w-md",children:[(0,r.jsxs)("div",{className:"bg-blue-500 text-white px-4 py-3 rounded-lg rounded-br-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)(h.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{className:"text-xs font-medium",children:"You"}),(0,r.jsxs)("span",{className:"text-xs opacity-75",children:[O&&new Date(O.createdAt).toLocaleDateString()," ",O&&new Date(O.createdAt).toLocaleTimeString()]})]}),(0,r.jsx)("p",{className:"text-sm font-medium mb-1",children:null==O?void 0:O.subject}),(0,r.jsx)("p",{className:"text-sm",children:null==O?void 0:O.description})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1 text-right",children:["Ticket #",null==O?void 0:O._id.slice(-8)]})]})}),U&&U.length>0?U.map((e,s)=>{var t,a,i,l,n;let d=((null==(t=e.senderId)?void 0:t._id)||(null==(a=e.senderId)?void 0:a.id)||e.senderId)===(null==q?void 0:q._id),c=(null==(i=e.senderId)?void 0:i.role)||e.senderType,o="admin"===c||"subadmin"===c||"support"===c;return(0,r.jsx)("div",{className:"flex mb-4 ".concat(d?"justify-end":"justify-start"),children:(0,r.jsxs)("div",{className:"max-w-xs lg:max-w-md",children:[(0,r.jsxs)("div",{className:"px-4 py-3 shadow-sm ".concat(d?"bg-blue-500 text-white rounded-lg rounded-br-sm":o?"bg-green-100 text-green-900 border border-green-200 rounded-lg rounded-bl-sm":"bg-white text-gray-900 border rounded-lg rounded-bl-sm"),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)(h.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{className:"text-xs font-medium",children:d?"You":o?"Support Team":(null==(l=e.senderId)?void 0:l.firstName)+" "+(null==(n=e.senderId)?void 0:n.lastName)}),(0,r.jsx)("span",{className:"text-xs opacity-75",children:new Date(e.createdAt).toLocaleTimeString()})]}),(0,r.jsx)("p",{className:"text-sm whitespace-pre-wrap",children:e.content||e.message})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1 ".concat(d?"text-right":"text-left"),children:["sent"===e.status&&"✓","delivered"===e.status&&"✓✓","read"===e.status&&(0,r.jsx)("span",{className:"text-blue-500",children:"✓✓"})]})]})},e._id||s)}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No messages yet. Start the conversation!"})]}),(0,r.jsx)("div",{ref:w})]})})]}),O&&"closed"!==O.status&&(0,r.jsx)(l.Zp,{children:(0,r.jsxs)(l.Wu,{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(c.T,{value:S,onChange:e=>A(e.target.value),placeholder:"Type your message here...",rows:2,className:"resize-none flex-1",onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),R())}}),(0,r.jsx)(n.$,{onClick:R,disabled:L||!S.trim(),className:"self-end",children:L?(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):(0,r.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Press Enter to send, Shift+Enter for new line"})]})})]})})}},2006:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2790:(e,s,t)=>{"use strict";t.d(s,{E:()=>n});var r=t(9605);t(9585);var a=t(7276),i=t(6994);let l=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:t,...a}=e;return(0,r.jsx)("div",{className:(0,i.cn)(l({variant:t}),s),...a})}},3089:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5460:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5756:(e,s,t)=>{Promise.resolve().then(t.bind(t,1856))},6467:(e,s,t)=>{"use strict";t.d(s,{T:()=>l});var r=t(9605),a=t(9585),i=t(6994);let l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...a})});l.displayName="Textarea"},7379:(e,s,t)=>{"use strict";t.d(s,{Rc:()=>c,g1:()=>r,iH:()=>h,ot:()=>a,u8:()=>i});let{useGetUserTicketsQuery:r,useGetTicketByIdQuery:a,useCreateSupportTicketMutation:i,useAddTicketResponseMutation:l,useCloseTicketMutation:n,useReopenTicketMutation:d,useAddTicketMessageMutation:c,useGetFAQCategoriesQuery:o,useGetFAQQuestionsQuery:u,useSearchFAQQuery:p,useRateFAQAnswerMutation:m,useGetSupportStatsQuery:x,useGetLiveChatStatusQuery:h,useStartLiveChatMutation:g,useSendChatMessageMutation:y,useEndLiveChatMutation:v,useGetChatHistoryQuery:j,useSubmitFeedbackMutation:b,useGetSupportContactsQuery:N}=t(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getUserTickets:e.query({query:e=>({url:"/support/tickets/my-tickets",params:{page:e.page||1,limit:e.limit||20,...e.status&&{status:e.status},...e.category&&{category:e.category},...e.priority&&{priority:e.priority},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),providesTags:e=>{var s;return(null==e||null==(s=e.data)?void 0:s.data)?[...e.data.data.map(e=>{let{_id:s}=e;return{type:"SupportTicket",id:s}}),{type:"SupportTicket",id:"LIST"}]:[{type:"SupportTicket",id:"LIST"}]},keepUnusedDataFor:120}),getTicketById:e.query({query:e=>"/support/tickets/".concat(e),providesTags:(e,s,t)=>[{type:"SupportTicket",id:t}],keepUnusedDataFor:300}),createSupportTicket:e.mutation({query:e=>({url:"/support/tickets",method:"POST",body:e}),invalidatesTags:[{type:"SupportTicket",id:"LIST"}]}),addTicketResponse:e.mutation({query:e=>{let{ticketId:s,...t}=e;return{url:"/support/tickets/".concat(s,"/responses"),method:"POST",body:t}},invalidatesTags:(e,s,t)=>{let{ticketId:r}=t;return[{type:"SupportTicket",id:r},{type:"SupportTicket",id:"LIST"}]}}),closeTicket:e.mutation({query:e=>{let{ticketId:s,...t}=e;return{url:"/support/tickets/".concat(s,"/close"),method:"PUT",body:t}},invalidatesTags:(e,s,t)=>{let{ticketId:r}=t;return[{type:"SupportTicket",id:r},{type:"SupportTicket",id:"LIST"}]}}),reopenTicket:e.mutation({query:e=>{let{ticketId:s,...t}=e;return{url:"/support/tickets/".concat(s,"/reopen"),method:"PUT",body:t}},invalidatesTags:(e,s,t)=>{let{ticketId:r}=t;return[{type:"SupportTicket",id:r},{type:"SupportTicket",id:"LIST"}]}}),addTicketMessage:e.mutation({query:e=>{let{ticketId:s,message:t,attachments:r}=e;return{url:"/support/tickets/".concat(s,"/messages"),method:"POST",body:{content:t,attachments:r}}},invalidatesTags:(e,s,t)=>{let{ticketId:r}=t;return[{type:"SupportTicket",id:r},{type:"SupportTicket",id:"LIST"}]}}),getFAQCategories:e.query({query:()=>"/support/faq/categories",providesTags:[{type:"SupportTicket",id:"FAQ_CATEGORIES"}],keepUnusedDataFor:3600}),getFAQQuestions:e.query({query:e=>({url:"/support/faq/questions",params:{page:e.page||1,limit:e.limit||20,...e.categoryId&&{categoryId:e.categoryId},...e.search&&{search:e.search}}}),providesTags:[{type:"SupportTicket",id:"FAQ_QUESTIONS"}],keepUnusedDataFor:1800}),searchFAQ:e.query({query:e=>{let{query:s,limit:t=10}=e;return{url:"/support/faq/search",params:{q:s,limit:t}}},keepUnusedDataFor:0}),rateFAQAnswer:e.mutation({query:e=>({url:"/support/faq/rate",method:"POST",body:e})}),getSupportStats:e.query({query:()=>"/support/stats",providesTags:[{type:"SupportTicket",id:"STATS"}],keepUnusedDataFor:600}),getLiveChatStatus:e.query({query:()=>"/support/live-chat/status",keepUnusedDataFor:60}),startLiveChat:e.mutation({query:e=>({url:"/support/live-chat/start",method:"POST",body:e})}),sendChatMessage:e.mutation({query:e=>{let{chatId:s,...t}=e;return{url:"/support/live-chat/".concat(s,"/messages"),method:"POST",body:t}}}),endLiveChat:e.mutation({query:e=>{let{chatId:s,...t}=e;return{url:"/support/live-chat/".concat(s,"/end"),method:"PUT",body:t}}}),getChatHistory:e.query({query:e=>({url:"/support/live-chat/history",params:{page:e.page||1,limit:e.limit||20,...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate}}}),providesTags:[{type:"SupportTicket",id:"CHAT_HISTORY"}],keepUnusedDataFor:600}),submitFeedback:e.mutation({query:e=>({url:"/support/feedback",method:"POST",body:e})}),getSupportContacts:e.query({query:()=>"/support/contacts",providesTags:[{type:"SupportTicket",id:"CONTACTS"}],keepUnusedDataFor:86400})})})},8163:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},8415:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[2094,5315,7436,7693,3590,1147,7627,3005,390,110,7358],()=>s(5756)),_N_E=e.O()}]);