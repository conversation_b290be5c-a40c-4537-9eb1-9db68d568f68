"use strict";exports.id=286,exports.ids=[286],exports.modules={5626:(e,t,n)=>{n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>ei,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(73356),o=n(60952),i=n(24629),a=n(64680),l=n(59705),c=n(26314),u=n(21431),s=n(25028),d=n(36146),f=n(22195),p=n(81861),h=n(43424),m=n(7111),v=n(87465),y=n(19334),g=n(40969),w="Dialog",[x,b]=(0,a.A)(w),[k,E]=x(w),A=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:a,modal:u=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,c.i)({prop:o,defaultProp:i??!1,onChange:a,caller:w});return(0,g.jsx)(k,{scope:t,triggerRef:s,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:u,children:n})};A.displayName=w;var S="DialogTrigger",C=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=E(S,n),l=(0,i.s)(t,a.triggerRef);return(0,g.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":G(a.open),...r,ref:l,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});C.displayName=S;var R="DialogPortal",[M,N]=x(R,{forceMount:void 0}),T=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,a=E(R,t);return(0,g.jsx)(M,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,g.jsx)(f.C,{present:n||a.open,children:(0,g.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};T.displayName=R;var L="DialogOverlay",P=r.forwardRef((e,t)=>{let n=N(L,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=E(L,e.__scopeDialog);return i.modal?(0,g.jsx)(f.C,{present:r||i.open,children:(0,g.jsx)(O,{...o,ref:t})}):null});P.displayName=L;var j=(0,y.TL)("DialogOverlay.RemoveScroll"),O=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(L,n);return(0,g.jsx)(m.A,{as:j,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(p.sG.div,{"data-state":G(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),D="DialogContent",I=r.forwardRef((e,t)=>{let n=N(D,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=E(D,e.__scopeDialog);return(0,g.jsx)(f.C,{present:r||i.open,children:i.modal?(0,g.jsx)(F,{...o,ref:t}):(0,g.jsx)(H,{...o,ref:t})})});I.displayName=D;var F=r.forwardRef((e,t)=>{let n=E(D,e.__scopeDialog),a=r.useRef(null),l=(0,i.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,g.jsx)(W,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),H=r.forwardRef((e,t)=>{let n=E(D,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,g.jsx)(W,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),W=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l,...c}=e,d=E(D,n),f=r.useRef(null),p=(0,i.s)(t,f);return(0,h.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,g.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":G(d.open),...c,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(X,{titleId:d.titleId}),(0,g.jsx)(Z,{contentRef:f,descriptionId:d.descriptionId})]})]})}),B="DialogTitle",_=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(B,n);return(0,g.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});_.displayName=B;var V="DialogDescription",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(V,n);return(0,g.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});z.displayName=V;var q="DialogClose",$=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=E(q,n);return(0,g.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function G(e){return e?"open":"closed"}$.displayName=q;var U="DialogTitleWarning",[K,Y]=(0,a.q)(U,{contentName:D,titleName:B,docsSlug:"dialog"}),X=({titleId:e})=>{let t=Y(U),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Z=({contentRef:e,descriptionId:t})=>{let n=Y("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},J=A,Q=C,ee=T,et=P,en=I,er=_,eo=z,ei=$},7111:(e,t,n)=>{n.d(t,{A:()=>G});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(73356)),c="right-scroll-bar-position",u="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=i({async:!0,ssr:!1},e),a}(),m=function(){},v=l.forwardRef(function(e,t){var n,r,o,c,u=l.useRef(null),p=l.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=p[0],y=p[1],g=e.forwardProps,w=e.children,x=e.className,b=e.removeScrollBar,k=e.enabled,E=e.shards,A=e.sideCar,S=e.noRelative,C=e.noIsolation,R=e.inert,M=e.allowPinchZoom,N=e.as,T=e.gapMode,L=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[u,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,c=o.facade,d(function(){var e=f.get(c);if(e){var t=new Set(e),r=new Set(n),o=c.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(c,n)},[n]),c),j=i(i({},L),v);return l.createElement(l.Fragment,null,k&&l.createElement(A,{sideCar:h,removeScrollBar:b,shards:E,noRelative:S,noIsolation:C,inert:R,setCallbacks:y,allowPinchZoom:!!M,lockRef:u,gapMode:T}),g?l.cloneElement(l.Children.only(w),i(i({},j),{ref:P})):l.createElement(void 0===N?"div":N,i({},j,{className:x,ref:P}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:u,zeroRight:c};var y=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,i({},n))};y.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=g();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},x=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},k=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[k(n),k(r),k(o)]},A=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},S=x(),C="data-scroll-locked",R=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(C,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(c," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(C,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},M=function(){var e=parseInt(document.body.getAttribute(C)||"0",10);return isFinite(e)?e:0},N=function(){l.useEffect(function(){return document.body.setAttribute(C,(M()+1).toString()),function(){var e=M()-1;e<=0?document.body.removeAttribute(C):document.body.setAttribute(C,e.toString())}},[])},T=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;N();var i=l.useMemo(function(){return A(o)},[o]);return l.createElement(S,{styles:R(i,!t,o,n?"":"!important")})},L=!1;if("undefined"!=typeof window)try{var P=Object.defineProperty({},"passive",{get:function(){return L=!0,!0}});window.addEventListener("test",P,P),window.removeEventListener("test",P,P)}catch(e){L=!1}var j=!!L&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},H=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,c=n.target,u=t.contains(c),s=!1,d=l>0,f=0,p=0;do{if(!c)break;var h=F(e,c),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&I(e,c)&&(f+=v,p+=m);var y=c.parentNode;c=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},_=function(e){return e&&"current"in e?e.current:e},V=0,z=[];let q=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(V++)[0],i=l.useState(x)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(_),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=W(e),l=n.current,c="deltaX"in e?e.deltaX:l[0]-i[0],u="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(c)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=D(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(c||u)&&(r.current=o),!o)return!0;var p=r.current||o;return H(p,t,e,"h"===p?c:u,!0)},[]),u=l.useCallback(function(e){if(z.length&&z[z.length-1]===i){var n="deltaY"in e?B(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(_).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=W(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,B(t),t.target,c(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,W(t),t.target,c(t,e.lockRef.current))},[]);l.useEffect(function(){return z.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,j),document.addEventListener("touchmove",u,j),document.addEventListener("touchstart",d,j),function(){z=z.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,j),document.removeEventListener("touchmove",u,j),document.removeEventListener("touchstart",d,j)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(T,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),y);var $=l.forwardRef(function(e,t){return l.createElement(v,i({},e,{ref:t,sideCar:q}))});$.classNames=v.classNames;let G=$},7409:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(73356),o=globalThis?.document?r.useLayoutEffect:()=>{}},8713:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9484:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},11627:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13315:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},16355:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},16860:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},21431:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(73356),i=n(60952),a=n(81861),l=n(24629),c=n(71060),u=n(40969),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:m,onFocusOutside:v,onInteractOutside:y,onDismiss:g,...w}=e,x=o.useContext(d),[b,k]=o.useState(null),E=b?.ownerDocument??globalThis?.document,[,A]=o.useState({}),S=(0,l.s)(t,e=>k(e)),C=Array.from(x.layers),[R]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),M=C.indexOf(R),N=b?C.indexOf(b):-1,T=x.layersWithOutsidePointerEventsDisabled.size>0,L=N>=M,P=function(e,t=globalThis?.document){let n=(0,c.c)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){h("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...x.branches].some(e=>e.contains(t));L&&!n&&(m?.(e),y?.(e),e.defaultPrevented||g?.())},E),j=function(e,t=globalThis?.document){let n=(0,c.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&h("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...x.branches].some(e=>e.contains(t))&&(v?.(e),y?.(e),e.defaultPrevented||g?.())},E);return!function(e,t=globalThis?.document){let n=(0,c.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===x.layers.size-1&&(f?.(e),!e.defaultPrevented&&g&&(e.preventDefault(),g()))},E),o.useEffect(()=>{if(b)return n&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(r=E.body.style.pointerEvents,E.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(b)),x.layers.add(b),p(),()=>{n&&1===x.layersWithOutsidePointerEventsDisabled.size&&(E.body.style.pointerEvents=r)}},[b,E,n,x]),o.useEffect(()=>()=>{b&&(x.layers.delete(b),x.layersWithOutsidePointerEventsDisabled.delete(b),p())},[b,x]),o.useEffect(()=>{let e=()=>A({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,u.jsx)(a.sG.div,{...w,ref:S,style:{pointerEvents:T?L?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,P.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,a.hO)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},21857:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},22195:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(73356),o=n(24629),i=n(7409),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),c=r.useRef(null),u=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=l(c.current);s.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=c.current,n=u.current;if(n!==e){let r=s.current,o=l(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,i.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,n=n=>{let r=l(c.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!u.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(s.current=l(c.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{c.current=e?getComputedStyle(e):null,a(e)},[])}}(t),c="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),u=(0,o.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(c));return"function"==typeof n||a.isPresent?r.cloneElement(c,{ref:u}):null};function l(e){return e?.animationName||"none"}a.displayName="Presence"},25028:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(73356),o=n(24629),i=n(81861),a=n(71060),l=n(40969),c="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:y,...g}=e,[w,x]=r.useState(null),b=(0,a.c)(v),k=(0,a.c)(y),E=r.useRef(null),A=(0,o.s)(t,e=>x(e)),S=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(S.paused||!w)return;let t=e.target;w.contains(t)?E.current=t:h(E.current,{select:!0})},t=function(e){if(S.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||h(E.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,S.paused]),r.useEffect(()=>{if(w){m.add(S);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(c,s);w.addEventListener(c,b),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(c,b),setTimeout(()=>{let t=new CustomEvent(u,s);w.addEventListener(u,k),w.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),w.removeEventListener(u,k),m.remove(S)},0)}}},[w,b,k,S]);let C=r.useCallback(e=>{if(!n&&!d||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,S.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...g,ref:A,onKeyDown:C})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},26314:(e,t,n)=>{n.d(t,{i:()=>l});var r,o=n(73356),i=n(7409),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,c]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return a(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&c.current?.(n)}else l(t)},[u,e,l,c])]}Symbol("RADIX:SYNC_STATE")},27470:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},28460:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},28501:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},31960:(e,t,n)=>{n.d(t,{UC:()=>nh,YJ:()=>nv,In:()=>nf,q7:()=>ng,VF:()=>nx,p4:()=>nw,JU:()=>ny,ZL:()=>np,bL:()=>nu,wn:()=>nk,PP:()=>nb,wv:()=>nE,l9:()=>ns,WT:()=>nd,LM:()=>nm});var r=n(73356),o=n(40813);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var a=n(60952),l=n(64680),c=n(24629),u=n(19334),s=n(40969),d=new WeakMap;function f(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=p(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function p(e){return e!=e||0===e?0:Math.trunc(e)}var h=r.createContext(void 0),m=n(21431),v=n(43424),y=n(25028),g=n(59705);let w=["top","right","bottom","left"],x=Math.min,b=Math.max,k=Math.round,E=Math.floor,A=e=>({x:e,y:e}),S={left:"right",right:"left",bottom:"top",top:"bottom"},C={start:"end",end:"start"};function R(e,t){return"function"==typeof e?e(t):e}function M(e){return e.split("-")[0]}function N(e){return e.split("-")[1]}function T(e){return"x"===e?"y":"x"}function L(e){return"y"===e?"height":"width"}let P=new Set(["top","bottom"]);function j(e){return P.has(M(e))?"y":"x"}function O(e){return e.replace(/start|end/g,e=>C[e])}let D=["left","right"],I=["right","left"],F=["top","bottom"],H=["bottom","top"];function W(e){return e.replace(/left|right|bottom|top/g,e=>S[e])}function B(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function _(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function V(e,t,n){let r,{reference:o,floating:i}=e,a=j(t),l=T(j(t)),c=L(l),u=M(t),s="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[c]/2-i[c]/2;switch(u){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(N(t)){case"start":r[l]-=p*(n&&s?-1:1);break;case"end":r[l]+=p*(n&&s?-1:1)}return r}let z=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),c=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=V(u,r,c),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:y,data:g,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=y?y:d,p={...p,[i]:{...p[i],...g}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(u=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=V(u,f,c)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function q(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=R(t,e),h=B(p),m=l[f?"floating"===d?"reference":"floating":d],v=_(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:s,strategy:c})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,g=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),w=await (null==i.isElement?void 0:i.isElement(g))&&await (null==i.getScale?void 0:i.getScale(g))||{x:1,y:1},x=_(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:g,strategy:c}):y);return{top:(v.top-x.top+h.top)/w.y,bottom:(x.bottom-v.bottom+h.bottom)/w.y,left:(v.left-x.left+h.left)/w.x,right:(x.right-v.right+h.right)/w.x}}function $(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function G(e){return w.some(t=>e[t]>=0)}let U=new Set(["left","top"]);async function K(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=M(n),l=N(n),c="y"===j(n),u=U.has(a)?-1:1,s=i&&c?-1:1,d=R(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof h&&(p="end"===l?-1*h:h),c?{x:p*s,y:f*u}:{x:f*u,y:p*s}}function Y(){return"undefined"!=typeof window}function X(e){return Q(e)?(e.nodeName||"").toLowerCase():"#document"}function Z(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function J(e){var t;return null==(t=(Q(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Q(e){return!!Y()&&(e instanceof Node||e instanceof Z(e).Node)}function ee(e){return!!Y()&&(e instanceof Element||e instanceof Z(e).Element)}function et(e){return!!Y()&&(e instanceof HTMLElement||e instanceof Z(e).HTMLElement)}function en(e){return!!Y()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof Z(e).ShadowRoot)}let er=new Set(["inline","contents"]);function eo(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=em(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!er.has(o)}let ei=new Set(["table","td","th"]),ea=[":popover-open",":modal"];function el(e){return ea.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let ec=["transform","translate","scale","rotate","perspective"],eu=["transform","translate","scale","rotate","perspective","filter"],es=["paint","layout","strict","content"];function ed(e){let t=ef(),n=ee(e)?em(e):e;return ec.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||eu.some(e=>(n.willChange||"").includes(e))||es.some(e=>(n.contain||"").includes(e))}function ef(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ep=new Set(["html","body","#document"]);function eh(e){return ep.has(X(e))}function em(e){return Z(e).getComputedStyle(e)}function ev(e){return ee(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ey(e){if("html"===X(e))return e;let t=e.assignedSlot||e.parentNode||en(e)&&e.host||J(e);return en(t)?t.host:t}function eg(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ey(t);return eh(n)?t.ownerDocument?t.ownerDocument.body:t.body:et(n)&&eo(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=Z(o);if(i){let e=ew(a);return t.concat(a,a.visualViewport||[],eo(o)?o:[],e&&n?eg(e):[])}return t.concat(o,eg(o,[],n))}function ew(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ex(e){let t=em(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=et(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=k(n)!==i||k(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function eb(e){return ee(e)?e:e.contextElement}function ek(e){let t=eb(e);if(!et(t))return A(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ex(t),a=(i?k(n.width):n.width)/r,l=(i?k(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let eE=A(0);function eA(e){let t=Z(e);return ef()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eE}function eS(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=eb(e),l=A(1);t&&(r?ee(r)&&(l=ek(r)):l=ek(e));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===Z(a))&&o)?eA(a):A(0),u=(i.left+c.x)/l.x,s=(i.top+c.y)/l.y,d=i.width/l.x,f=i.height/l.y;if(a){let e=Z(a),t=r&&ee(r)?Z(r):r,n=e,o=ew(n);for(;o&&r&&t!==n;){let e=ek(o),t=o.getBoundingClientRect(),r=em(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,s*=e.y,d*=e.x,f*=e.y,u+=i,s+=a,o=ew(n=Z(o))}}return _({width:d,height:f,x:u,y:s})}function eC(e,t){let n=ev(e).scrollLeft;return t?t.left+n:eS(J(e)).left+n}function eR(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eC(e,r)),y:r.top+t.scrollTop}}let eM=new Set(["absolute","fixed"]);function eN(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=Z(e),r=J(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,c=0;if(o){i=o.width,a=o.height;let e=ef();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,c=o.offsetTop)}return{width:i,height:a,x:l,y:c}}(e,n);else if("document"===t)r=function(e){let t=J(e),n=ev(e),r=e.ownerDocument.body,o=b(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=b(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+eC(e),l=-n.scrollTop;return"rtl"===em(r).direction&&(a+=b(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:l}}(J(e));else if(ee(t))r=function(e,t){let n=eS(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=et(e)?ek(e):A(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=eA(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return _(r)}function eT(e){return"static"===em(e).position}function eL(e,t){if(!et(e)||"fixed"===em(e).position)return null;if(t)return t(e);let n=e.offsetParent;return J(e)===n&&(n=n.ownerDocument.body),n}function eP(e,t){var n;let r=Z(e);if(el(e))return r;if(!et(e)){let t=ey(e);for(;t&&!eh(t);){if(ee(t)&&!eT(t))return t;t=ey(t)}return r}let o=eL(e,t);for(;o&&(n=o,ei.has(X(n)))&&eT(o);)o=eL(o,t);return o&&eh(o)&&eT(o)&&!ed(o)?r:o||function(e){let t=ey(e);for(;et(t)&&!eh(t);){if(ed(t))return t;if(el(t))break;t=ey(t)}return null}(e)||r}let ej=async function(e){let t=this.getOffsetParent||eP,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=et(t),o=J(t),i="fixed"===n,a=eS(e,!0,i,t),l={scrollLeft:0,scrollTop:0},c=A(0);if(r||!r&&!i)if(("body"!==X(t)||eo(o))&&(l=ev(t)),r){let e=eS(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=eC(o));i&&!r&&o&&(c.x=eC(o));let u=!o||r||i?A(0):eR(o,l);return{x:a.left+l.scrollLeft-c.x-u.x,y:a.top+l.scrollTop-c.y-u.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eO={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=J(r),l=!!t&&el(t.floating);if(r===a||l&&i)return n;let c={scrollLeft:0,scrollTop:0},u=A(1),s=A(0),d=et(r);if((d||!d&&!i)&&(("body"!==X(r)||eo(a))&&(c=ev(r)),et(r))){let e=eS(r);u=ek(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!a||d||i?A(0):eR(a,c,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-c.scrollLeft*u.x+s.x+f.x,y:n.y*u.y-c.scrollTop*u.y+s.y+f.y}},getDocumentElement:J,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?el(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eg(e,[],!1).filter(e=>ee(e)&&"body"!==X(e)),o=null,i="fixed"===em(e).position,a=i?ey(e):e;for(;ee(a)&&!eh(a);){let t=em(a),n=ed(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&eM.has(o.position)||eo(a)&&!n&&function e(t,n){let r=ey(t);return!(r===n||!ee(r)||eh(r))&&("fixed"===em(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=ey(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((e,n)=>{let r=eN(t,n,o);return e.top=b(r.top,e.top),e.right=x(r.right,e.right),e.bottom=x(r.bottom,e.bottom),e.left=b(r.left,e.left),e},eN(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eP,getElementRects:ej,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ex(e);return{width:t,height:n}},getScale:ek,isElement:ee,isRTL:function(e){return"rtl"===em(e).direction}};function eD(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eI=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:l,middlewareData:c}=t,{element:u,padding:s=0}=R(e,t)||{};if(null==u)return{};let d=B(s),f={x:n,y:r},p=T(j(o)),h=L(p),m=await a.getDimensions(u),v="y"===p,y=v?"clientHeight":"clientWidth",g=i.reference[h]+i.reference[p]-f[p]-i.floating[h],w=f[p]-i.reference[p],k=await (null==a.getOffsetParent?void 0:a.getOffsetParent(u)),E=k?k[y]:0;E&&await (null==a.isElement?void 0:a.isElement(k))||(E=l.floating[y]||i.floating[h]);let A=E/2-m[h]/2-1,S=x(d[v?"top":"left"],A),C=x(d[v?"bottom":"right"],A),M=E-m[h]-C,P=E/2-m[h]/2+(g/2-w/2),O=b(S,x(P,M)),D=!c.arrow&&null!=N(o)&&P!==O&&i.reference[h]/2-(P<S?S:C)-m[h]/2<0,I=D?P<S?P-S:P-M:0;return{[p]:f[p]+I,data:{[p]:O,centerOffset:P-O-I,...D&&{alignmentOffset:I}},reset:D}}}),eF=(e,t,n)=>{let r=new Map,o={platform:eO,...n},i={...o.platform,_c:r};return z(e,t,{...o,platform:i})};var eH="undefined"!=typeof document?r.useLayoutEffect:function(){};function eW(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eW(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eW(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eB(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e_(e,t){let n=eB(e);return Math.round(t*n)/n}function eV(e){let t=r.useRef(e);return eH(()=>{t.current=e}),t}let ez=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eI({element:n.current,padding:r}).fn(t):{}:n?eI({element:n,padding:r}).fn(t):{}}}),eq=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,c=await K(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:a}}}}}(e),options:[e,t]}),e$=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=R(e,t),u={x:n,y:r},s=await q(t,c),d=j(M(o)),f=T(d),p=u[f],h=u[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=b(n,x(p,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=b(n,x(h,r))}let m=l.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:a}}}}}}(e),options:[e,t]}),eG=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:c=!0,crossAxis:u=!0}=R(e,t),s={x:n,y:r},d=j(o),f=T(d),p=s[f],h=s[d],m=R(l,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(c){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var y,g;let e="y"===f?"width":"height",t=U.has(M(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(y=a.offset)?void 0:y[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(g=a.offset)?void 0:g[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),eU=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:c,rects:u,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:g=!0,...w}=R(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let x=M(l),b=j(s),k=M(s)===s,E=await (null==d.isRTL?void 0:d.isRTL(f.floating)),A=m||(k||!g?[W(s)]:function(e){let t=W(e);return[O(e),t,O(t)]}(s)),S="none"!==y;!m&&S&&A.push(...function(e,t,n,r){let o=N(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?I:D;return t?D:I;case"left":case"right":return t?F:H;default:return[]}}(M(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(O)))),i}(s,g,y,E));let C=[s,...A],P=await q(t,w),B=[],_=(null==(r=c.flip)?void 0:r.overflows)||[];if(p&&B.push(P[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=N(e),o=T(j(e)),i=L(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=W(a)),[a,W(a)]}(l,u,E);B.push(P[e[0]],P[e[1]])}if(_=[..._,{placement:l,overflows:B}],!B.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=C[e];if(t&&("alignment"!==h||b===j(t)||_.every(e=>e.overflows[0]>0&&j(e.placement)===b)))return{data:{index:e,overflows:_},reset:{placement:t}};let n=null==(i=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=_.filter(e=>{if(S){let t=j(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eK=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:a,rects:l,platform:c,elements:u}=t,{apply:s=()=>{},...d}=R(e,t),f=await q(t,d),p=M(a),h=N(a),m="y"===j(a),{width:v,height:y}=l.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==c.isRTL?void 0:c.isRTL(u.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let g=y-f.top-f.bottom,w=v-f.left-f.right,k=x(y-f[o],g),E=x(v-f[i],w),A=!t.middlewareData.shift,S=k,C=E;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=g),A&&!h){let e=b(f.left,0),t=b(f.right,0),n=b(f.top,0),r=b(f.bottom,0);m?C=v-2*(0!==e||0!==t?e+t:b(f.left,f.right)):S=y-2*(0!==n||0!==r?n+r:b(f.top,f.bottom))}await s({...t,availableWidth:C,availableHeight:S});let T=await c.getDimensions(u.floating);return v!==T.width||y!==T.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eY=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=R(e,t);switch(r){case"referenceHidden":{let e=$(await q(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:G(e)}}}case"escaped":{let e=$(await q(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:G(e)}}}default:return{}}}}}(e),options:[e,t]}),eX=(e,t)=>({...ez(e),options:[e,t]});var eZ=n(81861),eJ=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,s.jsx)(eZ.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,s.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eJ.displayName="Arrow";var eQ=n(71060),e0=n(7409),e1=n(39827),e2="Popper",[e8,e5]=(0,l.A)(e2),[e6,e4]=e8(e2),e3=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,s.jsx)(e6,{scope:t,anchor:o,onAnchorChange:i,children:n})};e3.displayName=e2;var e9="PopperAnchor",e7=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=e4(e9,n),l=r.useRef(null),u=(0,c.s)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,s.jsx)(eZ.sG.div,{...i,ref:u})});e7.displayName=e9;var te="PopperContent",[tt,tn]=e8(te),tr=r.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:a=0,align:l="center",alignOffset:u=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:y="optimized",onPlaced:g,...w}=e,k=e4(te,n),[A,S]=r.useState(null),C=(0,c.s)(t,e=>S(e)),[R,M]=r.useState(null),N=(0,e1.X)(R),T=N?.width??0,L=N?.height??0,P="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},j=Array.isArray(p)?p:[p],O=j.length>0,D={padding:P,boundary:j.filter(tl),altBoundary:O},{refs:I,floatingStyles:F,placement:H,isPositioned:W,middlewareData:B}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:a,elements:{reference:l,floating:c}={},transform:u=!0,whileElementsMounted:s,open:d}=e,[f,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=r.useState(i);eW(h,i)||m(i);let[v,y]=r.useState(null),[g,w]=r.useState(null),x=r.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),b=r.useCallback(e=>{e!==S.current&&(S.current=e,w(e))},[]),k=l||v,E=c||g,A=r.useRef(null),S=r.useRef(null),C=r.useRef(f),R=null!=s,M=eV(s),N=eV(a),T=eV(d),L=r.useCallback(()=>{if(!A.current||!S.current)return;let e={placement:t,strategy:n,middleware:h};N.current&&(e.platform=N.current),eF(A.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};P.current&&!eW(C.current,t)&&(C.current=t,o.flushSync(()=>{p(t)}))})},[h,t,n,N,T]);eH(()=>{!1===d&&C.current.isPositioned&&(C.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let P=r.useRef(!1);eH(()=>(P.current=!0,()=>{P.current=!1}),[]),eH(()=>{if(k&&(A.current=k),E&&(S.current=E),k&&E){if(M.current)return M.current(k,E,L);L()}},[k,E,L,M,R]);let j=r.useMemo(()=>({reference:A,floating:S,setReference:x,setFloating:b}),[x,b]),O=r.useMemo(()=>({reference:k,floating:E}),[k,E]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=e_(O.floating,f.x),r=e_(O.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eB(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,O.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:L,refs:j,elements:O,floatingStyles:D}),[f,L,j,O,D])}({strategy:"fixed",placement:i+("center"!==l?"-"+l:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,s=eb(e),d=i||a?[...s?eg(s):[],...eg(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=s&&c?function(e,t){let n,r=null,o=J(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(l,c){void 0===l&&(l=!1),void 0===c&&(c=1),i();let u=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=u;if(l||t(),!f||!p)return;let h=E(d),m=E(o.clientWidth-(s+f)),v={rootMargin:-h+"px "+-m+"px "+-E(o.clientHeight-(d+p))+"px "+-E(s)+"px",threshold:b(0,x(1,c))||1},y=!0;function g(t){let r=t[0].intersectionRatio;if(r!==c){if(!y)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||eD(u,e.getBoundingClientRect())||a(),y=!1}try{r=new IntersectionObserver(g,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(g,v)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;l&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!u&&h.observe(s),h.observe(t));let m=u?eS(e):null;return u&&function t(){let r=eS(e);m&&!eD(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,u&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===y}),elements:{reference:k.anchor},middleware:[eq({mainAxis:a+L,alignmentAxis:u}),f&&e$({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?eG():void 0,...D}),f&&eU({...D}),eK({...D,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),R&&eX({element:R,padding:d}),tc({arrowWidth:T,arrowHeight:L}),v&&eY({strategy:"referenceHidden",...D})]}),[_,V]=tu(H),z=(0,eQ.c)(g);(0,e0.N)(()=>{W&&z?.()},[W,z]);let q=B.arrow?.x,$=B.arrow?.y,G=B.arrow?.centerOffset!==0,[U,K]=r.useState();return(0,e0.N)(()=>{A&&K(window.getComputedStyle(A).zIndex)},[A]),(0,s.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...F,transform:W?F.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:U,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,s.jsx)(tt,{scope:n,placedSide:_,onArrowChange:M,arrowX:q,arrowY:$,shouldHideArrow:G,children:(0,s.jsx)(eZ.sG.div,{"data-side":_,"data-align":V,...w,ref:C,style:{...w.style,animation:W?void 0:"none"}})})})});tr.displayName=te;var to="PopperArrow",ti={top:"bottom",right:"left",bottom:"top",left:"right"},ta=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tn(to,n),i=ti[o.placedSide];return(0,s.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,s.jsx)(eJ,{...r,ref:t,style:{...r.style,display:"block"}})})});function tl(e){return null!==e}ta.displayName=to;var tc=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[c,u]=tu(n),s={start:"0%",center:"50%",end:"100%"}[u],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===c?(p=i?s:`${d}px`,h=`${-l}px`):"top"===c?(p=i?s:`${d}px`,h=`${r.floating.height+l}px`):"right"===c?(p=`${-l}px`,h=i?s:`${f}px`):"left"===c&&(p=`${r.floating.width+l}px`,h=i?s:`${f}px`),{data:{x:p,y:h}}}});function tu(e){let[t,n="center"]=e.split("-");return[t,n]}var ts=n(36146),td=n(26314),tf=n(42291),tp=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,s.jsx)(eZ.sG.span,{...e,ref:t,style:{...tp,...e.style}})).displayName="VisuallyHidden";var th=n(87465),tm=n(7111),tv=[" ","Enter","ArrowUp","ArrowDown"],ty=[" ","Enter"],tg="Select",[tw,tx,tb]=function(e){let t=e+"CollectionProvider",[n,o]=(0,l.A)(t),[i,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),a=r.useRef(new Map).current;return(0,s.jsx)(i,{scope:t,itemMap:a,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=(0,u.TL)(f),h=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=a(f,n),i=(0,c.s)(t,o.collectionRef);return(0,s.jsx)(p,{ref:i,children:r})});h.displayName=f;let m=e+"CollectionItemSlot",v="data-radix-collection-item",y=(0,u.TL)(m),g=r.forwardRef((e,t)=>{let{scope:n,children:o,...i}=e,l=r.useRef(null),u=(0,c.s)(t,l),d=a(m,n);return r.useEffect(()=>(d.itemMap.set(l,{ref:l,...i}),()=>void d.itemMap.delete(l))),(0,s.jsx)(y,{...{[v]:""},ref:u,children:o})});return g.displayName=m,[{Provider:d,Slot:h,ItemSlot:g},function(t){let n=a(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},o]}(tg),[tk,tE]=(0,l.A)(tg,[tb,e5]),tA=e5(),[tS,tC]=tk(tg),[tR,tM]=tk(tg),tN=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:a,value:l,defaultValue:c,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:m,required:v,form:y}=e,w=tA(t),[x,b]=r.useState(null),[k,E]=r.useState(null),[A,S]=r.useState(!1),C=function(e){let t=r.useContext(h);return e||t||"ltr"}(d),[R,M]=(0,td.i)({prop:o,defaultProp:i??!1,onChange:a,caller:tg}),[N,T]=(0,td.i)({prop:l,defaultProp:c,onChange:u,caller:tg}),L=r.useRef(null),P=!x||y||!!x.closest("form"),[j,O]=r.useState(new Set),D=Array.from(j).map(e=>e.props.value).join(";");return(0,s.jsx)(e3,{...w,children:(0,s.jsxs)(tS,{required:v,scope:t,trigger:x,onTriggerChange:b,valueNode:k,onValueNodeChange:E,valueNodeHasChildren:A,onValueNodeHasChildrenChange:S,contentId:(0,g.B)(),value:N,onValueChange:T,open:R,onOpenChange:M,dir:C,triggerPointerDownPosRef:L,disabled:m,children:[(0,s.jsx)(tw.Provider,{scope:t,children:(0,s.jsx)(tR,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{O(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),P?(0,s.jsxs)(ni,{"aria-hidden":!0,required:v,tabIndex:-1,name:f,autoComplete:p,value:N,onChange:e=>T(e.target.value),disabled:m,form:y,children:[void 0===N?(0,s.jsx)("option",{value:""}):null,Array.from(j)]},D):null]})})};tN.displayName=tg;var tT="SelectTrigger",tL=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,l=tA(n),u=tC(tT,n),d=u.disabled||o,f=(0,c.s)(t,u.onTriggerChange),p=tx(n),h=r.useRef("touch"),[m,v,y]=nl(e=>{let t=p().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=nc(t,e,n);void 0!==r&&u.onValueChange(r.value)}),g=e=>{d||(u.onOpenChange(!0),y()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,s.jsx)(e7,{asChild:!0,...l,children:(0,s.jsx)(eZ.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":na(u.value)?"":void 0,...i,ref:f,onClick:(0,a.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&g(e)}),onPointerDown:(0,a.m)(i.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,a.m)(i.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&tv.includes(e.key)&&(g(),e.preventDefault())})})})});tL.displayName=tT;var tP="SelectValue",tj=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:a="",...l}=e,u=tC(tP,n),{onValueNodeHasChildrenChange:d}=u,f=void 0!==i,p=(0,c.s)(t,u.onValueNodeChange);return(0,e0.N)(()=>{d(f)},[d,f]),(0,s.jsx)(eZ.sG.span,{...l,ref:p,style:{pointerEvents:"none"},children:na(u.value)?(0,s.jsx)(s.Fragment,{children:a}):i})});tj.displayName=tP;var tO=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,s.jsx)(eZ.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tO.displayName="SelectIcon";var tD=e=>(0,s.jsx)(ts.Z,{asChild:!0,...e});tD.displayName="SelectPortal";var tI="SelectContent",tF=r.forwardRef((e,t)=>{let n=tC(tI,e.__scopeSelect),[i,a]=r.useState();return((0,e0.N)(()=>{a(new DocumentFragment)},[]),n.open)?(0,s.jsx)(t_,{...e,ref:t}):i?o.createPortal((0,s.jsx)(tH,{scope:e.__scopeSelect,children:(0,s.jsx)(tw.Slot,{scope:e.__scopeSelect,children:(0,s.jsx)("div",{children:e.children})})}),i):null});tF.displayName=tI;var[tH,tW]=tk(tI),tB=(0,u.TL)("SelectContent.RemoveScroll"),t_=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:l,onPointerDownOutside:u,side:d,sideOffset:f,align:p,alignOffset:h,arrowPadding:g,collisionBoundary:w,collisionPadding:x,sticky:b,hideWhenDetached:k,avoidCollisions:E,...A}=e,S=tC(tI,n),[C,R]=r.useState(null),[M,N]=r.useState(null),T=(0,c.s)(t,e=>R(e)),[L,P]=r.useState(null),[j,O]=r.useState(null),D=tx(n),[I,F]=r.useState(!1),H=r.useRef(!1);r.useEffect(()=>{if(C)return(0,th.Eq)(C)},[C]),(0,v.Oh)();let W=r.useCallback(e=>{let[t,...n]=D().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&M&&(M.scrollTop=0),n===r&&M&&(M.scrollTop=M.scrollHeight),n?.focus(),document.activeElement!==o))return},[D,M]),B=r.useCallback(()=>W([L,C]),[W,L,C]);r.useEffect(()=>{I&&B()},[I,B]);let{onOpenChange:_,triggerPointerDownPosRef:V}=S;r.useEffect(()=>{if(C){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(V.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(V.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():C.contains(n.target)||_(!1),document.removeEventListener("pointermove",t),V.current=null};return null!==V.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[C,_,V]),r.useEffect(()=>{let e=()=>_(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[_]);let[z,q]=nl(e=>{let t=D().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=nc(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),$=r.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==S.value&&S.value===t||r)&&(P(e),r&&(H.current=!0))},[S.value]),G=r.useCallback(()=>C?.focus(),[C]),U=r.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==S.value&&S.value===t||r)&&O(e)},[S.value]),K="popper"===o?tz:tV,Y=K===tz?{side:d,sideOffset:f,align:p,alignOffset:h,arrowPadding:g,collisionBoundary:w,collisionPadding:x,sticky:b,hideWhenDetached:k,avoidCollisions:E}:{};return(0,s.jsx)(tH,{scope:n,content:C,viewport:M,onViewportChange:N,itemRefCallback:$,selectedItem:L,onItemLeave:G,itemTextRefCallback:U,focusSelectedItem:B,selectedItemText:j,position:o,isPositioned:I,searchRef:z,children:(0,s.jsx)(tm.A,{as:tB,allowPinchZoom:!0,children:(0,s.jsx)(y.n,{asChild:!0,trapped:S.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(i,e=>{S.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,s.jsx)(m.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>S.onOpenChange(!1),children:(0,s.jsx)(K,{role:"listbox",id:S.contentId,"data-state":S.open?"open":"closed",dir:S.dir,onContextMenu:e=>e.preventDefault(),...A,...Y,onPlaced:()=>F(!0),ref:T,style:{display:"flex",flexDirection:"column",outline:"none",...A.style},onKeyDown:(0,a.m)(A.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||q(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=D().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>W(t)),e.preventDefault()}})})})})})})});t_.displayName="SelectContentImpl";var tV=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...a}=e,l=tC(tI,n),u=tW(tI,n),[d,f]=r.useState(null),[p,h]=r.useState(null),m=(0,c.s)(t,e=>h(e)),v=tx(n),y=r.useRef(!1),g=r.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:b,focusSelectedItem:k}=u,E=r.useCallback(()=>{if(l.trigger&&l.valueNode&&d&&p&&w&&x&&b){let e=l.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),r=b.getBoundingClientRect();if("rtl"!==l.dir){let o=r.left-t.left,a=n.left-o,l=e.left-a,c=e.width+l,u=Math.max(c,t.width),s=i(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=c+"px",d.style.left=s+"px"}else{let o=t.right-r.right,a=window.innerWidth-n.right-o,l=window.innerWidth-e.right-a,c=e.width+l,u=Math.max(c,t.width),s=i(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=c+"px",d.style.right=s+"px"}let a=v(),c=window.innerHeight-20,u=w.scrollHeight,s=window.getComputedStyle(p),f=parseInt(s.borderTopWidth,10),h=parseInt(s.paddingTop,10),m=parseInt(s.borderBottomWidth,10),g=f+h+u+parseInt(s.paddingBottom,10)+m,k=Math.min(5*x.offsetHeight,g),E=window.getComputedStyle(w),A=parseInt(E.paddingTop,10),S=parseInt(E.paddingBottom,10),C=e.top+e.height/2-10,R=x.offsetHeight/2,M=f+h+(x.offsetTop+R);if(M<=C){let e=a.length>0&&x===a[a.length-1].ref.current;d.style.bottom="0px";let t=Math.max(c-C,R+(e?S:0)+(p.clientHeight-w.offsetTop-w.offsetHeight)+m);d.style.height=M+t+"px"}else{let e=a.length>0&&x===a[0].ref.current;d.style.top="0px";let t=Math.max(C,f+w.offsetTop+(e?A:0)+R);d.style.height=t+(g-M)+"px",w.scrollTop=M-C+w.offsetTop}d.style.margin="10px 0",d.style.minHeight=k+"px",d.style.maxHeight=c+"px",o?.(),requestAnimationFrame(()=>y.current=!0)}},[v,l.trigger,l.valueNode,d,p,w,x,b,l.dir,o]);(0,e0.N)(()=>E(),[E]);let[A,S]=r.useState();(0,e0.N)(()=>{p&&S(window.getComputedStyle(p).zIndex)},[p]);let C=r.useCallback(e=>{e&&!0===g.current&&(E(),k?.(),g.current=!1)},[E,k]);return(0,s.jsx)(tq,{scope:n,contentWrapper:d,shouldExpandOnScrollRef:y,onScrollButtonChange:C,children:(0,s.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:A},children:(0,s.jsx)(eZ.sG.div,{...a,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});tV.displayName="SelectItemAlignedPosition";var tz=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,a=tA(n);return(0,s.jsx)(tr,{...a,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tz.displayName="SelectPopperPosition";var[tq,t$]=tk(tI,{}),tG="SelectViewport",tU=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,l=tW(tG,n),u=t$(tG,n),d=(0,c.s)(t,l.onViewportChange),f=r.useRef(0);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,s.jsx)(tw.Slot,{scope:n,children:(0,s.jsx)(eZ.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,a.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if(r?.current&&n){let e=Math.abs(f.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,a=Math.min(r,i),l=i-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=l>0?l:0,n.style.justifyContent="flex-end")}}}f.current=t.scrollTop})})})]})});tU.displayName=tG;var tK="SelectGroup",[tY,tX]=tk(tK),tZ=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,g.B)();return(0,s.jsx)(tY,{scope:n,id:o,children:(0,s.jsx)(eZ.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});tZ.displayName=tK;var tJ="SelectLabel",tQ=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tX(tJ,n);return(0,s.jsx)(eZ.sG.div,{id:o.id,...r,ref:t})});tQ.displayName=tJ;var t0="SelectItem",[t1,t2]=tk(t0),t8=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:l,...u}=e,d=tC(t0,n),f=tW(t0,n),p=d.value===o,[h,m]=r.useState(l??""),[v,y]=r.useState(!1),w=(0,c.s)(t,e=>f.itemRefCallback?.(e,o,i)),x=(0,g.B)(),b=r.useRef("touch"),k=()=>{i||(d.onValueChange(o),d.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,s.jsx)(t1,{scope:n,value:o,disabled:i,textId:x,isSelected:p,onItemTextChange:r.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,s.jsx)(tw.ItemSlot,{scope:n,value:o,disabled:i,textValue:h,children:(0,s.jsx)(eZ.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":v?"":void 0,"aria-selected":p&&v,"data-state":p?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...u,ref:w,onFocus:(0,a.m)(u.onFocus,()=>y(!0)),onBlur:(0,a.m)(u.onBlur,()=>y(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==b.current&&k()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===b.current&&k()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{b.current=e.pointerType,i?f.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&f.onItemLeave?.()}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{(f.searchRef?.current===""||" "!==e.key)&&(ty.includes(e.key)&&k()," "===e.key&&e.preventDefault())})})})})});t8.displayName=t0;var t5="SelectItemText",t6=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:a,...l}=e,u=tC(t5,n),d=tW(t5,n),f=t2(t5,n),p=tM(t5,n),[h,m]=r.useState(null),v=(0,c.s)(t,e=>m(e),f.onItemTextChange,e=>d.itemTextRefCallback?.(e,f.value,f.disabled)),y=h?.textContent,g=r.useMemo(()=>(0,s.jsx)("option",{value:f.value,disabled:f.disabled,children:y},f.value),[f.disabled,f.value,y]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=p;return(0,e0.N)(()=>(w(g),()=>x(g)),[w,x,g]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eZ.sG.span,{id:f.textId,...l,ref:v}),f.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(l.children,u.valueNode):null]})});t6.displayName=t5;var t4="SelectItemIndicator",t3=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return t2(t4,n).isSelected?(0,s.jsx)(eZ.sG.span,{"aria-hidden":!0,...r,ref:t}):null});t3.displayName=t4;var t9="SelectScrollUpButton",t7=r.forwardRef((e,t)=>{let n=tW(t9,e.__scopeSelect),o=t$(t9,e.__scopeSelect),[i,a]=r.useState(!1),l=(0,c.s)(t,o.onScrollButtonChange);return(0,e0.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){a(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,s.jsx)(nn,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});t7.displayName=t9;var ne="SelectScrollDownButton",nt=r.forwardRef((e,t)=>{let n=tW(ne,e.__scopeSelect),o=t$(ne,e.__scopeSelect),[i,a]=r.useState(!1),l=(0,c.s)(t,o.onScrollButtonChange);return(0,e0.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,s.jsx)(nn,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});nt.displayName=ne;var nn=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,l=tW("SelectScrollButton",n),c=r.useRef(null),u=tx(n),d=r.useCallback(()=>{null!==c.current&&(window.clearInterval(c.current),c.current=null)},[]);return r.useEffect(()=>()=>d(),[d]),(0,e0.N)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,s.jsx)(eZ.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,a.m)(i.onPointerDown,()=>{null===c.current&&(c.current=window.setInterval(o,50))}),onPointerMove:(0,a.m)(i.onPointerMove,()=>{l.onItemLeave?.(),null===c.current&&(c.current=window.setInterval(o,50))}),onPointerLeave:(0,a.m)(i.onPointerLeave,()=>{d()})})}),nr=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,s.jsx)(eZ.sG.div,{"aria-hidden":!0,...r,ref:t})});nr.displayName="SelectSeparator";var no="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tA(n),i=tC(no,n),a=tW(no,n);return i.open&&"popper"===a.position?(0,s.jsx)(ta,{...o,...r,ref:t}):null}).displayName=no;var ni=r.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{let i=r.useRef(null),a=(0,c.s)(o,i),l=(0,tf.Z)(t);return r.useEffect(()=>{let e=i.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(l!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[l,t]),(0,s.jsx)(eZ.sG.select,{...n,style:{...tp,...n.style},ref:a,defaultValue:t})});function na(e){return""===e||void 0===e}function nl(e){let t=(0,eQ.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),a=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,a]}function nc(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,l=(r=e,o=Math.max(a,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(l=l.filter(e=>e!==n));let c=l.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return c!==n?c:void 0}ni.displayName="SelectBubbleInput";var nu=tN,ns=tL,nd=tj,nf=tO,np=tD,nh=tF,nm=tU,nv=tZ,ny=tQ,ng=t8,nw=t6,nx=t3,nb=t7,nk=nt,nE=nr},32550:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("calendar-plus",[["path",{d:"M16 19h6",key:"xwg31i"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M19 16v6",key:"tddt3s"}],["path",{d:"M21 12.598V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8.5",key:"1glfrc"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 2v4",key:"1cmpym"}]])},32954:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},33763:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},33808:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]])},35342:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},35918:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]])},36146:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(73356),o=n(40813),i=n(81861),a=n(7409),l=n(40969),c=r.forwardRef((e,t)=>{let{container:n,...c}=e,[u,s]=r.useState(!1);(0,a.N)(()=>s(!0),[]);let d=n||u&&globalThis?.document?.body;return d?o.createPortal((0,l.jsx)(i.sG.div,{...c,ref:t}),d):null});c.displayName="Portal"},39827:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(73356),o=n(7409);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},40716:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},42291:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(73356);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},43424:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(73356),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},58557:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},58896:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},59705:(e,t,n)=>{n.d(t,{B:()=>c});var r,o=n(73356),i=n(7409),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function c(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},60952:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},61115:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},64680:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>i});var r=n(73356),o=n(40969);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let c=t=>{let{scope:n,children:i,...c}=t,u=n?.[e]?.[l]||a,s=r.useMemo(()=>c,Object.values(c));return(0,o.jsx)(u.Provider,{value:s,children:i})};return c.displayName=t+"Provider",[c,function(n,o){let c=o?.[e]?.[l]||a,u=r.useContext(c);if(u)return u;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},66282:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},67117:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},68506:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},71060:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(73356);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},71727:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},77994:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("square-check-big",[["path",{d:"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344",key:"2acyp4"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},79123:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},83099:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},83427:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},83788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},87465:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,c=function(e){return e&&(e.host||c(e.parentNode))},u=function(e,t,n,r){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=c(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(u),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};u.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,c=(s.get(e)||0)+1;o.set(e,l),s.set(e,c),d.push(e),1===l&&a&&i.set(e,!0),1===c&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),u(o,i,n,"aria-hidden")):function(){return null}}},91714:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},91798:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},92409:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("calendar-days",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},92739:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},93012:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},93094:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},97009:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98085).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])}};