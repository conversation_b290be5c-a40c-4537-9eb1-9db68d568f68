(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{2790:(e,s,t)=>{"use strict";t.d(s,{E:()=>n});var a=t(9605);t(9585);var r=t(7276),l=t(6994);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:t}),s),...r})}},3704:(e,s,t)=>{"use strict";t.d(s,{k:()=>n});var a=t(9605),r=t(9585),l=t(5111),i=t(6994);let n=r.forwardRef((e,s)=>{let{className:t,value:r,...n}=e;return(0,a.jsx)(l.bL,{ref:s,className:(0,i.cn)("relative h-2 w-full overflow-hidden rounded-full bg-gray-200",t),...n,children:(0,a.jsx)(l.C1,{className:"h-full w-full flex-1 bg-blue-600 transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});n.displayName=l.bL.displayName},5097:(e,s,t)=>{"use strict";t.d(s,{J:()=>c});var a=t(9605),r=t(9585),l=t(8436),i=t(7276),n=t(6994);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.b,{ref:s,className:(0,n.cn)(d(),t),...r})});c.displayName=l.b.displayName},5505:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ep});var a=t(9605),r=t(9585),l=t(5935),i=t(3005),n=t(8063),d=t(2933),c=t(2790),o=t(498),m=t(6751),x=t(9449),u=t(7981),h=t(2006),p=t(6793),g=t(3089),f=t(1158),j=t(5460),y=t(360),b=t(5077),N=t(5215),v=t(9311),w=t(3815),k=t(6845),C=t(2407),S=t(7730),D=t(5706),A=t(7971),P=t(5097),F=t(9577),B=t(6162),R=t(9644),T=t(9269);let U=D.Ik({firstName:D.Yj().min(2,"First name is required"),lastName:D.Yj().min(2,"Last name is required"),email:D.Yj().email("Please enter a valid email address"),phone:D.Yj().min(10,"Please enter a valid phone number"),dateOfBirth:D.Yj().min(1,"Date of birth is required"),nationality:D.Yj().min(2,"Nationality is required"),placeOfBirth:D.Yj().min(2,"Place of birth is required"),gender:D.k5(["male","female","other"],{required_error:"Please select your gender"}),maritalStatus:D.k5(["single","married","divorced","widowed","separated"],{required_error:"Please select your marital status"})});function E(e){let{initialData:s,onNext:t,onPrevious:l}=e,i=(0,v.GV)(w.xu),[m,{isLoading:x}]=(0,b.cs)(),[u,h]=(0,r.useState)(!0),{register:g,handleSubmit:f,formState:{errors:j},setValue:y,watch:N,reset:D}=(0,C.mN)({resolver:(0,S.u)(U),defaultValues:{firstName:(null==i?void 0:i.firstName)||(null==s?void 0:s.firstName)||"",lastName:(null==i?void 0:i.lastName)||(null==s?void 0:s.lastName)||"",email:(null==i?void 0:i.email)||(null==s?void 0:s.email)||"",phone:(null==i?void 0:i.phone)||(null==s?void 0:s.phone)||"",dateOfBirth:(null==i?void 0:i.dateOfBirth)||(null==s?void 0:s.dateOfBirth)||"",nationality:(null==s?void 0:s.nationality)||"India",placeOfBirth:(null==s?void 0:s.placeOfBirth)||"",gender:(null==s?void 0:s.gender)||void 0,maritalStatus:(null==s?void 0:s.maritalStatus)||void 0}});(0,r.useEffect)(()=>{if(i){console.log("\uD83D\uDD04 Auto-filling form with user data and initial data"),console.log("User data:",i),console.log("Initial data:",s);let e={firstName:(null==s?void 0:s.firstName)||(null==i?void 0:i.firstName)||"",lastName:(null==s?void 0:s.lastName)||(null==i?void 0:i.lastName)||"",email:(null==s?void 0:s.email)||(null==i?void 0:i.email)||"",phone:(null==s?void 0:s.phone)||(null==i?void 0:i.phone)||"",dateOfBirth:(null==s?void 0:s.dateOfBirth)||((null==i?void 0:i.dateOfBirth)?new Date(i.dateOfBirth).toISOString().split("T")[0]:""),nationality:(null==s?void 0:s.nationality)||"India",placeOfBirth:(null==s?void 0:s.placeOfBirth)||"",gender:(null==s?void 0:s.gender)||void 0,maritalStatus:(null==s?void 0:s.maritalStatus)||void 0};console.log("\uD83D\uDD04 Form data prepared:",e),D(e),h(!1)}else h(!1)},[i,s,D]);let E=async e=>{try{console.log("Submitting personal info:",e);let s=await fetch("".concat("http://localhost:5000/api","/kyc"),{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to save personal information")}let a=await s.json();console.log("Personal info saved:",a),k.toast.success("Personal information saved successfully!"),setTimeout(()=>{t()},1e3)}catch(e){console.error("Personal info update error:",e),k.toast.error(e.message||"Failed to save personal information")}};return u?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)(p.A,{className:"h-12 w-12 animate-spin mx-auto text-sky-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-sky-600",children:"Loading Personal Information"}),(0,a.jsx)("p",{className:"text-sm text-sky-500",children:"Fetching your saved data..."})]})]})}):(0,a.jsx)("div",{className:"space-y-8",children:(0,a.jsxs)("form",{onSubmit:f(E),className:"space-y-6",children:[(0,a.jsxs)(n.Zp,{className:"border-0 shadow-lg bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg py-4",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,a.jsx)(B.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Basic Information"}),(0,a.jsx)(c.E,{className:"bg-white/20 text-white text-xs",children:"Update if needed"})]})}),(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"firstName",className:"text-sm font-bold text-sky-600",children:"First Name"}),(0,a.jsx)(A.p,{id:"firstName",...g("firstName"),className:"border-sky-200 focus:border-sky-500 bg-sky-50",placeholder:"Enter your first name"}),j.firstName&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:j.firstName.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"lastName",className:"text-sm font-bold text-sky-600",children:"Last Name"}),(0,a.jsx)(A.p,{id:"lastName",...g("lastName"),className:"border-sky-200 focus:border-sky-500 bg-sky-50",placeholder:"Enter your last name"}),j.lastName&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:j.lastName.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"email",className:"text-sm font-bold text-sky-600",children:"Email Address"}),(0,a.jsx)(A.p,{id:"email",type:"email",...g("email"),className:"border-sky-200 focus:border-sky-500 bg-sky-50",placeholder:"Enter your email address"}),j.email&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:j.email.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"phone",className:"text-sm font-bold text-sky-600",children:"Phone Number"}),(0,a.jsx)(A.p,{id:"phone",...g("phone"),className:"border-sky-200 focus:border-sky-500 bg-sky-50",placeholder:"Enter your phone number"}),j.phone&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:j.phone.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"dateOfBirth",className:"text-sm font-bold text-sky-600",children:"Date of Birth"}),(0,a.jsx)(A.p,{id:"dateOfBirth",type:"date",...g("dateOfBirth"),className:"border-sky-200 focus:border-sky-500 bg-sky-50"}),j.dateOfBirth&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:j.dateOfBirth.message})]})]})})]}),(0,a.jsxs)(n.Zp,{className:"border-0 shadow-lg bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg py-4",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Additional Details"})]})}),(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"nationality",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Nationality *"}),(0,a.jsx)(A.p,{...g("nationality"),id:"nationality",placeholder:"e.g., Indian",className:"h-9 text-sm border-2 transition-all duration-200 ".concat(j.nationality?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300")}),j.nationality&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:j.nationality.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"placeOfBirth",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Place of Birth *"}),(0,a.jsx)(A.p,{...g("placeOfBirth"),id:"placeOfBirth",placeholder:"e.g., Mumbai, India",className:"h-9 text-sm border-2 transition-all duration-200 ".concat(j.placeOfBirth?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300")}),j.placeOfBirth&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:j.placeOfBirth.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"gender",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Gender *"}),(0,a.jsxs)(F.l6,{onValueChange:e=>y("gender",e),children:[(0,a.jsx)(F.bq,{className:"h-9 text-sm border-2 transition-all duration-200 ".concat(j.gender?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"),children:(0,a.jsx)(F.yv,{placeholder:"Select gender"})}),(0,a.jsxs)(F.gC,{children:[(0,a.jsx)(F.eb,{value:"male",children:"\uD83D\uDC68 Male"}),(0,a.jsx)(F.eb,{value:"female",children:"\uD83D\uDC69 Female"}),(0,a.jsx)(F.eb,{value:"other",children:"⚧ Other"})]})]}),j.gender&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:j.gender.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"maritalStatus",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Marital Status *"}),(0,a.jsxs)(F.l6,{onValueChange:e=>y("maritalStatus",e),children:[(0,a.jsx)(F.bq,{className:"h-9 text-sm border-2 transition-all duration-200 ".concat(j.maritalStatus?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"),children:(0,a.jsx)(F.yv,{placeholder:"Select marital status"})}),(0,a.jsxs)(F.gC,{children:[(0,a.jsx)(F.eb,{value:"single",children:"\uD83D\uDC8D Single"}),(0,a.jsx)(F.eb,{value:"married",children:"\uD83D\uDC6B Married"}),(0,a.jsx)(F.eb,{value:"divorced",children:"\uD83D\uDCCB Divorced"}),(0,a.jsx)(F.eb,{value:"widowed",children:"\uD83D\uDD4A️ Widowed"})]})]}),j.maritalStatus&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:j.maritalStatus.message})]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-4",children:[(0,a.jsxs)(d.$,{type:"button",variant:"outline",onClick:l,className:"h-10 px-6 border-2 border-gray-300 text-black hover:bg-gray-50 font-medium",children:[(0,a.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"Previous Step"]}),(0,a.jsx)(d.$,{type:"submit",disabled:x,className:"h-10 px-8 bg-sky-500 hover:bg-sky-600 text-white font-bold min-w-[140px]",children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:["Save & Continue",(0,a.jsx)(T.A,{className:"h-4 w-4 ml-2"})]})})]})]})})}var O=t(95),I=t(4314);let q=D.Ik({street:D.Yj().min(5,"Street address must be at least 5 characters"),city:D.Yj().min(2,"City is required"),state:D.Yj().min(2,"State is required"),postalCode:D.Yj().min(5,"Valid postal code is required"),country:D.Yj().min(2,"Country is required"),addressType:D.k5(["permanent","current","mailing"],{required_error:"Please select address type"}),residenceSince:D.Yj().min(1,"Residence since date is required")}),L=["India","United States","United Kingdom","Canada","Australia","Singapore","UAE","Other"],Y=["Andhra Pradesh","Arunachal Pradesh","Assam","Bihar","Chhattisgarh","Goa","Gujarat","Haryana","Himachal Pradesh","Jharkhand","Karnataka","Kerala","Madhya Pradesh","Maharashtra","Manipur","Meghalaya","Mizoram","Nagaland","Odisha","Punjab","Rajasthan","Sikkim","Tamil Nadu","Telangana","Tripura","Uttar Pradesh","Uttarakhand","West Bengal","Delhi","Puducherry","Chandigarh","Dadra and Nagar Haveli","Daman and Diu","Lakshadweep","Ladakh","Jammu and Kashmir"];function M(e){let{initialData:s,onNext:t,onPrevious:l}=e,[i,c]=(0,r.useState)(!1),[o,{isLoading:x}]=(0,b.Yx)(),[u,h]=(0,r.useState)(!0),{register:g,handleSubmit:f,formState:{errors:j},setValue:y,watch:N,reset:v}=(0,C.mN)({resolver:(0,S.u)(q),defaultValues:{street:(null==s?void 0:s.street)||"",city:(null==s?void 0:s.city)||"",state:(null==s?void 0:s.state)||"",postalCode:(null==s?void 0:s.postalCode)||"",country:(null==s?void 0:s.country)||"India",addressType:(null==s?void 0:s.addressType)||"current",residenceSince:(null==s?void 0:s.residenceSince)||""}}),w=N("country");(0,r.useEffect)(()=>{(async()=>{try{h(!0),console.log("\uD83D\uDD04 Fetching existing address info...");let e=await fetch("http://localhost:5000/api/kyc/address-info",{method:"GET",credentials:"include"});if(e.ok){let s=await e.json();s.success&&s.data&&Object.keys(s.data).length>0&&(console.log("✅ Found existing address info:",s.data),v(s.data),console.log("\uD83D\uDD04 Address form auto-filled with existing data"))}}catch(e){console.error("Failed to fetch address info:",e)}finally{h(!1)}})()},[v]);let D=async e=>{try{console.log("Submitting address info:",e);let s=await fetch("http://localhost:5000/api/kyc/address",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to save address information")}let a=await s.json();console.log("Address info saved:",a),k.toast.success("Address information saved successfully!"),setTimeout(()=>{t()},1e3)}catch(e){console.error("Address update error:",e),k.toast.error(e.message||"Failed to save address information")}};return u?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)(p.A,{className:"h-12 w-12 animate-spin mx-auto text-sky-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-sky-600",children:"Loading Address Information"}),(0,a.jsx)("p",{className:"text-sm text-sky-500",children:"Fetching your saved data..."})]})]})}):(0,a.jsx)("div",{className:"space-y-8",children:(0,a.jsxs)("form",{onSubmit:f(D),className:"space-y-6",children:[(0,a.jsxs)(n.Zp,{className:"border-0 shadow-lg bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg py-4",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Address Information"})]})}),(0,a.jsxs)(n.Wu,{className:"p-6 space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"addressType",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Address Type *"}),(0,a.jsxs)(F.l6,{onValueChange:e=>y("addressType",e),children:[(0,a.jsx)(F.bq,{className:"h-9 text-sm border-2 transition-all duration-200 ".concat(j.addressType?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"),children:(0,a.jsx)(F.yv,{placeholder:"Select address type"})}),(0,a.jsxs)(F.gC,{children:[(0,a.jsx)(F.eb,{value:"current",children:"\uD83C\uDFE0 Current Address"}),(0,a.jsx)(F.eb,{value:"permanent",children:"\uD83C\uDFE1 Permanent Address"}),(0,a.jsx)(F.eb,{value:"mailing",children:"\uD83D\uDCEE Mailing Address"})]})]}),j.addressType&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:j.addressType.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"street",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Street Address *"}),(0,a.jsx)(A.p,{...g("street"),id:"street",placeholder:"House/Flat No., Building Name, Street",className:"h-9 text-sm border-2 transition-all duration-200 ".concat(j.street?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300")}),j.street&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:j.street.message})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"city",className:"text-xs font-bold text-black uppercase tracking-wide",children:"City *"}),(0,a.jsx)(A.p,{...g("city"),id:"city",placeholder:"e.g., Mumbai",className:"h-9 text-sm border-2 transition-all duration-200 ".concat(j.city?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300")}),j.city&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:j.city.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"postalCode",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Postal Code *"}),(0,a.jsx)(A.p,{...g("postalCode"),id:"postalCode",placeholder:"e.g., 400001",className:"h-9 text-sm border-2 transition-all duration-200 ".concat(j.postalCode?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300")}),j.postalCode&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:j.postalCode.message})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"state",className:"text-xs font-bold text-black uppercase tracking-wide",children:"State/Province *"}),"India"===w?(0,a.jsxs)(F.l6,{onValueChange:e=>y("state",e),children:[(0,a.jsx)(F.bq,{className:"h-9 text-sm border-2 transition-all duration-200 ".concat(j.state?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"),children:(0,a.jsx)(F.yv,{placeholder:"Select state"})}),(0,a.jsx)(F.gC,{children:Y.map(e=>(0,a.jsx)(F.eb,{value:e,children:e},e))})]}):(0,a.jsx)(A.p,{...g("state"),id:"state",placeholder:"e.g., California",className:"h-9 text-sm border-2 transition-all duration-200 ".concat(j.state?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300")}),j.state&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:j.state.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"country",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Country *"}),(0,a.jsxs)(F.l6,{onValueChange:e=>y("country",e),children:[(0,a.jsx)(F.bq,{className:"h-9 text-sm border-2 transition-all duration-200 ".concat(j.country?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300"),children:(0,a.jsx)(F.yv,{placeholder:"Select country"})}),(0,a.jsx)(F.gC,{children:L.map(e=>(0,a.jsxs)(F.eb,{value:e,children:["\uD83C\uDF0D ",e]},e))})]}),j.country&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:j.country.message})]})]})]})]}),(0,a.jsxs)(n.Zp,{className:"border-0 shadow-lg bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg py-4",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,a.jsx)(O.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Residence Information"})]})}),(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{htmlFor:"residenceSince",className:"text-xs font-bold text-black uppercase tracking-wide",children:"Living at this address since *"}),(0,a.jsx)(A.p,{...g("residenceSince"),id:"residenceSince",type:"date",className:"h-9 text-sm border-2 transition-all duration-200 ".concat(j.residenceSince?"border-red-500 focus:border-red-600":"border-gray-200 focus:border-sky-500 hover:border-sky-300")}),j.residenceSince&&(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:j.residenceSince.message}),(0,a.jsx)("div",{className:"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-xs text-black font-medium",children:"\uD83D\uDCA1 This helps us verify your address stability for compliance purposes."})})]})})]}),(0,a.jsx)(n.Zp,{className:"bg-yellow-50 border-2 border-yellow-300 shadow-lg",children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-full",children:(0,a.jsx)(I.A,{className:"h-6 w-6 text-yellow-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-bold text-black text-lg mb-2",children:"\uD83D\uDCCB Address Verification Required"}),(0,a.jsx)("p",{className:"text-sm text-black mb-3",children:"You'll need to upload address proof documents in the next step. Accepted documents include:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-black",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,a.jsx)("span",{children:"Utility bills (not older than 3 months)"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-black",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,a.jsx)("span",{children:"Bank statements (not older than 3 months)"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-black",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,a.jsx)("span",{children:"Rental agreement or property documents"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-black",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,a.jsx)("span",{children:"Government-issued address proof"})]})]})]})]})})}),(0,a.jsxs)("div",{className:"flex justify-between pt-4",children:[(0,a.jsxs)(d.$,{type:"button",variant:"outline",onClick:l,className:"h-10 px-6 border-2 border-gray-300 text-black hover:bg-gray-50 font-medium",children:[(0,a.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"Previous Step"]}),(0,a.jsx)(d.$,{type:"submit",disabled:x,className:"h-10 px-8 bg-sky-500 hover:bg-sky-600 text-white font-bold min-w-[140px]",children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:["Save & Continue",(0,a.jsx)(T.A,{className:"h-4 w-4 ml-2"})]})})]})]})})}var _=t(3704),W=t(9249),$=t(2311),Z=t(1470),z=t(1716);let J=async(e,s,t)=>new Promise((a,r)=>{let l=new XMLHttpRequest;l.upload.addEventListener("progress",e=>{e.lengthComputable&&t&&t({loaded:e.loaded,total:e.total,percentage:Math.round(e.loaded/e.total*100)})}),l.addEventListener("load",()=>{200===l.status?a():r(Error("Upload failed with status: ".concat(l.status)))}),l.addEventListener("error",()=>{r(Error("Upload failed due to network error"))}),l.addEventListener("abort",()=>{r(Error("Upload was aborted"))}),l.open("PUT",s),l.setRequestHeader("Content-Type",e.type),l.send(e)}),V=e=>e.size>0xa00000?{isValid:!1,error:"File size must be less than 10MB"}:["image/jpeg","image/jpg","image/png","image/webp","image/gif","image/bmp","image/tiff","image/svg+xml","image/heic","image/heif","application/pdf"].includes(e.type)?{isValid:!0}:{isValid:!1,error:"Supported formats: JPEG, PNG, WebP, GIF, BMP, TIFF, SVG, HEIC, HEIF, PDF"},G=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},K=e=>e.startsWith("image/")?"\uD83D\uDDBC️":"application/pdf"===e?"\uD83D\uDCC4":"\uD83D\uDCCE",H=e=>{let s=Date.now(),t=Math.random().toString(36).substring(2,8),a=e.split(".").pop(),r=e.replace(/\.[^/.]+$/,"");return"".concat(r,"_").concat(s,"_").concat(t,".").concat(a)},X=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1920,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.8;return new Promise(a=>{if(!e.type.startsWith("image/")||e.size<1048576)return void a(e);let r=document.createElement("canvas"),l=r.getContext("2d"),i=new Image;i.onload=()=>{let n=Math.min(s/i.width,s/i.height);r.width=i.width*n,r.height=i.height*n,null==l||l.drawImage(i,0,0,r.width,r.height),r.toBlob(s=>{s?a(new File([s],e.name,{type:e.type,lastModified:Date.now()})):a(e)},e.type,t)},i.onerror=()=>a(e),i.src=URL.createObjectURL(e)})},Q=[{value:"aadhar",label:"Aadhaar Card",description:"Government issued identity card",category:"identity",required:!0},{value:"pan",label:"PAN Card",description:"Permanent Account Number card",category:"identity",required:!0},{value:"passport",label:"Passport",description:"International travel document",category:"identity",required:!1},{value:"drivers_license",label:"Driving License",description:"Government issued driving permit",category:"identity",required:!1},{value:"voter_id",label:"Voter ID Card",description:"Election commission identity card",category:"identity",required:!1},{value:"national_id",label:"National ID",description:"Government issued national identity card",category:"identity",required:!1},{value:"utility_bill",label:"Utility Bill",description:"Electricity, water, or gas bill",category:"address",required:!0},{value:"bank_statement",label:"Bank Statement",description:"Recent bank account statement",category:"address",required:!1},{value:"rental_agreement",label:"Rental Agreement",description:"Property rental contract",category:"address",required:!1}],ee={identity:{title:"Identity Documents",description:"At least 2 identity documents required",required:2,options:["aadhar","pan","passport","drivers_license","voter_id","national_id"]},address:{title:"Address Proof",description:"At least 1 address proof required",required:1,options:["utility_bill","bank_statement","rental_agreement"]}};function es(e){let{onNext:s,onPrevious:t}=e,[l,i]=(0,r.useState)(null),[u,f]=(0,r.useState)(null),[j,N]=(0,r.useState)(""),[v,w]=(0,r.useState)(""),[C,S]=(0,r.useState)(0),[D,B]=(0,r.useState)(!1),R=(0,r.useRef)(null),[T]=(0,b.Gy)(),[U]=(0,b.i6)(),{data:E,refetch:O}=(0,b.fW)(),I=(null==E?void 0:E.data)||[],q=(()=>{let e=I.filter(e=>ee.identity.options.includes(e.subType||"")&&"selfie"!==e.subType),s=I.filter(e=>ee.address.options.includes(e.subType||"")),t=Math.min(e.length,ee.identity.required),a=Math.min(s.length,ee.address.required),r=ee.identity.required+ee.address.required,l=t+a;return{identityDocs:e,addressDocs:s,identityProgress:t,addressProgress:a,totalRequired:r,totalUploaded:l,progressPercentage:Math.round(l/r*100),isComplete:l>=r}})(),{isComplete:L,progressPercentage:Y}=q,M=async()=>{if(!l||!j)return void k.toast.error("Please select a document and document type");try{B(!0),S(5);let e=await X(l);S(10);let t=H(e.name),a=await U({fileName:t,fileType:e.type,fileSize:e.size,uploadType:"user-document"}).unwrap();if(!a.success||!a.data)throw Error("Failed to get upload URL");let{presignedUrl:r,uploadUrl:i}=a.data;S(20),await J(e,r,e=>{S(20+.6*e.percentage)}),S(85);let n=Q.find(e=>e.value===j),d=(null==n?void 0:n.category)||"identity";await T({type:d,subType:j,documentNumber:v,fileUrl:i,fileName:e.name,fileSize:e.size,mimeType:e.type}).unwrap(),S(100),k.toast.success("Document uploaded successfully!"),setTimeout(()=>{S(0),B(!1),O(),setTimeout(()=>{s()},1e3)},1e3)}catch(s){var e;console.error("Document upload error:",s),k.toast.error((null==s||null==(e=s.data)?void 0:e.message)||"Failed to upload document"),B(!1),S(0)}};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)(n.Zp,{className:"border-0 shadow-xl bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-gradient-to-r from-sky-500 to-blue-600 text-white rounded-t-xl",children:(0,a.jsxs)(n.ZB,{className:"flex items-center justify-between text-xl",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(y.A,{className:"h-6 w-6"}),(0,a.jsx)("span",{children:"Document Requirements"})]}),(0,a.jsxs)(c.E,{className:"bg-white/20 text-white border-white/30",children:[q.totalUploaded,"/",q.totalRequired," Required"]})]})}),(0,a.jsxs)(n.Wu,{className:"p-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Overall Progress"}),(0,a.jsxs)("span",{className:"font-bold text-sky-600",children:[Math.round(Y),"%"]})]}),(0,a.jsx)(_.k,{value:Y,className:"h-3"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-blue-600"}),ee.identity.title]}),(0,a.jsxs)(c.E,{variant:q.identityProgress>=ee.identity.required?"default":"secondary",children:[q.identityProgress,"/",ee.identity.required," Required"]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:ee.identity.description}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:ee.identity.options.map(e=>{let s=Q.find(s=>s.value===e),t=q.identityDocs.some(s=>s.subType===e);return(0,a.jsx)("div",{className:"p-3 rounded-lg border-2 transition-all ".concat(t?"bg-green-50 border-green-200":"bg-gray-50 border-gray-200"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:null==s?void 0:s.label}),t?(0,a.jsx)(h.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(g.A,{className:"h-4 w-4 text-gray-400"})]})},e)})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-green-600"}),ee.address.title]}),(0,a.jsxs)(c.E,{variant:q.addressProgress>=ee.address.required?"default":"secondary",children:[q.addressProgress,"/",ee.address.required," Required"]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:ee.address.description}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:ee.address.options.map(e=>{let s=Q.find(s=>s.value===e),t=q.addressDocs.some(s=>s.subType===e);return(0,a.jsx)("div",{className:"p-3 rounded-lg border-2 transition-all ".concat(t?"bg-green-50 border-green-200":"bg-gray-50 border-gray-200"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:null==s?void 0:s.label}),t?(0,a.jsx)(h.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(g.A,{className:"h-4 w-4 text-gray-400"})]})},e)})})]})]}),I.length>0&&(0,a.jsxs)("div",{className:"p-6 rounded-xl border-2 bg-green-50 border-green-200 shadow-lg mt-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-white shadow-sm",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-bold text-lg text-green-700",children:"Documents Uploaded"}),(0,a.jsxs)(c.E,{className:"ml-2 bg-green-500 text-white text-xs",children:["✓ ",I.length," Documents"]})]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Great! You have uploaded ",I.length," document(s). You can upload additional documents if needed."]}),(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Uploaded Documents:"}),I.filter(e=>"selfie"!==e.subType).map((e,s)=>{var t;return(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-white rounded-lg border",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:(null==(t=e.subType)?void 0:t.replace("_"," ").toUpperCase())||"Document"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.documentNumber?"".concat(e.documentNumber," • "):"","Uploaded: ",new Date(e.uploadedAt).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.E,{className:"text-xs ".concat("verified"===e.status?"bg-green-100 text-green-700":"rejected"===e.status?"bg-red-100 text-red-700":"bg-yellow-100 text-yellow-700"),children:e.status}),(0,a.jsx)(h.A,{className:"h-5 w-5 text-green-600"})]})]},s)})]})]})]})]}),(0,a.jsxs)(n.Zp,{className:"border-0 shadow-xl bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-xl",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-3 text-xl",children:[(0,a.jsx)(x.A,{className:"h-6 w-6"}),(0,a.jsx)("span",{children:L?"Upload Additional Document":"Upload Required Documents"})]})}),(0,a.jsxs)(n.Wu,{className:"p-8 space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(P.J,{className:"text-sm font-bold text-gray-700",children:"Document Type *"}),(0,a.jsxs)(F.l6,{value:j,onValueChange:N,children:[(0,a.jsx)(F.bq,{className:"h-12 border-2 border-gray-200 focus:border-sky-500",children:(0,a.jsx)(F.yv,{placeholder:"Select document type"})}),(0,a.jsx)(F.gC,{children:Q.map(e=>(0,a.jsx)(F.eb,{value:e.value,children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:e.label}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:e.description})]})},e.value))})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(P.J,{className:"text-sm font-bold text-gray-700",children:"Document Number (Optional)"}),(0,a.jsx)(A.p,{value:v,onChange:e=>w(e.target.value),placeholder:"e.g., A1234567",className:"h-12 border-2 border-gray-200 focus:border-sky-500"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(P.J,{className:"text-sm font-bold text-gray-700",children:"Upload Document *"}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-blue-700",children:[(0,a.jsx)(W.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Supported: JPEG, PNG, WebP, GIF, BMP, TIFF, SVG, HEIC, HEIF, PDF • Max: 10MB"})]})}),(0,a.jsxs)("div",{className:"border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ".concat(l?"border-green-400 bg-green-50":"border-gray-300 hover:border-sky-400 hover:bg-sky-50/50"),children:[(0,a.jsx)("input",{ref:R,type:"file",accept:"image/*,.pdf",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(t){let e=V(t);if(!e.isValid)return void k.toast.error(e.error);i(t),f(URL.createObjectURL(t))}},className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer",disabled:D}),l?(0,a.jsxs)("div",{className:"space-y-4",children:[u&&(0,a.jsx)("div",{className:"mx-auto w-64 h-48 rounded-lg overflow-hidden shadow-lg border-2 border-green-200",children:"application/pdf"===l.type?(0,a.jsx)("div",{className:"w-full h-full bg-red-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(x.A,{className:"h-12 w-12 text-red-600 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm font-medium text-red-700",children:"PDF Document"}),(0,a.jsx)("p",{className:"text-xs text-red-600",children:l.name})]})}):(0,a.jsx)("img",{src:u,alt:"Document preview",className:"w-full h-full object-cover"})}),(0,a.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-green-700 mb-2",children:l.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[K(l.type)," ",G(l.size)]}),(0,a.jsxs)("div",{className:"flex justify-center space-x-3 mt-3",children:[(0,a.jsxs)(d.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{i(null),f(null),R.current&&(R.current.value="")},disabled:D,children:[(0,a.jsx)($.A,{className:"h-4 w-4 mr-1"}),"Remove"]}),u&&"application/pdf"!==l.type&&(0,a.jsxs)(d.$,{type:"button",variant:"outline",size:"sm",onClick:()=>window.open(u,"_blank"),children:[(0,a.jsx)(Z.A,{className:"h-4 w-4 mr-1"}),"View Full"]})]})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"mx-auto w-16 h-16 bg-sky-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(z.A,{className:"h-8 w-8 text-sky-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-gray-700 mb-2",children:"Drag and drop your document here"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"or click to browse files"}),(0,a.jsxs)(d.$,{type:"button",variant:"outline",className:"border-sky-500 text-sky-600 hover:bg-sky-50",onClick:()=>{var e;return null==(e=R.current)?void 0:e.click()},disabled:D,children:[(0,a.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Choose File"]})]})]})]})]}),C>0&&C<100&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Uploading..."}),(0,a.jsxs)("span",{className:"font-bold text-sky-600",children:[C,"%"]})]}),(0,a.jsx)(_.k,{value:C,className:"h-3"})]}),(0,a.jsx)(d.$,{onClick:M,disabled:!l||!j||D,className:"w-full h-14 text-lg font-bold bg-sky-500 hover:bg-sky-600 text-white",children:D?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-3 h-5 w-5 animate-spin"}),"Uploading to Secure Storage..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(z.A,{className:"mr-3 h-5 w-5"}),"Upload Document Securely"]})})]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-6",children:[(0,a.jsx)(d.$,{onClick:t,variant:"outline",className:"px-8 py-3 text-lg",children:"Previous"}),L&&(0,a.jsx)(d.$,{onClick:s,className:"px-8 py-3 text-lg bg-green-500 hover:bg-green-600 text-white",children:"Continue"})]})]})}var et=t(9580),ea=t(4926),er=t(5457);function el(e){let{onCapture:s,onClose:t,title:l="Take Photo",instructions:i="Position yourself in the center and ensure good lighting"}=e,c=(0,r.useRef)(null),o=(0,r.useRef)(null),[m,x]=(0,r.useState)(null),[u,h]=(0,r.useState)(!1),[p,g]=(0,r.useState)(null),[f,j]=(0,r.useState)(!1),y=(0,r.useCallback)(async()=>{try{j(!0);let e=await navigator.mediaDevices.getUserMedia({video:{width:{ideal:1280},height:{ideal:720},facingMode:"user"},audio:!1});c.current&&(c.current.srcObject=e,c.current.play(),x(e),h(!0))}catch(e){console.error("Error accessing camera:",e),k.toast.error("Unable to access camera. Please check permissions.")}finally{j(!1)}},[]),b=(0,r.useCallback)(()=>{m&&(m.getTracks().forEach(e=>e.stop()),x(null),h(!1))},[m]),N=(0,r.useCallback)(()=>{if(!c.current||!o.current)return;let e=c.current,s=o.current,t=s.getContext("2d");t&&(s.width=e.videoWidth,s.height=e.videoHeight,t.drawImage(e,0,0,s.width,s.height),s.toBlob(e=>{e&&(g(s.toDataURL("image/jpeg",.8)),b())},"image/jpeg",.8))},[b]),v=(0,r.useCallback)(()=>{g(null),y()},[y]),w=(0,r.useCallback)(()=>{p&&o.current&&o.current.toBlob(e=>{e&&(s(e,p),t())},"image/jpeg",.8)},[p,s,t]),C=(0,r.useCallback)(()=>{b(),t()},[b,t]),S=(0,r.useCallback)(e=>{var a;let r=null==(a=e.target.files)?void 0:a[0];if(!r)return;if(!r.type.startsWith("image/"))return void k.toast.error("Please select an image file");let l=new FileReader;l.onload=e=>{var a;let r=null==(a=e.target)?void 0:a.result;g(r),b(),fetch(r).then(e=>e.blob()).then(e=>{s(e,r),t()})},l.readAsDataURL(r)},[s,t,b]);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,a.jsxs)(n.Zp,{className:"w-full max-w-2xl mx-4",children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between",children:[(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:l})]}),(0,a.jsx)(d.$,{variant:"ghost",size:"sm",onClick:C,children:(0,a.jsx)($.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:i})}),(0,a.jsxs)("div",{className:"relative bg-gray-100 rounded-lg overflow-hidden aspect-video",children:[p?(0,a.jsx)("img",{src:p,alt:"Captured",className:"w-full h-full object-cover"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("video",{ref:c,className:"w-full h-full object-cover",autoPlay:!0,playsInline:!0,muted:!0}),!u&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(ea.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Camera not started"})]})})]}),u&&!p&&(0,a.jsxs)("div",{className:"absolute inset-0 pointer-events-none",children:[(0,a.jsx)("div",{className:"absolute inset-4 border-2 border-white border-dashed rounded-lg opacity-50"}),(0,a.jsx)("div",{className:"absolute top-4 left-4 right-4 text-center",children:(0,a.jsx)("p",{className:"text-white text-sm bg-black bg-opacity-50 rounded px-2 py-1",children:"Position yourself within the frame"})})]})]}),(0,a.jsxs)("div",{className:"flex justify-center space-x-4",children:[!u&&!p&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(d.$,{onClick:y,disabled:f,children:[(0,a.jsx)(ea.A,{className:"h-4 w-4 mr-2"}),f?"Starting Camera...":"Start Camera"]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:S,className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer"}),(0,a.jsxs)(d.$,{variant:"outline",children:[(0,a.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Upload Image"]})]})]}),u&&!p&&(0,a.jsxs)(d.$,{onClick:N,size:"lg",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5 mr-2"}),"Capture Photo"]}),p&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(d.$,{variant:"outline",onClick:v,children:[(0,a.jsx)(et.A,{className:"h-4 w-4 mr-2"}),"Retake"]}),(0,a.jsxs)(d.$,{onClick:w,children:[(0,a.jsx)(er.A,{className:"h-4 w-4 mr-2"}),"Use Photo"]})]})]}),(0,a.jsx)("canvas",{ref:o,className:"hidden"})]})]})})}function ei(e){let{onNext:s,onPrevious:t}=e,[l,i]=(0,r.useState)(null),[o,m]=(0,r.useState)(null),[x,p]=(0,r.useState)(!1),[g,f]=(0,r.useState)(!1),[j,y]=(0,r.useState)(!1),[N,v]=(0,r.useState)(!1),[w,C]=(0,r.useState)(0),S=(0,r.useRef)(null),D=(0,r.useRef)(null),A=(0,r.useRef)(null),[P]=(0,b.Gy)(),[F]=(0,b.i6)();r.useEffect(()=>{let e=S.current;if(!e)return;let s=e.getContext("2d");s&&(s.strokeStyle="#000000",s.lineWidth=2,s.lineCap="round",s.lineJoin="round",s.fillStyle="#ffffff",s.fillRect(0,0,e.width,e.height))},[]);let B=e=>{let s=S.current;if(!s)return{x:0,y:0};let t=s.getBoundingClientRect(),a=s.width/t.width,r=s.height/t.height;if(!("touches"in e))return{x:(e.clientX-t.left)*a,y:(e.clientY-t.top)*r};{let s=e.touches[0]||e.changedTouches[0];return{x:(s.clientX-t.left)*a,y:(s.clientY-t.top)*r}}},U=(0,r.useCallback)(e=>{e.preventDefault(),p(!0);let s=S.current;if(!s)return;let t=s.getContext("2d");if(!t)return;let a=B(e);t.beginPath(),t.moveTo(a.x,a.y)},[]),E=(0,r.useCallback)(e=>{if(e.preventDefault(),!x)return;let s=S.current;if(!s)return;let t=s.getContext("2d");if(!t)return;let a=B(e);t.lineTo(a.x,a.y),t.stroke()},[x]),O=(0,r.useCallback)(e=>{e.preventDefault(),p(!1)},[]),I=async()=>{try{if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)return void k.toast.error("Camera not supported on this device");let e=await navigator.mediaDevices.getUserMedia({video:{facingMode:"user",width:{ideal:640},height:{ideal:480}}});A.current=e,D.current&&(D.current.srcObject=e,D.current.onloadedmetadata=()=>{var e;null==(e=D.current)||e.play()},f(!0))}catch(e){console.error("Error accessing camera:",e),"NotAllowedError"===e.name?k.toast.error("Camera permission denied. Please allow camera access and try again."):"NotFoundError"===e.name?k.toast.error("No camera found on this device."):k.toast.error("Unable to access camera. Please check permissions.")}},q=()=>{A.current&&(A.current.getTracks().forEach(e=>e.stop()),A.current=null),f(!1)},L=(e,s)=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(!a)return;if(a.size>5242880)return void k.toast.error("File size must be less than 5MB");if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(a.type))return void k.toast.error("Please upload a valid image file (JPEG, PNG, or WebP)");let r=new FileReader;r.onload=e=>{var t;let a=null==(t=e.target)?void 0:t.result;if(!a)return void k.toast.error("Failed to read file. Please try again.");"signature"===s?i(a):m(a),k.toast.success("".concat("signature"===s?"Signature":"Selfie"," uploaded successfully!"))},r.onerror=()=>{k.toast.error("Failed to read file. Please try again.")},r.readAsDataURL(a),e.target.value=""},Y=async(e,s,t)=>{let a=await fetch(e),r=await a.blob(),l=new File([r],s,{type:r.type});null==t||t(10);let i=await F({fileName:H(s),fileType:l.type,fileSize:l.size,uploadType:"user-document"}).unwrap();if(!i.success||!i.data)throw Error("Failed to get upload URL");let{presignedUrl:n,uploadUrl:d}=i.data;return null==t||t(30),await J(l,n,e=>{null==t||t(30+.6*e.percentage)}),null==t||t(100),d},M=async()=>{if(console.log("\uD83D\uDE80 Digital signature submit clicked",{hasSignature:!!l,hasSelfie:!!o}),!l||!o){k.toast.error("Please provide both digital signature and selfie"),console.log("❌ Missing signature or selfie",{signature:!!l,selfie:!!o});return}console.log("✅ Starting upload process..."),v(!0),C(0);try{console.log("\uD83D\uDCDD Uploading signature to S3..."),C(5);let e=await Y(l,"signature_".concat(Date.now(),".png"),e=>C(5+.4*e));console.log("✅ Signature uploaded:",e),console.log("\uD83D\uDCF8 Uploading selfie to S3..."),C(50);let t=await Y(o,"selfie_".concat(Date.now(),".jpg"),e=>C(50+.4*e));console.log("✅ Selfie uploaded:",t),console.log("\uD83D\uDCBE Saving documents to database..."),C(95),console.log("\uD83D\uDCBE Saving signature to database..."),await P({type:"other",subType:"digital_signature",fileUrl:e,fileName:"signature_".concat(Date.now(),".png"),fileSize:0,mimeType:"image/png"}).unwrap(),console.log("✅ Signature saved to database"),console.log("\uD83D\uDCBE Saving selfie to database..."),await P({type:"other",subType:"selfie",fileUrl:t,fileName:"selfie_".concat(Date.now(),".jpg"),fileSize:0,mimeType:"image/jpeg"}).unwrap(),console.log("✅ Selfie saved to database"),C(100),k.toast.success("Digital signature and selfie submitted successfully!"),console.log("✅ Digital signature step completed, advancing to next step..."),setTimeout(()=>{console.log("\uD83D\uDD04 Calling onNext() to advance to next step"),s()},1500)}catch(s){var e;console.error("Upload error:",s),k.toast.error((null==s||null==(e=s.data)?void 0:e.message)||"Failed to submit. Please try again.")}finally{v(!1),C(0)}};return(0,a.jsxs)(a.Fragment,{children:[j&&(0,a.jsx)(el,{onCapture:(e,s)=>{m(s),y(!1),k.toast.success("Selfie captured successfully!")},onClose:()=>y(!1),title:"Take Selfie",instructions:"Position your face in the center and ensure good lighting for verification"}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)(n.Zp,{className:"border-0 shadow-lg bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg py-4",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,a.jsx)(u.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Digital Signature"}),(0,a.jsx)(c.E,{className:"bg-white/20 text-white",children:"Required"})]})}),(0,a.jsx)(n.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-medium text-sky-600 mb-2",children:"\uD83D\uDCDD Create Your Digital Signature"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Draw your signature in the box below using your mouse or finger"})]}),(0,a.jsxs)("div",{className:"bg-sky-50 border-2 border-sky-200 rounded-xl p-6",children:[(0,a.jsx)("div",{className:"text-center mb-4",children:(0,a.jsx)("p",{className:"text-xs font-medium text-sky-700 uppercase tracking-wide",children:"Signature Area"})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("canvas",{ref:S,width:600,height:200,className:"border-2 border-sky-300 rounded-lg cursor-crosshair w-full bg-white shadow-sm",style:{maxWidth:"100%",height:"auto",touchAction:"none"},onMouseDown:U,onMouseMove:E,onMouseUp:O,onMouseLeave:O,onTouchStart:U,onTouchMove:E,onTouchEnd:O}),!l&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center pointer-events-none",children:(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Sign here"})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 mt-6",children:[(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>{let e=S.current;if(!e)return;let s=e.getContext("2d");s&&(s.fillStyle="#ffffff",s.fillRect(0,0,e.width,e.height),s.strokeStyle="#000000",s.lineWidth=2,s.lineCap="round",s.lineJoin="round",i(null),k.toast.success("Signature cleared!"))},className:"flex-1 h-10 border-2 border-sky-500 text-sky-600 hover:bg-sky-50 font-medium",children:[(0,a.jsx)(et.A,{className:"h-4 w-4 mr-2"}),"Clear Signature"]}),(0,a.jsxs)(d.$,{onClick:()=>{let e=S.current;if(!e)return;let s=e.getContext("2d");if(!s)return;let t=s.getImageData(0,0,e.width,e.height).data,a=!0;for(let e=0;e<t.length;e+=4)if(255!==t[e]||255!==t[e+1]||255!==t[e+2]){a=!1;break}if(a)return void k.toast.error("Please draw your signature first!");i(e.toDataURL("image/png",.8)),k.toast.success("Signature saved successfully!")},className:"flex-1 h-10 bg-sky-500 hover:bg-sky-600 text-white font-bold",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Save Signature"]})]})]}),(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-200"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-4 bg-white text-gray-500",children:"Or upload an image"})})]}),(0,a.jsx)("input",{type:"file",accept:"image/jpeg,image/jpg,image/png,image/webp",onChange:e=>L(e,"signature"),className:"hidden",id:"signature-upload"}),(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>{var e;return null==(e=document.getElementById("signature-upload"))?void 0:e.click()},className:"mt-4 h-10 border-2 border-sky-500 text-sky-600 hover:bg-sky-50 font-medium",children:[(0,a.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Upload Signature Image"]})]}),l&&(0,a.jsxs)("div",{className:"p-6 bg-sky-50 border-2 border-sky-200 rounded-xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-sky-500 rounded-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-bold text-sky-800",children:"Signature Saved Successfully"}),(0,a.jsx)("p",{className:"text-sm text-sky-600",children:"Your digital signature is ready"})]})]}),(0,a.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>i(null),className:"text-sky-600 hover:bg-sky-100",children:(0,a.jsx)($.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-white rounded-lg border border-sky-200",children:(0,a.jsx)("img",{src:l,alt:"Signature",className:"max-h-16 mx-auto"})})]})]})})]}),(0,a.jsxs)(n.Zp,{className:"border-0 shadow-lg bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg py-4",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Identity Verification Selfie"}),(0,a.jsx)(c.E,{className:"bg-white/20 text-white",children:"Required"})]})}),(0,a.jsx)(n.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-medium text-sky-600 mb-2",children:"\uD83D\uDCF8 Take Your Identity Selfie"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Take a clear photo of yourself for identity verification"})]}),!o&&(0,a.jsx)("div",{className:"bg-sky-50 border-2 border-sky-200 rounded-xl p-6",children:(0,a.jsxs)("div",{className:"text-center space-y-6",children:[g?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"relative mx-auto max-w-sm",children:[(0,a.jsxs)("div",{className:"relative overflow-hidden rounded-xl border-4 border-sky-300 shadow-xl bg-black",children:[(0,a.jsx)("video",{ref:D,autoPlay:!0,playsInline:!0,muted:!0,className:"w-full h-auto object-cover",style:{aspectRatio:"3/4",maxHeight:"400px"}}),(0,a.jsxs)("div",{className:"absolute inset-0 pointer-events-none",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-48 h-64 border-2 border-white/50 rounded-full border-dashed"})}),(0,a.jsx)("div",{className:"absolute top-4 left-4 right-4",children:(0,a.jsx)("div",{className:"bg-black/70 text-white text-xs px-3 py-2 rounded-lg text-center",children:"\uD83D\uDCF8 Position your face within the oval guide"})})]}),(0,a.jsx)("div",{className:"absolute top-2 left-2 w-6 h-6 border-l-2 border-t-2 border-sky-400"}),(0,a.jsx)("div",{className:"absolute top-2 right-2 w-6 h-6 border-r-2 border-t-2 border-sky-400"}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 w-6 h-6 border-l-2 border-b-2 border-sky-400"}),(0,a.jsx)("div",{className:"absolute bottom-2 right-2 w-6 h-6 border-r-2 border-b-2 border-sky-400"})]}),(0,a.jsxs)("div",{className:"mt-3 text-center",children:[(0,a.jsx)("p",{className:"text-sm text-sky-700 font-medium",children:"\uD83D\uDCCB Make sure your face is clearly visible and well-lit"}),(0,a.jsx)("p",{className:"text-xs text-sky-600 mt-1",children:"Look directly at the camera • Remove glasses if possible"})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,a.jsxs)(d.$,{onClick:()=>{let e=D.current;if(!e||4!==e.readyState)return void k.toast.error("Camera not ready. Please wait a moment and try again.");let s=document.createElement("canvas");s.width=e.videoWidth||640,s.height=e.videoHeight||480;let t=s.getContext("2d");if(!t)return void k.toast.error("Failed to capture selfie. Please try again.");t.drawImage(e,0,0,s.width,s.height);let a=s.toDataURL("image/jpeg",.8);if("data:,"===a)return void k.toast.error("Failed to capture selfie. Please try again.");m(a),q(),k.toast.success("Selfie captured successfully!")},className:"h-14 px-8 bg-sky-500 hover:bg-sky-600 text-white font-bold text-lg shadow-lg",children:[(0,a.jsx)(ea.A,{className:"h-6 w-6 mr-2"}),"\uD83D\uDCF8 Capture Photo"]}),(0,a.jsx)(d.$,{variant:"outline",onClick:q,className:"h-14 px-8 border-2 border-sky-500 text-sky-600 hover:bg-sky-50 font-medium",children:"❌ Cancel"})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"w-32 h-32 bg-gradient-to-br from-sky-100 to-sky-200 rounded-full flex items-center justify-center mx-auto shadow-lg",children:(0,a.jsx)(ea.A,{className:"h-16 w-16 text-sky-600"})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-sky-800",children:"\uD83D\uDCF8 Take Your Selfie"}),(0,a.jsx)("p",{className:"text-sm text-sky-600 max-w-md mx-auto",children:"We need a clear photo of your face for identity verification. Make sure you're in a well-lit area."})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)(d.$,{onClick:()=>{y(!0)},className:"h-14 px-10 bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white font-bold text-lg shadow-lg transform hover:scale-105 transition-all duration-200",children:[(0,a.jsx)(ea.A,{className:"h-6 w-6 mr-3"}),"\uD83D\uDCF7 Take Photo"]}),(0,a.jsxs)(d.$,{variant:"outline",onClick:I,className:"h-14 px-10 border-2 border-sky-500 text-sky-600 hover:bg-sky-50 font-bold text-lg",children:[(0,a.jsx)(ea.A,{className:"h-6 w-6 mr-3"}),"\uD83D\uDCF9 Live Camera"]})]})]}),(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-200"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-4 bg-sky-50 text-gray-500",children:"Or upload a photo"})})]}),(0,a.jsx)("input",{type:"file",accept:"image/jpeg,image/jpg,image/png,image/webp",onChange:e=>L(e,"selfie"),className:"hidden",id:"selfie-upload"}),(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>{var e;return null==(e=document.getElementById("selfie-upload"))?void 0:e.click()},className:"mt-4 h-10 border-2 border-sky-500 text-sky-600 hover:bg-sky-50 font-medium",children:[(0,a.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Upload Selfie Image"]})]})]})}),o&&(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-green-50 to-sky-50 border-2 border-green-200 rounded-xl shadow-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-bold text-green-800 text-lg",children:"✅ Selfie Captured Successfully!"}),(0,a.jsx)("p",{className:"text-sm text-green-600",children:"Your identity photo looks great"})]})]}),(0,a.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>m(null),className:"text-green-600 hover:bg-green-100 rounded-full",children:(0,a.jsx)($.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("div",{className:"inline-block p-4 bg-white rounded-2xl border-2 border-green-200 shadow-xl",children:(0,a.jsx)("img",{src:o,alt:"Captured Selfie",className:"max-h-40 max-w-40 rounded-xl object-cover shadow-lg"})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>m(null),className:"h-12 px-6 border-2 border-sky-500 text-sky-600 hover:bg-sky-50 font-medium",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5 mr-2"}),"\uD83D\uDCF8 Retake Photo"]}),(0,a.jsxs)(d.$,{onClick:()=>{},className:"h-12 px-6 bg-green-500 hover:bg-green-600 text-white font-medium",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 mr-2"}),"✅ Use This Photo"]})]})]})]})]})})]}),N&&w>0&&(0,a.jsx)(n.Zp,{className:"border-0 shadow-lg bg-white",children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Uploading..."}),(0,a.jsxs)("span",{className:"font-bold text-sky-600",children:[Math.round(w),"%"]})]}),(0,a.jsx)(_.k,{value:w,className:"h-3"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 text-center",children:w<50?"Uploading signature...":w<95?"Uploading selfie...":100===w?"✅ Complete! Advancing to next step...":"Saving to database..."})]})})}),(0,a.jsxs)("div",{className:"flex justify-between pt-4",children:[(0,a.jsxs)(d.$,{type:"button",variant:"outline",onClick:t,className:"h-10 px-6 border-2 border-sky-300 text-sky-600 hover:bg-sky-50 font-medium",children:[(0,a.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"Previous Step"]}),(0,a.jsx)(d.$,{onClick:M,disabled:!l||!o||N,className:"h-10 px-8 bg-sky-500 hover:bg-sky-600 text-white font-bold min-w-[140px]",children:N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Submitting..."]}):(0,a.jsxs)(a.Fragment,{children:["Submit & Continue",(0,a.jsx)(T.A,{className:"h-4 w-4 ml-2"})]})})]})]})]})}var en=t(5509),ed=t(2747),ec=t(6994);let eo=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(en.bL,{ref:s,className:(0,ec.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-gray-300 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white",t),...r,children:(0,a.jsx)(en.C1,{className:(0,ec.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(ed.A,{className:"h-4 w-4"})})})});eo.displayName=en.bL.displayName;var em=t(8424),ex=t(8163);function eu(e){let{onPrevious:s,onStepChange:t}=e,[l,i]=(0,r.useState)(!1),[c,u]=(0,r.useState)(!1),g=(0,v.GV)(w.xu),{data:f,refetch:j}=(0,b.xs)(),{data:N}=(0,b.jY)(),{data:C}=(0,b.VI)(),{data:S}=(0,b.fW)(),[D,{isLoading:A}]=(0,b.fx)();null==f||f.data;let P=null==N?void 0:N.data,F=null==C?void 0:C.data,B=(null==S?void 0:S.data)||[],T={firstName:(null==P?void 0:P.firstName)||(null==g?void 0:g.firstName)||"Not provided",lastName:(null==P?void 0:P.lastName)||(null==g?void 0:g.lastName)||"Not provided",email:(null==P?void 0:P.email)||(null==g?void 0:g.email)||"Not provided",phone:(null==P?void 0:P.phone)||(null==g?void 0:g.phone)||"Not provided",dateOfBirth:(null==P?void 0:P.dateOfBirth)||(null==g?void 0:g.dateOfBirth)||"Not provided",nationality:(null==P?void 0:P.nationality)||"Not provided",placeOfBirth:(null==P?void 0:P.placeOfBirth)||"Not provided",gender:(null==P?void 0:P.gender)||"Not provided",maritalStatus:(null==P?void 0:P.maritalStatus)||"Not provided"},U={street:(null==F?void 0:F.street)||"Not provided",city:(null==F?void 0:F.city)||"Not provided",state:(null==F?void 0:F.state)||"Not provided",postalCode:(null==F?void 0:F.postalCode)||"Not provided",country:(null==F?void 0:F.country)||"Not provided",addressType:(null==F?void 0:F.addressType)||"Not provided",residenceSince:(null==F?void 0:F.residenceSince)||null},E=async()=>{if(!l||!c)return void k.toast.error("Please agree to all terms and conditions");try{console.log("\uD83D\uDE80 Submitting KYC for review..."),await D().unwrap(),console.log("✅ KYC submitted successfully"),k.toast.success("\uD83C\uDF89 KYC submitted successfully! We will review your documents within 1-2 business days."),j()}catch(s){var e;console.error("KYC submission error:",s),k.toast.error((null==s||null==(e=s.data)?void 0:e.message)||"Failed to submit KYC. Please try again.")}},O=async()=>{try{console.log("\uD83D\uDCC4 Downloading KYC PDF...");let e=await fetch("".concat("http://localhost:5000/api","/kyc/download-pdf"),{method:"GET",credentials:"include",headers:{Accept:"application/pdf"}});if(!e.ok)throw Error("Failed to download PDF");let s=await e.blob(),t=window.URL.createObjectURL(s),a=document.createElement("a");a.href=t,a.download="KYC_Report_".concat(new Date().toISOString().split("T")[0],".pdf"),document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(t),k.toast.success("PDF downloaded successfully!")}catch(e){console.error("PDF download error:",e),k.toast.error("Failed to download PDF. Please try again.")}};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)(n.Zp,{className:"border-0 shadow-xl bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-xl",children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-3 text-xl",children:[(0,a.jsx)(y.A,{className:"h-6 w-6"}),(0,a.jsx)("span",{children:"KYC Review & Submission"})]})}),(0,a.jsx)(n.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-6 bg-sky-50 border-2 border-sky-200 rounded-xl",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-sky-500 rounded-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-7 w-7 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-black mb-1",children:"\uD83C\uDF89 KYC Information Complete"}),(0,a.jsx)("p",{className:"text-sky-700",children:"All required information has been provided. Please review and submit for verification."})]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Personal Information"})]}),(0,a.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>t(0),children:[(0,a.jsx)(em.A,{className:"h-4 w-4 mr-2"}),"Edit"]})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Name:"}),(0,a.jsxs)("span",{className:"ml-2",children:[null==g?void 0:g.firstName," ",null==g?void 0:g.lastName]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Email:"}),(0,a.jsx)("span",{className:"ml-2",children:null==g?void 0:g.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Phone:"}),(0,a.jsx)("span",{className:"ml-2",children:(null==g?void 0:g.phone)||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Nationality:"}),(0,a.jsx)("span",{className:"ml-2",children:T.nationality||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Place of Birth:"}),(0,a.jsx)("span",{className:"ml-2",children:T.placeOfBirth||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Gender:"}),(0,a.jsx)("span",{className:"ml-2 capitalize",children:T.gender||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Marital Status:"}),(0,a.jsx)("span",{className:"ml-2 capitalize",children:T.maritalStatus||"Not provided"})]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Address Information"})]}),(0,a.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>t(1),children:[(0,a.jsx)(em.A,{className:"h-4 w-4 mr-2"}),"Edit"]})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Address Type:"}),(0,a.jsx)("span",{className:"ml-2 capitalize",children:U.addressType||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Street Address:"}),(0,a.jsx)("span",{className:"ml-2",children:U.street||"Not provided"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"City:"}),(0,a.jsx)("span",{className:"ml-2",children:U.city||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"State:"}),(0,a.jsx)("span",{className:"ml-2",children:U.state||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Postal Code:"}),(0,a.jsx)("span",{className:"ml-2",children:U.postalCode||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Country:"}),(0,a.jsx)("span",{className:"ml-2",children:U.country||"Not provided"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Living since:"}),(0,a.jsx)("span",{className:"ml-2",children:U.residenceSince?new Date(U.residenceSince).toLocaleDateString():"Not provided"})]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Uploaded Documents"})]}),(0,a.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>t(2),children:[(0,a.jsx)(em.A,{className:"h-4 w-4 mr-2"}),"Edit"]})]})}),(0,a.jsx)(n.Wu,{children:B.length>0?(0,a.jsx)("div",{className:"space-y-3",children:B.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-gray-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.type}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.documentNumber&&"Document #: ".concat(e.documentNumber)})]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:new Date(e.uploadedAt).toLocaleDateString()})]},s))}):(0,a.jsx)("p",{className:"text-gray-600",children:"No documents uploaded yet."})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Terms and Conditions"})]})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(eo,{id:"terms",checked:l,onCheckedChange:e=>i(e)}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("label",{htmlFor:"terms",className:"font-medium cursor-pointer",children:"I agree to the Terms and Conditions"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"I confirm that all information provided is accurate and complete. I understand that providing false information may result in account suspension."})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(eo,{id:"dataProcessing",checked:c,onCheckedChange:e=>u(e)}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("label",{htmlFor:"dataProcessing",className:"font-medium cursor-pointer",children:"I consent to data processing"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"I consent to the processing of my personal data for KYC verification purposes in accordance with applicable data protection laws."})]})]})]})]}),(0,a.jsx)(n.Zp,{className:"border-blue-200 bg-blue-50",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(W.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-blue-800",children:"What happens next?"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 mt-2 space-y-1",children:[(0,a.jsx)("li",{children:"• Your documents will be reviewed by our compliance team"}),(0,a.jsx)("li",{children:"• Verification typically takes 1-2 business days"}),(0,a.jsx)("li",{children:"• You'll receive an email notification once verification is complete"}),(0,a.jsx)("li",{children:"• If additional information is needed, we'll contact you directly"})]})]})]})})}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)(d.$,{type:"button",variant:"outline",onClick:s,children:[(0,a.jsx)(R.A,{className:"mr-2 h-4 w-4"}),"Previous"]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)(d.$,{type:"button",variant:"outline",onClick:O,className:"min-w-[140px]",children:[(0,a.jsx)(er.A,{className:"mr-2 h-4 w-4"}),"Download PDF"]}),(0,a.jsx)(d.$,{onClick:E,disabled:!l||!c||A,className:"min-w-[140px]",children:A?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Submitting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ex.A,{className:"mr-2 h-4 w-4"}),"Submit for Verification"]})})]})]})]})}let eh=[{id:"personal",title:"Personal Info",description:"Basic details",icon:o.A,required:!0},{id:"address",title:"Address",description:"Location details",icon:m.A,required:!0},{id:"documents",title:"Documents",description:"ID & proof",icon:x.A,required:!0},{id:"signature",title:"Signature",description:"Digital sign",icon:u.A,required:!0},{id:"review",title:"Review",description:"Final check",icon:h.A,required:!0}];function ep(){var e,s;(0,l.useRouter)();let t=(0,v.GV)(w.xu),[o,m]=(0,r.useState)(0),[u,C]=(0,r.useState)([]),{data:S,isLoading:D,error:A,refetch:P}=(0,b.xs)(),{data:F,isLoading:B,refetch:R}=(0,N.M7)(),[T,{isLoading:U}]=(0,b.et)(),[O,I]=(0,r.useState)({firstName:"",lastName:"",email:"",phone:"",dateOfBirth:"",nationality:"",placeOfBirth:"",gender:void 0,maritalStatus:void 0});(0,r.useEffect)(()=>{let e=(null==F?void 0:F.data)||t;e&&(console.log("\uD83D\uDD04 Auto-filling user data:",e),I({firstName:e.firstName||"",lastName:e.lastName||"",email:e.email||"",phone:e.phone||"",dateOfBirth:e.dateOfBirth?new Date(e.dateOfBirth).toISOString().split("T")[0]:"",nationality:"India",placeOfBirth:"",gender:void 0,maritalStatus:void 0}))},[t,F]);let{data:q,isLoading:L,error:Y}=(0,b.jY)(void 0,{skip:!t});(0,r.useEffect)(()=>{if(null==q?void 0:q.data){console.log("\uD83D\uDD04 Loading saved KYC data:",q.data);let e=q.data;I(s=>({...s,firstName:e.firstName||s.firstName,lastName:e.lastName||s.lastName,email:e.email||s.email,phone:e.phone||s.phone,dateOfBirth:e.dateOfBirth||s.dateOfBirth,nationality:e.nationality||s.nationality,placeOfBirth:e.placeOfBirth||s.placeOfBirth,gender:e.gender||s.gender,maritalStatus:e.maritalStatus||s.maritalStatus}))}},[q]),(0,r.useEffect)(()=>{var e;if(null==S||null==(e=S.data)?void 0:e.kyc){let e=S.data.kyc;console.log("\uD83D\uDD0D KYC Data:",e);let s=[];e.completedSteps&&Array.isArray(e.completedSteps)?console.log("✅ Using backend completed steps:",s=[...e.completedSteps]):(console.log("⚠️ No completedSteps found, using fallback logic"),("pending_verification"===e.status||"verified"===e.status||"approved"===e.status)&&s.push("signature"),console.log("✅ Determined completed steps from data:",s)),C(s);let t=eh.findIndex(e=>!s.includes(e.id));-1!==t?(m(t),console.log("\uD83C\uDFAF Current step set to:",eh[t].title,"at index",t)):"verified"===e.status&&(m(eh.length-1),console.log("\uD83C\uDFAF All steps completed, showing review step"))}},[S]);let _=()=>{var e;if(console.log("\uD83D\uDD04 KYC handleNext called",{currentStep:o,totalSteps:eh.length,currentStepTitle:null==(e=eh[o])?void 0:e.title}),o<eh.length-1){let e=eh[o].id,s=u.includes(e)?u:[...u,e];console.log("✅ Advancing from step",o,"to step",o+1),console.log("\uD83D\uDCDD Marking step as completed:",e),C(s),m(o+1),P()}else console.log("⚠️ Already at last step, cannot advance further")},W=()=>{o>0&&m(o-1)},$=e=>{(e<=o||u.includes(eh[e].id))&&m(e)},Z=async()=>{try{console.log("\uD83D\uDCC4 Downloading KYC PDF...");let e=await T().unwrap(),s=window.URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="KYC_Report_".concat(O.firstName,"_").concat(O.lastName,".pdf"),document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(s),k.toast.success("KYC PDF downloaded successfully!")}catch(s){var e;console.error("PDF download failed:",s),k.toast.error((null==s||null==(e=s.data)?void 0:e.message)||"Failed to download PDF")}};if(D||B)return(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)(p.A,{className:"h-12 w-12 animate-spin mx-auto text-sky-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-sky-600",children:"Loading KYC Information"}),(0,a.jsx)("p",{className:"text-sm text-sky-500",children:"Fetching your profile and KYC status..."})]})]})})});let z=eh[o],J=(o+1)/eh.length*100,V=(null==S||null==(s=S.data)||null==(e=s.kyc)?void 0:e.status)||"not_submitted";return(console.log("kycData",S),console.log("kycStatus",V),"pending_verification"===V||"under_review"===V)?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,a.jsxs)("div",{className:"text-center space-y-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"mx-auto w-24 h-24 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(g.A,{className:"h-12 w-12 text-yellow-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"KYC Under Review"}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:"Your documents are being verified by our team"})]})]}),(0,a.jsx)(n.Zp,{className:"max-w-2xl mx-auto border-yellow-200 bg-yellow-50",children:(0,a.jsx)(n.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-lg font-semibold text-yellow-800",children:"Verification in Progress"}),(0,a.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full animate-pulse"})]}),(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-700",children:"Thank you for submitting your KYC documents. Our verification team is currently reviewing your information."}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"What happens next?"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2 text-left",children:[(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{children:"Documents received and under review"})]}),(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsx)("span",{children:"Verification process (1-2 business days)"})]}),(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 text-blue-500"}),(0,a.jsx)("span",{children:"Email notification upon completion"})]})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"You will receive an email notification once the verification is complete. If you have any questions, please contact our support team."})]})]})})}),(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600",children:"Need help or want to download your KYC report?"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,a.jsxs)(d.$,{variant:"outline",className:"border-blue-300 text-blue-600 hover:bg-blue-50",onClick:Z,disabled:U,children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Download PDF Report"]}),(0,a.jsxs)(d.$,{variant:"outline",className:"border-sky-300 text-sky-600 hover:bg-sky-50",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Contact Support"]})]})]})]})})}):(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"min-h-screen bg-white py-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-6",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-sky-600 mb-2 flex items-center gap-3",children:[(0,a.jsx)(y.A,{className:"h-8 w-8"}),"KYC Verification"]}),(0,a.jsx)("p",{className:"text-sky-600",children:"Complete your identity verification to unlock all investment features"})]}),(0,a.jsxs)("div",{className:"text-right space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(d.$,{onClick:Z,disabled:U,variant:"outline",size:"sm",className:"border-sky-500 text-sky-600 hover:bg-sky-50",children:U?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Generating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Download PDF"]})}),(0,a.jsxs)("div",{className:"flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium ".concat((e=>{switch(e){case"approved":return"bg-green-100 text-green-700 border-green-300";case"pending":case"under_review":return"bg-yellow-100 text-yellow-700 border-yellow-300";case"rejected":return"bg-red-100 text-red-700 border-red-300";default:return"bg-gray-100 text-gray-700 border-gray-300"}})(V)),children:[(e=>{switch(e){case"approved":return(0,a.jsx)(h.A,{className:"h-4 w-4"});case"pending":case"under_review":return(0,a.jsx)(g.A,{className:"h-4 w-4"});case"rejected":return(0,a.jsx)(j.A,{className:"h-4 w-4"});default:return(0,a.jsx)(y.A,{className:"h-4 w-4"})}})(V),(0,a.jsx)("span",{className:"capitalize",children:V.replace("_"," ")})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-sm text-sky-600 mb-1",children:"Progress"}),(0,a.jsxs)("div",{className:"text-3xl font-bold text-sky-600",children:[Math.round(J),"%"]})]})]})]})}),(0,a.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-8",children:eh.map((e,s)=>{let t=u.includes(e.id),r=s===o,l=s<=o||t;return(0,a.jsx)(n.Zp,{className:"cursor-pointer transition-all duration-200 hover:shadow-lg ".concat(t?"border-sky-500 bg-sky-50 shadow-md":r?"border-yellow-400 bg-yellow-50 shadow-lg scale-105":l?"border-sky-200 bg-white hover:bg-sky-50":"border-gray-200 bg-gray-50 opacity-60"),onClick:()=>l&&$(s),children:(0,a.jsxs)(n.Wu,{className:"p-3 text-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2 ".concat(t?"bg-sky-500 text-white":r?"bg-yellow-500 text-white":l?"bg-sky-100 text-sky-600":"bg-gray-100 text-gray-400"),children:t?(0,a.jsx)(h.A,{className:"h-5 w-5"}):(0,a.jsx)(e.icon,{className:"h-5 w-5"})}),(0,a.jsx)("h3",{className:"text-xs font-bold mb-1 ".concat(r?"text-yellow-700":t?"text-sky-700":"text-sky-600"),children:e.title}),(0,a.jsx)("p",{className:"text-xs ".concat(r?"text-yellow-600":t?"text-sky-600":"text-sky-500"),children:e.description})]})},e.id)})}),(0,a.jsxs)(n.Zp,{className:"shadow-lg border-sky-200 bg-white",children:[(0,a.jsx)(n.aR,{className:"bg-sky-500 text-white rounded-t-lg",children:(0,a.jsxs)(n.ZB,{className:"flex items-center gap-3",children:[(0,a.jsx)(z.icon,{className:"h-6 w-6"}),z.title,(0,a.jsxs)(c.E,{className:"bg-white/20 text-white",children:["Step ",o+1," of ",eh.length]})]})}),(0,a.jsxs)(n.Wu,{className:"p-8",children:[0===o&&(0,a.jsx)(E,{onNext:_,onPrevious:W,initialData:O}),1===o&&(0,a.jsx)(M,{onNext:_,onPrevious:W}),2===o&&(0,a.jsx)(es,{onNext:_,onPrevious:W}),3===o&&(0,a.jsx)(ei,{onNext:_,onPrevious:W}),4===o&&(0,a.jsx)(eu,{onPrevious:W,onStepChange:m})]})]})]})})})}},7971:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var a=t(9605),r=t(9585),l=t(6994);let i=r.forwardRef((e,s)=>{let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},9564:(e,s,t)=>{Promise.resolve().then(t.bind(t,5505))},9577:(e,s,t)=>{"use strict";t.d(s,{bq:()=>x,eb:()=>g,gC:()=>p,l6:()=>o,yv:()=>m});var a=t(9605),r=t(9585),l=t(5080),i=t(9695),n=t(6054),d=t(2747),c=t(6994);let o=l.bL;l.YJ;let m=l.WT,x=r.forwardRef((e,s)=>{let{className:t,children:r,...n}=e;return(0,a.jsxs)(l.l9,{ref:s,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...n,children:[r,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});x.displayName=l.l9.displayName;let u=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});u.displayName=l.PP.displayName;let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let p=r.forwardRef((e,s)=>{let{className:t,children:r,position:i="popper",...n}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:s,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-white text-gray-950 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,a.jsx)(u,{}),(0,a.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});p.displayName=l.UC.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.JU,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...r})}).displayName=l.JU.displayName;let g=r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(l.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:r})]})});g.displayName=l.q7.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-gray-100",t),...r})}).displayName=l.wv.displayName}},e=>{var s=s=>e(e.s=s);e.O(0,[2094,5315,7436,7693,3403,542,4329,4245,9467,1147,7627,3005,390,110,7358],()=>s(9564)),_N_E=e.O()}]);