(()=>{var e={};e.id=2454,e.ids=[2454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4942:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(62544);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},5568:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(99024).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},6788:(e,s,t)=>{Promise.resolve().then(t.bind(t,73192))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22324:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(99024).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},24689:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(99024).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},26806:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(99024).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29419:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(99024).A)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},33873:e=>{"use strict";e.exports=require("path")},40002:(e,s,t)=>{"use strict";t.d(s,{B3:()=>n,Lv:()=>m,Ng:()=>l,_L:()=>r,ac:()=>f,ge:()=>a,nd:()=>c,pv:()=>p,tl:()=>d,uU:()=>o});let{useLoginMutation:r,useRegisterMutation:a,useLogoutMutation:l,useRefreshTokenMutation:i,useVerifyEmailMutation:o,useResendVerificationEmailMutation:n,useForgotPasswordMutation:c,useVerifyPasswordResetOTPMutation:d,useResetPasswordMutation:m,useChangePasswordMutation:u,useVerifyPhoneMutation:h,useSendPhoneOTPMutation:x,useSendEmailOTPMutation:f,useVerifyEmailOTPMutation:p,useGetCurrentUserQuery:g,useGetUserProfileQuery:y,useCheckAuthStatusQuery:b}=t(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({login:e.mutation({query:e=>({url:"/auth/login",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),register:e.mutation({query:e=>({url:"/auth/register",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),logout:e.mutation({query:()=>({url:"/auth/logout",method:"POST"}),invalidatesTags:["Auth","User"]}),refreshToken:e.mutation({query:e=>({url:"/auth/refresh",method:"POST",body:e})}),verifyEmail:e.mutation({query:e=>({url:"/auth/verify-email",method:"POST",body:e}),invalidatesTags:["User"]}),resendVerificationEmail:e.mutation({query:e=>({url:"/auth/send-verification",method:"POST",body:e})}),forgotPassword:e.mutation({query:e=>({url:"/auth/forgot-password",method:"POST",body:e})}),verifyPasswordResetOTP:e.mutation({query:e=>({url:"/auth/verify-reset-otp",method:"POST",body:e})}),resetPassword:e.mutation({query:e=>({url:"/auth/reset-password",method:"POST",body:e})}),changePassword:e.mutation({query:e=>({url:"/auth/change-password",method:"POST",body:e}),invalidatesTags:["User"]}),verifyPhone:e.mutation({query:e=>({url:"/auth/verify-phone",method:"POST",body:e}),invalidatesTags:["User"]}),sendPhoneOTP:e.mutation({query:e=>({url:"/auth/send-phone-otp",method:"POST",body:e})}),sendEmailOTP:e.mutation({query:e=>({url:"/auth/send-otp",method:"POST",body:e})}),verifyEmailOTP:e.mutation({query:e=>({url:"/auth/verify-otp",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),getCurrentUser:e.query({query:()=>"/auth/me",providesTags:["User"]}),checkAuthStatus:e.query({query:()=>"/auth/status",providesTags:["Auth"]}),getUserProfile:e.query({query:()=>"/auth/profile",providesTags:["User","Auth"]})})})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65653:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(99024).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},73192:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>q});var r=t(40969),a=t(73356),l=t(12011),i=t(27092),o=t.n(i),n=t(58121),c=t(66782),d=t(65406),m=t(24689),u=t(5825),h=t(26806),x=t(49610),f=t(85701),p=t(85538),g=t(22324),y=t(5568),b=t(65653),j=t(29419),v=t(75694),w=t(46411),N=t(66949),P=t(40002),k=t(92462),T=t(61631),A=t(77038),C=t(1507);let S=({content:e,children:s})=>{let[t,l]=(0,a.useState)(!1),i=(0,a.useRef)(null),o=()=>{i.current=setTimeout(()=>l(!0),100)},n=()=>{i.current&&clearTimeout(i.current),l(!1)};return(0,r.jsxs)("span",{className:"relative inline-block",onMouseEnter:o,onMouseLeave:n,onFocus:o,onBlur:n,tabIndex:0,"aria-describedby":"tooltip",children:[s,t&&(0,r.jsx)("span",{id:"tooltip",role:"tooltip",className:"absolute z-50 left-1/2 -translate-x-1/2 mt-2 px-3 py-1 rounded bg-gray-900 text-white text-xs shadow-lg whitespace-nowrap",style:{top:"100%"},children:e})]})},O=d.Ik({firstName:d.Yj().min(2,"First name must be at least 2 characters"),lastName:d.Yj().min(2,"Last name must be at least 2 characters"),email:d.Yj().email("Please enter a valid email address"),phone:d.Yj().min(10,"Please enter a valid phone number"),password:d.Yj().min(8,"Password must be at least 8 characters"),confirmPassword:d.Yj(),referralCode:d.Yj().optional(),agreeToTerms:d.zM().refine(e=>!0===e,{message:"You must agree to the terms and conditions"})}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function q(){let e=(0,l.useRouter)();(0,l.useSearchParams)();let s=(0,k.jL)();(0,k.GV)(T.Kc);let[t,i]=(0,a.useState)(!1),[d,q]=(0,a.useState)(!1),[M,{isLoading:R}]=(0,P.ge)(),[E]=(0,P.ac)(),[$]=(0,P.pv)(),[F,L]=(0,a.useState)(0),[B,U]=(0,a.useState)(!1),[_,Y]=(0,a.useState)(!1),[z,G]=(0,a.useState)(""),[I,D]=(0,a.useState)(""),[W,V]=(0,a.useState)(!1),[Z,K]=(0,a.useState)(!1),[H,J]=(0,a.useState)(0),{getNextStepForUser:X}=(0,A.YK)(),{register:Q,handleSubmit:ee,formState:{errors:es},setError:et,watch:er,setValue:ea}=(0,n.mN)({resolver:(0,c.u)(O),defaultValues:{firstName:"",lastName:"",email:"",phone:"",password:"",confirmPassword:"",referralCode:"",agreeToTerms:!1}}),el=er("password"),ei=er("referralCode"),eo=async t=>{try{let r=await M(t).unwrap();if(r.success&&r.data){s((0,T.LA)({user:r.data.user,token:r.data.token,refreshToken:r.data.refreshToken}));let a=X(r.data.user);"kyc"===a?(C.toast.success("Account created successfully! Please complete your KYC verification."),e.push("/kyc")):r.data.otpSent&&r.data.otpRequired?(G(t.email),Y(!0),J(60),C.toast.success("Account created successfully! Please check your email for the OTP code.")):"email_verification"===a||r.data.emailVerificationRequired?(G(t.email),U(!0),C.toast.success("Account created successfully! Please check your email to verify your account.")):(C.toast.success("Account created successfully! Please login with your credentials."),localStorage.setItem("loginCredentials",JSON.stringify({email:t.email,referralCode:t.referralCode||""})),e.push("/login?registered=true"))}}catch(e){console.error("Registration error:",e),409===e.status?et("email",{message:"Email already exists"}):e.data?.message?C.toast.error(e.data.message):C.toast.error("Registration failed. Please try again.")}},en=async()=>{if(!I||6!==I.length)return void C.toast.error("Please enter a valid 6-digit OTP");V(!0);try{if((await $({email:z,otp:I}).unwrap()).success){C.toast.success("Email verified successfully! Your account is now active.");let s=er();localStorage.setItem("loginCredentials",JSON.stringify({email:z,referralCode:s.referralCode||""})),e.push("/login?verified=true")}}catch(e){console.error("OTP verification error:",e),e.data?.message?C.toast.error(e.data.message):C.toast.error("Invalid OTP. Please try again.")}finally{V(!1)}},ec=async()=>{if(!z)return void C.toast.error("Email not found. Please try registering again.");if(H>0)return void C.toast.error(`Please wait ${H} seconds before requesting another OTP.`);K(!0);try{(await E({email:z}).unwrap()).success&&(C.toast.success("OTP sent successfully! Please check your email."),D(""),J(60))}catch(e){console.error("Resend OTP error:",e),e.data?.message?C.toast.error(e.data.message):C.toast.error("Failed to resend OTP. Please try again.")}finally{K(!1)}};return _?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,r.jsx)(m.A,{className:"h-12 w-12 text-blue-600"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"PropertyInvest"})]}),(0,r.jsxs)(N.Zp,{className:"shadow-2xl border-0 p-2",children:[(0,r.jsxs)(N.aR,{className:"space-y-1",children:[(0,r.jsx)(N.ZB,{className:"text-2xl font-bold text-center text-green-600",children:"Verify Your Email"}),(0,r.jsxs)(N.BT,{className:"text-center",children:["We've sent a 6-digit OTP to ",(0,r.jsx)("strong",{children:z})]})]}),(0,r.jsxs)(N.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"otp",className:"text-sm font-medium text-gray-700",children:"Enter OTP"}),(0,r.jsx)("input",{id:"otp",type:"text",maxLength:6,value:I,onChange:e=>D(e.target.value.replace(/\D/g,"")),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-center text-lg font-mono tracking-widest focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"000000"})]}),(0,r.jsx)(w.$,{onClick:en,disabled:W||6!==I.length,className:"w-full",children:W?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Verifying..."]}):"Verify OTP"}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Didn't receive the OTP?"}),(0,r.jsx)(w.$,{variant:"outline",onClick:ec,disabled:Z||H>0,className:"text-blue-600 hover:text-blue-700",children:Z?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending..."]}):H>0?`Resend OTP (${H}s)`:"Resend OTP"})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(w.$,{variant:"ghost",onClick:()=>{Y(!1),D("")},className:"text-gray-600 hover:text-gray-700",children:"Back to Registration"})})]})]})]})}):B?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,r.jsx)(m.A,{className:"h-12 w-12 text-blue-600"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"SGM "})]}),(0,r.jsxs)(N.Zp,{className:"shadow-2xl border-0 p-2",children:[(0,r.jsxs)(N.aR,{className:"space-y-1",children:[(0,r.jsx)(N.ZB,{className:"text-2xl font-bold text-center text-green-600",children:"Account Created Successfully!"}),(0,r.jsx)(N.BT,{className:"text-center",children:"Please verify your email to activate your account"})]}),(0,r.jsxs)(N.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(h.A,{className:"h-16 w-16 text-blue-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-700 mb-2",children:"We've sent a verification email to:"}),(0,r.jsx)("p",{className:"font-semibold text-blue-600 mb-4",children:z}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Click the verification link in your email to activate your account and start investing."})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(w.$,{onClick:()=>e.push("/login"),className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:"Go to Login"}),(0,r.jsx)(w.$,{onClick:()=>U(!1),variant:"outline",className:"w-full",children:"Back to Registration"})]}),(0,r.jsxs)("div",{className:"text-center text-sm text-gray-600",children:[(0,r.jsx)("p",{children:"Didn't receive the email? Check your spam folder or"}),(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-500 font-medium",onClick:()=>{C.toast.info("Resend verification feature coming soon!")},children:"resend verification email"})]})]})]})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8 animate-fade-in",children:[(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsx)(v.A,{size:"lg",variant:"full"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-black mb-2",children:"Create Your Account"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Start your real estate investment journey"}),(0,r.jsx)("div",{className:"w-24 h-1 bg-sky-500 mx-auto mt-4 rounded-full"})]}),(0,r.jsxs)(N.Zp,{className:"sgm-card sgm-card-hover border-t-4 border-t-sky-500",children:[(0,r.jsxs)(N.aR,{className:"space-y-1",children:[(0,r.jsx)(N.ZB,{className:"text-2xl font-bold text-center text-black",children:"Create Account"}),(0,r.jsx)(N.BT,{className:"text-center text-gray-600",children:"Join thousands of investors building wealth through real estate"})]}),(0,r.jsxs)(N.Wu,{children:[(0,r.jsxs)("form",{onSubmit:ee(eo),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"firstName",className:"text-sm font-medium text-gray-700",children:"First Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...Q("firstName"),type:"text",id:"firstName","aria-label":"First Name",placeholder:"First name",className:`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all duration-200 ${es.firstName?"border-red-500":"border-gray-300"}`})]}),es.firstName&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:es.firstName.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"lastName",className:"text-sm font-medium text-gray-700",children:"Last Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...Q("lastName"),type:"text",id:"lastName","aria-label":"Last Name",placeholder:"Last name",className:`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${es.lastName?"border-red-500":"border-gray-300"}`})]}),es.lastName&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:es.lastName.message})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...Q("email"),type:"email",id:"email","aria-label":"Email Address",placeholder:"Enter your email",className:`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${es.email?"border-red-500":"border-gray-300"}`})]}),es.email&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:es.email.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"phone",className:"text-sm font-medium text-gray-700",children:"Phone Number"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...Q("phone"),type:"tel",id:"phone","aria-label":"Phone Number",placeholder:"Enter your phone number",className:`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${es.phone?"border-red-500":"border-gray-300"}`})]}),es.phone&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:es.phone.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...Q("password"),type:t?"text":"password",id:"password","aria-label":"Password",placeholder:"Create a password",className:`w-full pl-10 pr-12 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${es.password?"border-red-500":"border-gray-300"}`}),(0,r.jsx)("button",{type:"button",onClick:()=>i(!t),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600","aria-label":t?"Hide password":"Show password",children:t?(0,r.jsx)(g.A,{className:"h-4 w-4"}):(0,r.jsx)(y.A,{className:"h-4 w-4"})})]}),el&&el.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"h-2 w-full bg-gray-200 rounded-full mt-1",children:(0,r.jsx)("div",{className:`h-2 rounded-full transition-all ${F<=2?"bg-red-400 w-1/5":3===F?"bg-yellow-400 w-3/5":F>=4?"bg-green-500 w-full":""}`})}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[F<=2&&"Weak password",3===F&&"Medium strength",F>=4&&"Strong password"]})]}),es.password&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:es.password.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...Q("confirmPassword"),type:d?"text":"password",id:"confirmPassword","aria-label":"Confirm Password",placeholder:"Confirm your password",className:`w-full pl-10 pr-12 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${es.confirmPassword?"border-red-500":"border-gray-300"}`}),(0,r.jsx)("button",{type:"button",onClick:()=>q(!d),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600","aria-label":d?"Hide confirm password":"Show confirm password",children:d?(0,r.jsx)(g.A,{className:"h-4 w-4"}):(0,r.jsx)(y.A,{className:"h-4 w-4"})})]}),es.confirmPassword&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:es.confirmPassword.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{htmlFor:"referralCode",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["Referral Code (Optional)",(0,r.jsx)(S,{content:"If you have a referral code, enter it to earn bonus rewards!",children:(0,r.jsx)(b.A,{className:"h-4 w-4 text-blue-400 cursor-pointer"})})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...Q("referralCode"),type:"text",id:"referralCode","aria-label":"Referral Code",placeholder:"Enter referral code",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),ei&&(0,r.jsx)("p",{className:"text-sm text-green-600",children:"\uD83C\uDF89 You'll earn bonus rewards with this referral!"})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)("input",{...Q("agreeToTerms"),type:"checkbox",id:"agreeToTerms","aria-label":"Agree to Terms",className:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsxs)("label",{htmlFor:"agreeToTerms",className:"text-sm text-gray-700",children:["I agree to the"," ",(0,r.jsx)(o(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",(0,r.jsx)(o(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})]}),es.agreeToTerms&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:es.agreeToTerms.message}),(0,r.jsx)(w.$,{type:"submit",disabled:R,className:"w-full bg-sky-500 hover:bg-sky-600 text-white py-3 px-4 rounded-lg font-medium transition-colors","aria-label":"Create Account",children:R?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating account..."]}):"Create Account"})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,r.jsx)(o(),{href:"/login",className:"text-blue-600 hover:text-blue-500 font-medium",children:"Sign in here"})]})})]})]})]})})}},75694:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(40969);t(73356);let a=({size:e="md",variant:s="full",className:t=""})=>{let a={sm:"h-8 w-8",md:"h-12 w-12",lg:"h-16 w-16",xl:"h-24 w-24"},l={sm:"text-lg",md:"text-2xl",lg:"text-3xl",xl:"text-4xl"},i=()=>(0,r.jsx)("div",{className:`${a[e]} ${t} relative`,children:(0,r.jsxs)("svg",{viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"w-full h-full",children:[(0,r.jsx)("circle",{cx:"50",cy:"50",r:"48",fill:"#0ea5e9",stroke:"#ffffff",strokeWidth:"2"}),(0,r.jsxs)("g",{transform:"translate(20, 25)",children:[(0,r.jsx)("rect",{x:"15",y:"20",width:"30",height:"35",fill:"#ffffff",rx:"2"}),(0,r.jsx)("rect",{x:"20",y:"25",width:"6",height:"6",fill:"#0ea5e9"}),(0,r.jsx)("rect",{x:"34",y:"25",width:"6",height:"6",fill:"#0ea5e9"}),(0,r.jsx)("rect",{x:"20",y:"35",width:"6",height:"6",fill:"#0ea5e9"}),(0,r.jsx)("rect",{x:"34",y:"35",width:"6",height:"6",fill:"#0ea5e9"}),(0,r.jsx)("rect",{x:"27",y:"45",width:"6",height:"10",fill:"#fbbf24"}),(0,r.jsx)("rect",{x:"5",y:"30",width:"8",height:"25",fill:"#ffffff",rx:"1"}),(0,r.jsx)("rect",{x:"47",y:"35",width:"8",height:"20",fill:"#ffffff",rx:"1"}),(0,r.jsx)("path",{d:"M10 15 L20 5 L30 10 L40 2 L50 8",stroke:"#fbbf24",strokeWidth:"3",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("polygon",{points:"45,2 50,8 45,8",fill:"#fbbf24"})]}),(0,r.jsx)("text",{x:"50",y:"75",textAnchor:"middle",fill:"#ffffff",fontSize:"12",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:"SGM"})]})});switch(s){case"icon":return(0,r.jsx)(i,{});case"text":return(0,r.jsx)(()=>(0,r.jsxs)("div",{className:`${t} flex items-center`,children:[(0,r.jsx)("span",{className:`${l[e]} font-bold sgm-accent-text`,children:"SGM"}),(0,r.jsx)("span",{className:`${l[e]} font-light sgm-primary-text ml-1`,children:"Investments"})]}),{});default:return(0,r.jsx)(()=>(0,r.jsxs)("div",{className:`${t} flex items-center space-x-3`,children:[(0,r.jsx)(i,{}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:`${l[e]} font-bold sgm-accent-text leading-none`,children:"SGM"}),(0,r.jsx)("span",{className:"text-sm sgm-primary-text leading-none",children:"Investments"})]})]}),{})}}},79113:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=t(10557),a=t(68490),l=t(13172),i=t.n(l),o=t(68835),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(s,n);let c={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,88718)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\register\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79551:e=>{"use strict";e.exports=require("url")},85538:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(99024).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},85701:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(99024).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},88718:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\register\\page.tsx","default")},93284:(e,s,t)=>{Promise.resolve().then(t.bind(t,88718))}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[755,3777,2544,7092,3035,2487],()=>t(79113));module.exports=r})();