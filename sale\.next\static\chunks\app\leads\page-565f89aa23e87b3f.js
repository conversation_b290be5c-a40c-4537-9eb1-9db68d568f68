(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[362],{192:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(6501).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},783:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(6501).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2731:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(6501).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3734:(e,s,a)=>{"use strict";a.d(s,{S:()=>o});var t=a(9605),r=a(9585),l=a(5509),i=a(1817),d=a(6994);let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.bL,{ref:s,className:(0,d.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...r,children:(0,t.jsx)(l.C1,{className:(0,d.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})})});o.displayName=l.bL.displayName},3786:(e,s,a)=>{Promise.resolve().then(a.bind(a,6540))},5509:(e,s,a)=>{"use strict";a.d(s,{C1:()=>w,bL:()=>f});var t=a(9585),r=a(4455),l=a(8972),i=a(4761),d=a(9728),o=a(8069),n=a(4281),c=a(4853),u=a(7905),m=a(9605),x="Checkbox",[h,p]=(0,l.A)(x),[y,g]=h(x);function v(e){let{__scopeCheckbox:s,checked:a,children:r,defaultChecked:l,disabled:i,form:o,name:n,onCheckedChange:c,required:u,value:h="on",internal_do_not_use_render:p}=e,[g,v]=(0,d.i)({prop:a,defaultProp:null!=l&&l,onChange:c,caller:x}),[b,j]=t.useState(null),[f,N]=t.useState(null),w=t.useRef(!1),k=!b||!!o||!!b.closest("form"),q={checked:g,disabled:i,setChecked:v,control:b,setControl:j,name:n,form:o,value:h,hasConsumerStoppedPropagationRef:w,required:u,defaultChecked:!P(l)&&l,isFormControl:k,bubbleInput:f,setBubbleInput:N};return(0,m.jsx)(y,{scope:s,...q,children:"function"==typeof p?p(q):r})}var b="CheckboxTrigger",j=t.forwardRef((e,s)=>{let{__scopeCheckbox:a,onKeyDown:l,onClick:d,...o}=e,{control:n,value:c,disabled:x,checked:h,required:p,setControl:y,setChecked:v,hasConsumerStoppedPropagationRef:j,isFormControl:f,bubbleInput:N}=g(b,a),w=(0,r.s)(s,y),k=t.useRef(h);return t.useEffect(()=>{let e=null==n?void 0:n.form;if(e){let s=()=>v(k.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[n,v]),(0,m.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":P(h)?"mixed":h,"aria-required":p,"data-state":C(h),"data-disabled":x?"":void 0,disabled:x,value:c,...o,ref:w,onKeyDown:(0,i.m)(l,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(d,e=>{v(e=>!!P(e)||!e),N&&f&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})})});j.displayName=b;var f=t.forwardRef((e,s)=>{let{__scopeCheckbox:a,name:t,checked:r,defaultChecked:l,required:i,disabled:d,value:o,onCheckedChange:n,form:c,...u}=e;return(0,m.jsx)(v,{__scopeCheckbox:a,checked:r,defaultChecked:l,disabled:d,required:i,onCheckedChange:n,name:t,form:c,value:o,internal_do_not_use_render:e=>{let{isFormControl:t}=e;return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(j,{...u,ref:s,__scopeCheckbox:a}),t&&(0,m.jsx)(q,{__scopeCheckbox:a})]})}})});f.displayName=x;var N="CheckboxIndicator",w=t.forwardRef((e,s)=>{let{__scopeCheckbox:a,forceMount:t,...r}=e,l=g(N,a);return(0,m.jsx)(c.C,{present:t||P(l.checked)||!0===l.checked,children:(0,m.jsx)(u.sG.span,{"data-state":C(l.checked),"data-disabled":l.disabled?"":void 0,...r,ref:s,style:{pointerEvents:"none",...e.style}})})});w.displayName=N;var k="CheckboxBubbleInput",q=t.forwardRef((e,s)=>{let{__scopeCheckbox:a,...l}=e,{control:i,hasConsumerStoppedPropagationRef:d,checked:c,defaultChecked:x,required:h,disabled:p,name:y,value:v,form:b,bubbleInput:j,setBubbleInput:f}=g(k,a),N=(0,r.s)(s,f),w=(0,o.Z)(c),q=(0,n.X)(i);t.useEffect(()=>{if(!j)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!d.current;if(w!==c&&e){let a=new Event("click",{bubbles:s});j.indeterminate=P(c),e.call(j,!P(c)&&c),j.dispatchEvent(a)}},[j,w,c,d]);let C=t.useRef(!P(c)&&c);return(0,m.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=x?x:C.current,required:h,disabled:p,name:y,value:v,form:b,...l,tabIndex:-1,ref:N,style:{...l.style,...q,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function P(e){return"indeterminate"===e}function C(e){return P(e)?"indeterminate":e?"checked":"unchecked"}q.displayName=k},5818:(e,s,a)=>{"use strict";a.d(s,{Cl:()=>m,af:()=>b,dy:()=>x,zq:()=>t});let{useGetPropertiesQuery:t,useGetPropertyByIdQuery:r,useGetMyPropertiesQuery:l,useAssignPropertyToSalesRepMutation:i,useGetPropertyInquiriesQuery:d,useCreatePropertyInquiryMutation:o,useUpdatePropertyInquiryMutation:n,useGetPropertyAnalyticsQuery:c,useSearchPropertiesQuery:u,useGetPropertyTypesQuery:m,useGetPropertyLocationsQuery:x,useAddToWatchlistMutation:h,useRemoveFromWatchlistMutation:p,useGetWatchlistQuery:y,useComparePropertiesQuery:g,useGetPropertyStockQuery:v,useGetPropertiesWithStocksQuery:b}=a(6701).q.injectEndpoints({endpoints:e=>({getProperties:e.query({query:e=>({url:"/sales/properties",params:e}),providesTags:["Property"]}),getPropertyById:e.query({query:e=>"/sales/properties/".concat(e),providesTags:(e,s,a)=>[{type:"Property",id:a}]}),getMyProperties:e.query({query:()=>"/sales/properties/my-properties",providesTags:["Property"]}),assignPropertyToSalesRep:e.mutation({query:e=>{let{propertyId:s,salesRepId:a}=e;return{url:"/sales/properties/".concat(s,"/assign"),method:"POST",body:{salesRepId:a}}},invalidatesTags:["Property"]}),getPropertyInquiries:e.query({query:e=>({url:"/sales/property-inquiries",params:e}),providesTags:["Property"]}),createPropertyInquiry:e.mutation({query:e=>({url:"/sales/property-inquiries",method:"POST",body:e}),invalidatesTags:["Property","Lead"]}),updatePropertyInquiry:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/sales/property-inquiries/".concat(s),method:"PUT",body:a}},invalidatesTags:(e,s,a)=>{let{id:t}=a;return[{type:"Property",id:t},"Property"]}}),getPropertyAnalytics:e.query({query:e=>({url:"/sales/properties/analytics",params:e}),providesTags:["Property","Dashboard"]}),searchProperties:e.query({query:e=>{let{query:s,filters:a}=e;return{url:"/sales/properties/search",params:{query:s,...a}}},providesTags:["Property"]}),getPropertyTypes:e.query({query:()=>"/sales/properties/types",providesTags:["Property"]}),getPropertyLocations:e.query({query:()=>"/sales/properties/locations",providesTags:["Property"]}),addToWatchlist:e.mutation({query:e=>({url:"/sales/properties/".concat(e,"/watchlist"),method:"POST"}),invalidatesTags:["Property"]}),removeFromWatchlist:e.mutation({query:e=>({url:"/sales/properties/".concat(e,"/watchlist"),method:"DELETE"}),invalidatesTags:["Property"]}),getWatchlist:e.query({query:()=>"/sales/properties/watchlist",providesTags:["Property"]}),compareProperties:e.query({query:e=>({url:"/sales/properties/compare",method:"POST",body:{propertyIds:e}}),providesTags:["Property"]}),getPropertyStock:e.query({query:e=>"/sales/properties/".concat(e,"/stock"),providesTags:(e,s,a)=>[{type:"Property",id:"".concat(a,"-stock")}]}),getPropertiesWithStocks:e.query({query:e=>({url:"/sales/properties/with-stocks",params:e}),providesTags:["Property"]})})})},6540:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>L});var t=a(9605),r=a(9585),l=a(6440),i=a(8063),d=a(2933),o=a(7971),n=a(2790),c=a(9577),u=a(4685),m=a(5097),x=a(6467),h=a(3734),p=a(783),y=a(192),g=a(2731),v=a(7183),b=a(8135),j=a(8232),f=a(1468),N=a(5857),w=a(7229),k=a(9581),q=a(1713),P=a(9192),C=a(7456),T=a(6994),A=a(8642),F=a(5818);let S=e=>{switch(e){case"new":default:return"bg-gray-100 text-gray-800";case"contacted":return"bg-sky-100 text-sky-800";case"qualified":case"closed_won":return"bg-green-100 text-green-800";case"proposal":case"negotiation":return"bg-yellow-100 text-yellow-800";case"closed_lost":return"bg-red-100 text-red-800"}},D=e=>e>=80?"text-red-600 bg-red-100":e>=60?"text-yellow-600 bg-yellow-100":e>=40?"text-blue-600 bg-blue-100":"text-gray-600 bg-gray-100";function L(){var e,s,a;let[L,E]=(0,r.useState)(""),[U,M]=(0,r.useState)("all"),[I,_]=(0,r.useState)("all"),[O,R]=(0,r.useState)(1),[$,J]=(0,r.useState)(!1),[z,W]=(0,r.useState)({fullName:"",email:"",phone:"",city:"",country:"India",source:"website",priority:"medium",interestedProperties:[],budget:{min:"",max:""},notes:"",tags:[],nextFollowUpDate:"",expectedCloseDate:""}),{data:B,isLoading:G,error:V,refetch:Z}=(0,A.$V)({page:O,limit:20,search:L||void 0,status:"all"!==U?U:void 0,source:"all"!==I?I:void 0,sortBy:"createdAt",sortOrder:"desc"}),{data:H,isLoading:Q}=(0,F.zq)({limit:100,status:"active"}),[K,{isLoading:X}]=(0,A.FO)(),Y=(null==B||null==(e=B.data)?void 0:e.leads)||[],ee=null==B||null==(s=B.data)?void 0:s.pagination,es=(null==H||null==(a=H.data)?void 0:a.properties)||[],ea=()=>{W({fullName:"",email:"",phone:"",city:"",country:"India",source:"website",priority:"medium",interestedProperties:[],budget:{min:"",max:""},notes:"",tags:[],nextFollowUpDate:"",expectedCloseDate:""})},et=async e=>{e.preventDefault();try{let e={fullName:z.fullName,email:z.email,phone:z.phone,city:z.city,country:z.country,source:z.source,priority:z.priority,interestedProperties:z.interestedProperties,budget:z.budget.min||z.budget.max?{min:parseFloat(z.budget.min)||0,max:parseFloat(z.budget.max)||0}:void 0,notes:z.notes,tags:z.tags,nextFollowUpDate:z.nextFollowUpDate||void 0,expectedCloseDate:z.expectedCloseDate||void 0};await K(e).unwrap(),J(!1),ea(),Z()}catch(e){console.error("Failed to create lead:",e)}},er=e=>{W(s=>({...s,interestedProperties:s.interestedProperties.includes(e)?s.interestedProperties.filter(s=>s!==e):[...s.interestedProperties,e]}))};return G?(0,t.jsx)(l.A,{title:"Leads Management",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)(p.A,{className:"w-8 h-8 animate-spin text-sky-500"})})})}):V?(0,t.jsx)(l.A,{title:"Leads Management",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(y.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Failed to load leads"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Please try refreshing the page"}),(0,t.jsx)(d.$,{onClick:()=>Z(),className:"bg-sky-500 hover:bg-sky-600",children:"Try Again"})]})})})}):(0,t.jsxs)(l.A,{title:"Leads Management",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Leads"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage and track your sales leads"})]}),(0,t.jsxs)(d.$,{className:"bg-sky-500 hover:bg-sky-600 text-white",onClick:()=>J(!0),children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Add New Lead"]})]}),(0,t.jsx)(i.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(o.p,{placeholder:"Search leads by name, email, or phone...",value:L,onChange:e=>E(e.target.value),className:"pl-10 border-gray-300 focus:border-sky-500 focus:ring-sky-500"})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(c.l6,{value:U,onValueChange:M,children:[(0,t.jsx)(c.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,t.jsx)(c.yv,{placeholder:"Status"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"all",children:"All Status"}),(0,t.jsx)(c.eb,{value:"new",children:"New"}),(0,t.jsx)(c.eb,{value:"contacted",children:"Contacted"}),(0,t.jsx)(c.eb,{value:"qualified",children:"Qualified"}),(0,t.jsx)(c.eb,{value:"proposal",children:"Proposal"}),(0,t.jsx)(c.eb,{value:"negotiation",children:"Negotiation"}),(0,t.jsx)(c.eb,{value:"closed_won",children:"Closed Won"}),(0,t.jsx)(c.eb,{value:"closed_lost",children:"Closed Lost"})]})]}),(0,t.jsxs)(c.l6,{value:I,onValueChange:_,children:[(0,t.jsx)(c.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,t.jsx)(c.yv,{placeholder:"Source"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"all",children:"All Sources"}),(0,t.jsx)(c.eb,{value:"website",children:"Website"}),(0,t.jsx)(c.eb,{value:"referral",children:"Referral"}),(0,t.jsx)(c.eb,{value:"social_media",children:"Social Media"}),(0,t.jsx)(c.eb,{value:"advertisement",children:"Advertisement"}),(0,t.jsx)(c.eb,{value:"cold_call",children:"Cold Call"})]})]}),(0,t.jsxs)(d.$,{variant:"outline",className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:[(0,t.jsx)(b.A,{className:"w-4 h-4 mr-2"}),"More Filters"]})]})]})})}),(0,t.jsxs)(i.Zp,{className:"border-gray-200 shadow-sm",children:[(0,t.jsx)(i.aR,{className:"bg-white border-b border-gray-200",children:(0,t.jsxs)(i.ZB,{className:"text-gray-900",children:["Leads (",Y.length,")",ee&&(0,t.jsxs)("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["Page ",ee.currentPage," of ",ee.totalPages,"(",ee.totalItems," total)"]})]})}),(0,t.jsxs)(i.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"space-y-0",children:Y.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-100 hover:bg-sky-50 transition-colors cursor-pointer",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-sky-100 rounded-full flex items-center justify-center flex-shrink-0",children:(0,t.jsx)(j.A,{className:"w-6 h-6 text-sky-600"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsxs)("h4",{className:"text-lg font-medium text-gray-900 truncate",children:[e.firstName," ",e.lastName]}),(0,t.jsx)(n.E,{className:"text-xs ".concat(S(e.status)),children:e.status.replace("_"," ").toUpperCase()}),e.score&&(0,t.jsx)("div",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(D(e.score)),children:e.score})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 text-sm text-gray-600",children:[(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"w-3 h-3 mr-1 text-sky-500"}),e.email]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"w-3 h-3 mr-1 text-green-500"}),e.phone]}),(e.interestedProperty||e.propertyInterest)&&(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(w.A,{className:"w-3 h-3 mr-1 text-yellow-500"}),e.interestedProperty||e.propertyInterest]}),e.budget&&(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(k.A,{className:"w-3 h-3 mr-1 text-green-600"}),(0,T.vv)(e.budget)]})]}),e.tags&&e.tags.length>0&&(0,t.jsx)("div",{className:"flex items-center space-x-1 mt-2",children:e.tags.map(e=>(0,t.jsx)(n.E,{variant:"outline",className:"text-xs border-sky-200 text-sky-700",children:e},e))})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 flex-shrink-0",children:[(e.nextFollowUp||e.followUpDate)&&(0,t.jsxs)("div",{className:"text-right hidden sm:block",children:[(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,t.jsx)(q.A,{className:"w-3 h-3 mr-1 text-yellow-500"}),"Next follow-up"]}),(0,t.jsx)("div",{className:"text-xs font-medium text-gray-900",children:(0,T.Yq)(e.nextFollowUp||e.followUpDate)})]}),(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)(d.$,{size:"sm",variant:"outline",className:"h-8 w-8 p-0 border-green-300 text-green-600 hover:bg-green-50",children:(0,t.jsx)(N.A,{className:"w-3 h-3"})}),(0,t.jsx)(d.$,{size:"sm",variant:"outline",className:"h-8 w-8 p-0 border-sky-300 text-sky-600 hover:bg-sky-50",children:(0,t.jsx)(f.A,{className:"w-3 h-3"})}),(0,t.jsx)(d.$,{size:"sm",variant:"outline",className:"h-8 w-8 p-0 border-yellow-300 text-yellow-600 hover:bg-yellow-50",children:(0,t.jsx)(P.A,{className:"w-3 h-3"})}),(0,t.jsx)(d.$,{size:"sm",variant:"outline",className:"h-8 w-8 p-0 border-gray-300 text-gray-600 hover:bg-gray-50",children:(0,t.jsx)(C.A,{className:"w-3 h-3"})})]})]})]},e.id))}),0===Y.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(j.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500",children:"No leads found matching your criteria"}),(0,t.jsx)(d.$,{className:"mt-4 bg-sky-500 hover:bg-sky-600 text-white",onClick:()=>Z(),children:"Refresh"})]}),ee&&ee.totalPages>1&&(0,t.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(ee.currentPage-1)*ee.itemsPerPage+1," to"," ",Math.min(ee.currentPage*ee.itemsPerPage,ee.totalItems)," of"," ",ee.totalItems," results"]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>R(ee.currentPage-1),disabled:1===ee.currentPage,className:"border-gray-300",children:"Previous"}),(0,t.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>R(ee.currentPage+1),disabled:ee.currentPage===ee.totalPages,className:"border-gray-300",children:"Next"})]})]})]})]})]}),(0,t.jsx)(u.lG,{open:$,onOpenChange:e=>{J(e),e||ea()},children:(0,t.jsxs)(u.Cf,{className:"sm:max-w-[700px] max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(u.c7,{children:(0,t.jsxs)(u.L3,{className:"text-xl font-semibold flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"w-5 h-5 text-sky-500"}),"Create New Lead"]})}),(0,t.jsxs)("form",{onSubmit:et,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Basic Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"fullName",children:"Full Name *"}),(0,t.jsx)(o.p,{id:"fullName",value:z.fullName,onChange:e=>W(s=>({...s,fullName:e.target.value})),placeholder:"Enter full name",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"email",children:"Email *"}),(0,t.jsx)(o.p,{id:"email",type:"email",value:z.email,onChange:e=>W(s=>({...s,email:e.target.value})),placeholder:"Enter email address",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"phone",children:"Phone *"}),(0,t.jsx)(o.p,{id:"phone",value:z.phone,onChange:e=>W(s=>({...s,phone:e.target.value})),placeholder:"Enter phone number",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"city",children:"City *"}),(0,t.jsx)(o.p,{id:"city",value:z.city,onChange:e=>W(s=>({...s,city:e.target.value})),placeholder:"Enter city",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"country",children:"Country *"}),(0,t.jsx)(o.p,{id:"country",value:z.country,onChange:e=>W(s=>({...s,country:e.target.value})),placeholder:"Enter country",required:!0})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Lead Details"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"source",children:"Source *"}),(0,t.jsxs)(c.l6,{value:z.source,onValueChange:e=>W(s=>({...s,source:e})),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"website",children:"Website"}),(0,t.jsx)(c.eb,{value:"social_media",children:"Social Media"}),(0,t.jsx)(c.eb,{value:"referral",children:"Referral"}),(0,t.jsx)(c.eb,{value:"cold_call",children:"Cold Call"}),(0,t.jsx)(c.eb,{value:"email_campaign",children:"Email Campaign"}),(0,t.jsx)(c.eb,{value:"advertisement",children:"Advertisement"}),(0,t.jsx)(c.eb,{value:"walk_in",children:"Walk In"}),(0,t.jsx)(c.eb,{value:"other",children:"Other"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"priority",children:"Priority"}),(0,t.jsxs)(c.l6,{value:z.priority,onValueChange:e=>W(s=>({...s,priority:e})),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"low",children:"Low"}),(0,t.jsx)(c.eb,{value:"medium",children:"Medium"}),(0,t.jsx)(c.eb,{value:"high",children:"High"}),(0,t.jsx)(c.eb,{value:"urgent",children:"Urgent"})]})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Budget Range"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"budgetMin",children:"Minimum Budget"}),(0,t.jsx)(o.p,{id:"budgetMin",type:"number",value:z.budget.min,onChange:e=>W(s=>({...s,budget:{...s.budget,min:e.target.value}})),placeholder:"Enter minimum budget"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"budgetMax",children:"Maximum Budget"}),(0,t.jsx)(o.p,{id:"budgetMax",type:"number",value:z.budget.max,onChange:e=>W(s=>({...s,budget:{...s.budget,max:e.target.value}})),placeholder:"Enter maximum budget"})]})]})]}),es.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Interested Properties"}),(0,t.jsx)("div",{className:"max-h-40 overflow-y-auto space-y-2 border rounded-lg p-3",children:es.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.S,{id:"property-".concat(e.id),checked:z.interestedProperties.includes(e.id),onCheckedChange:()=>er(e.id)}),(0,t.jsxs)(m.J,{htmlFor:"property-".concat(e.id),className:"text-sm cursor-pointer flex-1",children:[e.title||e.name," - ",(0,T.vv)(e.pricePerStock)]})]},e.id))})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Follow-up Schedule"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"nextFollowUp",children:"Next Follow-up Date"}),(0,t.jsx)(o.p,{id:"nextFollowUp",type:"datetime-local",value:z.nextFollowUpDate,onChange:e=>W(s=>({...s,nextFollowUpDate:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"expectedClose",children:"Expected Close Date"}),(0,t.jsx)(o.p,{id:"expectedClose",type:"date",value:z.expectedCloseDate,onChange:e=>W(s=>({...s,expectedCloseDate:e.target.value}))})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Additional Information"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"notes",children:"Notes"}),(0,t.jsx)(x.T,{id:"notes",value:z.notes,onChange:e=>W(s=>({...s,notes:e.target.value})),placeholder:"Enter any additional notes about the lead...",rows:3})]})]}),(0,t.jsxs)(u.Es,{className:"flex space-x-2",children:[(0,t.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>J(!1),disabled:X,children:"Cancel"}),(0,t.jsx)(d.$,{type:"submit",className:"bg-sky-500 hover:bg-sky-600 text-white",disabled:X,children:X?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Creating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Create Lead"]})})]})]})]})})]})}},7456:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(6501).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},8135:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(6501).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},8642:(e,s,a)=>{"use strict";a.d(s,{$V:()=>d,FO:()=>n,Hu:()=>l,OT:()=>P,Pb:()=>r,WD:()=>v,Zu:()=>j,aT:()=>g,cm:()=>b,nK:()=>x,pv:()=>q,ro:()=>f});let t=a(6701).q.injectEndpoints({endpoints:e=>({getDashboardStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getDashboardActivities:e.query({query:e=>({url:"/dashboard/activities",params:e}),providesTags:["Dashboard"]}),getSalesStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getLeads:e.query({query:e=>({url:"/sales/leads",params:e}),providesTags:["Lead"]}),getLeadById:e.query({query:e=>"/sales/leads/".concat(e),providesTags:(e,s,a)=>[{type:"Lead",id:a}]}),createLead:e.mutation({query:e=>({url:"/sales/leads",method:"POST",body:e}),invalidatesTags:["Lead","Dashboard"]}),updateLead:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/sales/leads/".concat(s),method:"PUT",body:a}},invalidatesTags:(e,s,a)=>{let{id:t}=a;return[{type:"Lead",id:t},"Lead","Dashboard"]}}),deleteLead:e.mutation({query:e=>({url:"/sales/leads/".concat(e),method:"DELETE"}),invalidatesTags:["Lead","Dashboard"]}),assignLead:e.mutation({query:e=>{let{leadId:s,salesRepId:a}=e;return{url:"/sales/leads/".concat(s,"/assign"),method:"POST",body:{salesRepId:a}}},invalidatesTags:["Lead"]}),getCustomers:e.query({query:e=>({url:"/sales/customers",params:e}),providesTags:["Customer"]}),getCustomerById:e.query({query:e=>"/sales/customers/".concat(e),providesTags:(e,s,a)=>[{type:"Customer",id:a}]}),createCustomer:e.mutation({query:e=>({url:"/sales/customers",method:"POST",body:e}),invalidatesTags:["Customer","Dashboard"]}),updateCustomer:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/sales/customers/".concat(s),method:"PUT",body:a}},invalidatesTags:(e,s,a)=>{let{id:t}=a;return[{type:"Customer",id:t},"Customer"]}}),getCommissions:e.query({query:e=>({url:"/sales/commissions",params:e}),providesTags:["Commission"]}),getSalesTargets:e.query({query:()=>"/sales/targets",providesTags:["Report"]}),createSalesTarget:e.mutation({query:e=>({url:"/sales/targets",method:"POST",body:e}),invalidatesTags:["Report","Dashboard"]}),updateSalesTarget:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/sales/targets/".concat(s),method:"PUT",body:a}},invalidatesTags:["Report","Dashboard"]}),getFollowUps:e.query({query:e=>({url:"/follow-ups",params:e}),providesTags:["Task"]}),createFollowUp:e.mutation({query:e=>({url:"/follow-ups",method:"POST",body:e}),invalidatesTags:["Task","Dashboard"]}),updateFollowUp:e.mutation({query:e=>{let{id:s,data:a}=e;return{url:"/follow-ups/".concat(s),method:"PUT",body:a}},invalidatesTags:["Task","Dashboard"]}),deleteFollowUp:e.mutation({query:e=>({url:"/follow-ups/".concat(e),method:"DELETE"}),invalidatesTags:["Task","Dashboard"]})})}),{useGetDashboardStatsQuery:r,useGetDashboardActivitiesQuery:l,useGetSalesStatsQuery:i,useGetLeadsQuery:d,useGetLeadByIdQuery:o,useCreateLeadMutation:n,useUpdateLeadMutation:c,useDeleteLeadMutation:u,useAssignLeadMutation:m,useGetCustomersQuery:x,useGetCustomerByIdQuery:h,useCreateCustomerMutation:p,useUpdateCustomerMutation:y,useGetCommissionsQuery:g,useGetSalesTargetsQuery:v,useCreateSalesTargetMutation:b,useUpdateSalesTargetMutation:j,useGetFollowUpsQuery:f,useCreateFollowUpMutation:N,useUpdateFollowUpMutation:w,useDeleteFollowUpMutation:k}=t,{useGetCustomersQuery:q,useGetCommissionsQuery:P}=t},9192:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(6501).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[399,995,713,668,663,75,440,390,110,358],()=>s(3786)),_N_E=e.O()}]);