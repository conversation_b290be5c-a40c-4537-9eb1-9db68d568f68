{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/adminKycApi.ts"], "sourcesContent": ["import { base<PERSON><PERSON>, <PERSON>piR<PERSON>po<PERSON>, BaseQueryParams, createQueryParams } from './baseApi'\n\n// KYC Document Interface\nexport interface KYCDocument {\n  _id: string\n  type: 'identity' | 'address' | 'income' | 'bank' | 'photo' | 'signature' | 'other'\n  subType: string\n  documentNumber?: string\n  issuedBy?: string\n  issuedDate?: string\n  expiryDate?: string\n  status: 'pending' | 'verified' | 'rejected'\n  rejectionReason?: string\n  fileUrl?: string\n  uploadedAt: string\n  verifiedAt?: string\n}\n\n// KYC Status Interface\nexport interface KYCStatus {\n  _id: string\n  userId: {\n    _id: string\n    firstName: string\n    lastName: string\n    email: string\n    phone?: string\n    profileImage?: string\n  }\n  status: 'not_started' | 'pending' | 'approved' | 'rejected' | 'under_review'\n  level: 'basic' | 'advanced' | 'premium'\n  submittedAt?: string\n  reviewedAt?: string\n  reviewedBy?: {\n    _id: string\n    firstName: string\n    lastName: string\n  }\n  rejectionReason?: string\n  documents: KYCDocument[]\n  personalInfo: {\n    nationality?: string\n    placeOfBirth?: string\n    gender?: string\n    maritalStatus?: string\n  }\n  address: {\n    street?: string\n    city?: string\n    state?: string\n    postalCode?: string\n    country?: string\n    addressType?: 'permanent' | 'current' | 'mailing'\n    residenceSince?: string\n  }\n  identityInfo: {\n    aadharNumber?: string\n    panNumber?: string\n    passportNumber?: string\n    drivingLicenseNumber?: string\n  }\n  bankInfo: {\n    accountNumber?: string\n    ifscCode?: string\n    bankName?: string\n    accountType?: string\n    accountHolderName?: string\n  }\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface KYCQueryParams extends BaseQueryParams {\n  status?: string\n  level?: string\n  userId?: string\n}\n\nexport interface KYCApprovalRequest {\n  status: 'approved' | 'rejected'\n  rejectionReason?: string\n  level?: 'basic' | 'advanced' | 'premium'\n  notes?: string\n}\n\nexport const adminKycApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // Get all KYC submissions for admin review\n    getAllKYCSubmissions: builder.query<ApiResponse<{\n      kycs: KYCStatus[]\n      total: number\n      page: number\n      limit: number\n      totalPages: number\n    }>, KYCQueryParams>({\n      query: (params = {}) => ({\n        url: '/admin/kyc',\n        params: createQueryParams(params)\n      }),\n      providesTags: ['KYC'],\n    }),\n\n    // Get KYC by ID for detailed review\n    getKYCById: builder.query<ApiResponse<KYCStatus>, string>({\n      query: (id) => `/admin/kyc/${id}`,\n      providesTags: (_, __, id) => [{ type: 'KYC', id }],\n    }),\n\n    // Get KYC by User ID\n    getKYCByUserId: builder.query<ApiResponse<KYCStatus>, string>({\n      query: (userId) => `/kyc/user/${userId}`,\n      providesTags: (_, __, userId) => [{ type: 'KYC', id: userId }],\n    }),\n\n    // Approve or reject KYC\n    updateKYCStatus: builder.mutation<ApiResponse<KYCStatus>, { id: string; data: KYCApprovalRequest }>({\n      query: ({ id, data }) => ({\n        url: `/kyc/${id}/status`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (_, __, { id }) => [{ type: 'KYC', id }, 'KYC', 'User'],\n    }),\n\n    // Get KYC statistics for admin dashboard\n    getKYCStats: builder.query<ApiResponse<{\n      totalSubmissions: number\n      pendingReview: number\n      approved: number\n      rejected: number\n      underReview: number\n      basicLevel: number\n      advancedLevel: number\n      premiumLevel: number\n      recentSubmissions: Array<{\n        _id: string\n        userId: {\n          firstName: string\n          lastName: string\n          email: string\n        }\n        status: string\n        submittedAt: string\n      }>\n    }>, void>({\n      query: () => '/admin/kyc/stats',\n      providesTags: ['KYC'],\n    }),\n\n    // Bulk approve/reject KYCs\n    bulkUpdateKYCStatus: builder.mutation<ApiResponse<{ updated: number }>, {\n      ids: string[]\n      data: KYCApprovalRequest\n    }>({\n      query: ({ ids, data }) => ({\n        url: '/admin/kyc/bulk-update',\n        method: 'PUT',\n        body: { ids, ...data },\n      }),\n      invalidatesTags: ['KYC', 'User'],\n    }),\n\n    // Get KYC document by ID\n    getKYCDocument: builder.query<ApiResponse<KYCDocument>, string>({\n      query: (documentId) => `/admin/kyc/document/${documentId}`,\n      providesTags: (_, __, id) => [{ type: 'KYC', id }],\n    }),\n\n    // Verify/reject specific document\n    updateDocumentStatus: builder.mutation<ApiResponse<KYCDocument>, {\n      documentId: string\n      status: 'verified' | 'rejected'\n      rejectionReason?: string\n    }>({\n      query: ({ documentId, ...data }) => ({\n        url: `/admin/kyc/document/${documentId}/status`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: ['KYC'],\n    }),\n\n    // Get KYC history/audit trail\n    getKYCHistory: builder.query<ApiResponse<Array<{\n      action: string\n      status: string\n      timestamp: string\n      details?: string\n      performedBy: {\n        _id: string\n        firstName: string\n        lastName: string\n      }\n    }>>, string>({\n      query: (kycId) => `/admin/kyc/${kycId}/history`,\n      providesTags: (_, __, id) => [{ type: 'KYC', id }],\n    }),\n\n    // Download KYC PDF\n    downloadKYCPDF: builder.mutation<Blob, string>({\n      query: (kycId) => ({\n        url: `/kyc/${kycId}/download-pdf`,\n        method: 'GET',\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Export KYC data\n    exportKYCData: builder.mutation<ApiResponse<{ downloadUrl: string }>, {\n      format: 'csv' | 'excel' | 'pdf'\n      filters?: KYCQueryParams\n    }>({\n      query: (data) => ({\n        url: '/admin/kyc/export',\n        method: 'POST',\n        body: data,\n      }),\n    }),\n  }),\n})\n\nexport const {\n  useGetAllKYCSubmissionsQuery,\n  useGetKYCByIdQuery,\n  useGetKYCByUserIdQuery,\n  useUpdateKYCStatusMutation,\n  useDownloadKYCPDFMutation,\n  useGetKYCStatsQuery,\n  useBulkUpdateKYCStatusMutation,\n  useGetKYCDocumentQuery,\n  useUpdateDocumentStatusMutation,\n  useGetKYCHistoryQuery,\n  useExportKYCDataMutation,\n} = adminKycApi\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAqFO,MAAM,cAAc,8HAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACjD,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,2CAA2C;YAC3C,sBAAsB,QAAQ,KAAK,CAMf;gBAClB,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE;oBAC5B,CAAC;gBACD,cAAc;oBAAC;iBAAM;YACvB;YAEA,oCAAoC;YACpC,YAAY,QAAQ,KAAK,CAAiC;gBACxD,OAAO,CAAC,KAAO,CAAC,WAAW,EAAE,IAAI;gBACjC,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;qBAAE;YACpD;YAEA,qBAAqB;YACrB,gBAAgB,QAAQ,KAAK,CAAiC;gBAC5D,OAAO,CAAC,SAAW,CAAC,UAAU,EAAE,QAAQ;gBACxC,cAAc,CAAC,GAAG,IAAI,SAAW;wBAAC;4BAAE,MAAM;4BAAO,IAAI;wBAAO;qBAAE;YAChE;YAEA,wBAAwB;YACxB,iBAAiB,QAAQ,QAAQ,CAAmE;gBAClG,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC;wBACxB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;wBAAG;wBAAO;qBAAO;YAC1E;YAEA,yCAAyC;YACzC,aAAa,QAAQ,KAAK,CAmBhB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAM;YACvB;YAEA,2BAA2B;YAC3B,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;wBACzB,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAK,GAAG,IAAI;wBAAC;oBACvB,CAAC;gBACD,iBAAiB;oBAAC;oBAAO;iBAAO;YAClC;YAEA,yBAAyB;YACzB,gBAAgB,QAAQ,KAAK,CAAmC;gBAC9D,OAAO,CAAC,aAAe,CAAC,oBAAoB,EAAE,YAAY;gBAC1D,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;qBAAE;YACpD;YAEA,kCAAkC;YAClC,sBAAsB,QAAQ,QAAQ,CAInC;gBACD,OAAO,CAAC,EAAE,UAAU,EAAE,GAAG,MAAM,GAAK,CAAC;wBACnC,KAAK,CAAC,oBAAoB,EAAE,WAAW,OAAO,CAAC;wBAC/C,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAM;YAC1B;YAEA,8BAA8B;YAC9B,eAAe,QAAQ,KAAK,CAUf;gBACX,OAAO,CAAC,QAAU,CAAC,WAAW,EAAE,MAAM,QAAQ,CAAC;gBAC/C,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;qBAAE;YACpD;YAEA,mBAAmB;YACnB,gBAAgB,QAAQ,QAAQ,CAAe;gBAC7C,OAAO,CAAC,QAAU,CAAC;wBACjB,KAAK,CAAC,KAAK,EAAE,MAAM,aAAa,CAAC;wBACjC,QAAQ;wBACR,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,kBAAkB;YAClB,eAAe,QAAQ,QAAQ,CAG5B;gBACD,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,4BAA4B,EAC5B,kBAAkB,EAClB,sBAAsB,EACtB,0BAA0B,EAC1B,yBAAyB,EACzB,mBAAmB,EACnB,8BAA8B,EAC9B,sBAAsB,EACtB,+BAA+B,EAC/B,qBAAqB,EACrB,wBAAwB,EACzB,GAAG", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/userDetailsApi.ts"], "sourcesContent": ["import { baseApi } from './baseApi'\n\n// Types for user-specific data\nexport interface UserInvestment {\n  _id: string\n  propertyId: string\n  propertyName: string\n  location: string\n  stocksOwned: number\n  stockPrice: number\n  totalInvestment: number\n  currentValue: number\n  returns: number\n  roi: number\n  purchaseDate: string\n  status: 'active' | 'sold'\n  expectedAnnualReturn: number\n  propertyImage?: string\n}\n\nexport interface UserTransaction {\n  _id: string\n  type: 'deposit' | 'withdrawal' | 'investment' | 'return' | 'refund'\n  amount: number\n  status: 'pending' | 'completed' | 'failed' | 'cancelled'\n  paymentMethod?: string\n  description: string\n  createdAt: string\n  updatedAt: string\n  propertyId?: string\n  propertyName?: string\n  reference?: string\n}\n\nexport interface UserActivity {\n  _id: string\n  action: string\n  entity: string\n  entityId?: string\n  description: string\n  ipAddress?: string\n  userAgent?: string\n  location?: string\n  createdAt: string\n  metadata?: any\n}\n\nexport interface UserStats {\n  totalInvestments: number\n  totalReturns: number\n  activeInvestments: number\n  totalTransactions: number\n  walletBalance: number\n  portfolioValue: number\n  roi: number\n  joinDate: string\n  lastActivity: string\n}\n\n// API endpoints for user details\nexport const userDetailsApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // Get user investments\n    getUserInvestments: builder.query<{\n      success: boolean\n      data: {\n        investments: UserInvestment[]\n        pagination: {\n          currentPage: number\n          totalPages: number\n          totalItems: number\n          itemsPerPage: number\n        }\n      }\n    }, {\n      userId: string\n      page?: number\n      limit?: number\n      status?: string\n      sortBy?: string\n      sortOrder?: 'asc' | 'desc'\n    }>({\n      query: ({ userId, ...params }) => ({\n        url: `/admin/users/${userId}/investments`,\n        params,\n      }),\n      providesTags: (result, error, { userId }) => [\n        { type: 'User', id: userId },\n        'Investment'\n      ],\n    }),\n\n    // Get user transactions\n    getUserTransactions: builder.query<{\n      success: boolean\n      data: {\n        transactions: UserTransaction[]\n        pagination: {\n          currentPage: number\n          totalPages: number\n          totalItems: number\n          itemsPerPage: number\n        }\n      }\n    }, {\n      userId: string\n      page?: number\n      limit?: number\n      type?: string\n      status?: string\n      startDate?: string\n      endDate?: string\n      sortBy?: string\n      sortOrder?: 'asc' | 'desc'\n    }>({\n      query: ({ userId, ...params }) => ({\n        url: `/admin/users/${userId}/transactions`,\n        params,\n      }),\n      providesTags: (result, error, { userId }) => [\n        { type: 'User', id: userId },\n        'Transaction'\n      ],\n    }),\n\n    // Get user activity log\n    getUserActivity: builder.query<{\n      success: boolean\n      data: {\n        activities: UserActivity[]\n        pagination: {\n          currentPage: number\n          totalPages: number\n          totalItems: number\n          itemsPerPage: number\n        }\n      }\n    }, {\n      userId: string\n      page?: number\n      limit?: number\n      action?: string\n      entity?: string\n      startDate?: string\n      endDate?: string\n      sortBy?: string\n      sortOrder?: 'asc' | 'desc'\n    }>({\n      query: ({ userId, ...params }) => ({\n        url: `/admin/users/${userId}/activity`,\n        params,\n      }),\n      providesTags: (result, error, { userId }) => [\n        { type: 'User', id: userId },\n        'Activity'\n      ],\n    }),\n\n    // Get user statistics\n    getUserStats: builder.query<{\n      success: boolean\n      data: UserStats\n    }, string>({\n      query: (userId) => `/admin/users/${userId}/stats`,\n      providesTags: (result, error, userId) => [\n        { type: 'User', id: userId },\n        'UserStats'\n      ],\n    }),\n\n    // Get user portfolio performance\n    getUserPortfolioPerformance: builder.query<{\n      success: boolean\n      data: {\n        chartData: Array<{\n          date: string\n          value: number\n          invested: number\n          returns: number\n        }>\n        summary: {\n          totalInvested: number\n          currentValue: number\n          totalReturns: number\n          roi: number\n          bestPerforming: UserInvestment\n          worstPerforming: UserInvestment\n        }\n      }\n    }, {\n      userId: string\n      period?: '1M' | '3M' | '6M' | '1Y' | 'ALL'\n    }>({\n      query: ({ userId, period = '1Y' }) => ({\n        url: `/admin/users/${userId}/portfolio-performance`,\n        params: { period },\n      }),\n      providesTags: (result, error, { userId }) => [\n        { type: 'User', id: userId },\n        'Portfolio'\n      ],\n    }),\n  }),\n})\n\nexport const {\n  useGetUserInvestmentsQuery,\n  useGetUserTransactionsQuery,\n  useGetUserActivityQuery,\n  useGetUserStatsQuery,\n  useGetUserPortfolioPerformanceQuery,\n} = userDetailsApi\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AA4DO,MAAM,iBAAiB,8HAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACpD,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,uBAAuB;YACvB,oBAAoB,QAAQ,KAAK,CAkB9B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,QAAQ,GAAK,CAAC;wBACjC,KAAK,CAAC,aAAa,EAAE,OAAO,YAAY,CAAC;wBACzC;oBACF,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC3C;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,KAAK,CAqB/B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,QAAQ,GAAK,CAAC;wBACjC,KAAK,CAAC,aAAa,EAAE,OAAO,aAAa,CAAC;wBAC1C;oBACF,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC3C;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,wBAAwB;YACxB,iBAAiB,QAAQ,KAAK,CAqB3B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,QAAQ,GAAK,CAAC;wBACjC,KAAK,CAAC,aAAa,EAAE,OAAO,SAAS,CAAC;wBACtC;oBACF,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC3C;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,sBAAsB;YACtB,cAAc,QAAQ,KAAK,CAGhB;gBACT,OAAO,CAAC,SAAW,CAAC,aAAa,EAAE,OAAO,MAAM,CAAC;gBACjD,cAAc,CAAC,QAAQ,OAAO,SAAW;wBACvC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,iCAAiC;YACjC,6BAA6B,QAAQ,KAAK,CAqBvC;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,SAAS,IAAI,EAAE,GAAK,CAAC;wBACrC,KAAK,CAAC,aAAa,EAAE,OAAO,sBAAsB,CAAC;wBACnD,QAAQ;4BAAE;wBAAO;oBACnB,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC3C;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,0BAA0B,EAC1B,2BAA2B,EAC3B,uBAAuB,EACvB,oBAAoB,EACpB,mCAAmC,EACpC,GAAG", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/salesApi.ts"], "sourcesContent": ["import { baseApi } from './baseApi'\n\n// Types based on server models\nexport interface SalesTeamMember {\n  _id: string\n  firstName: string\n  lastName: string\n  email: string\n  phone: string\n  role: 'sales' | 'sales_manager' | 'team_lead'\n  status: 'active' | 'inactive' | 'on_leave'\n  territory?: string\n  team?: string\n  manager?: string\n  joinDate: string\n  profileImage?: string\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface SalesTask {\n  _id: string\n  title: string\n  description?: string\n  type: 'call' | 'email' | 'meeting' | 'follow_up' | 'demo' | 'proposal'\n  priority: 'low' | 'medium' | 'high' | 'urgent'\n  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'\n  dueDate: string\n  dueTime?: string\n  estimatedDuration?: number\n  actualDuration?: number\n  assignedTo: string\n  createdBy: string\n  relatedTo?: {\n    type: 'lead' | 'customer' | 'property'\n    id: string\n    name: string\n  }\n  tags: string[]\n  notes?: string\n  completedAt?: string\n  reminders: Array<{\n    time: string\n    sent: boolean\n  }>\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface SalesTarget {\n  _id: string\n  salesRepId: string\n  period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'\n  targetAmount: number\n  achievedAmount: number\n  targetLeads: number\n  achievedLeads: number\n  targetDeals: number\n  achievedDeals: number\n  startDate: string\n  endDate: string\n  status: 'active' | 'completed' | 'paused' | 'cancelled'\n  notes?: string\n  createdBy: string\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Commission {\n  _id: string\n  transactionId: string\n  commissionType: 'referral' | 'sales' | 'bonus'\n  earnerId: string\n  customerId: string\n  stockTransactionId: string\n  propertyId: string\n  stockQuantity: number\n  commissionRate?: number\n  commissionPerStock?: number\n  calculationMethod: 'percentage' | 'fixed_per_stock' | 'fixed_amount'\n  investmentAmount: number\n  commissionAmount: number\n  status: 'pending' | 'approved' | 'paid' | 'rejected'\n  rejectionReason?: string\n  approvedBy?: string\n  approvedAt?: string\n  paidAt?: string\n  notes?: string\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface SalesCalendarEvent {\n  _id: string\n  title: string\n  description?: string\n  type: 'meeting' | 'call' | 'demo' | 'follow_up' | 'presentation' | 'site_visit'\n  startDate: string\n  startTime: string\n  endDate: string\n  endTime: string\n  location?: string\n  isAllDay: boolean\n  attendees: Array<{\n    userId: string\n    name: string\n    email: string\n    status: 'pending' | 'accepted' | 'declined' | 'tentative'\n  }>\n  relatedTo?: {\n    type: 'lead' | 'customer' | 'property' | 'deal'\n    id: string\n    name: string\n  }\n  meetingLink?: string\n  notes?: string\n  reminders: Array<{\n    time: string\n    sent: boolean\n  }>\n  createdBy: string\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface SalesStats {\n  totalSales: number\n  totalRevenue: number\n  totalCommission: number\n  conversionRate: number\n  totalLeads: number\n  convertedLeads: number\n  activeTargets: number\n  completedTargets: number\n  pendingTasks: number\n  completedTasks: number\n  teamMembers: number\n  activeMembers: number\n}\n\nexport interface ApiResponse<T> {\n  success: boolean\n  message: string\n  data: T\n  pagination?: {\n    currentPage: number\n    totalPages: number\n    totalItems: number\n    itemsPerPage: number\n  }\n}\n\nexport const salesApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    // Sales Stats\n    getSalesStats: builder.query<ApiResponse<SalesStats>, void>({\n      query: () => '/sales/stats',\n      providesTags: ['Sales'],\n    }),\n\n    // Sales Team Management\n    getSalesTeam: builder.query<ApiResponse<SalesTeamMember[]>, {\n      page?: number\n      limit?: number\n      status?: string\n      role?: string\n      search?: string\n    }>({\n      query: (params) => ({\n        url: '/sales/team',\n        params\n      }),\n      providesTags: ['SalesTeam'],\n    }),\n\n    getSalesTeamMember: builder.query<ApiResponse<SalesTeamMember>, string>({\n      query: (id) => `/users/${id}`,\n      providesTags: (result, error, id) => [{ type: 'SalesTeam', id }],\n    }),\n\n    createSalesTeamMember: builder.mutation<ApiResponse<SalesTeamMember>, Partial<SalesTeamMember>>({\n      query: (data) => ({\n        url: '/users',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['SalesTeam'],\n    }),\n\n    updateSalesTeamMember: builder.mutation<ApiResponse<SalesTeamMember>, {\n      id: string\n      data: Partial<SalesTeamMember>\n    }>({\n      query: ({ id, data }) => ({\n        url: `/users/${id}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [\n        { type: 'SalesTeam', id },\n        'SalesTeam'\n      ],\n    }),\n\n    deleteSalesTeamMember: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/users/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['SalesTeam'],\n    }),\n\n    // Sales Tasks Management\n    getSalesTasks: builder.query<ApiResponse<SalesTask[]>, {\n      page?: number\n      limit?: number\n      status?: string\n      priority?: string\n      type?: string\n      assignedTo?: string\n      search?: string\n    }>({\n      query: (params) => ({\n        url: '/sales/tasks',\n        params,\n      }),\n      providesTags: ['SalesTask'],\n    }),\n\n    getSalesTask: builder.query<ApiResponse<SalesTask>, string>({\n      query: (id) => `/sales/tasks/${id}`,\n      providesTags: (result, error, id) => [{ type: 'SalesTask', id }],\n    }),\n\n    createSalesTask: builder.mutation<ApiResponse<SalesTask>, Partial<SalesTask>>({\n      query: (data) => ({\n        url: '/sales/tasks',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['SalesTask'],\n    }),\n\n    updateSalesTask: builder.mutation<ApiResponse<SalesTask>, {\n      id: string\n      data: Partial<SalesTask>\n    }>({\n      query: ({ id, data }) => ({\n        url: `/sales/tasks/${id}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [\n        { type: 'SalesTask', id },\n        'SalesTask'\n      ],\n    }),\n\n    completeSalesTask: builder.mutation<ApiResponse<SalesTask>, string>({\n      query: (id) => ({\n        url: `/sales/tasks/${id}/complete`,\n        method: 'POST',\n      }),\n      invalidatesTags: (result, error, id) => [\n        { type: 'SalesTask', id },\n        'SalesTask'\n      ],\n    }),\n\n    deleteSalesTask: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/sales/tasks/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['SalesTask'],\n    }),\n\n    // Sales Targets Management\n    getSalesTargets: builder.query<ApiResponse<SalesTarget[]>, {\n      page?: number\n      limit?: number\n      status?: string\n      period?: string\n      salesRepId?: string\n    }>({\n      query: (params) => ({\n        url: '/sales/targets',\n        params,\n      }),\n      providesTags: ['SalesTarget'],\n    }),\n\n    createSalesTarget: builder.mutation<ApiResponse<SalesTarget>, Partial<SalesTarget>>({\n      query: (data) => ({\n        url: '/sales/targets',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['SalesTarget'],\n    }),\n\n    updateSalesTarget: builder.mutation<ApiResponse<SalesTarget>, {\n      id: string\n      data: Partial<SalesTarget>\n    }>({\n      query: ({ id, data }) => ({\n        url: `/sales/targets/${id}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [\n        { type: 'SalesTarget', id },\n        'SalesTarget'\n      ],\n    }),\n\n    // Commissions Management\n    getCommissions: builder.query<ApiResponse<Commission[]>, {\n      page?: number\n      limit?: number\n      status?: string\n      earnerId?: string\n      startDate?: string\n      endDate?: string\n    }>({\n      query: (params) => ({\n        url: '/sales/commissions',\n        params,\n      }),\n      providesTags: ['Commission'],\n    }),\n\n    approveCommission: builder.mutation<ApiResponse<Commission>, string>({\n      query: (id) => ({\n        url: `/sales/commissions/${id}/approve`,\n        method: 'POST',\n      }),\n      invalidatesTags: ['Commission'],\n    }),\n\n    rejectCommission: builder.mutation<ApiResponse<Commission>, {\n      id: string\n      reason: string\n    }>({\n      query: ({ id, reason }) => ({\n        url: `/sales/commissions/${id}/reject`,\n        method: 'POST',\n        body: { reason },\n      }),\n      invalidatesTags: ['Commission'],\n    }),\n\n    // Sales Calendar Management\n    getCalendarEvents: builder.query<ApiResponse<SalesCalendarEvent[]>, {\n      startDate: string\n      endDate: string\n      type?: string\n      attendeeId?: string\n    }>({\n      query: (params) => ({\n        url: '/sales/calendar/events',\n        params,\n      }),\n      providesTags: ['Calendar'],\n    }),\n\n    createCalendarEvent: builder.mutation<ApiResponse<SalesCalendarEvent>, Partial<SalesCalendarEvent>>({\n      query: (data) => ({\n        url: '/sales/calendar/events',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Calendar'],\n    }),\n\n    updateCalendarEvent: builder.mutation<ApiResponse<SalesCalendarEvent>, {\n      id: string\n      data: Partial<SalesCalendarEvent>\n    }>({\n      query: ({ id, data }) => ({\n        url: `/sales/calendar/events/${id}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [\n        { type: 'Calendar', id },\n        'Calendar'\n      ],\n    }),\n\n    deleteCalendarEvent: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/sales/calendar/events/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Calendar'],\n    }),\n  }),\n})\n\nexport const {\n  useGetSalesStatsQuery,\n  useGetSalesTeamQuery,\n  useGetSalesTeamMemberQuery,\n  useCreateSalesTeamMemberMutation,\n  useUpdateSalesTeamMemberMutation,\n  useDeleteSalesTeamMemberMutation,\n  useGetSalesTasksQuery,\n  useGetSalesTaskQuery,\n  useCreateSalesTaskMutation,\n  useUpdateSalesTaskMutation,\n  useCompleteSalesTaskMutation,\n  useDeleteSalesTaskMutation,\n  useGetSalesTargetsQuery,\n  useCreateSalesTargetMutation,\n  useUpdateSalesTargetMutation,\n  useGetCommissionsQuery,\n  useApproveCommissionMutation,\n  useRejectCommissionMutation,\n  useGetCalendarEventsQuery,\n  useCreateCalendarEventMutation,\n  useUpdateCalendarEventMutation,\n  useDeleteCalendarEventMutation,\n} = salesApi\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAwJO,MAAM,WAAW,8HAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAC9C,WAAW,CAAC,UAAY,CAAC;YACvB,cAAc;YACd,eAAe,QAAQ,KAAK,CAAgC;gBAC1D,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAQ;YACzB;YAEA,wBAAwB;YACxB,cAAc,QAAQ,KAAK,CAMxB;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAY;YAC7B;YAEA,oBAAoB,QAAQ,KAAK,CAAuC;gBACtE,OAAO,CAAC,KAAO,CAAC,OAAO,EAAE,IAAI;gBAC7B,cAAc,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAa;wBAAG;qBAAE;YAClE;YAEA,uBAAuB,QAAQ,QAAQ,CAAyD;gBAC9F,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAY;YAChC;YAEA,uBAAuB,QAAQ,QAAQ,CAGpC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,OAAO,EAAE,IAAI;wBACnB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAa;wBAAG;wBACxB;qBACD;YACH;YAEA,uBAAuB,QAAQ,QAAQ,CAA4B;gBACjE,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,OAAO,EAAE,IAAI;wBACnB,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAY;YAChC;YAEA,yBAAyB;YACzB,eAAe,QAAQ,KAAK,CAQzB;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAY;YAC7B;YAEA,cAAc,QAAQ,KAAK,CAAiC;gBAC1D,OAAO,CAAC,KAAO,CAAC,aAAa,EAAE,IAAI;gBACnC,cAAc,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAa;wBAAG;qBAAE;YAClE;YAEA,iBAAiB,QAAQ,QAAQ,CAA6C;gBAC5E,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAY;YAChC;YAEA,iBAAiB,QAAQ,QAAQ,CAG9B;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,aAAa,EAAE,IAAI;wBACzB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAa;wBAAG;wBACxB;qBACD;YACH;YAEA,mBAAmB,QAAQ,QAAQ,CAAiC;gBAClE,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,aAAa,EAAE,GAAG,SAAS,CAAC;wBAClC,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,KAAO;wBACtC;4BAAE,MAAM;4BAAa;wBAAG;wBACxB;qBACD;YACH;YAEA,iBAAiB,QAAQ,QAAQ,CAA4B;gBAC3D,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,aAAa,EAAE,IAAI;wBACzB,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAY;YAChC;YAEA,2BAA2B;YAC3B,iBAAiB,QAAQ,KAAK,CAM3B;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAc;YAC/B;YAEA,mBAAmB,QAAQ,QAAQ,CAAiD;gBAClF,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAc;YAClC;YAEA,mBAAmB,QAAQ,QAAQ,CAGhC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,eAAe,EAAE,IAAI;wBAC3B,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAe;wBAAG;wBAC1B;qBACD;YACH;YAEA,yBAAyB;YACzB,gBAAgB,QAAQ,KAAK,CAO1B;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAa;YAC9B;YAEA,mBAAmB,QAAQ,QAAQ,CAAkC;gBACnE,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,mBAAmB,EAAE,GAAG,QAAQ,CAAC;wBACvC,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAa;YACjC;YAEA,kBAAkB,QAAQ,QAAQ,CAG/B;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAK,CAAC;wBAC1B,KAAK,CAAC,mBAAmB,EAAE,GAAG,OAAO,CAAC;wBACtC,QAAQ;wBACR,MAAM;4BAAE;wBAAO;oBACjB,CAAC;gBACD,iBAAiB;oBAAC;iBAAa;YACjC;YAEA,4BAA4B;YAC5B,mBAAmB,QAAQ,KAAK,CAK7B;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAW;YAC5B;YAEA,qBAAqB,QAAQ,QAAQ,CAA+D;gBAClG,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAW;YAC/B;YAEA,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,uBAAuB,EAAE,IAAI;wBACnC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAY;wBAAG;wBACvB;qBACD;YACH;YAEA,qBAAqB,QAAQ,QAAQ,CAA4B;gBAC/D,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,uBAAuB,EAAE,IAAI;wBACnC,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAW;YAC/B;QACF,CAAC;AACH;AAEO,MAAM,EACX,qBAAqB,EACrB,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,qBAAqB,EACrB,oBAAoB,EACpB,0BAA0B,EAC1B,0BAA0B,EAC1B,4BAA4B,EAC5B,0BAA0B,EAC1B,uBAAuB,EACvB,4BAA4B,EAC5B,4BAA4B,EAC5B,sBAAsB,EACtB,4BAA4B,EAC5B,2BAA2B,EAC3B,yBAAyB,EACzB,8BAA8B,EAC9B,8BAA8B,EAC9B,8BAA8B,EAC/B,GAAG", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/index.ts"], "sourcesContent": ["// Export all API hooks and types for easy importing\n\n// Base API\nexport { baseApi } from './baseApi'\nexport type { ApiResponse, BaseQueryParams } from './baseApi'\n\n// Users API\nexport * from './usersApi'\n\n// Properties API - selective exports to avoid conflicts\nexport {\n  useGetPropertiesQuery,\n  useGetPropertiesStatsQuery, // Fixed: this exists in propertiesApi\n  useGetPropertyByIdQuery,\n  useCreatePropertyMutation,\n  useUpdatePropertyMutation,\n  useDeletePropertyMutation,\n  useBulkUpdatePropertiesMutation,\n  useExportPropertiesMutation,\n  useFeaturePropertyMutation,\n  useGetPresignedUrlMutation,\n  useConfirmFileUploadMutation,\n  useDeletePropertyFileMutation,\n  useAddPropertyOwnerMutation,\n  useRemovePropertyOwnerMutation,\n  useGetPropertyOwnersQuery,\n  useCreatePropertyStocksMutation,\n  useLazyGetPropertyByIdQuery,\n  // Note: useGetPropertyStocksQuery from propertiesApi is excluded to avoid conflict\n} from './propertiesApi'\n\n// Property Stocks API - selective exports to avoid conflicts\nexport {\n  useCreateStockMutation,\n  useGetPropertyStocksQuery, // This is the main one we want from propertyStocksApi\n  useGetPropertyStockByIdQuery,\n  useUpdatePropertyStockMutation,\n  useDeletePropertyStockMutation,\n  useGetStockTransactionsQuery,\n  useGetStockInvestorsQuery,\n} from './propertyStocksApi'\n\n// Admin KYC API\nexport * from './adminKycApi'\n\n// User Details API\nexport * from './userDetailsApi'\n\n// Leads API\nexport * from './leadsApi'\n\n// Dashboard API\nexport * from './dashboardApi'\n\n// Finance API\nexport * from './financeApi'\n\n// Stocks API - selective exports to avoid conflicts\nexport {\n  useGetStocksQuery,\n  useUpdateStockReturnsMutation,\n  useTransferStockMutation,\n  useSellStockMutation\n} from './stocksApi'\n\n// Property Owners API - skip to avoid conflicts with propertiesApi\n// export * from './propertyOwnersApi'\n\n// Support API\nexport * from './supportApi'\n\n// Settings API\nexport * from './settingsApi'\n\n// Sales API\nexport * from './salesApi'\n\n// Analytics API\nexport * from './analyticsApi'\n\n// Re-export common types\nexport type {\n  User,\n  Property,\n  Lead,\n  UserRole,\n  UserStatus,\n  PropertyStatus,\n  LeadStatus,\n  TransactionStatus,\n  TransactionType,\n} from '@/types'\n\n// Re-export Transaction from financeApi to avoid conflicts\nexport type { Transaction } from './financeApi'\n"], "names": [], "mappings": "AAAA,oDAAoD;AAEpD,WAAW;;AACX;AAGA,YAAY;AACZ;AAEA,wDAAwD;AACxD;AAqBA,6DAA6D;AAC7D;AAUA,gBAAgB;AAChB;AAEA,mBAAmB;AACnB;AAEA,YAAY;AACZ;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AAEA,oDAAoD;AACpD;AAOA,mEAAmE;AACnE,sCAAsC;AAEtC,cAAc;AACd;AAEA,eAAe;AACf;AAEA,YAAY;AACZ;AAEA,gBAAgB;AAChB", "debugId": null}}, {"offset": {"line": 609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format currency\nexport function formatCurrency(amount: number, currency = \"₹\"): string {\n  return `${currency}${amount.toLocaleString('en-IN', { \n    minimumFractionDigits: 2, \n    maximumFractionDigits: 2 \n  })}`\n}\n\n// Format date\nexport function formatDate(date: string | Date, format = \"dd/MM/yyyy\"): string {\n  const d = new Date(date)\n  \n  if (format === \"dd/MM/yyyy\") {\n    return d.toLocaleDateString('en-GB')\n  }\n  \n  if (format === \"relative\") {\n    const now = new Date()\n    const diffInMs = now.getTime() - d.getTime()\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n    \n    if (diffInDays === 0) return \"Today\"\n    if (diffInDays === 1) return \"Yesterday\"\n    if (diffInDays < 7) return `${diffInDays} days ago`\n    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`\n    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`\n    return `${Math.floor(diffInDays / 365)} years ago`\n  }\n  \n  return d.toLocaleDateString()\n}\n\n// Truncate text\nexport function truncateText(text: string, length = 50): string {\n  if (text.length <= length) return text\n  return text.substring(0, length) + \"...\"\n}\n\n// Generate initials\nexport function getInitials(firstName?: string, lastName?: string, email?: string): string {\n  if (firstName && lastName) {\n    return `${firstName[0]}${lastName[0]}`.toUpperCase()\n  }\n  \n  if (firstName) {\n    return firstName.substring(0, 2).toUpperCase()\n  }\n  \n  if (email) {\n    return email.substring(0, 2).toUpperCase()\n  }\n  \n  return \"U\"\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Sleep function\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n// Generate random ID\nexport function generateId(prefix = \"\"): string {\n  const timestamp = Date.now().toString(36)\n  const random = Math.random().toString(36).substring(2, 8)\n  return `${prefix}${timestamp}${random}`.toUpperCase()\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// Validate phone\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\n// Format phone number\nexport function formatPhone(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '')\n  \n  if (cleaned.length === 10) {\n    return `+91 ${cleaned.substring(0, 5)} ${cleaned.substring(5)}`\n  }\n  \n  if (cleaned.length === 12 && cleaned.startsWith('91')) {\n    return `+${cleaned.substring(0, 2)} ${cleaned.substring(2, 7)} ${cleaned.substring(7)}`\n  }\n  \n  return phone\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0\n  return Math.round((value / total) * 100)\n}\n\n// Get status color\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    active: \"text-green-600 bg-green-100\",\n    inactive: \"text-gray-600 bg-gray-100\",\n    pending: \"text-yellow-600 bg-yellow-100\",\n    suspended: \"text-red-600 bg-red-100\",\n    completed: \"text-green-600 bg-green-100\",\n    failed: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    new: \"text-blue-600 bg-blue-100\",\n    contacted: \"text-purple-600 bg-purple-100\",\n    qualified: \"text-indigo-600 bg-indigo-100\",\n    converted: \"text-green-600 bg-green-100\",\n    lost: \"text-red-600 bg-red-100\",\n  }\n  \n  return statusColors[status.toLowerCase()] || \"text-gray-600 bg-gray-100\"\n}\n\n// Copy to clipboard\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error)\n    return false\n  }\n}\n\n// Download file\nexport function downloadFile(data: any, filename: string, type = 'application/json'): void {\n  const blob = new Blob([typeof data === 'string' ? data : JSON.stringify(data, null, 2)], { type })\n  const url = URL.createObjectURL(blob)\n  const link = document.createElement('a')\n  link.href = url\n  link.download = filename\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  URL.revokeObjectURL(url)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Get file extension\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)\n}\n\n// Check if mobile device\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// Scroll to element\nexport function scrollToElement(elementId: string, offset = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({ top, behavior: 'smooth' })\n  }\n}\n\n// Local storage helpers\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null\n    \n    try {\n      const item = localStorage.getItem(key)\n      return item ? JSON.parse(item) : defaultValue || null\n    } catch (error) {\n      console.error(`Error getting item from localStorage:`, error)\n      return defaultValue || null\n    }\n  },\n  \n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.setItem(key, JSON.stringify(value))\n    } catch (error) {\n      console.error(`Error setting item in localStorage:`, error)\n    }\n  },\n  \n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.removeItem(key)\n    } catch (error) {\n      console.error(`Error removing item from localStorage:`, error)\n    }\n  },\n  \n  clear: (): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.clear()\n    } catch (error) {\n      console.error(`Error clearing localStorage:`, error)\n    }\n  }\n}\n\n// Format time\nexport function formatTime(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAW,GAAG;IAC3D,OAAO,GAAG,WAAW,OAAO,cAAc,CAAC,SAAS;QAClD,uBAAuB;QACvB,uBAAuB;IACzB,IAAI;AACN;AAGO,SAAS,WAAW,IAAmB,EAAE,SAAS,YAAY;IACnE,MAAM,IAAI,IAAI,KAAK;IAEnB,IAAI,WAAW,cAAc;QAC3B,OAAO,EAAE,kBAAkB,CAAC;IAC9B;IAEA,IAAI,WAAW,YAAY;QACzB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,EAAE,OAAO;QAC1C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,SAAS,CAAC;QACnD,IAAI,aAAa,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;QACrE,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,WAAW,CAAC;QACxE,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,UAAU,CAAC;IACpD;IAEA,OAAO,EAAE,kBAAkB;AAC7B;AAGO,SAAS,aAAa,IAAY,EAAE,SAAS,EAAE;IACpD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,UAAU;AACrC;AAGO,SAAS,YAAY,SAAkB,EAAE,QAAiB,EAAE,KAAc;IAC/E,IAAI,aAAa,UAAU;QACzB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW;IACpD;IAEA,IAAI,WAAW;QACb,OAAO,UAAU,SAAS,CAAC,GAAG,GAAG,WAAW;IAC9C;IAEA,IAAI,OAAO;QACT,OAAO,MAAM,SAAS,CAAC,GAAG,GAAG,WAAW;IAC1C;IAEA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,WAAW,SAAS,EAAE;IACpC,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,OAAO,GAAG,SAAS,YAAY,QAAQ,CAAC,WAAW;AACrD;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,YAAY,KAAa;IACvC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,CAAC,IAAI,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACjE;IAEA,IAAI,QAAQ,MAAM,KAAK,MAAM,QAAQ,UAAU,CAAC,OAAO;QACrD,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACzF;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;QACR,WAAW;QACX,KAAK;QACL,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;IACR;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,SAAS,aAAa,IAAS,EAAE,QAAgB,EAAE,OAAO,kBAAkB;IACjF,MAAM,OAAO,IAAI,KAAK;QAAC,OAAO,SAAS,WAAW,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM;KAAG,EAAE;QAAE;IAAK;IAChG,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAGO,SAAS;IACd,wCAAmC,OAAO;;AAE5C;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAS,CAAC;IAC3D,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YAAE;YAAK,UAAU;QAAS;IAC5C;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,wCAAmC,OAAO,gBAAgB;;IAS5D;IAEA,KAAK,CAAI,KAAa;QACpB,wCAAmC;;IAOrC;IAEA,QAAQ,CAAC;QACP,wCAAmC;;IAOrC;IAEA,OAAO;QACL,wCAAmC;;IAOrC;AACF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 818, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"card-base text-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6BACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"btn-primary\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600\",\n        outline: \"btn-outline\",\n        secondary: \"btn-secondary\",\n        ghost: \"hover:bg-primary-100 text-primary\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,mQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6WAAC,8QAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,8QAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,8QAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,6QAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,6QAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,6QAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,6QAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/logo.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'full' | 'icon' | 'text'\n  className?: string\n}\n\nconst Logo: React.FC<LogoProps> = ({ \n  size = 'md', \n  variant = 'full', \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'h-6 w-6',\n    md: 'h-8 w-8', \n    lg: 'h-10 w-10',\n    xl: 'h-12 w-12'\n  }\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl', \n    xl: 'text-3xl'\n  }\n\n  const LogoIcon = () => (\n    <div className={`${sizeClasses[size]} bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center shadow-lg ${className}`}>\n      <div className=\"relative\">\n        {/* S */}\n        <div className=\"absolute -left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5 ml-1\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n        </div>\n        \n        {/* G */}\n        <div className=\"absolute left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-4 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"absolute right-0 top-2 w-1 h-2 bg-white rounded-full\"></div>\n          <div className=\"absolute right-0 top-3 w-1.5 h-1 bg-white rounded-full\"></div>\n        </div>\n        \n        {/* M */}\n        <div className=\"absolute left-4 top-0\">\n          <div className=\"w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-0.5 top-1 w-1 h-1 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1 top-0 w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1.5 top-0 w-1 h-5 bg-white rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const LogoText = () => (\n    <span className={`font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent ${textSizeClasses[size]} ${className}`}>\n      SGM\n    </span>\n  )\n\n  if (variant === 'icon') {\n    return <LogoIcon />\n  }\n\n  if (variant === 'text') {\n    return <LogoText />\n  }\n\n  return (\n    <div className={`flex items-center gap-3 ${className}`}>\n      <LogoIcon />\n      <LogoText />\n    </div>\n  )\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;;AAQA,MAAM,OAA4B,CAAC,EACjC,OAAO,IAAI,EACX,UAAU,MAAM,EAChB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,WAAW,kBACf,6WAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,mGAAmG,EAAE,WAAW;sBACnJ,cAAA,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,WAAW,kBACf,6WAAC;YAAK,WAAW,CAAC,mFAAmF,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;sBAAE;;;;;;IAK/I,IAAI,YAAY,QAAQ;QACtB,qBAAO,6WAAC;;;;;IACV;IAEA,IAAI,YAAY,QAAQ;QACtB,qBAAO,6WAAC;;;;;IACV;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,6WAAC;;;;;0BACD,6WAAC;;;;;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 1259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAppSelector } from '@/store'\nimport { selectUser } from '@/store/slices/authSlice'\nimport { UserRole } from '@/types'\nimport { cn } from '@/lib/utils'\nimport Logo from '@/components/ui/logo'\nimport {\n  LayoutDashboard,\n  Users,\n  Building,\n  TrendingUp,\n  Target,\n  DollarSign,\n  Headphones,\n  Settings,\n  Plus,\n  UserPlus,\n  UserCheck,\n  BarChart3,\n  Shield,\n  Bell,\n  PieChart,\n  CheckSquare,\n  CalendarDays,\n  UserCog,\n  Award,\n  Minus,\n  Receipt,\n  CreditCard\n} from 'lucide-react'\n\ninterface SidebarProps {\n  collapsed?: boolean\n  onToggle?: () => void\n}\n\nexport default function Sidebar({ collapsed = false, onToggle }: SidebarProps) {\n  const pathname = usePathname()\n  const user = useAppSelector((state) => selectUser(state as any))\n\n  console.log('Sidebar rendering...', { collapsed, pathname, user })\n\n  const menuSections = [\n    {\n      title: \"Dashboard\",\n      items: [\n        {\n          id: \"main-dashboard\",\n          label: \"Overview\",\n          icon: LayoutDashboard,\n          href: \"/dashboard\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES, UserRole.USER]\n        }\n        \n      ]\n    },\n    {\n      title: \"User Management\",\n      items: [\n        {\n          id: \"users-overview\",\n          label: \"All Users\",\n          icon: Users,\n          href: \"/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-management-comprehensive\",\n          label: \"User Management\",\n          icon: Users,\n          href: \"/user-management\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-user\",\n          label: \"Add User\",\n          icon: UserPlus,\n          href: \"/users/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"role-management\",\n          label: \"Role Management\",\n          icon: UserCheck,\n          href: \"/users/roles\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Property Management\",\n      items: [\n        {\n          id: \"properties-overview\",\n          label: \"All Properties\",\n          icon: Building,\n          href: \"/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-property\",\n          label: \"Add Property\",\n          icon: Plus,\n          href: \"/properties/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-owners\",\n          label: \"Property Owners\",\n          icon: UserCheck,\n          href: \"/property-owners\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Stock Investments\",\n      items: [\n        {\n          id: \"stocks-overview\",\n          label: \"All Stocks\",\n          icon: TrendingUp,\n          href: \"/stocks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"create-stock\",\n          label: \"Create Stock\",\n          icon: Plus,\n          href: \"/stocks/create\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Lead Management\",\n      items: [\n        {\n          id: \"leads-overview\",\n          label: \"Lead Management\",\n          icon: Target,\n          href: \"/leads\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES]\n        },\n        {\n          id: \"sales-analytics\",\n          label: \"Sales Analytics\",\n          icon: BarChart3,\n          href: \"/sales-analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Sales Management\",\n      items: [\n        {\n          id: \"sales-team\",\n          label: \"Sales Team\",\n          icon: UserCog,\n          href: \"/sales-team\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-tasks\",\n          label: \"Sales Tasks\",\n          icon: CheckSquare,\n          href: \"/sales-tasks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-calendar\",\n          label: \"Sales Calendar\",\n          icon: CalendarDays,\n          href: \"/sales-calendar\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-targets\",\n          label: \"Sales Targets\",\n          icon: Award,\n          href: \"/sales-targets\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"commissions\",\n          label: \"Commissions\",\n          icon: DollarSign,\n          href: \"/commissions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Financial Management\",\n      items: [\n        {\n          id: \"finance-overview\",\n          label: \"Financial Management\",\n          icon: DollarSign,\n          href: \"/finance\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-funds\",\n          label: \"Add Funds\",\n          icon: Plus,\n          href: \"/add-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"deduct-funds\",\n          label: \"Deduct Funds\",\n          icon: Minus,\n          href: \"/deduct-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"admin-transactions\",\n          label: \"All Transactions\",\n          icon: Receipt,\n          href: \"/admin-transactions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"withdrawal-requests\",\n          label: \"Withdrawal Requests\",\n          icon: CreditCard,\n          href: \"/withdrawal-requests\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Support Management\",\n      items: [\n        {\n          id: \"support-dashboard\",\n          label: \"Support Management\",\n          icon: Headphones,\n          href: \"/support\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"System & Settings\",\n      items: [\n        {\n          id: \"system-settings\",\n          label: \"Settings Management\",\n          icon: Settings,\n          href: \"/settings\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Reports & Analytics\",\n      items: [\n        {\n          id: \"analytics-dashboard\",\n          label: \"Analytics Dashboard\",\n          icon: BarChart3,\n          href: \"/analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-reports\",\n          label: \"User Reports\",\n          icon: Users,\n          href: \"/reports/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-reports\",\n          label: \"Property Reports\",\n          icon: Building,\n          href: \"/reports/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"financial-reports\",\n          label: \"Financial Reports\",\n          icon: DollarSign,\n          href: \"/reports/financial\",\n          roles: [UserRole.ADMIN]\n        },\n       \n      ]\n    }\n  ]\n\n  // Filter menu sections based on user role\n  const userRole = user?.role as UserRole || UserRole.USER\n  const filteredSections = menuSections.map(section => ({\n    ...section,\n    items: section.items.filter(item => item.roles.includes(userRole))\n  })).filter(section => section.items.length > 0)\n\n  // Debug logging\n  console.log('User:', user)\n  console.log('User Role:', user?.role)\n  console.log('UserRole enum:', userRole)\n  console.log('Menu Sections:', menuSections.length)\n  console.log('Filtered Sections:', filteredSections.length)\n\n  // Use filtered sections based on user role\n  const sectionsToShow = filteredSections\n\n  const isActive = (href: string) => {\n    if (!pathname) return false\n    if (href === '/dashboard') {\n      return pathname === '/dashboard'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <div className={cn(\n      \"h-screen bg-white border-r border-sky-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg\",\n      collapsed ? \"w-20\" : \"w-72\"\n    )}>\n      {/* Logo */}\n      <div className=\"flex items-center space-x-3 p-4 border-b border-sky-200 flex-shrink-0 bg-gradient-to-r from-sky-50 to-sky-100\">\n        <Logo size={collapsed ? \"lg\" : \"xl\"} variant={collapsed ? \"icon\" : \"full\"} />\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-300 scrollbar-track-sky-100\">\n        <div className=\"space-y-2\">\n          {sectionsToShow.length === 0 ? (\n            <div className=\"text-center text-red-500 p-4\">\n              <p>No menu items found!</p>\n              <p>User: {user?.firstName || 'Not logged in'}</p>\n              <p>Role: {user?.role || 'No role'}</p>\n            </div>\n          ) : (\n            sectionsToShow.map((section) => (\n              <div key={section.title} className=\"space-y-1 mb-6\">\n                {!collapsed && (\n                  <h3 className=\"px-3 py-2 text-xs font-semibold text-sky-600 uppercase tracking-wider\">\n                    {section.title}\n                  </h3>\n                )}\n\n                {section.items.map((item) => (\n                  <Link\n                    key={item.id}\n                    href={item.href || '#'}\n                    className={cn(\n                      \"flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200\",\n                      isActive(item.href || '')\n                        ? \"bg-sky-600 text-white shadow-md\"\n                        : \"text-gray-700 hover:text-sky-600 hover:bg-sky-50\"\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                    {!collapsed && (\n                      <span className=\"flex-1 truncate\">{item.label}</span>\n                    )}\n                  </Link>\n                ))}\n              </div>\n            ))\n          )}\n        </div>\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-sky-200 bg-gradient-to-r from-sky-50 to-sky-100\">\n        {!collapsed && (\n          <div className=\"text-xs text-sky-600 text-center font-medium\">\n            © 2025 SGM. All rights reserved.\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;AAwCe,SAAS,QAAQ,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAgB;IAC3E,MAAM,WAAW,CAAA,GAAA,iQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,QAAU,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;IAElD,QAAQ,GAAG,CAAC,wBAAwB;QAAE;QAAW;QAAU;IAAK;IAEhE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,gTAAA,CAAA,kBAAe;oBACrB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,IAAI;qBAAC;gBAC3E;aAED;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,oSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,oSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,0RAAA,CAAA,SAAM;oBACZ,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBAC5D;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,gSAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,+SAAA,CAAA,cAAW;oBACjB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,0SAAA,CAAA,eAAY;oBAClB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,4RAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aAED;QACH;KACD;IAED,0CAA0C;IAC1C,MAAM,WAAW,MAAM,QAAoB,qHAAA,CAAA,WAAQ,CAAC,IAAI;IACxD,MAAM,mBAAmB,aAAa,GAAG,CAAC,CAAA,UAAW,CAAC;YACpD,GAAG,OAAO;YACV,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;QAC1D,CAAC,GAAG,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,CAAC,MAAM,GAAG;IAE7C,gBAAgB;IAChB,QAAQ,GAAG,CAAC,SAAS;IACrB,QAAQ,GAAG,CAAC,cAAc,MAAM;IAChC,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,kBAAkB,aAAa,MAAM;IACjD,QAAQ,GAAG,CAAC,sBAAsB,iBAAiB,MAAM;IAEzD,2CAA2C;IAC3C,MAAM,iBAAiB;IAEvB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,6GACA,YAAY,SAAS;;0BAGrB,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,gIAAA,CAAA,UAAI;oBAAC,MAAM,YAAY,OAAO;oBAAM,SAAS,YAAY,SAAS;;;;;;;;;;;0BAIrE,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;8BACZ,eAAe,MAAM,KAAK,kBACzB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;0CAAE;;;;;;0CACH,6WAAC;;oCAAE;oCAAO,MAAM,aAAa;;;;;;;0CAC7B,6WAAC;;oCAAE;oCAAO,MAAM,QAAQ;;;;;;;;;;;;+BAG1B,eAAe,GAAG,CAAC,CAAC,wBAClB,6WAAC;4BAAwB,WAAU;;gCAChC,CAAC,2BACA,6WAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAIjB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6WAAC,2RAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI,IAAI;wCACnB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,SAAS,KAAK,IAAI,IAAI,MAClB,oCACA;;0DAGN,6WAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,CAAC,2BACA,6WAAC;gDAAK,WAAU;0DAAmB,KAAK,KAAK;;;;;;;uCAX1C,KAAK,EAAE;;;;;;2BATR,QAAQ,KAAK;;;;;;;;;;;;;;;0BA+B/B,6WAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,6WAAC;oBAAI,WAAU;8BAA+C;;;;;;;;;;;;;;;;;AAOxE", "debugId": null}}, {"offset": {"line": 1794, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector, useAppDispatch } from '@/store'\nimport { selectUser, logoutAsync } from '@/store/slices/authSlice'\nimport { Button } from '@/components/ui/button'\nimport Sidebar from './Sidebar'\nimport { Menu, Bell, Search, LogOut, User, Settings, HelpCircle, ChevronDown } from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n  const [userMenuOpen, setUserMenuOpen] = useState(false)\n  const router = useRouter()\n  const dispatch = useAppDispatch()\n  const user = useAppSelector(selectUser)\n\n  const handleLogout = async () => {\n    await dispatch(logoutAsync())\n    router.push('/login')\n  }\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed)\n  }\n\n  return (\n    <div className=\"h-screen bg-white overflow-hidden\">\n      <div className=\"flex h-full\">\n        {/* Sidebar */}\n        <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col h-full overflow-hidden\">\n          {/* Header */}\n          <header className=\"bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg border-b border-sky-700 flex-shrink-0 z-40\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex justify-between items-center py-4\">\n                {/* Left side */}\n                <div className=\"flex items-center space-x-4\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={toggleSidebar}\n                    className=\"lg:hidden hover:bg-white/20 text-white\"\n                  >\n                    <Menu className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Search Bar */}\n                  <div className=\"hidden md:flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2.5 min-w-[350px] border border-white/30\">\n                    <Search className=\"h-4 w-4 text-white/70\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search users, properties, transactions...\"\n                      className=\"bg-transparent border-none outline-none flex-1 text-sm placeholder:text-white/70 text-white\"\n                    />\n                    <kbd className=\"hidden lg:inline-flex items-center px-2 py-1 text-xs font-medium text-sky-600 bg-white rounded border border-white/30\">\n                      ⌘K\n                    </kbd>\n                  </div>\n                </div>\n\n                {/* Right side */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Quick Actions */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"hover:bg-white/20 text-white\">\n                    <HelpCircle className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Notifications */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"relative hover:bg-white/20 text-white\">\n                    <Bell className=\"h-5 w-5\" />\n                    <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-yellow-500 text-black text-xs rounded-full flex items-center justify-center font-semibold\">\n                      5\n                    </span>\n                  </Button>\n\n                  {/* User Menu */}\n                  <div className=\"relative\">\n                    <Button\n                      variant=\"ghost\"\n                      onClick={() => setUserMenuOpen(!userMenuOpen)}\n                      className=\"flex items-center space-x-3 px-3 py-2 hover:bg-white/20 rounded-lg text-white\"\n                    >\n                      <div className=\"hidden sm:block text-right\">\n                        <p className=\"text-sm font-medium text-white\">\n                          {user?.firstName} {user?.lastName}\n                        </p>\n                        <div className=\"flex items-center justify-end space-x-1\">\n                          <div className=\"bg-yellow-500 px-2 py-0.5 rounded-full\">\n                            <span className=\"text-xs font-medium text-black capitalize\">\n                              {user?.role}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"h-10 w-10 bg-white/20 rounded-full flex items-center justify-center shadow-sm border border-white/30\">\n                        <User className=\"h-5 w-5 text-white\" />\n                      </div>\n\n                      <ChevronDown className=\"h-4 w-4 text-white/70\" />\n                    </Button>\n\n                    {/* User Dropdown Menu */}\n                    {userMenuOpen && (\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sky-200 py-2 z-50\">\n                        <div className=\"px-4 py-2 border-b border-sky-100\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {user?.firstName} {user?.lastName}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                        </div>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <User className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Profile\n                        </Button>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <Settings className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Settings\n                        </Button>\n\n                        <div className=\"border-t border-sky-100 mt-2 pt-2\">\n                          <Button\n                            variant=\"ghost\"\n                            className=\"w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                            onClick={handleLogout}\n                          >\n                            <LogOut className=\"h-4 w-4 mr-3\" />\n                            Logout\n                          </Button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Main Content Area */}\n          <main className=\"flex-1 overflow-y-auto bg-gray-50\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAce,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,mIAAA,CAAA,aAAU;IAEtC,MAAM,eAAe;QACnB,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC,uIAAA,CAAA,UAAO;oBAAC,WAAW;oBAAkB,UAAU;;;;;;8BAGhD,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAO,WAAU;sCAChB,cAAA,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDAEb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAIlB,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,0RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6WAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6WAAC;4DAAI,WAAU;sEAAwH;;;;;;;;;;;;;;;;;;sDAO3I,6WAAC;4CAAI,WAAU;;8DAEb,6WAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;8DAC5C,cAAA,6WAAC,kTAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAIxB,6WAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;;sEAC5C,6WAAC,sRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6WAAC;4DAAK,WAAU;sEAAgI;;;;;;;;;;;;8DAMlJ,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;;8EAEV,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,6WAAC;4EAAI,WAAU;sFACb,cAAA,6WAAC;gFAAI,WAAU;0FACb,cAAA,6WAAC;oFAAK,WAAU;8FACb,MAAM;;;;;;;;;;;;;;;;;;;;;;8EAMf,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGlB,6WAAC,wSAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;wDAIxB,8BACC,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,6WAAC;4EAAE,WAAU;sFAAyB,MAAM;;;;;;;;;;;;8EAG9C,6WAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,6WAAC,sRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIhD,6WAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,6WAAC,8RAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIpD,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,WAAU;wEACV,SAAS;;0FAET,6WAAC,8RAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAarD,6WAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 2201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState, useMemo } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector, useAppDispatch } from '@/store'\nimport { selectIsAuthenticated, checkAuthAsync, selectUser } from '@/store/slices/authSlice'\nimport {\n  useGetDashboardStatsQuery,\n  useGetRecentActivitiesQuery,\n  useGetUsersStatsQuery,\n  useGetPropertiesStatsQuery,\n  useGetTopPropertiesQuery,\n  useGetRecentTransactionsQuery,\n  useGetNotificationsQuery,\n  useGetQuickActionsQuery\n} from '@/store/api'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { cn } from '@/lib/utils'\nimport DashboardLayout from '@/components/layout/DashboardLayout'\nimport {\n  Users,\n  Building,\n  DollarSign,\n  RefreshCw,\n  Loader2,\n  TrendingUp,\n  Activity,\n  Clock,\n  CheckCircle,\n  ArrowUp,\n  ArrowDown,\n  Eye,\n  UserPlus,\n  UserCheck,\n  Home,\n  Target,\n  Wallet,\n  CreditCard,\n  Star,\n  AlertTriangle,\n  Bell,\n  Calendar,\n  Download,\n  Zap,\n  BarChart3,\n  FileText,\n  Settings\n} from 'lucide-react'\n\nexport default function DashboardPage() {\n  const router = useRouter()\n  const dispatch = useAppDispatch()\n  const isAuthenticated = useAppSelector(selectIsAuthenticated)\n  const user = useAppSelector(selectUser)\n  const [timeRange, setTimeRange] = useState('30d')\n  const [activeTab, setActiveTab] = useState('overview')\n  const [refreshing, setRefreshing] = useState(false)\n\n  // RTK Query hooks for real data\n  const {\n    data: dashboardStats,\n    isLoading: dashboardLoading,\n    error: dashboardError,\n    refetch: refetchDashboard,\n    isFetching: dashboardFetching\n  } = useGetDashboardStatsQuery({ period: timeRange })\n\n  const {\n    data: usersStats,\n    isLoading: usersStatsLoading\n  } = useGetUsersStatsQuery()\n\n  const {\n    data: propertiesStats,\n    isLoading: propertiesStatsLoading\n  } = useGetPropertiesStatsQuery()\n\n  const {\n    data: recentActivities,\n    isLoading: activitiesLoading\n  } = useGetRecentActivitiesQuery({ limit: 10 })\n\n  const {\n    data: topProperties,\n    isLoading: topPropertiesLoading\n  } = useGetTopPropertiesQuery({ limit: 5 })\n\n  const {\n    data: recentTransactions,\n    isLoading: transactionsLoading\n  } = useGetRecentTransactionsQuery({ limit: 10 })\n\n  const {\n    data: notifications,\n    isLoading: notificationsLoading\n  } = useGetNotificationsQuery({ limit: 5 })\n\n  const {\n    data: quickActions,\n    isLoading: quickActionsLoading\n  } = useGetQuickActionsQuery()\n\n  // REMOVED: Auth check that was causing infinite loops\n  // Let middleware handle authentication instead\n\n  // Memoized data processing with fallbacks\n  const stats = useMemo(() => {\n    const data = dashboardStats?.data;\n    const usersData = usersStats?.data;\n    const propertiesData = propertiesStats?.data;\n    const activitiesData = recentActivities?.data || [];\n    const transactionsData = recentTransactions?.data || [];\n    const quickActionsData = quickActions?.data;\n\n    return {\n      // User metrics from real API\n      totalUsers: data?.totalUsers || usersData?.totalUsers || 0,\n      activeUsers: data?.activeUsers || usersData?.activeUsers || 0,\n      newUsersThisMonth: data?.newUsersThisMonth || usersData?.newThisMonth || 0,\n      pendingKyc: usersData?.pendingKyc || 0,\n      userGrowth: data?.userGrowth || 0,\n      \n      // Property metrics from real API\n      totalProperties: data?.totalProperties || propertiesData?.totalProperties || 0,\n      activeProperties: data?.activeProperties || propertiesData?.activeProperties || 0,\n      soldOutProperties: data?.soldOutProperties || propertiesData?.soldOut || 0,\n      featuredProperties: data?.featuredProperties || propertiesData?.featuredProperties || 0,\n      totalPropertyValue: propertiesData?.totalValue || 0,\n      averagePropertyPrice: propertiesData?.averagePrice || 0,\n      propertyGrowth: data?.propertyGrowth || 0,\n      \n      // Investment & Revenue metrics from real API\n      totalInvestments: data?.totalInvestments || 0,\n      totalInvestment: data?.totalInvestment || 0,\n      totalRevenue: data?.totalRevenue || 0,\n      revenueGrowth: data?.revenueGrowth || 0,\n      investmentGrowth: data?.investmentGrowth || 0,\n      averageInvestment: data?.averageInvestment || 0,\n      \n      // Lead metrics from real API\n      activeLeads: data?.activeLeads || 0,\n      totalLeads: data?.totalLeads || 0,\n      convertedLeads: data?.convertedLeads || 0,\n      leadGrowth: data?.leadGrowth || 0,\n      \n      // Transaction metrics from real API\n      pendingTransactions: data?.pendingTransactions || 0,\n      totalTransactions: data?.totalTransactions || transactionsData.length || 0,\n      completedTransactions: data?.completedTransactions || 0,\n      transactionGrowth: data?.transactionGrowth || 0,\n      \n      // Quick actions from real API\n      pendingApprovals: quickActionsData?.pendingApprovals || 0,\n      newLeads: quickActionsData?.newLeads || 0,\n      expiringSoon: quickActionsData?.expiringSoon || 0,\n      \n      // Activity metrics\n      recentActivitiesCount: activitiesData.length || 0,\n      \n      // Additional metrics for dashboard\n      monthlyRevenue: data?.monthlyRevenue || (data?.totalRevenue ? data.totalRevenue * 0.08 : 0),\n      activeInvestments: data?.activeInvestments || Math.floor((data?.totalInvestments || 0) * 0.85),\n      maturedInvestments: data?.maturedInvestments || Math.floor((data?.totalInvestments || 0) * 0.15),\n      \n      // Calculated metrics\n      totalValue: (propertiesData?.totalValue || 0) + (data?.totalRevenue || 0),\n      leadConversionRate: data?.convertedLeads && data?.totalLeads \n        ? ((data.convertedLeads / data.totalLeads) * 100).toFixed(1)\n        : 0,\n      growthTrend: (data?.revenueGrowth ?? 0) > 0 ? 'up' : (data?.revenueGrowth ?? 0) < 0 ? 'down' : 'stable',\n    };\n  }, [dashboardStats, usersStats, propertiesStats, recentActivities, recentTransactions, quickActions])\n\n  const activities = useMemo(() => {\n    try {\n      const responseData = recentActivities?.data;\n      if (responseData && typeof responseData === 'object' && 'data' in responseData) {\n        const innerData = responseData.data;\n        return Array.isArray(innerData) ? innerData : [];\n      }\n      return Array.isArray(responseData) ? responseData : [];\n    } catch (error) {\n      console.error('Error processing activities data:', error);\n      return [];\n    }\n  }, [recentActivities])\n\n  const alerts = useMemo(() => {\n    try {\n      const responseData = notifications?.data;\n      if (responseData && typeof responseData === 'object' && 'data' in responseData) {\n        const innerData = responseData.data;\n        return Array.isArray(innerData) ? innerData : [];\n      }\n      return Array.isArray(responseData) ? responseData : [];\n    } catch (error) {\n      console.error('Error processing notifications data:', error);\n      return [];\n    }\n  }, [notifications])\n\n  const isLoading = dashboardLoading || usersStatsLoading || propertiesStatsLoading || activitiesLoading || topPropertiesLoading || transactionsLoading || notificationsLoading || quickActionsLoading\n  const hasError = dashboardError\n\n  const handleRefresh = async () => {\n    setRefreshing(true)\n    try {\n      await refetchDashboard()\n      setTimeout(() => setRefreshing(false), 1000)\n    } catch (error) {\n      console.error('Refresh failed:', error)\n      setRefreshing(false)\n    }\n  }\n\n  const handleTimeRangeChange = (newRange: string) => {\n    setTimeRange(newRange)\n    handleRefresh()\n  }\n\n  // Utility function to safely render values and avoid rendering objects\n  const safeRender = (value: any): string => {\n    if (value === null || value === undefined) return '0'\n    if (typeof value === 'object') {\n      if (value.value !== undefined) return String(value.value)\n      return '0'\n    }\n    return String(value)\n  }\n\n  // Format numbers for display\n  const formatNumber = (num: any): string => {\n    const value = Number(safeRender(num)) || 0\n    if (value >= 10000000) return `₹${(value / 10000000).toFixed(1)}Cr`\n    if (value >= 100000) return `₹${(value / 100000).toFixed(1)}L`\n    if (value >= 1000) return `₹${(value / 1000).toFixed(1)}K`\n    return `₹${value.toLocaleString()}`\n  }\n\n  const getGreeting = () => {\n    const hour = new Date().getHours()\n    if (hour < 12) return 'Good Morning'\n    if (hour < 17) return 'Good Afternoon'\n    return 'Good Evening'\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n          <h1 className=\"text-2xl font-bold mb-2\">Loading Dashboard...</h1>\n          <p className=\"text-gray-600\">Please wait while we prepare your dashboard</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"p-6 space-y-6\">\n        {/* Header - Same style as leads page */}\n        <div className=\"bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg p-6 text-white\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-3xl font-bold mb-2 flex items-center gap-3\">\n                <BarChart3 className=\"h-8 w-8\" />\n                {getGreeting()}, {user?.firstName || 'Admin'}! 👋\n              </h1>\n              <p className=\"text-sky-100\">Complete business overview with real-time analytics, insights, and performance metrics</p>\n            </div>\n\n            <div className=\"flex items-center gap-3\">\n              <div className=\"flex items-center space-x-2 bg-white/20 hover:bg-white/30 rounded-lg px-3 py-2 border border-white/30 backdrop-blur-sm\">\n                <Calendar className=\"h-4 w-4 text-white\" />\n                <select\n                  value={timeRange}\n                  onChange={(e) => handleTimeRangeChange(e.target.value)}\n                  className=\"bg-transparent border-none outline-none text-sm font-medium text-white\"\n                >\n                  <option value=\"24h\" className=\"text-gray-800\">Last 24 Hours</option>\n                  <option value=\"7d\" className=\"text-gray-800\">Last 7 Days</option>\n                  <option value=\"30d\" className=\"text-gray-800\">Last 30 Days</option>\n                  <option value=\"90d\" className=\"text-gray-800\">Last 3 Months</option>\n                  <option value=\"1y\" className=\"text-gray-800\">Last Year</option>\n                </select>\n              </div>\n\n              <Button\n                variant=\"secondary\"\n                onClick={handleRefresh}\n                disabled={isLoading || dashboardFetching}\n                className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n              >\n                <RefreshCw className={cn(\"h-4 w-4 mr-2\", (isLoading || dashboardFetching) && \"animate-spin\")} />\n                Refresh\n              </Button>\n\n              <Button\n                className=\"bg-yellow-500 hover:bg-yellow-600 text-black font-semibold\"\n              >\n                <Download className=\"h-4 w-4 mr-2\" />\n                Export Report\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Dashboard Tabs */}\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-4\">\n            <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n            <TabsTrigger value=\"analytics\">Analytics</TabsTrigger>\n            <TabsTrigger value=\"properties\">Properties</TabsTrigger>\n            <TabsTrigger value=\"users\">Users</TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"overview\" className=\"space-y-6 mt-6\">\n            {/* Advanced Business Metrics Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {/* Total Users */}\n              <Card className=\"border-sky-200 hover:shadow-lg transition-all duration-200\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Total Users</p>\n                      <p className=\"text-3xl font-bold text-gray-900\">{safeRender(stats?.totalUsers)}</p>\n                      <p className=\"text-xs text-green-600 mt-1 flex items-center\">\n                        <TrendingUp className=\"h-3 w-3 mr-1\" />\n                        {stats?.userGrowth ? `+${stats.userGrowth.toFixed(1)}%` : '+12%'} from last month\n                      </p>\n                    </div>\n                    <div className=\"p-3 rounded-full bg-sky-100\">\n                      <Users className=\"h-6 w-6 text-sky-600\" />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Total Investment */}\n              <Card className=\"border-green-200 hover:shadow-lg transition-all duration-200\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Total Investment</p>\n                      <p className=\"text-3xl font-bold text-gray-900\">\n                        ₹{((Number(safeRender(stats?.totalInvestments || stats?.totalInvestment)) || 0) / 10000000).toFixed(1)}Cr\n                      </p>\n                      <p className=\"text-xs text-green-600 mt-1 flex items-center\">\n                        <TrendingUp className=\"h-3 w-3 mr-1\" />\n                        {stats?.investmentGrowth ? `+${stats.investmentGrowth.toFixed(1)}%` : '+15%'} from last month\n                      </p>\n                    </div>\n                    <div className=\"p-3 rounded-full bg-green-100\">\n                      <DollarSign className=\"h-6 w-6 text-green-600\" />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Active Properties */}\n              <Card className=\"border-purple-200 hover:shadow-lg transition-all duration-200\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Active Properties</p>\n                      <p className=\"text-3xl font-bold text-gray-900\">\n                        {safeRender(stats?.activeProperties || stats?.totalProperties)}\n                      </p>\n                      <p className=\"text-xs text-green-600 mt-1 flex items-center\">\n                        <TrendingUp className=\"h-3 w-3 mr-1\" />\n                        {stats?.propertyGrowth ? `+${stats.propertyGrowth.toFixed(1)}%` : '+8%'} from last month\n                      </p>\n                    </div>\n                    <div className=\"p-3 rounded-full bg-purple-100\">\n                      <Building className=\"h-6 w-6 text-purple-600\" />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Total Revenue */}\n              <Card className=\"border-orange-200 hover:shadow-lg transition-all duration-200\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Total Revenue</p>\n                      <p className=\"text-3xl font-bold text-gray-900\">\n                        ₹{((Number(safeRender(stats?.totalRevenue)) || 0) / 10000000).toFixed(1)}Cr\n                      </p>\n                      <p className=\"text-xs text-green-600 mt-1 flex items-center\">\n                        <TrendingUp className=\"h-3 w-3 mr-1\" />\n                        {stats?.revenueGrowth ? `+${stats.revenueGrowth.toFixed(1)}%` : '+22%'} from last month\n                      </p>\n                    </div>\n                    <div className=\"p-3 rounded-full bg-orange-100\">\n                      <Target className=\"h-6 w-6 text-orange-600\" />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Performance Indicators */}\n            <Card className=\"bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2 text-blue-800\">\n                  <Activity className=\"h-5 w-5\" />\n                  Performance Indicators\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\">\n                  <div className=\"text-center\">\n                    <div className=\"h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3\">\n                      <Wallet className=\"h-8 w-8 text-blue-600\" />\n                    </div>\n                    <p className=\"text-2xl font-bold text-blue-900\">{safeRender(stats?.activeLeads || stats?.totalLeads)}</p>\n                    <p className=\"text-sm text-blue-600 font-medium\">Active Leads</p>\n                    <p className=\"text-xs text-green-600 mt-1\">+5% this week</p>\n                  </div>\n\n                  <div className=\"text-center\">\n                    <div className=\"h-16 w-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3\">\n                      <CreditCard className=\"h-8 w-8 text-orange-600\" />\n                    </div>\n                    <p className=\"text-2xl font-bold text-orange-900\">{safeRender(stats?.pendingTransactions)}</p>\n                    <p className=\"text-sm text-orange-600 font-medium\">Pending Transactions</p>\n                    <p className=\"text-xs text-yellow-600 mt-1\">Needs attention</p>\n                  </div>\n\n                  <div className=\"text-center\">\n                    <div className=\"h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3\">\n                      <CheckCircle className=\"h-8 w-8 text-green-600\" />\n                    </div>\n                    <p className=\"text-2xl font-bold text-green-900\">{safeRender(stats?.activeUsers)}</p>\n                    <p className=\"text-sm text-green-600 font-medium\">KYC Approved</p>\n                    <p className=\"text-xs text-green-600 mt-1\">+12% this month</p>\n                  </div>\n\n                  <div className=\"text-center\">\n                    <div className=\"h-16 w-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3\">\n                      <Star className=\"h-8 w-8 text-purple-600\" />\n                    </div>\n                    <p className=\"text-2xl font-bold text-purple-900\">{safeRender(stats?.featuredProperties)}</p>\n                    <p className=\"text-sm text-purple-600 font-medium\">Featured Properties</p>\n                    <p className=\"text-xs text-green-600 mt-1\">High demand</p>\n                  </div>\n\n                  <div className=\"text-center\">\n                    <div className=\"h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3\">\n                      <AlertTriangle className=\"h-8 w-8 text-red-600\" />\n                    </div>\n                    <p className=\"text-2xl font-bold text-red-900\">{safeRender(stats?.pendingKyc)}</p>\n                    <p className=\"text-sm text-red-600 font-medium\">Pending KYC</p>\n                    <p className=\"text-xs text-red-600 mt-1\">Requires review</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Quick Actions Hub */}\n            <Card className=\"bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2 text-gray-800\">\n                  <Zap className=\"h-6 w-6 text-yellow-500\" />\n                  Quick Actions Hub\n                </CardTitle>\n                <p className=\"text-sm text-gray-600 mt-1\">Access key features and manage your business efficiently</p>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4\">\n                  <Card className=\"hover:shadow-lg transition-all duration-300 cursor-pointer group border-blue-200 hover:border-blue-400\" onClick={() => router.push('/users')}>\n                    <CardContent className=\"p-4 text-center\">\n                      <div className=\"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors\">\n                        <Users className=\"h-6 w-6 text-blue-600\" />\n                      </div>\n                      <p className=\"text-sm font-semibold text-blue-900\">Manage Users</p>\n                      <p className=\"text-xs text-blue-600 mt-1\">{safeRender(stats?.totalUsers)} total</p>\n                    </CardContent>\n                  </Card>\n\n                  <Card className=\"hover:shadow-lg transition-all duration-300 cursor-pointer group border-green-200 hover:border-green-400\" onClick={() => router.push('/properties')}>\n                    <CardContent className=\"p-4 text-center\">\n                      <div className=\"h-12 w-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-green-200 transition-colors\">\n                        <Building className=\"h-6 w-6 text-green-600\" />\n                      </div>\n                      <p className=\"text-sm font-semibold text-green-900\">Properties</p>\n                      <p className=\"text-xs text-green-600 mt-1\">{safeRender(stats?.totalProperties)} active</p>\n                    </CardContent>\n                  </Card>\n\n                  <Card className=\"hover:shadow-lg transition-all duration-300 cursor-pointer group border-purple-200 hover:border-purple-400\" onClick={() => router.push('/leads')}>\n                    <CardContent className=\"p-4 text-center\">\n                      <div className=\"h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-200 transition-colors\">\n                        <Target className=\"h-6 w-6 text-purple-600\" />\n                      </div>\n                      <p className=\"text-sm font-semibold text-purple-900\">Leads</p>\n                      <p className=\"text-xs text-purple-600 mt-1\">{safeRender(stats?.totalLeads)} leads</p>\n                    </CardContent>\n                  </Card>\n\n                  <Card className=\"hover:shadow-lg transition-all duration-300 cursor-pointer group border-orange-200 hover:border-orange-400\" onClick={() => router.push('/transactions')}>\n                    <CardContent className=\"p-4 text-center\">\n                      <div className=\"h-12 w-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-orange-200 transition-colors\">\n                        <CreditCard className=\"h-6 w-6 text-orange-600\" />\n                      </div>\n                      <p className=\"text-sm font-semibold text-orange-900\">Transactions</p>\n                      <p className=\"text-xs text-orange-600 mt-1\">{safeRender(stats?.totalTransactions)} total</p>\n                    </CardContent>\n                  </Card>\n\n                  <Card className=\"hover:shadow-lg transition-all duration-300 cursor-pointer group border-red-200 hover:border-red-400\" onClick={() => router.push('/reports')}>\n                    <CardContent className=\"p-4 text-center\">\n                      <div className=\"h-12 w-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-red-200 transition-colors\">\n                        <FileText className=\"h-6 w-6 text-red-600\" />\n                      </div>\n                      <p className=\"text-sm font-semibold text-red-900\">Reports</p>\n                      <p className=\"text-xs text-red-600 mt-1\">Analytics</p>\n                    </CardContent>\n                  </Card>\n\n                  <Card className=\"hover:shadow-lg transition-all duration-300 cursor-pointer group border-indigo-200 hover:border-indigo-400\" onClick={() => router.push('/settings')}>\n                    <CardContent className=\"p-4 text-center\">\n                      <div className=\"h-12 w-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-indigo-200 transition-colors\">\n                        <Settings className=\"h-6 w-6 text-indigo-600\" />\n                      </div>\n                      <p className=\"text-sm font-semibold text-indigo-900\">Settings</p>\n                      <p className=\"text-xs text-indigo-600 mt-1\">Configure</p>\n                    </CardContent>\n                  </Card>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Recent Activities & Alerts */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              {/* Recent Activities */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center space-x-2\">\n                    <Activity className=\"h-5 w-5 text-primary\" />\n                    <span>Recent Activities</span>\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  {activitiesLoading ? (\n                    <div className=\"flex items-center justify-center py-8\">\n                      <Loader2 className=\"h-6 w-6 animate-spin text-primary\" />\n                    </div>\n                  ) : activities.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {activities.slice(0, 5).map((activity: any, index: number) => (\n                        <div key={index} className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n                          <div className=\"h-8 w-8 bg-primary/10 rounded-full flex items-center justify-center\">\n                            <Activity className=\"h-4 w-4 text-primary\" />\n                          </div>\n                          <div className=\"flex-1\">\n                            <p className=\"text-sm font-medium\">{activity.title || activity.description || 'Activity'}</p>\n                            <p className=\"text-xs text-muted-foreground\">\n                              {activity.timestamp ? new Date(activity.timestamp).toLocaleString() : 'Just now'}\n                            </p>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-8\">\n                      <Activity className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                      <p className=\"text-muted-foreground\">No recent activities</p>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n\n              {/* Alerts & Notifications */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center space-x-2\">\n                    <Bell className=\"h-5 w-5 text-primary\" />\n                    <span>Alerts & Notifications</span>\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  {notificationsLoading ? (\n                    <div className=\"flex items-center justify-center py-8\">\n                      <Loader2 className=\"h-6 w-6 animate-spin text-primary\" />\n                    </div>\n                  ) : alerts.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {alerts.slice(0, 5).map((alert: any, index: number) => (\n                        <div key={index} className=\"flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200\">\n                          <div className=\"h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center\">\n                            <Bell className=\"h-4 w-4 text-yellow-600\" />\n                          </div>\n                          <div className=\"flex-1\">\n                            <p className=\"text-sm font-medium\">{alert.title || alert.message || 'Notification'}</p>\n                            <p className=\"text-xs text-muted-foreground\">\n                              {alert.timestamp ? new Date(alert.timestamp).toLocaleString() : 'Just now'}\n                            </p>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-8\">\n                      <Bell className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                      <p className=\"text-muted-foreground\">No new notifications</p>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n            </div>\n          </TabsContent>\n\n          {/* Analytics Tab */}\n          <TabsContent value=\"analytics\" className=\"space-y-6 mt-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <BarChart3 className=\"h-5 w-5 text-primary\" />\n                  <span>Analytics Overview</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                  <div className=\"text-center p-6 bg-blue-50 rounded-lg\">\n                    <TrendingUp className=\"h-12 w-12 text-blue-600 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-semibold text-blue-900 mb-2\">Revenue Growth</h3>\n                    <p className=\"text-3xl font-bold text-blue-600\">\n                      {stats?.revenueGrowth ? `${stats.revenueGrowth.toFixed(1)}%` : '0%'}\n                    </p>\n                    <p className=\"text-sm text-blue-600 mt-2\">This month</p>\n                  </div>\n\n                  <div className=\"text-center p-6 bg-green-50 rounded-lg\">\n                    <Users className=\"h-12 w-12 text-green-600 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-semibold text-green-900 mb-2\">User Growth</h3>\n                    <p className=\"text-3xl font-bold text-green-600\">\n                      {stats?.userGrowth ? `${stats.userGrowth.toFixed(1)}%` : '0%'}\n                    </p>\n                    <p className=\"text-sm text-green-600 mt-2\">This month</p>\n                  </div>\n\n                  <div className=\"text-center p-6 bg-purple-50 rounded-lg\">\n                    <Building className=\"h-12 w-12 text-purple-600 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-semibold text-purple-900 mb-2\">Property Growth</h3>\n                    <p className=\"text-3xl font-bold text-purple-600\">\n                      {stats?.propertyGrowth ? `${stats.propertyGrowth.toFixed(1)}%` : '0%'}\n                    </p>\n                    <p className=\"text-sm text-purple-600 mt-2\">This month</p>\n                  </div>\n                </div>\n\n                <div className=\"mt-8\">\n                  <h3 className=\"text-lg font-semibold mb-4\">Performance Metrics</h3>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <div className=\"flex justify-between text-sm mb-2\">\n                        <span>Lead Conversion Rate</span>\n                        <span>{stats?.leadConversionRate}%</span>\n                      </div>\n                      <Progress value={Number(stats?.leadConversionRate) || 0} className=\"h-2\" />\n                    </div>\n\n                    <div>\n                      <div className=\"flex justify-between text-sm mb-2\">\n                        <span>User Engagement</span>\n                        <span>{stats?.activeUsers && stats?.totalUsers ? ((stats.activeUsers / stats.totalUsers) * 100).toFixed(1) : 0}%</span>\n                      </div>\n                      <Progress value={stats?.activeUsers && stats?.totalUsers ? (stats.activeUsers / stats.totalUsers) * 100 : 0} className=\"h-2\" />\n                    </div>\n\n                    <div>\n                      <div className=\"flex justify-between text-sm mb-2\">\n                        <span>Property Occupancy</span>\n                        <span>{stats?.activeProperties && stats?.totalProperties ? ((stats.activeProperties / stats.totalProperties) * 100).toFixed(1) : 0}%</span>\n                      </div>\n                      <Progress value={stats?.activeProperties && stats?.totalProperties ? (stats.activeProperties / stats.totalProperties) * 100 : 0} className=\"h-2\" />\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Properties Tab */}\n          <TabsContent value=\"properties\" className=\"space-y-6 mt-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <Building className=\"h-5 w-5 text-primary\" />\n                  <span>Properties Overview</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                  <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n                    <Home className=\"h-8 w-8 text-blue-600 mx-auto mb-2\" />\n                    <p className=\"text-2xl font-bold text-blue-900\">{safeRender(stats?.totalProperties)}</p>\n                    <p className=\"text-sm text-blue-600\">Total Properties</p>\n                  </div>\n\n                  <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n                    <CheckCircle className=\"h-8 w-8 text-green-600 mx-auto mb-2\" />\n                    <p className=\"text-2xl font-bold text-green-900\">{safeRender(stats?.activeProperties)}</p>\n                    <p className=\"text-sm text-green-600\">Active Properties</p>\n                  </div>\n\n                  <div className=\"text-center p-4 bg-yellow-50 rounded-lg\">\n                    <Star className=\"h-8 w-8 text-yellow-600 mx-auto mb-2\" />\n                    <p className=\"text-2xl font-bold text-yellow-900\">{safeRender(stats?.featuredProperties)}</p>\n                    <p className=\"text-sm text-yellow-600\">Featured</p>\n                  </div>\n\n                  <div className=\"text-center p-4 bg-red-50 rounded-lg\">\n                    <AlertTriangle className=\"h-8 w-8 text-red-600 mx-auto mb-2\" />\n                    <p className=\"text-2xl font-bold text-red-900\">{safeRender(stats?.soldOutProperties)}</p>\n                    <p className=\"text-sm text-red-600\">Sold Out</p>\n                  </div>\n                </div>\n\n                <div className=\"mt-6\">\n                  <h3 className=\"text-lg font-semibold mb-4\">Property Value</h3>\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-600\">Total Portfolio Value</span>\n                      <span className=\"text-xl font-bold\">₹{((Number(safeRender(stats?.totalPropertyValue)) || 0) / 10000000).toFixed(1)}Cr</span>\n                    </div>\n                    <div className=\"flex justify-between items-center mt-2\">\n                      <span className=\"text-sm text-gray-600\">Average Property Price</span>\n                      <span className=\"text-lg font-semibold\">₹{((Number(safeRender(stats?.averagePropertyPrice)) || 0) / 100000).toFixed(1)}L</span>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Users Tab */}\n          <TabsContent value=\"users\" className=\"space-y-6 mt-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <Users className=\"h-5 w-5 text-primary\" />\n                  <span>Users Overview</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                  <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n                    <Users className=\"h-8 w-8 text-blue-600 mx-auto mb-2\" />\n                    <p className=\"text-2xl font-bold text-blue-900\">{safeRender(stats?.totalUsers)}</p>\n                    <p className=\"text-sm text-blue-600\">Total Users</p>\n                  </div>\n\n                  <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n                    <UserCheck className=\"h-8 w-8 text-green-600 mx-auto mb-2\" />\n                    <p className=\"text-2xl font-bold text-green-900\">{safeRender(stats?.activeUsers)}</p>\n                    <p className=\"text-sm text-green-600\">Active Users</p>\n                  </div>\n\n                  <div className=\"text-center p-4 bg-yellow-50 rounded-lg\">\n                    <UserPlus className=\"h-8 w-8 text-yellow-600 mx-auto mb-2\" />\n                    <p className=\"text-2xl font-bold text-yellow-900\">{safeRender(stats?.newUsersThisMonth)}</p>\n                    <p className=\"text-sm text-yellow-600\">New This Month</p>\n                  </div>\n\n                  <div className=\"text-center p-4 bg-red-50 rounded-lg\">\n                    <Clock className=\"h-8 w-8 text-red-600 mx-auto mb-2\" />\n                    <p className=\"text-2xl font-bold text-red-900\">{safeRender(stats?.pendingKyc)}</p>\n                    <p className=\"text-sm text-red-600\">Pending KYC</p>\n                  </div>\n                </div>\n\n                <div className=\"mt-6\">\n                  <h3 className=\"text-lg font-semibold mb-4\">User Engagement</h3>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <div className=\"flex justify-between text-sm mb-2\">\n                        <span>Active User Rate</span>\n                        <span>{stats?.activeUsers && stats?.totalUsers ? ((stats.activeUsers / stats.totalUsers) * 100).toFixed(1) : 0}%</span>\n                      </div>\n                      <Progress value={stats?.activeUsers && stats?.totalUsers ? (stats.activeUsers / stats.totalUsers) * 100 : 0} className=\"h-2\" />\n                    </div>\n\n                    <div>\n                      <div className=\"flex justify-between text-sm mb-2\">\n                        <span>KYC Completion Rate</span>\n                        <span>{stats?.totalUsers && stats?.pendingKyc ? (((stats.totalUsers - stats.pendingKyc) / stats.totalUsers) * 100).toFixed(1) : 0}%</span>\n                      </div>\n                      <Progress value={stats?.totalUsers && stats?.pendingKyc ? ((stats.totalUsers - stats.pendingKyc) / stats.totalUsers) * 100 : 0} className=\"h-2\" />\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAUA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAvBA;;;;;;;;;;;;;;AAqDe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,kBAAkB,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,mIAAA,CAAA,wBAAqB;IAC5D,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,mIAAA,CAAA,aAAU;IACtC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,gCAAgC;IAChC,MAAM,EACJ,MAAM,cAAc,EACpB,WAAW,gBAAgB,EAC3B,OAAO,cAAc,EACrB,SAAS,gBAAgB,EACzB,YAAY,iBAAiB,EAC9B,GAAG,CAAA,GAAA,mIAAA,CAAA,4BAAyB,AAAD,EAAE;QAAE,QAAQ;IAAU;IAElD,MAAM,EACJ,MAAM,UAAU,EAChB,WAAW,iBAAiB,EAC7B,GAAG,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD;IAExB,MAAM,EACJ,MAAM,eAAe,EACrB,WAAW,sBAAsB,EAClC,GAAG,CAAA,GAAA,oIAAA,CAAA,6BAA0B,AAAD;IAE7B,MAAM,EACJ,MAAM,gBAAgB,EACtB,WAAW,iBAAiB,EAC7B,GAAG,CAAA,GAAA,mIAAA,CAAA,8BAA2B,AAAD,EAAE;QAAE,OAAO;IAAG;IAE5C,MAAM,EACJ,MAAM,aAAa,EACnB,WAAW,oBAAoB,EAChC,GAAG,CAAA,GAAA,mIAAA,CAAA,2BAAwB,AAAD,EAAE;QAAE,OAAO;IAAE;IAExC,MAAM,EACJ,MAAM,kBAAkB,EACxB,WAAW,mBAAmB,EAC/B,GAAG,CAAA,GAAA,mIAAA,CAAA,gCAA6B,AAAD,EAAE;QAAE,OAAO;IAAG;IAE9C,MAAM,EACJ,MAAM,aAAa,EACnB,WAAW,oBAAoB,EAChC,GAAG,CAAA,GAAA,mIAAA,CAAA,2BAAwB,AAAD,EAAE;QAAE,OAAO;IAAE;IAExC,MAAM,EACJ,MAAM,YAAY,EAClB,WAAW,mBAAmB,EAC/B,GAAG,CAAA,GAAA,mIAAA,CAAA,0BAAuB,AAAD;IAE1B,sDAAsD;IACtD,+CAA+C;IAE/C,0CAA0C;IAC1C,MAAM,QAAQ,CAAA,GAAA,oUAAA,CAAA,UAAO,AAAD,EAAE;QACpB,MAAM,OAAO,gBAAgB;QAC7B,MAAM,YAAY,YAAY;QAC9B,MAAM,iBAAiB,iBAAiB;QACxC,MAAM,iBAAiB,kBAAkB,QAAQ,EAAE;QACnD,MAAM,mBAAmB,oBAAoB,QAAQ,EAAE;QACvD,MAAM,mBAAmB,cAAc;QAEvC,OAAO;YACL,6BAA6B;YAC7B,YAAY,MAAM,cAAc,WAAW,cAAc;YACzD,aAAa,MAAM,eAAe,WAAW,eAAe;YAC5D,mBAAmB,MAAM,qBAAqB,WAAW,gBAAgB;YACzE,YAAY,WAAW,cAAc;YACrC,YAAY,MAAM,cAAc;YAEhC,iCAAiC;YACjC,iBAAiB,MAAM,mBAAmB,gBAAgB,mBAAmB;YAC7E,kBAAkB,MAAM,oBAAoB,gBAAgB,oBAAoB;YAChF,mBAAmB,MAAM,qBAAqB,gBAAgB,WAAW;YACzE,oBAAoB,MAAM,sBAAsB,gBAAgB,sBAAsB;YACtF,oBAAoB,gBAAgB,cAAc;YAClD,sBAAsB,gBAAgB,gBAAgB;YACtD,gBAAgB,MAAM,kBAAkB;YAExC,6CAA6C;YAC7C,kBAAkB,MAAM,oBAAoB;YAC5C,iBAAiB,MAAM,mBAAmB;YAC1C,cAAc,MAAM,gBAAgB;YACpC,eAAe,MAAM,iBAAiB;YACtC,kBAAkB,MAAM,oBAAoB;YAC5C,mBAAmB,MAAM,qBAAqB;YAE9C,6BAA6B;YAC7B,aAAa,MAAM,eAAe;YAClC,YAAY,MAAM,cAAc;YAChC,gBAAgB,MAAM,kBAAkB;YACxC,YAAY,MAAM,cAAc;YAEhC,oCAAoC;YACpC,qBAAqB,MAAM,uBAAuB;YAClD,mBAAmB,MAAM,qBAAqB,iBAAiB,MAAM,IAAI;YACzE,uBAAuB,MAAM,yBAAyB;YACtD,mBAAmB,MAAM,qBAAqB;YAE9C,8BAA8B;YAC9B,kBAAkB,kBAAkB,oBAAoB;YACxD,UAAU,kBAAkB,YAAY;YACxC,cAAc,kBAAkB,gBAAgB;YAEhD,mBAAmB;YACnB,uBAAuB,eAAe,MAAM,IAAI;YAEhD,mCAAmC;YACnC,gBAAgB,MAAM,kBAAkB,CAAC,MAAM,eAAe,KAAK,YAAY,GAAG,OAAO,CAAC;YAC1F,mBAAmB,MAAM,qBAAqB,KAAK,KAAK,CAAC,CAAC,MAAM,oBAAoB,CAAC,IAAI;YACzF,oBAAoB,MAAM,sBAAsB,KAAK,KAAK,CAAC,CAAC,MAAM,oBAAoB,CAAC,IAAI;YAE3F,qBAAqB;YACrB,YAAY,CAAC,gBAAgB,cAAc,CAAC,IAAI,CAAC,MAAM,gBAAgB,CAAC;YACxE,oBAAoB,MAAM,kBAAkB,MAAM,aAC9C,CAAC,AAAC,KAAK,cAAc,GAAG,KAAK,UAAU,GAAI,GAAG,EAAE,OAAO,CAAC,KACxD;YACJ,aAAa,CAAC,MAAM,iBAAiB,CAAC,IAAI,IAAI,OAAO,CAAC,MAAM,iBAAiB,CAAC,IAAI,IAAI,SAAS;QACjG;IACF,GAAG;QAAC;QAAgB;QAAY;QAAiB;QAAkB;QAAoB;KAAa;IAEpG,MAAM,aAAa,CAAA,GAAA,oUAAA,CAAA,UAAO,AAAD,EAAE;QACzB,IAAI;YACF,MAAM,eAAe,kBAAkB;YACvC,IAAI,gBAAgB,OAAO,iBAAiB,YAAY,UAAU,cAAc;gBAC9E,MAAM,YAAY,aAAa,IAAI;gBACnC,OAAO,MAAM,OAAO,CAAC,aAAa,YAAY,EAAE;YAClD;YACA,OAAO,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,EAAE;QACX;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,SAAS,CAAA,GAAA,oUAAA,CAAA,UAAO,AAAD,EAAE;QACrB,IAAI;YACF,MAAM,eAAe,eAAe;YACpC,IAAI,gBAAgB,OAAO,iBAAiB,YAAY,UAAU,cAAc;gBAC9E,MAAM,YAAY,aAAa,IAAI;gBACnC,OAAO,MAAM,OAAO,CAAC,aAAa,YAAY,EAAE;YAClD;YACA,OAAO,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO,EAAE;QACX;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,YAAY,oBAAoB,qBAAqB,0BAA0B,qBAAqB,wBAAwB,uBAAuB,wBAAwB;IACjL,MAAM,WAAW;IAEjB,MAAM,gBAAgB;QACpB,cAAc;QACd,IAAI;YACF,MAAM;YACN,WAAW,IAAM,cAAc,QAAQ;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,cAAc;QAChB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,aAAa;QACb;IACF;IAEA,uEAAuE;IACvE,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAClD,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,MAAM,KAAK,KAAK,WAAW,OAAO,OAAO,MAAM,KAAK;YACxD,OAAO;QACT;QACA,OAAO,OAAO;IAChB;IAEA,6BAA6B;IAC7B,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,OAAO,WAAW,SAAS;QACzC,IAAI,SAAS,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,QAAQ,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;QACnE,IAAI,SAAS,QAAQ,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC9D,IAAI,SAAS,MAAM,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC1D,OAAO,CAAC,CAAC,EAAE,MAAM,cAAc,IAAI;IACrC;IAEA,MAAM,cAAc;QAClB,MAAM,OAAO,IAAI,OAAO,QAAQ;QAChC,IAAI,OAAO,IAAI,OAAO;QACtB,IAAI,OAAO,IAAI,OAAO;QACtB,OAAO;IACT;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;;;;;kCACf,6WAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6WAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6WAAC,+IAAA,CAAA,UAAe;kBACd,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;;kDACC,6WAAC;wCAAG,WAAU;;0DACZ,6WAAC,sSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CACpB;4CAAc;4CAAG,MAAM,aAAa;4CAAQ;;;;;;;kDAE/C,6WAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;0CAG9B,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6WAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;gDACrD,WAAU;;kEAEV,6WAAC;wDAAO,OAAM;wDAAM,WAAU;kEAAgB;;;;;;kEAC9C,6WAAC;wDAAO,OAAM;wDAAK,WAAU;kEAAgB;;;;;;kEAC7C,6WAAC;wDAAO,OAAM;wDAAM,WAAU;kEAAgB;;;;;;kEAC9C,6WAAC;wDAAO,OAAM;wDAAM,WAAU;kEAAgB;;;;;;kEAC9C,6WAAC;wDAAO,OAAM;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAIjD,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU,aAAa;wCACvB,WAAU;;0DAEV,6WAAC,oSAAA,CAAA,YAAS;gDAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,CAAC,aAAa,iBAAiB,KAAK;;;;;;4CAAmB;;;;;;;kDAIlG,6WAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;;0DAEV,6WAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAQ7C,6WAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,6WAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6WAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,6WAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY;;;;;;8CAC/B,6WAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAa;;;;;;8CAChC,6WAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAQ;;;;;;;;;;;;sCAG7B,6WAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;;8CAEtC,6WAAC;oCAAI,WAAU;;sDAEb,6WAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6WAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;;8EACC,6WAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6WAAC;oEAAE,WAAU;8EAAoC,WAAW,OAAO;;;;;;8EACnE,6WAAC;oEAAE,WAAU;;sFACX,6WAAC,sSAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEACrB,OAAO,aAAa,CAAC,CAAC,EAAE,MAAM,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;wEAAO;;;;;;;;;;;;;sEAGrE,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC,wRAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOzB,6WAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6WAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;;8EACC,6WAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6WAAC;oEAAE,WAAU;;wEAAmC;wEAC5C,CAAC,CAAC,OAAO,WAAW,OAAO,oBAAoB,OAAO,qBAAqB,CAAC,IAAI,QAAQ,EAAE,OAAO,CAAC;wEAAG;;;;;;;8EAEzG,6WAAC;oEAAE,WAAU;;sFACX,6WAAC,sSAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEACrB,OAAO,mBAAmB,CAAC,CAAC,EAAE,MAAM,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;wEAAO;;;;;;;;;;;;;sEAGjF,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO9B,6WAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6WAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;;8EACC,6WAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6WAAC;oEAAE,WAAU;8EACV,WAAW,OAAO,oBAAoB,OAAO;;;;;;8EAEhD,6WAAC;oEAAE,WAAU;;sFACX,6WAAC,sSAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEACrB,OAAO,iBAAiB,CAAC,CAAC,EAAE,MAAM,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;wEAAM;;;;;;;;;;;;;sEAG5E,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC,8RAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO5B,6WAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6WAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;;8EACC,6WAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6WAAC;oEAAE,WAAU;;wEAAmC;wEAC5C,CAAC,CAAC,OAAO,WAAW,OAAO,kBAAkB,CAAC,IAAI,QAAQ,EAAE,OAAO,CAAC;wEAAG;;;;;;;8EAE3E,6WAAC;oEAAE,WAAU;;sFACX,6WAAC,sSAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEACrB,OAAO,gBAAgB,CAAC,CAAC,EAAE,MAAM,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;wEAAO;;;;;;;;;;;;;sEAG3E,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC,0RAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ5B,6WAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6WAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIpC,6WAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;0EACb,cAAA,6WAAC,0RAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,6WAAC;gEAAE,WAAU;0EAAoC,WAAW,OAAO,eAAe,OAAO;;;;;;0EACzF,6WAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6WAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;kEAG7C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;0EACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;0EAExB,6WAAC;gEAAE,WAAU;0EAAsC,WAAW,OAAO;;;;;;0EACrE,6WAAC;gEAAE,WAAU;0EAAsC;;;;;;0EACnD,6WAAC;gEAAE,WAAU;0EAA+B;;;;;;;;;;;;kEAG9C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;0EACb,cAAA,6WAAC,+SAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;0EAEzB,6WAAC;gEAAE,WAAU;0EAAqC,WAAW,OAAO;;;;;;0EACpE,6WAAC;gEAAE,WAAU;0EAAqC;;;;;;0EAClD,6WAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;kEAG7C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;0EACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,6WAAC;gEAAE,WAAU;0EAAsC,WAAW,OAAO;;;;;;0EACrE,6WAAC;gEAAE,WAAU;0EAAsC;;;;;;0EACnD,6WAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;kEAG7C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;0EACb,cAAA,6WAAC,4SAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;;;;;;0EAE3B,6WAAC;gEAAE,WAAU;0EAAmC,WAAW,OAAO;;;;;;0EAClE,6WAAC;gEAAE,WAAU;0EAAmC;;;;;;0EAChD,6WAAC;gEAAE,WAAU;0EAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOjD,6WAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6WAAC,gIAAA,CAAA,aAAU;;8DACT,6WAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6WAAC,oRAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAA4B;;;;;;;8DAG7C,6WAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,6WAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,gIAAA,CAAA,OAAI;wDAAC,WAAU;wDAAyG,SAAS,IAAM,OAAO,IAAI,CAAC;kEAClJ,cAAA,6WAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,wRAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,6WAAC;oEAAE,WAAU;8EAAsC;;;;;;8EACnD,6WAAC;oEAAE,WAAU;;wEAA8B,WAAW,OAAO;wEAAY;;;;;;;;;;;;;;;;;;kEAI7E,6WAAC,gIAAA,CAAA,OAAI;wDAAC,WAAU;wDAA2G,SAAS,IAAM,OAAO,IAAI,CAAC;kEACpJ,cAAA,6WAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,8RAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,6WAAC;oEAAE,WAAU;8EAAuC;;;;;;8EACpD,6WAAC;oEAAE,WAAU;;wEAA+B,WAAW,OAAO;wEAAiB;;;;;;;;;;;;;;;;;;kEAInF,6WAAC,gIAAA,CAAA,OAAI;wDAAC,WAAU;wDAA6G,SAAS,IAAM,OAAO,IAAI,CAAC;kEACtJ,cAAA,6WAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,0RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;8EAEpB,6WAAC;oEAAE,WAAU;8EAAwC;;;;;;8EACrD,6WAAC;oEAAE,WAAU;;wEAAgC,WAAW,OAAO;wEAAY;;;;;;;;;;;;;;;;;;kEAI/E,6WAAC,gIAAA,CAAA,OAAI;wDAAC,WAAU;wDAA6G,SAAS,IAAM,OAAO,IAAI,CAAC;kEACtJ,cAAA,6WAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;;;;;;8EAExB,6WAAC;oEAAE,WAAU;8EAAwC;;;;;;8EACrD,6WAAC;oEAAE,WAAU;;wEAAgC,WAAW,OAAO;wEAAmB;;;;;;;;;;;;;;;;;;kEAItF,6WAAC,gIAAA,CAAA,OAAI;wDAAC,WAAU;wDAAuG,SAAS,IAAM,OAAO,IAAI,CAAC;kEAChJ,cAAA,6WAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,kSAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,6WAAC;oEAAE,WAAU;8EAAqC;;;;;;8EAClD,6WAAC;oEAAE,WAAU;8EAA4B;;;;;;;;;;;;;;;;;kEAI7C,6WAAC,gIAAA,CAAA,OAAI;wDAAC,WAAU;wDAA6G,SAAS,IAAM,OAAO,IAAI,CAAC;kEACtJ,cAAA,6WAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,8RAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,6WAAC;oEAAE,WAAU;8EAAwC;;;;;;8EACrD,6WAAC;oEAAE,WAAU;8EAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQtD,6WAAC;oCAAI,WAAU;;sDAEb,6WAAC,gIAAA,CAAA,OAAI;;8DACH,6WAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6WAAC,8RAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6WAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,6WAAC,gIAAA,CAAA,cAAW;8DACT,kCACC,6WAAC;wDAAI,WAAU;kEACb,cAAA,6WAAC,qSAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;+DAEnB,WAAW,MAAM,GAAG,kBACtB,6WAAC;wDAAI,WAAU;kEACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAe,sBAC1C,6WAAC;gEAAgB,WAAU;;kFACzB,6WAAC;wEAAI,WAAU;kFACb,cAAA,6WAAC,8RAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;;;;;;kFAEtB,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFAAE,WAAU;0FAAuB,SAAS,KAAK,IAAI,SAAS,WAAW,IAAI;;;;;;0FAC9E,6WAAC;gFAAE,WAAU;0FACV,SAAS,SAAS,GAAG,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc,KAAK;;;;;;;;;;;;;+DAPlE;;;;;;;;;6EAcd,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,8RAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6WAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;sDAO7C,6WAAC,gIAAA,CAAA,OAAI;;8DACH,6WAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6WAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,6WAAC,gIAAA,CAAA,cAAW;8DACT,qCACC,6WAAC;wDAAI,WAAU;kEACb,cAAA,6WAAC,qSAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;+DAEnB,OAAO,MAAM,GAAG,kBAClB,6WAAC;wDAAI,WAAU;kEACZ,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAY,sBACnC,6WAAC;gEAAgB,WAAU;;kFACzB,6WAAC;wEAAI,WAAU;kFACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFAAE,WAAU;0FAAuB,MAAM,KAAK,IAAI,MAAM,OAAO,IAAI;;;;;;0FACpE,6WAAC;gFAAE,WAAU;0FACV,MAAM,SAAS,GAAG,IAAI,KAAK,MAAM,SAAS,EAAE,cAAc,KAAK;;;;;;;;;;;;;+DAP5D;;;;;;;;;6EAcd,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6WAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASjD,6WAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;sCACvC,cAAA,6WAAC,gIAAA,CAAA,OAAI;;kDACH,6WAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6WAAC,sSAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6WAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6WAAC,gIAAA,CAAA,cAAW;;0DACV,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,sSAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6WAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,6WAAC;gEAAE,WAAU;0EACV,OAAO,gBAAgB,GAAG,MAAM,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;;;;;;0EAEjE,6WAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAG5C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,wRAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6WAAC;gEAAG,WAAU;0EAA4C;;;;;;0EAC1D,6WAAC;gEAAE,WAAU;0EACV,OAAO,aAAa,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;;;;;;0EAE3D,6WAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;kEAG7C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,8RAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6WAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAC3D,6WAAC;gEAAE,WAAU;0EACV,OAAO,iBAAiB,GAAG,MAAM,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;;;;;;0EAEnE,6WAAC;gEAAE,WAAU;0EAA+B;;;;;;;;;;;;;;;;;;0DAIhD,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;;kFACC,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;0FAAK;;;;;;0FACN,6WAAC;;oFAAM,OAAO;oFAAmB;;;;;;;;;;;;;kFAEnC,6WAAC,oIAAA,CAAA,WAAQ;wEAAC,OAAO,OAAO,OAAO,uBAAuB;wEAAG,WAAU;;;;;;;;;;;;0EAGrE,6WAAC;;kFACC,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;0FAAK;;;;;;0FACN,6WAAC;;oFAAM,OAAO,eAAe,OAAO,aAAa,CAAC,AAAC,MAAM,WAAW,GAAG,MAAM,UAAU,GAAI,GAAG,EAAE,OAAO,CAAC,KAAK;oFAAE;;;;;;;;;;;;;kFAEjH,6WAAC,oIAAA,CAAA,WAAQ;wEAAC,OAAO,OAAO,eAAe,OAAO,aAAa,AAAC,MAAM,WAAW,GAAG,MAAM,UAAU,GAAI,MAAM;wEAAG,WAAU;;;;;;;;;;;;0EAGzH,6WAAC;;kFACC,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;0FAAK;;;;;;0FACN,6WAAC;;oFAAM,OAAO,oBAAoB,OAAO,kBAAkB,CAAC,AAAC,MAAM,gBAAgB,GAAG,MAAM,eAAe,GAAI,GAAG,EAAE,OAAO,CAAC,KAAK;oFAAE;;;;;;;;;;;;;kFAErI,6WAAC,oIAAA,CAAA,WAAQ;wEAAC,OAAO,OAAO,oBAAoB,OAAO,kBAAkB,AAAC,MAAM,gBAAgB,GAAG,MAAM,eAAe,GAAI,MAAM;wEAAG,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASvJ,6WAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAa,WAAU;sCACxC,cAAA,6WAAC,gIAAA,CAAA,OAAI;;kDACH,6WAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6WAAC,8RAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6WAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6WAAC,gIAAA,CAAA,cAAW;;0DACV,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,uRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6WAAC;gEAAE,WAAU;0EAAoC,WAAW,OAAO;;;;;;0EACnE,6WAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAGvC,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,+SAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6WAAC;gEAAE,WAAU;0EAAqC,WAAW,OAAO;;;;;;0EACpE,6WAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;kEAGxC,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6WAAC;gEAAE,WAAU;0EAAsC,WAAW,OAAO;;;;;;0EACrE,6WAAC;gEAAE,WAAU;0EAA0B;;;;;;;;;;;;kEAGzC,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,4SAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;0EACzB,6WAAC;gEAAE,WAAU;0EAAmC,WAAW,OAAO;;;;;;0EAClE,6WAAC;gEAAE,WAAU;0EAAuB;;;;;;;;;;;;;;;;;;0DAIxC,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,6WAAC;wEAAK,WAAU;;4EAAoB;4EAAE,CAAC,CAAC,OAAO,WAAW,OAAO,wBAAwB,CAAC,IAAI,QAAQ,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAErH,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,6WAAC;wEAAK,WAAU;;4EAAwB;4EAAE,CAAC,CAAC,OAAO,WAAW,OAAO,0BAA0B,CAAC,IAAI,MAAM,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASnI,6WAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAQ,WAAU;sCACnC,cAAA,6WAAC,gIAAA,CAAA,OAAI;;kDACH,6WAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6WAAC,wRAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6WAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6WAAC,gIAAA,CAAA,cAAW;;0DACV,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,wRAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6WAAC;gEAAE,WAAU;0EAAoC,WAAW,OAAO;;;;;;0EACnE,6WAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAGvC,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,oSAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6WAAC;gEAAE,WAAU;0EAAqC,WAAW,OAAO;;;;;;0EACpE,6WAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;kEAGxC,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,kSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6WAAC;gEAAE,WAAU;0EAAsC,WAAW,OAAO;;;;;;0EACrE,6WAAC;gEAAE,WAAU;0EAA0B;;;;;;;;;;;;kEAGzC,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,wRAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6WAAC;gEAAE,WAAU;0EAAmC,WAAW,OAAO;;;;;;0EAClE,6WAAC;gEAAE,WAAU;0EAAuB;;;;;;;;;;;;;;;;;;0DAIxC,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;;kFACC,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;0FAAK;;;;;;0FACN,6WAAC;;oFAAM,OAAO,eAAe,OAAO,aAAa,CAAC,AAAC,MAAM,WAAW,GAAG,MAAM,UAAU,GAAI,GAAG,EAAE,OAAO,CAAC,KAAK;oFAAE;;;;;;;;;;;;;kFAEjH,6WAAC,oIAAA,CAAA,WAAQ;wEAAC,OAAO,OAAO,eAAe,OAAO,aAAa,AAAC,MAAM,WAAW,GAAG,MAAM,UAAU,GAAI,MAAM;wEAAG,WAAU;;;;;;;;;;;;0EAGzH,6WAAC;;kFACC,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;0FAAK;;;;;;0FACN,6WAAC;;oFAAM,OAAO,cAAc,OAAO,aAAa,CAAC,AAAC,CAAC,MAAM,UAAU,GAAG,MAAM,UAAU,IAAI,MAAM,UAAU,GAAI,GAAG,EAAE,OAAO,CAAC,KAAK;oFAAE;;;;;;;;;;;;;kFAEpI,6WAAC,oIAAA,CAAA,WAAQ;wEAAC,OAAO,OAAO,cAAc,OAAO,aAAa,AAAC,CAAC,MAAM,UAAU,GAAG,MAAM,UAAU,IAAI,MAAM,UAAU,GAAI,MAAM;wEAAG,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhK", "debugId": null}}]}