(()=>{var e={};e.id=5663,e.ids=[5663],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17646:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(99024).A)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22945:(e,s,r)=>{Promise.resolve().then(r.bind(r,27445))},26806:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(99024).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},27445:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\referrals\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\referrals\\page.tsx","default")},28149:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(99024).A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34807:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=r(10557),t=r(68490),l=r(13172),i=r.n(l),n=r(68835),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let c={children:["",{children:["referrals",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,27445)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\referrals\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\referrals\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/referrals/page",pathname:"/referrals",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},36097:(e,s,r)=>{Promise.resolve().then(r.bind(r,73673))},56021:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(99024).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},56525:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(99024).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63407:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(99024).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},65827:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(99024).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},73673:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>B});var a=r(40969),t=r(73356),l=r(37020),i=r(66949),n=r(46411),d=r(14125),c=r(14216),o=r(56021),x=r(29419),m=r(28149),h=r(91596),u=r(17646),p=r(45548),f=r(63407),y=r(37124),g=r(56525),j=r(26806),v=r(65827),N=r(99024);let b=(0,N.A)("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),k=(0,N.A)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),w=(0,N.A)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);var R=r(21764);let{useGetUserReferralsQuery:A,useGetReferralStatsQuery:C,useGetReferralEarningsQuery:q,useGetReferralCodeQuery:T,useGenerateNewReferralCodeMutation:P,useShareReferralMutation:M,useGetReferralAnalyticsQuery:S,useGetReferralProgramQuery:D,useRequestPayoutMutation:E,useGetPayoutHistoryQuery:L,useTrackReferralClickMutation:U,useGetReferralLeaderboardQuery:$,useSetReferralPreferencesMutation:O}=r(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getUserReferrals:e.query({query:e=>({url:"/referrals",params:{page:e.page||1,limit:e.limit||20,...e.status&&{status:e.status},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),providesTags:e=>e?.data?.data?[...e.data.data.map(({_id:e})=>({type:"Referral",id:e})),{type:"Referral",id:"LIST"}]:[{type:"Referral",id:"LIST"}],keepUnusedDataFor:300}),getReferralStats:e.query({query:({period:e="1Y"})=>({url:"/referrals/stats",params:{period:e}}),providesTags:[{type:"Referral",id:"STATS"}],keepUnusedDataFor:600}),getReferralEarnings:e.query({query:e=>({url:"/referrals/earnings",params:{page:e.page||1,limit:e.limit||20,...e.status&&{status:e.status},...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate}}}),providesTags:[{type:"Referral",id:"EARNINGS"}],keepUnusedDataFor:300}),getReferralCode:e.query({query:()=>"/referrals/code",providesTags:[{type:"Referral",id:"CODE"}],keepUnusedDataFor:3600}),generateNewReferralCode:e.mutation({query:()=>({url:"/referrals/generate-code",method:"POST"}),invalidatesTags:[{type:"Referral",id:"CODE"}]}),shareReferral:e.mutation({query:e=>({url:"/referrals/share",method:"POST",body:e})}),getReferralAnalytics:e.query({query:({period:e="1Y"})=>({url:"/referrals/analytics",params:{period:e}}),providesTags:[{type:"Referral",id:"ANALYTICS"}],keepUnusedDataFor:900}),getReferralProgram:e.query({query:()=>"/referrals/program",providesTags:[{type:"Referral",id:"PROGRAM"}],keepUnusedDataFor:3600}),requestPayout:e.mutation({query:e=>({url:"/referrals/request-payout",method:"POST",body:e}),invalidatesTags:[{type:"Referral",id:"EARNINGS"},{type:"Referral",id:"STATS"}]}),getPayoutHistory:e.query({query:e=>({url:"/referrals/payouts",params:{page:e.page||1,limit:e.limit||20,...e.status&&{status:e.status},...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate}}}),providesTags:[{type:"Referral",id:"PAYOUTS"}],keepUnusedDataFor:600}),trackReferralClick:e.mutation({query:e=>({url:"/referrals/track-click",method:"POST",body:e})}),getReferralLeaderboard:e.query({query:({period:e="1M",limit:s=10})=>({url:"/referrals/leaderboard",params:{period:e,limit:s}}),providesTags:[{type:"Referral",id:"LEADERBOARD"}],keepUnusedDataFor:1800}),setReferralPreferences:e.mutation({query:e=>({url:"/referrals/preferences",method:"PUT",body:e}),invalidatesTags:[{type:"Referral",id:"CODE"}]})})});var _=r(1507);function B(){let[e,s]=(0,t.useState)("1M"),[r,N]=(0,t.useState)(!1),{data:q,isLoading:P}=A({limit:50}),{data:S,isLoading:D}=C({period:e}),{data:E,isLoading:L}=T(),[U]=M(),$=q?.data?.data||[],O=S?.data||{totalReferrals:0,activeReferrals:0,totalEarnings:0,pendingEarnings:0,paidEarnings:0,conversionRate:0,averageEarningPerReferral:0,monthlyStats:[],topReferrals:[]},B=E?.data||{referralCode:"",referralLink:"",qrCode:"",shareText:""},z=[{title:"Total Referrals",value:O.totalReferrals.toString(),icon:d.A,color:"text-blue-600",bgColor:"bg-blue-100"},{title:"Active Referrals",value:O.activeReferrals.toString(),icon:c.A,color:"text-green-600",bgColor:"bg-green-100"},{title:"Total Earnings",value:(0,R.vv)(O.totalEarnings),icon:o.A,color:"text-purple-600",bgColor:"bg-purple-100"},{title:"Pending Earnings",value:(0,R.vv)(O.pendingEarnings),icon:x.A,color:"text-orange-600",bgColor:"bg-orange-100"}],F=async()=>{try{await (0,R.lW)(B.referralCode),_.toast.success("Referral code copied to clipboard!")}catch(e){_.toast.error("Failed to copy referral code")}},Y=async()=>{try{await (0,R.lW)(B.referralLink),_.toast.success("Referral link copied to clipboard!")}catch(e){_.toast.error("Failed to copy referral link")}},Z=async(e,s)=>{try{await U({method:e,platform:s,message:B.shareText}).unwrap(),_.toast.success("Referral shared successfully!"),N(!1)}catch(e){_.toast.error("Failed to share referral")}};return P||D||L?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6",children:[1,2,3,4].map(e=>(0,a.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg"},e))}),(0,a.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg"})]})})}):(0,a.jsxs)(l.A,{children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Referral Program"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Earn rewards by referring friends to SGM "})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("select",{value:e,onChange:e=>s(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[{value:"1M",label:"Last Month"},{value:"3M",label:"Last 3 Months"},{value:"6M",label:"Last 6 Months"},{value:"1Y",label:"Last Year"},{value:"ALL",label:"All Time"}].map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,a.jsxs)(n.$,{onClick:()=>N(!0),className:"flex items-center space-x-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Share & Earn"})]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:z.map((e,s)=>{let r=e.icon;return(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:e.title}),(0,a.jsx)("div",{className:`p-2 rounded-full ${e.bgColor}`,children:(0,a.jsx)(r,{className:`h-4 w-4 ${e.color}`})})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.value})})]},s)})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 mr-2"}),"Your Referral Code"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Referral Code"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"text",value:B.referralCode,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:F,children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Referral Link"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"text",value:B.referralLink,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-sm"}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:Y,children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>N(!0),className:"flex-1",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Share"]}),(0,a.jsxs)(n.$,{variant:"outline",className:"flex-1",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"QR Code"]})]})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"How Referrals Work"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm",children:"1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Share Your Code"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Share your unique referral code with friends and family"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-600 font-semibold text-sm",children:"2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"They Sign Up"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Your friends register using your referral code"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-semibold text-sm",children:"3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"They Invest"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"When they make their first investment, you both earn rewards"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-semibold text-sm",children:"4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Earn Rewards"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Get commission on their investments and bonus rewards"})]})]})]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(i.Zp,{className:"lg:col-span-2",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 mr-2"}),"Monthly Performance"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:O.monthlyStats.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"font-medium",children:e.month})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"font-semibold",children:[e.referrals," referrals"]}),(0,a.jsx)("div",{className:"text-sm text-green-600",children:(0,R.vv)(e.earnings)})]})]},s))})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 mr-2"}),"Top Referrals"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:O.topReferrals.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(d.A,{className:"h-4 w-4 text-gray-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.user.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:(0,R.Yq)(e.joinDate)})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"font-semibold text-green-600",children:(0,R.vv)(e.commissionEarned)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:(0,R.vv)(e.totalInvestment)})]})]},s))})})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(i.ZB,{children:"My Referrals"}),(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Export"]})]})}),(0,a.jsx)(i.Wu,{children:$.length>0?(0,a.jsx)("div",{className:"space-y-4",children:$.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(d.A,{className:"h-5 w-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-medium",children:[e.referredUser.firstName," ",e.referredUser.lastName]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(f.A,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:["Joined ",(0,R.Yq)(e.createdAt)]}),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"active"===e.status?"bg-green-100 text-green-800":"completed"===e.status?"bg-blue-100 text-blue-800":"bg-yellow-100 text-yellow-800"}`,children:e.status})]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"font-semibold text-green-600",children:(0,R.vv)(e.commissionEarned)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.commissionPaid?"Paid":"Pending"})]})]},e._id))}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(d.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No referrals yet"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Start sharing your referral code to earn rewards when friends join and invest."}),(0,a.jsx)(n.$,{onClick:()=>N(!0),children:"Share Your Code"})]})})]})]}),r&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Share Your Referral"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>Z("email"),className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Email"})]}),(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>Z("sms"),className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"SMS"})]}),(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>Z("social","whatsapp"),className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"WhatsApp"})]}),(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>Z("social","facebook"),className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)(b,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Facebook"})]}),(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>Z("social","twitter"),className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)(k,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Twitter"})]}),(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>Z("social","linkedin"),className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)(w,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"LinkedIn"})]})]}),(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsx)(n.$,{variant:"outline",onClick:()=>N(!1),className:"w-full",children:"Close"})})]})]})})]})}},79551:e=>{"use strict";e.exports=require("url")},91596:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(99024).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[755,3777,2544,7092,7555,2487,3427],()=>r(34807));module.exports=a})();