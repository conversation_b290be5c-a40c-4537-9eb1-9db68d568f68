(()=>{var e={};e.id=6400,e.ids=[6400],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28496:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(99024).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41628:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>j});var t=a(40969);a(73356);var r=a(37020),l=a(85306),n=a(66949),i=a(76650),d=a(31745),c=a(63407),x=a(83284),o=a(56525),m=a(45548),h=a(14216),p=a(37124),u=a(14125);function j(){let e=e=>"active"===e?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800";return(0,t.jsx)(r.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(l.Ay,{title:"Referral Analytics",description:"Detailed insights into your referral performance and earnings",icon:d.A,gradient:!0,breadcrumbs:[{label:"Referrals",href:"/referrals"},{label:"Analytics"}],actions:(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(l.lX.Secondary,{children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Date Range"]}),(0,t.jsxs)(l.lX.Secondary,{children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Settings"]}),(0,t.jsxs)(l.lX.Primary,{children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Export Report"]})]})}),(0,t.jsx)(l.T0,{stats:[{label:"Total Referrals",value:"33",change:"+8",trend:"up"},{label:"Active Referrals",value:"24",change:"+5",trend:"up"},{label:"Conversion Rate",value:"72.7%",change:"+5.2%",trend:"up"},{label:"Avg. Earnings/Referral",value:"₹1,387",change:"+12%",trend:"up"}]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.A,{className:"h-5 w-5 text-sky-600"}),(0,t.jsx)("span",{children:"Monthly Performance"})]})}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 rounded-lg",children:(0,t.jsx)("p",{className:"text-gray-500",children:"Performance chart will be displayed here"})}),(0,t.jsxs)("div",{className:"mt-4 grid grid-cols-3 gap-4 text-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-sky-600",children:"33"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Total Referrals"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:"24"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Conversions"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:"₹48,350"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Total Earnings"})]})]})]})]}),(0,t.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 text-sky-600"}),(0,t.jsx)("span",{children:"Conversion Funnel"})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Referral Links Shared"}),(0,t.jsx)("span",{className:"font-bold",children:"45"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"100%"}})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Clicked Links"}),(0,t.jsx)("span",{className:"font-bold",children:"38"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"84%"}})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Registrations"}),(0,t.jsx)("span",{className:"font-bold",children:"33"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-yellow-600 h-2 rounded-full",style:{width:"73%"}})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"First Investment"}),(0,t.jsx)("span",{className:"font-bold",children:"24"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-purple-600 h-2 rounded-full",style:{width:"53%"}})})]})})]})]}),(0,t.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-5 w-5 text-yellow-600"}),(0,t.jsx)("span",{children:"Top Performing Referrals"})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-600",children:"Referral"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-600",children:"Join Date"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-600",children:"Investments"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-600",children:"Total Invested"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-600",children:"Your Earnings"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-600",children:"Status"})]})}),(0,t.jsx)("tbody",{children:[{name:"Amit Sharma",email:"<EMAIL>",joinDate:"2024-01-15",investments:3,totalInvested:125e3,yourEarnings:6250,status:"active"},{name:"Priya Patel",email:"<EMAIL>",joinDate:"2024-01-12",investments:2,totalInvested:85e3,yourEarnings:4250,status:"active"},{name:"Rahul Kumar",email:"<EMAIL>",joinDate:"2024-01-10",investments:4,totalInvested:2e5,yourEarnings:1e4,status:"active"},{name:"Sneha Gupta",email:"<EMAIL>",joinDate:"2024-01-08",investments:1,totalInvested:5e4,yourEarnings:2500,status:"inactive"}].map((s,a)=>(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:s.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:s.email})]})}),(0,t.jsx)("td",{className:"py-4 px-4 text-sm text-gray-600",children:s.joinDate}),(0,t.jsx)("td",{className:"py-4 px-4 font-medium",children:s.investments}),(0,t.jsxs)("td",{className:"py-4 px-4 font-medium",children:["₹",s.totalInvested.toLocaleString()]}),(0,t.jsxs)("td",{className:"py-4 px-4 font-medium text-green-600",children:["₹",s.yourEarnings.toLocaleString()]}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(i.E,{className:e(s.status),children:s.status})})]},a))})]})})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("span",{children:"Referral Sources"})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"WhatsApp"}),(0,t.jsx)("span",{className:"font-bold",children:"45%"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Social Media"}),(0,t.jsx)("span",{className:"font-bold",children:"30%"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Email"}),(0,t.jsx)("span",{className:"font-bold",children:"15%"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Direct Link"}),(0,t.jsx)("span",{className:"font-bold",children:"10%"})]})]})})]}),(0,t.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsx)("span",{children:"Growth Metrics"})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Monthly Growth"}),(0,t.jsx)("span",{className:"font-bold text-green-600",children:"+18%"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Retention Rate"}),(0,t.jsx)("span",{className:"font-bold",children:"85%"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Avg. Investment"}),(0,t.jsx)("span",{className:"font-bold",children:"₹65,000"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Repeat Investors"}),(0,t.jsx)("span",{className:"font-bold",children:"67%"})]})]})})]}),(0,t.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 text-purple-600"}),(0,t.jsx)("span",{children:"Goals Progress"})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-sm",children:"Monthly Target"}),(0,t.jsx)("span",{className:"text-sm",children:"8/10"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-purple-600 h-2 rounded-full",style:{width:"80%"}})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-sm",children:"Earnings Goal"}),(0,t.jsx)("span",{className:"text-sm",children:"₹48K/₹60K"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"80%"}})})]})]})})]})]})]})})}},42867:(e,s,a)=>{Promise.resolve().then(a.bind(a,41628))},43157:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>c});var t=a(10557),r=a(68490),l=a(13172),n=a.n(l),i=a(68835),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(s,d);let c={children:["",{children:["referrals",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,65334)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\referrals\\analytics\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\referrals\\analytics\\page.tsx"],o={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/referrals/analytics/page",pathname:"/referrals/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},56525:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(99024).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63407:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(99024).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},65334:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\referrals\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\referrals\\analytics\\page.tsx","default")},66427:(e,s,a)=>{Promise.resolve().then(a.bind(a,65334))},76650:(e,s,a)=>{"use strict";a.d(s,{E:()=>i});var t=a(40969);a(73356);var r=a(52774),l=a(21764);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:s,...a}){return(0,t.jsx)("div",{className:(0,l.cn)(n({variant:s}),e),...a})}},79551:e=>{"use strict";e.exports=require("url")},85306:(e,s,a)=>{"use strict";a.d(s,{Ay:()=>c,T0:()=>x,lX:()=>o});var t=a(40969);a(73356);var r=a(46411),l=a(76650),n=a(21764),i=a(28496),d=a(12011);function c({title:e,description:s,icon:a,badge:c,actions:x,breadcrumbs:o,showBackButton:m=!1,className:h,gradient:p=!1}){let u=(0,d.useRouter)();return(0,t.jsxs)("div",{className:(0,n.cn)("relative overflow-hidden",p&&"bg-gradient-to-r from-sky-50 via-blue-50 to-indigo-50",!p&&"bg-white",h),children:[p&&(0,t.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,t.jsx)("div",{className:"relative px-4 sm:px-6 lg:px-8 py-6 sm:py-8",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[o&&o.length>0&&(0,t.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,t.jsx)("ol",{className:"flex items-center space-x-2 text-sm",children:o.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center",children:[s>0&&(0,t.jsx)("span",{className:"mx-2 text-gray-400",children:"/"}),e.href?(0,t.jsx)("button",{onClick:()=>u.push(e.href),className:"text-gray-600 hover:text-sky-600 transition-colors",children:e.label}):(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:e.label})]},s))})}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[m&&(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>u.back(),className:"flex-shrink-0 mt-1",children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}),a&&(0,t.jsx)("div",{className:(0,n.cn)("flex-shrink-0 p-3 rounded-xl",p?"bg-white/80 backdrop-blur-sm shadow-lg":"bg-sky-50","text-sky-600"),children:(0,t.jsx)(a,{className:"h-6 w-6"})}),(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center flex-wrap gap-3 mb-2",children:[(0,t.jsx)("h1",{className:(0,n.cn)("text-2xl sm:text-3xl font-bold text-gray-900 leading-tight",p&&"bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent"),children:e}),c&&(0,t.jsx)(l.E,{variant:c.variant||"default",className:(0,n.cn)("text-xs font-medium",c.className),children:c.text})]}),s&&(0,t.jsx)("p",{className:"text-gray-600 text-sm sm:text-base max-w-2xl leading-relaxed",children:s})]})]}),x&&(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"flex items-center space-x-3",children:x})})]})]})}),(0,t.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"})]})}function x({stats:e,className:s}){return(0,t.jsx)("div",{className:(0,n.cn)("grid grid-cols-2 sm:grid-cols-4 gap-4 mt-6",s),children:e.map((e,s)=>(0,t.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50",children:[(0,t.jsx)("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide",children:e.label}),(0,t.jsx)("p",{className:"text-lg font-bold text-gray-900 mt-1",children:e.value}),e.change&&(0,t.jsx)("p",{className:(0,n.cn)("text-xs font-medium mt-1","up"===e.trend&&"text-green-600","down"===e.trend&&"text-red-600","neutral"===e.trend&&"text-gray-600"),children:e.change})]},s))})}let o={Primary:({children:e,...s})=>(0,t.jsx)(r.$,{className:"bg-sky-600 hover:bg-sky-700 text-white shadow-lg",...s,children:e}),Secondary:({children:e,...s})=>(0,t.jsx)(r.$,{variant:"outline",className:"border-sky-200 text-sky-700 hover:bg-sky-50",...s,children:e}),Ghost:({children:e,...s})=>(0,t.jsx)(r.$,{variant:"ghost",className:"text-gray-600 hover:text-sky-600 hover:bg-sky-50",...s,children:e})}}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[755,3777,2544,7092,7555,2487,3427],()=>a(43157));module.exports=t})();