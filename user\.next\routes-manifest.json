{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/properties/[id]", "regex": "^/properties/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/properties/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/support/tickets/[ticketId]", "regex": "^/support/tickets/([^/]+?)(?:/)?$", "routeKeys": {"nxtPticketId": "nxtPticketId"}, "namedRegex": "^/support/tickets/(?<nxtPticketId>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/calculator", "regex": "^/calculator(?:/)?$", "routeKeys": {}, "namedRegex": "^/calculator(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/analytics", "regex": "^/dashboard/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/analytics(?:/)?$"}, {"page": "/faq", "regex": "^/faq(?:/)?$", "routeKeys": {}, "namedRegex": "^/faq(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/kyc", "regex": "^/kyc(?:/)?$", "routeKeys": {}, "namedRegex": "^/kyc(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/notifications", "regex": "^/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/notifications(?:/)?$"}, {"page": "/portfolio", "regex": "^/portfolio(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio(?:/)?$"}, {"page": "/portfolio/history", "regex": "^/portfolio/history(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio/history(?:/)?$"}, {"page": "/portfolio/investments", "regex": "^/portfolio/investments(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio/investments(?:/)?$"}, {"page": "/portfolio/performance", "regex": "^/portfolio/performance(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio/performance(?:/)?$"}, {"page": "/portfolio/returns", "regex": "^/portfolio/returns(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio/returns(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/properties", "regex": "^/properties(?:/)?$", "routeKeys": {}, "namedRegex": "^/properties(?:/)?$"}, {"page": "/properties/featured", "regex": "^/properties/featured(?:/)?$", "routeKeys": {}, "namedRegex": "^/properties/featured(?:/)?$"}, {"page": "/referrals", "regex": "^/referrals(?:/)?$", "routeKeys": {}, "namedRegex": "^/referrals(?:/)?$"}, {"page": "/referrals/analytics", "regex": "^/referrals/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/referrals/analytics(?:/)?$"}, {"page": "/referrals/earnings", "regex": "^/referrals/earnings(?:/)?$", "routeKeys": {}, "namedRegex": "^/referrals/earnings(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/reset-password", "regex": "^/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/reset\\-password(?:/)?$"}, {"page": "/support", "regex": "^/support(?:/)?$", "routeKeys": {}, "namedRegex": "^/support(?:/)?$"}, {"page": "/support/faq", "regex": "^/support/faq(?:/)?$", "routeKeys": {}, "namedRegex": "^/support/faq(?:/)?$"}, {"page": "/verify-email", "regex": "^/verify\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/verify\\-email(?:/)?$"}, {"page": "/wallet", "regex": "^/wallet(?:/)?$", "routeKeys": {}, "namedRegex": "^/wallet(?:/)?$"}, {"page": "/wallet/add", "regex": "^/wallet/add(?:/)?$", "routeKeys": {}, "namedRegex": "^/wallet/add(?:/)?$"}, {"page": "/wallet/transactions", "regex": "^/wallet/transactions(?:/)?$", "routeKeys": {}, "namedRegex": "^/wallet/transactions(?:/)?$"}, {"page": "/wallet/withdraw", "regex": "^/wallet/withdraw(?:/)?$", "routeKeys": {}, "namedRegex": "^/wallet/withdraw(?:/)?$"}, {"page": "/wishlist", "regex": "^/wishlist(?:/)?$", "routeKeys": {}, "namedRegex": "^/wishlist(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}