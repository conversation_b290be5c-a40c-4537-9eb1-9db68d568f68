(()=>{var e={};e.id=1968,e.ids=[1968],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5506:(e,t,s)=>{"use strict";s.d(t,{oR:()=>M});var r,a=s(73356);let i={data:""},n=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||i,l=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,o=/\/\*[^]*?\*\/|  +/g,c=/\n+/g,d=(e,t)=>{let s="",r="",a="";for(let i in e){let n=e[i];"@"==i[0]?"i"==i[1]?s=i+" "+n+";":r+="f"==i[1]?d(n,i):i+"{"+d(n,"k"==i[1]?"":t)+"}":"object"==typeof n?r+=d(n,t?t.replace(/([^,])+/g,e=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):i):null!=n&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=d.p?d.p(i,n):i+":"+n+";")}return s+(t&&a?t+"{"+a+"}":a)+r},m={},u=e=>{if("object"==typeof e){let t="";for(let s in e)t+=s+u(e[s]);return t}return e},p=(e,t,s,r,a)=>{let i=u(e),n=m[i]||(m[i]=(e=>{let t=0,s=11;for(;t<e.length;)s=101*s+e.charCodeAt(t++)>>>0;return"go"+s})(i));if(!m[n]){let t=i!==e?e:(e=>{let t,s,r=[{}];for(;t=l.exec(e.replace(o,""));)t[4]?r.shift():t[3]?(s=t[3].replace(c," ").trim(),r.unshift(r[0][s]=r[0][s]||{})):r[0][t[1]]=t[2].replace(c," ").trim();return r[0]})(e);m[n]=d(a?{["@keyframes "+n]:t}:t,s?"":"."+n)}let p=s&&m.g?m.g:null;return s&&(m.g=m[n]),((e,t,s,r)=>{r?t.data=t.data.replace(r,e):-1===t.data.indexOf(e)&&(t.data=s?e+t.data:t.data+e)})(m[n],t,r,p),n},x=(e,t,s)=>e.reduce((e,r,a)=>{let i=t[a];if(i&&i.call){let e=i(s),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;i=t?"."+t:e&&"object"==typeof e?e.props?"":d(e,""):!1===e?"":e}return e+r+(null==i?"":i)},"");function h(e){let t=this||{},s=e.call?e(t.p):e;return p(s.unshift?s.raw?x(s,[].slice.call(arguments,1),t.p):s.reduce((e,s)=>Object.assign(e,s&&s.call?s(t.p):s),{}):s,n(t.target),t.g,t.o,t.k)}h.bind({g:1});let f,y,g,v=h.bind({k:1});function b(e,t){let s=this||{};return function(){let r=arguments;function a(i,n){let l=Object.assign({},i),o=l.className||a.className;s.p=Object.assign({theme:y&&y()},l),s.o=/ *go\d+/.test(o),l.className=h.apply(s,r)+(o?" "+o:""),t&&(l.ref=n);let c=e;return e[0]&&(c=l.as||e,delete l.as),g&&c[0]&&g(l),f(c,l)}return t?t(a):a}}var N=e=>"function"==typeof e,w=(e,t)=>N(e)?e(t):e,k=(()=>{let e=0;return()=>(++e).toString()})(),S=(()=>{let e;return()=>e})(),P=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:s}=t;return P(e,{type:+!!e.toasts.find(e=>e.id===s.id),toast:s});case 3:let{toastId:r}=t;return{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},T=[],A={toasts:[],pausedAt:void 0},I=e=>{A=P(A,e),T.forEach(e=>{e(A)})},C={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},q=(e={})=>{let[t,s]=j(A),r=Q(A);H(()=>(r.current!==A&&s(A),T.push(s),()=>{let e=T.indexOf(s);e>-1&&T.splice(e,1)}),[]);let a=t.toasts.map(t=>{var s,r,a;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(s=e[t.type])?void 0:s.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(r=e[t.type])?void 0:r.duration)||(null==e?void 0:e.duration)||C[t.type],style:{...e.style,...null==(a=e[t.type])?void 0:a.style,...t.style}}});return{...t,toasts:a}},D=(e,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...s,id:(null==s?void 0:s.id)||k()}),E=e=>(t,s)=>{let r=D(t,e,s);return I({type:2,toast:r}),r.id},M=(e,t)=>E("blank")(e,t);M.error=E("error"),M.success=E("success"),M.loading=E("loading"),M.custom=E("custom"),M.dismiss=e=>{I({type:3,toastId:e})},M.remove=e=>I({type:4,toastId:e}),M.promise=(e,t,s)=>{let r=M.loading(t.loading,{...s,...null==s?void 0:s.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=t.success?w(t.success,e):void 0;return a?M.success(a,{id:r,...s,...null==s?void 0:s.success}):M.dismiss(r),e}).catch(e=>{let a=t.error?w(t.error,e):void 0;a?M.error(a,{id:r,...s,...null==s?void 0:s.error}):M.dismiss(r)}),e};var R=(e,t)=>{I({type:1,toast:{id:e,height:t}})},$=()=>{I({type:5,time:Date.now()})},O=new Map,L=1e3,_=(e,t=L)=>{if(O.has(e))return;let s=setTimeout(()=>{O.delete(e),I({type:4,toastId:e})},t);O.set(e,s)},U=v`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,F=v`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,W=v`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=(b("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${U} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${F} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${W} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,v`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`),z=(b("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${B} 1s linear infinite;
`,v`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`),Z=v`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Y=(b("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${z} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Z} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,b("div")`
  position: absolute;
`,b("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,v`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`);b("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Y} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,b("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,b("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,r=a.createElement,d.p=void 0,f=r,y=void 0,g=void 0,h`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`},5568:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},7409:(e,t,s)=>{"use strict";s.d(t,{N:()=>a});var r=s(73356),a=globalThis?.document?r.useLayoutEffect:()=>{}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12049:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(10557),a=s(68490),i=s(13172),n=s.n(i),l=s(68835),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["properties",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,64382)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\properties\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\properties\\[id]\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/properties/[id]/page",pathname:"/properties/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22195:(e,t,s)=>{"use strict";s.d(t,{C:()=>n});var r=s(73356),a=s(24629),i=s(7409),n=e=>{let{present:t,children:s}=e,n=function(e){var t,s;let[a,n]=r.useState(),o=r.useRef(null),c=r.useRef(e),d=r.useRef("none"),[m,u]=(t=e?"mounted":"unmounted",s={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>s[e][t]??e,t));return r.useEffect(()=>{let e=l(o.current);d.current="mounted"===m?e:"none"},[m]),(0,i.N)(()=>{let t=o.current,s=c.current;if(s!==e){let r=d.current,a=l(t);e?u("MOUNT"):"none"===a||t?.display==="none"?u("UNMOUNT"):s&&r!==a?u("ANIMATION_OUT"):u("UNMOUNT"),c.current=e}},[e,u]),(0,i.N)(()=>{if(a){let e,t=a.ownerDocument.defaultView??window,s=s=>{let r=l(o.current).includes(s.animationName);if(s.target===a&&r&&(u("ANIMATION_END"),!c.current)){let s=a.style.animationFillMode;a.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=s)})}},r=e=>{e.target===a&&(d.current=l(o.current))};return a.addEventListener("animationstart",r),a.addEventListener("animationcancel",s),a.addEventListener("animationend",s),()=>{t.clearTimeout(e),a.removeEventListener("animationstart",r),a.removeEventListener("animationcancel",s),a.removeEventListener("animationend",s)}}u("ANIMATION_END")},[a,u]),{isPresent:["mounted","unmountSuspended"].includes(m),ref:r.useCallback(e=>{o.current=e?getComputedStyle(e):null,n(e)},[])}}(t),o="function"==typeof s?s({present:n.isPresent}):r.Children.only(s),c=(0,a.s)(n.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,s=t&&"isReactWarning"in t&&t.isReactWarning;return s?e.ref:(s=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof s||n.isPresent?r.cloneElement(o,{ref:c}):null};function l(e){return e?.animationName||"none"}n.displayName="Presence"},26314:(e,t,s)=>{"use strict";s.d(t,{i:()=>l});var r,a=s(73356),i=s(7409),n=(r||(r=s.t(a,2)))[" useInsertionEffect ".trim().toString()]||i.N;function l({prop:e,defaultProp:t,onChange:s=()=>{},caller:r}){let[i,l,o]=function({defaultProp:e,onChange:t}){let[s,r]=a.useState(e),i=a.useRef(s),l=a.useRef(t);return n(()=>{l.current=t},[t]),a.useEffect(()=>{i.current!==s&&(l.current?.(s),i.current=s)},[s,i]),[s,r,l]}({defaultProp:t,onChange:s}),c=void 0!==e,d=c?e:i;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[d,a.useCallback(t=>{if(c){let s="function"==typeof t?t(e):t;s!==e&&o.current?.(s)}else l(t)},[c,e,l,o])]}Symbol("RADIX:SYNC_STATE")},27627:(e,t,s)=>{"use strict";s.d(t,{$k:()=>o,Zj:()=>i,m9:()=>x,zq:()=>r});let{useGetPropertiesQuery:r,useGetFeaturedPropertiesQuery:a,useGetPropertyByIdQuery:i,useGetPropertyAnalyticsQuery:n,useGetSimilarPropertiesQuery:l,useCalculateInvestmentQuery:o,useGetPropertyReviewsQuery:c,useAddPropertyReviewMutation:d,useGetPropertyLocationsQuery:m,useGetPropertyTypesQuery:u,useSearchPropertiesQuery:p,usePurchasePropertyStocksMutation:x}=s(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getProperties:e.query({query:e=>({url:"/properties",params:{page:e.page||1,limit:e.limit||12,...e.search&&{search:e.search},...e.type&&{type:e.type},...e.status&&{status:e.status},...void 0!==e.featured&&{featured:e.featured},...e.minPrice&&{minPrice:e.minPrice},...e.maxPrice&&{maxPrice:e.maxPrice},...e.location&&{location:e.location},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),providesTags:e=>e?.data?.data?[...e.data.data.map(({_id:e})=>({type:"Property",id:e})),{type:"Property",id:"LIST"}]:[{type:"Property",id:"LIST"}],keepUnusedDataFor:600}),getFeaturedProperties:e.query({query:({limit:e=6})=>({url:"/properties/featured",params:{limit:e}}),providesTags:[{type:"Property",id:"FEATURED"}],keepUnusedDataFor:900}),getPropertyById:e.query({query:e=>`/properties/${e}`,providesTags:(e,t,s)=>[{type:"Property",id:s}],keepUnusedDataFor:1800}),getPropertyAnalytics:e.query({query:e=>`/properties/${e}/analytics`,providesTags:(e,t,s)=>[{type:"Property",id:`${s}-analytics`}],keepUnusedDataFor:300}),getSimilarProperties:e.query({query:({propertyId:e,limit:t=4})=>({url:`/properties/${e}/similar`,params:{limit:t}}),providesTags:(e,t,{propertyId:s})=>[{type:"Property",id:`${s}-similar`}],keepUnusedDataFor:1200}),calculateInvestment:e.query({query:({propertyId:e,stockQuantity:t,investmentPeriod:s})=>({url:`/properties/${e}/calculate`,params:{stockQuantity:t,investmentPeriod:s}}),keepUnusedDataFor:0}),getPropertyReviews:e.query({query:({propertyId:e,page:t=1,limit:s=10,rating:r})=>({url:`/properties/${e}/reviews`,params:{page:t,limit:s,...r&&{rating:r}}}),providesTags:(e,t,{propertyId:s})=>[{type:"Property",id:`${s}-reviews`}],keepUnusedDataFor:600}),addPropertyReview:e.mutation({query:({propertyId:e,...t})=>({url:`/properties/${e}/reviews`,method:"POST",body:t}),invalidatesTags:(e,t,{propertyId:s})=>[{type:"Property",id:`${s}-reviews`},{type:"Property",id:s}]}),getPropertyLocations:e.query({query:()=>"/properties/locations",providesTags:[{type:"Property",id:"LOCATIONS"}],keepUnusedDataFor:3600}),getPropertyTypes:e.query({query:()=>"/properties/types",providesTags:[{type:"Property",id:"TYPES"}],keepUnusedDataFor:3600}),searchProperties:e.query({query:({query:e,limit:t=10})=>({url:"/properties/search",params:{q:e,limit:t}}),keepUnusedDataFor:0}),purchasePropertyStocks:e.mutation({query:e=>({url:"/transactions/stock-purchase",method:"POST",body:e}),invalidatesTags:(e,t,{propertyId:s})=>[{type:"Property",id:s},{type:"Property",id:"LIST"},{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]})})})},28149:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},28496:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29655:(e,t,s)=>{"use strict";s.d(t,{AR:()=>a,rx:()=>r});let{useGetWalletBalanceQuery:r,useGetWalletTransactionsQuery:a,useAddMoneyToWalletMutation:i,useWithdrawMoneyMutation:n,useGetPaymentMethodsQuery:l,useAddPaymentMethodMutation:o,useUpdatePaymentMethodMutation:c,useDeletePaymentMethodMutation:d,useVerifyPaymentMethodMutation:m,useGetTransactionByIdQuery:u,useGetWalletAnalyticsQuery:p,useExportWalletStatementMutation:x,useSetTransactionAlertsMutation:h,useGetWalletLimitsQuery:f,useRequestLimitIncreaseMutation:y}=s(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getWalletBalance:e.query({query:()=>"/wallet",providesTags:[{type:"Wallet",id:"BALANCE"}],keepUnusedDataFor:120}),getWalletTransactions:e.query({query:e=>({url:"/wallet/transactions",params:{page:e.page||1,limit:e.limit||20,...e.type&&{type:e.type},...e.status&&{status:e.status},...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),transformResponse:e=>e?.success&&e?.data?.data?.length?e:{success:!0,message:"Transactions retrieved successfully",data:{data:[{_id:"demo-txn-1",type:"stock_purchase",amount:25e3,status:"completed",description:"Property Investment - Luxury Apartments",createdAt:new Date().toISOString(),reference:"TXN001"},{_id:"demo-txn-2",type:"deposit",amount:5e4,status:"completed",description:"Wallet Deposit",createdAt:new Date(Date.now()-864e5).toISOString(),reference:"TXN002"},{_id:"demo-txn-3",type:"investment",amount:1e5,status:"completed",description:"Property Investment - Commercial Complex",createdAt:new Date(Date.now()-1728e5).toISOString(),reference:"TXN003"}],pagination:{page:1,limit:20,total:3,pages:1}}},providesTags:e=>e?.data?.data?[...e.data.data.map(({_id:e})=>({type:"Transaction",id:e})),{type:"Transaction",id:"LIST"}]:[{type:"Transaction",id:"LIST"}],keepUnusedDataFor:300}),addMoneyToWallet:e.mutation({query:e=>({url:"/wallet/add-funds",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),withdrawMoney:e.mutation({query:e=>({url:"/wallet/withdraw",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),getPaymentMethods:e.query({query:()=>"/wallet/payment-methods",providesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}],keepUnusedDataFor:600}),addPaymentMethod:e.mutation({query:e=>({url:"/wallet/payment-methods",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),updatePaymentMethod:e.mutation({query:({id:e,...t})=>({url:`/wallet/payment-methods/${e}`,method:"PUT",body:t}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),deletePaymentMethod:e.mutation({query:e=>({url:`/wallet/payment-methods/${e}`,method:"DELETE"}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),verifyPaymentMethod:e.mutation({query:({id:e,verificationData:t})=>({url:`/wallet/payment-methods/${e}/verify`,method:"POST",body:t}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),getTransactionById:e.query({query:e=>`/wallet/transactions/${e}`,providesTags:(e,t,s)=>[{type:"Transaction",id:s}],keepUnusedDataFor:1800}),getWalletAnalytics:e.query({query:({period:e="1Y"})=>({url:"/wallet/analytics",params:{period:e}}),providesTags:[{type:"Wallet",id:"ANALYTICS"}],keepUnusedDataFor:900}),exportWalletStatement:e.mutation({query:e=>({url:"/wallet/export-statement",method:"POST",body:e})}),setTransactionAlerts:e.mutation({query:e=>({url:"/wallet/alerts",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"}]}),getWalletLimits:e.query({query:()=>"/wallet/limits",providesTags:[{type:"Wallet",id:"LIMITS"}],keepUnusedDataFor:3600}),requestLimitIncrease:e.mutation({query:e=>({url:"/wallet/request-limit-increase",method:"POST",body:e})})})})},33873:e=>{"use strict";e.exports=require("path")},50537:(e,t,s)=>{Promise.resolve().then(s.bind(s,64382))},59705:(e,t,s)=>{"use strict";s.d(t,{B:()=>o});var r,a=s(73356),i=s(7409),n=(r||(r=s.t(a,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function o(e){let[t,s]=a.useState(n());return(0,i.N)(()=>{e||s(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},60952:(e,t,s)=>{"use strict";function r(e,t,{checkForDefaultPrevented:s=!0}={}){return function(r){if(e?.(r),!1===s||!r.defaultPrevented)return t?.(r)}}s.d(t,{m:()=>r})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63407:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(99024).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},64382:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\properties\\[id]\\page.tsx","default")},71060:(e,t,s)=>{"use strict";s.d(t,{c:()=>a});var r=s(73356);function a(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},76650:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(40969);s(73356);var a=s(52774),i=s(21764);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,i.cn)(n({variant:t}),e),...s})}},76818:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var r=s(40969),a=s(73356),i=s(12011),n=s(37020),l=s(66949),o=s(46411),c=s(24689),d=s(28496),m=s(9041),u=s(28149),p=s(28293),x=s(58573),h=s(63407),f=s(14125);let y=(0,s(99024).A)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);var g=s(37124),v=s(56525),b=s(5568),N=s(60545),w=s(79929),k=s(7157),S=s(21764),P=s(27627),T=s(34882),A=s(57387),I=s(12053),C=s(76650),q=s(81861),D="horizontal",E=["horizontal","vertical"],M=a.forwardRef((e,t)=>{var s;let{decorative:a,orientation:i=D,...n}=e,l=(s=i,E.includes(s))?i:D;return(0,r.jsx)(q.sG.div,{"data-orientation":l,...a?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...n,ref:t})});M.displayName="Separator";let R=a.forwardRef(({className:e,orientation:t="horizontal",decorative:s=!0,...a},i)=>(0,r.jsx)(M,{ref:i,decorative:s,orientation:t,className:(0,S.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));R.displayName=M.displayName;var $=s(26712),O=s(44356),L=s(29419),_=s(1110),U=s(5506),F=s(29655),W=s(79249);function B({isOpen:e,onClose:t,property:s,onPurchase:n}){let[c,d]=(0,a.useState)(1),[m,u]=(0,a.useState)(""),[p,x]=(0,a.useState)(!1),h=(0,i.useRouter)(),{data:f}=(0,F.rx)(),{data:y}=(0,W.xs)(),g=f?.data?.balance||0,v=y?.data?.kyc?.status,[b,{isLoading:N}]=(0,P.m9)(),w=c*s.pricePerShare,S=g>=w,q=s.pricePerShare>0?Math.floor(g/s.pricePerShare):0,D=Math.min(s.availableShares,q),E=e=>{let t=parseInt(e)||0;t>=1&&t<=D&&d(t)},M=async()=>{if("approved"!==v){U.oR.error("KYC approval required for investment"),t(),h.push("/kyc?message=kyc_required");return}if(!S)return void U.oR.error("Insufficient wallet balance");if(c<1||c>s.availableShares)return void U.oR.error("Invalid number of shares");if(w<s.minimumInvestment)return void U.oR.error(`Minimum investment is ₹${s.minimumInvestment.toLocaleString()}`);x(!0);try{await b({propertyId:s._id,stockQuantity:c,paymentMethod:"wallet",referralCode:m.trim()||void 0}).unwrap(),U.oR.success("Property purchased successfully!"),t(),n&&await n({propertyId:s._id,shares:c,amount:w,referralCode:m.trim()||void 0})}catch(e){console.error("Purchase error:",e),U.oR.error(e?.data?.message||"Failed to purchase property")}finally{x(!1)}};return(0,r.jsx)(T.lG,{open:e,onOpenChange:t,children:(0,r.jsxs)(T.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)(T.c7,{children:(0,r.jsx)(T.L3,{className:"text-xl font-semibold",children:"Purchase Property Shares"})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("img",{src:s.images[0]||"/placeholder-property.jpg",alt:s.title,className:"w-20 h-20 object-cover rounded-lg"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg",children:s.title}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:s.location}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,r.jsxs)(C.E,{variant:"secondary",children:["₹",s.pricePerShare.toLocaleString(),"/share"]}),(0,r.jsxs)(C.E,{variant:"outline",children:[s.availableShares," available"]}),(0,r.jsxs)(C.E,{className:"bg-green-100 text-green-800",children:[s.expectedReturns,"% returns"]})]})]})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)($.A,{className:"h-5 w-5 text-blue-600"}),(0,r.jsx)("span",{className:"font-medium",children:"Wallet Balance"})]}),(0,r.jsxs)("span",{className:"text-lg font-semibold text-green-600",children:["₹",g.toLocaleString()]})]})})}),"approved"!==v&&(0,r.jsx)(l.Zp,{className:"border-orange-200 bg-orange-50",children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(O.A,{className:"h-5 w-5 text-orange-600"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"font-medium text-orange-800",children:"KYC Verification Required"}),(0,r.jsx)("p",{className:"text-sm text-orange-700",children:"Complete your KYC verification to start investing in properties."})]}),(0,r.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>{t(),h.push("/kyc")},className:"border-orange-300 text-orange-700 hover:bg-orange-100",children:"Complete KYC"})]})})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(I.J,{htmlFor:"shares",className:"text-sm font-medium",children:"Number of Shares"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,r.jsx)(A.p,{id:"shares",type:"number",min:"1",max:D,value:c,onChange:e=>E(e.target.value),className:"flex-1"}),(0,r.jsxs)(o.$,{variant:"outline",size:"sm",onClick:()=>{d(D)},disabled:0===D,children:["Max (",D,")"]})]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Maximum affordable: ",q," shares"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(I.J,{htmlFor:"referralCode",className:"text-sm font-medium",children:"Referral Code (Optional)"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,r.jsx)(L.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)(A.p,{id:"referralCode",type:"text",placeholder:"Enter referral code for bonus",value:m,onChange:e=>u(e.target.value.toUpperCase()),className:"flex-1"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Get bonus rewards with a valid referral code"})]})]}),(0,r.jsx)(R,{}),(0,r.jsx)(l.Zp,{children:(0,r.jsxs)(l.Wu,{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,r.jsx)(k.A,{className:"h-5 w-5 text-blue-600"}),(0,r.jsx)("span",{className:"font-medium",children:"Purchase Summary"})]}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Shares:"}),(0,r.jsx)("span",{children:c.toLocaleString()})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Price per share:"}),(0,r.jsxs)("span",{children:["₹",s.pricePerShare.toLocaleString()]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Subtotal:"}),(0,r.jsxs)("span",{children:["₹",w.toLocaleString()]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Platform fee (2%):"}),(0,r.jsxs)("span",{children:["₹",Math.round(.02*w).toLocaleString()]})]}),(0,r.jsx)(R,{}),(0,r.jsxs)("div",{className:"flex justify-between font-semibold text-lg",children:[(0,r.jsx)("span",{children:"Total Amount:"}),(0,r.jsxs)("span",{children:["₹",Math.round(1.02*w).toLocaleString()]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,r.jsx)("span",{children:"Remaining balance:"}),(0,r.jsxs)("span",{children:["₹",Math.max(0,g-Math.round(1.02*w)).toLocaleString()]})]})]})]})}),!S&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsx)(O.A,{className:"h-5 w-5 text-red-600"}),(0,r.jsx)("span",{className:"text-red-700 text-sm",children:"Insufficient wallet balance. Please add money to your wallet."})]}),w<s.minimumInvestment&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,r.jsx)(O.A,{className:"h-5 w-5 text-yellow-600"}),(0,r.jsxs)("span",{className:"text-yellow-700 text-sm",children:["Minimum investment amount is ₹",s.minimumInvestment.toLocaleString()]})]}),S&&w>=s.minimumInvestment&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,r.jsx)(_.A,{className:"h-5 w-5 text-green-600"}),(0,r.jsxs)("span",{className:"text-green-700 text-sm",children:["Ready to purchase! You will own ",(c/s.totalShares*100).toFixed(2),"% of this property."]})]}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)(o.$,{variant:"outline",onClick:t,className:"flex-1",disabled:p,children:"Cancel"}),(0,r.jsx)(o.$,{onClick:M,className:"flex-1",disabled:"approved"!==v||!S||w<s.minimumInvestment||p,children:p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing..."]}):"approved"!==v?"Complete KYC to Purchase":`Purchase for ₹${Math.round(1.02*w).toLocaleString()}`})]})]})]})})}function z(){let e=(0,i.useParams)().id,[t,s]=(0,a.useState)(0),[T,A]=(0,a.useState)(1),[I,C]=(0,a.useState)(!1),[q,D]=(0,a.useState)(!1),[E,M]=(0,a.useState)(!1),{data:R,isLoading:$,error:O}=(0,P.Zj)(e),{data:L,isLoading:_}=(0,P.$k)({propertyId:e,stockQuantity:T,investmentPeriod:12},{skip:!E}),F=R?.data,W=L?.data,z=async e=>{try{console.log("Purchase data:",e),U.oR.success(`Successfully purchased ${e.shares} shares for ₹${e.amount.toLocaleString()}!`)}catch(e){throw console.error("Purchase error:",e),e}};if($)return(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,r.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg"}),(0,r.jsx)("div",{className:"h-48 bg-gray-200 rounded-lg"})]}),(0,r.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg"})]})]})});if(O||!F)return(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(c.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Property not found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"The property you're looking for doesn't exist or has been removed."}),(0,r.jsxs)(o.$,{onClick:()=>window.history.back(),children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Go Back"]})]})});let Z=T*(F.stockInfo?.stockPrice||0);return(0,r.jsxs)(n.A,{children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(o.$,{variant:"outline",onClick:()=>window.history.back(),className:"flex items-center space-x-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Back to Properties"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)(o.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Save"]}),(0,r.jsxs)(o.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Share"]})]})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:F.name}),F.featured&&(0,r.jsxs)("div",{className:"bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-medium flex items-center",children:[(0,r.jsx)(p.A,{className:"h-3 w-3 mr-1"}),"Featured"]})]}),(0,r.jsxs)("div",{className:"flex items-center text-gray-600 mb-4",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"string"==typeof F.location?F.location:F.location?.address||"Location not specified"]}),(0,r.jsx)("p",{className:"text-gray-700 mb-4",children:F.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)("span",{children:"string"==typeof F.developer?F.developer:F.developer?.name||"Developer"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsxs)("span",{children:["Launch: ",(0,S.Yq)(F.launchDate)]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsxs)("span",{children:[F.soldStocks," investors"]})]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-1",children:(0,S.vv)(F.pricePerStock)}),(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"per stock"}),(0,r.jsxs)("div",{className:"text-lg font-semibold text-green-600",children:[(0,S.Ee)(F.roi)," returns"]})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-0",children:F.images&&F.images.length>0?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"relative h-96 rounded-t-lg overflow-hidden",children:[(0,r.jsx)("img",{src:F.images[t]?.url,alt:F.name,className:"w-full h-full object-cover"}),(0,r.jsx)("button",{className:"absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70",children:(0,r.jsx)(y,{className:"h-4 w-4"})})]}),F.images.length>1&&(0,r.jsx)("div",{className:"flex space-x-2 p-4 overflow-x-auto",children:F.images.map((e,a)=>(0,r.jsx)("button",{onClick:()=>s(a),className:`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${t===a?"border-blue-500":"border-gray-200"}`,children:(0,r.jsx)("img",{src:e.url,alt:`${F.name} ${a+1}`,className:"w-full h-full object-cover"})},a))})]}):(0,r.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg flex items-center justify-center",children:(0,r.jsx)(c.A,{className:"h-16 w-16 text-gray-400"})})})}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Property Details"})}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Total Stocks"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:(F.stockInfo?.totalStocks||0).toLocaleString()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Available"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:(F.stockInfo?.availableStocks||0).toLocaleString()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Sold"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:(F.stockInfo?.stocksSold||0).toLocaleString()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Progress"}),(0,r.jsxs)("p",{className:"text-2xl font-bold",children:[F.stockInfo?.totalStocks?Math.round((F.stockInfo?.stocksSold||0)/F.stockInfo.totalStocks*100):0,"%"]})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,r.jsx)("span",{children:"Funding Progress"}),(0,r.jsxs)("span",{children:[F.stockInfo?.stocksSold||0," / ",F.stockInfo?.totalStocks||0," stocks"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${(F.stockInfo?.stocksSold||0)/(F.stockInfo?.totalStocks||1)*100}%`}})})]})]})]}),F.amenities&&F.amenities.length>0&&(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Amenities"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:F.amenities.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm",children:e})]},t))})})]}),F.documents&&F.documents.length>0&&(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Documents"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"space-y-3",children:F.documents.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded flex items-center justify-center",children:(0,r.jsx)(v.A,{className:"h-4 w-4 text-blue-600"})}),(0,r.jsx)("span",{className:"font-medium",children:e.name})]}),(0,r.jsxs)(o.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"View"]})]},t))})})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(l.Zp,{className:"sticky top-6",children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Invest in this Property"})}),(0,r.jsxs)(l.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Number of Stocks"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>A(Math.max(1,T-1)),disabled:T<=1,children:(0,r.jsx)(N.A,{className:"h-4 w-4"})}),(0,r.jsx)("input",{type:"number",value:T,onChange:e=>A(Math.max(1,parseInt(e.target.value)||1)),className:"flex-1 text-center px-3 py-2 border border-gray-300 rounded-lg",min:"1",max:F.availableStocks}),(0,r.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>A(Math.min(F.availableStocks,T+1)),disabled:T>=F.availableStocks,children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-3 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Stock Price"}),(0,r.jsx)("span",{className:"font-medium",children:F.stockInfo?.stockPrice?(0,S.vv)(F.stockInfo.stockPrice):"Not Available"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Quantity"}),(0,r.jsxs)("span",{className:"font-medium",children:[T," stocks"]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-lg font-semibold border-t pt-3",children:[(0,r.jsx)("span",{children:"Total Investment"}),(0,r.jsx)("span",{className:"text-blue-600",children:(0,S.vv)(Z)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Expected Returns"}),(0,r.jsx)("span",{className:"font-medium text-green-600",children:F.stockInfo?.expectedROI?(0,S.Ee)(F.stockInfo.expectedROI):F.expectedReturns?(0,S.Ee)(F.expectedReturns):"TBD"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(o.$,{onClick:()=>D(!0),className:"w-full",disabled:0===(F.stockInfo?.availableStocks||0),children:0===(F.stockInfo?.availableStocks||0)?"Sold Out":"Buy Shares"}),(0,r.jsxs)(o.$,{variant:"outline",onClick:()=>M(!0),className:"w-full",children:[(0,r.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Calculate Returns"]})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:["Minimum investment: ",(0,S.vv)(F.stockInfo?.minimumPurchase||F.stockInfo?.stockPrice||0)]})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Key Metrics"})}),(0,r.jsxs)(l.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Property Type"}),(0,r.jsx)("span",{className:"font-medium capitalize",children:F.type})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Total Value"}),(0,r.jsx)("span",{className:"font-medium",children:(0,S.vv)(F.totalValue)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Expected ROI"}),(0,r.jsx)("span",{className:"font-medium text-green-600",children:(0,S.Ee)(F.roi)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Completion Date"}),(0,r.jsx)("span",{className:"font-medium",children:(0,S.Yq)(F.completionDate)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Status"}),(0,r.jsx)("span",{className:`font-medium capitalize ${"active"===F.status?"text-green-600":"upcoming"===F.status?"text-blue-600":"text-gray-600"}`,children:F.status})]})]})]})]})]})]}),I&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Confirm Investment"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:F.name}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Stocks:"}),(0,r.jsx)("span",{children:T})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Price per stock:"}),(0,r.jsx)("span",{children:(0,S.vv)(F.pricePerStock)})]}),(0,r.jsxs)("div",{className:"flex justify-between font-semibold border-t pt-2",children:[(0,r.jsx)("span",{children:"Total:"}),(0,r.jsx)("span",{children:(0,S.vv)(Z)})]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(o.$,{variant:"outline",onClick:()=>C(!1),className:"flex-1",children:"Cancel"}),(0,r.jsx)(o.$,{className:"flex-1",children:"Confirm Investment"})]})]})]})}),E&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Investment Calculator"}),(0,r.jsxs)("div",{className:"space-y-4",children:[W&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Investment Amount:"}),(0,r.jsx)("span",{className:"font-medium",children:(0,S.vv)(W.totalAmount)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Expected Returns (1 year):"}),(0,r.jsx)("span",{className:"font-medium text-green-600",children:(0,S.vv)(W.yearlyReturns)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Maturity Amount:"}),(0,r.jsx)("span",{className:"font-medium text-blue-600",children:(0,S.vv)(W.maturityAmount)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"ROI:"}),(0,r.jsx)("span",{className:"font-medium",children:(0,S.Ee)(W.roi)})]})]}),(0,r.jsx)(o.$,{variant:"outline",onClick:()=>M(!1),className:"w-full",children:"Close"})]})]})}),F&&(0,r.jsx)(B,{isOpen:q,onClose:()=>D(!1),property:{_id:F._id,title:F.name,pricePerShare:F.stockInfo?.stockPrice||0,availableShares:F.stockInfo?.availableStocks||0,totalShares:F.stockInfo?.totalStocks||0,minimumInvestment:F.stockInfo?.minimumPurchase||F.stockInfo?.stockPrice||0,images:F.images?.map(e=>e.url)||[],location:"string"==typeof F.location?F.location:F.location?.address||"",expectedReturns:F.stockInfo?.expectedROI||F.expectedReturns||0},onPurchase:z})]})}},79551:e=>{"use strict";e.exports=require("url")},92393:(e,t,s)=>{Promise.resolve().then(s.bind(s,76818))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[755,3777,2544,7092,7555,6521,2487,3427,5337],()=>s(12049));module.exports=r})();