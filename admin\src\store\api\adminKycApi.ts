import { base<PERSON><PERSON>, <PERSON>piR<PERSON>po<PERSON>, BaseQueryParams, createQueryParams } from './baseApi'

// KYC Document Interface
export interface KYCDocument {
  _id: string
  type: 'identity' | 'address' | 'income' | 'bank' | 'photo' | 'signature' | 'other'
  subType: string
  documentNumber?: string
  issuedBy?: string
  issuedDate?: string
  expiryDate?: string
  status: 'pending' | 'verified' | 'rejected'
  rejectionReason?: string
  fileUrl?: string
  uploadedAt: string
  verifiedAt?: string
}

// KYC Status Interface
export interface KYCStatus {
  _id: string
  userId: {
    _id: string
    firstName: string
    lastName: string
    email: string
    phone?: string
    profileImage?: string
  }
  status: 'not_started' | 'pending' | 'approved' | 'rejected' | 'under_review'
  level: 'basic' | 'advanced' | 'premium'
  submittedAt?: string
  reviewedAt?: string
  reviewedBy?: {
    _id: string
    firstName: string
    lastName: string
  }
  rejectionReason?: string
  documents: KYCDocument[]
  personalInfo: {
    nationality?: string
    placeOfBirth?: string
    gender?: string
    maritalStatus?: string
  }
  address: {
    street?: string
    city?: string
    state?: string
    postalCode?: string
    country?: string
    addressType?: 'permanent' | 'current' | 'mailing'
    residenceSince?: string
  }
  identityInfo: {
    aadharNumber?: string
    panNumber?: string
    passportNumber?: string
    drivingLicenseNumber?: string
  }
  bankInfo: {
    accountNumber?: string
    ifscCode?: string
    bankName?: string
    accountType?: string
    accountHolderName?: string
  }
  createdAt: string
  updatedAt: string
}

export interface KYCQueryParams extends BaseQueryParams {
  status?: string
  level?: string
  userId?: string
}

export interface KYCApprovalRequest {
  status: 'approved' | 'rejected'
  rejectionReason?: string
  level?: 'basic' | 'advanced' | 'premium'
  notes?: string
}

export const adminKycApi = baseApi.injectEndpoints({
  overrideExisting: true,
  endpoints: (builder) => ({
    // Get all KYC submissions for admin review
    getAllKYCSubmissions: builder.query<ApiResponse<{
      kycs: KYCStatus[]
      total: number
      page: number
      limit: number
      totalPages: number
    }>, KYCQueryParams>({
      query: (params = {}) => ({
        url: '/admin/kyc',
        params: createQueryParams(params)
      }),
      providesTags: ['KYC'],
    }),

    // Get KYC by ID for detailed review
    getKYCById: builder.query<ApiResponse<KYCStatus>, string>({
      query: (id) => `/admin/kyc/${id}`,
      providesTags: (_, __, id) => [{ type: 'KYC', id }],
    }),

    // Get KYC by User ID
    getKYCByUserId: builder.query<ApiResponse<KYCStatus>, string>({
      query: (userId) => `/kyc/user/${userId}`,
      providesTags: (_, __, userId) => [{ type: 'KYC', id: userId }],
    }),

    // Approve or reject KYC
    updateKYCStatus: builder.mutation<ApiResponse<KYCStatus>, { id: string; data: KYCApprovalRequest }>({
      query: ({ id, data }) => ({
        url: `/kyc/${id}/status`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (_, __, { id }) => [{ type: 'KYC', id }, 'KYC', 'User'],
    }),

    // Get KYC statistics for admin dashboard
    getKYCStats: builder.query<ApiResponse<{
      totalSubmissions: number
      pendingReview: number
      approved: number
      rejected: number
      underReview: number
      basicLevel: number
      advancedLevel: number
      premiumLevel: number
      recentSubmissions: Array<{
        _id: string
        userId: {
          firstName: string
          lastName: string
          email: string
        }
        status: string
        submittedAt: string
      }>
    }>, void>({
      query: () => '/admin/kyc/stats',
      providesTags: ['KYC'],
    }),

    // Bulk approve/reject KYCs
    bulkUpdateKYCStatus: builder.mutation<ApiResponse<{ updated: number }>, {
      ids: string[]
      data: KYCApprovalRequest
    }>({
      query: ({ ids, data }) => ({
        url: '/admin/kyc/bulk-update',
        method: 'PUT',
        body: { ids, ...data },
      }),
      invalidatesTags: ['KYC', 'User'],
    }),

    // Get KYC document by ID
    getKYCDocument: builder.query<ApiResponse<KYCDocument>, string>({
      query: (documentId) => `/admin/kyc/document/${documentId}`,
      providesTags: (_, __, id) => [{ type: 'KYC', id }],
    }),

    // Verify/reject specific document
    updateDocumentStatus: builder.mutation<ApiResponse<KYCDocument>, {
      documentId: string
      status: 'verified' | 'rejected'
      rejectionReason?: string
    }>({
      query: ({ documentId, ...data }) => ({
        url: `/admin/kyc/document/${documentId}/status`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['KYC'],
    }),

    // Get KYC history/audit trail
    getKYCHistory: builder.query<ApiResponse<Array<{
      action: string
      status: string
      timestamp: string
      details?: string
      performedBy: {
        _id: string
        firstName: string
        lastName: string
      }
    }>>, string>({
      query: (kycId) => `/admin/kyc/${kycId}/history`,
      providesTags: (_, __, id) => [{ type: 'KYC', id }],
    }),

    // Download KYC PDF
    downloadKYCPDF: builder.mutation<Blob, string>({
      query: (kycId) => ({
        url: `/kyc/${kycId}/download-pdf`,
        method: 'GET',
        responseHandler: (response) => response.blob(),
      }),
    }),

    // Export KYC data
    exportKYCData: builder.mutation<ApiResponse<{ downloadUrl: string }>, {
      format: 'csv' | 'excel' | 'pdf'
      filters?: KYCQueryParams
    }>({
      query: (data) => ({
        url: '/admin/kyc/export',
        method: 'POST',
        body: data,
      }),
    }),
  }),
})

export const {
  useGetAllKYCSubmissionsQuery,
  useGetKYCByIdQuery,
  useGetKYCByUserIdQuery,
  useUpdateKYCStatusMutation,
  useDownloadKYCPDFMutation,
  useGetKYCStatsQuery,
  useBulkUpdateKYCStatusMutation,
  useGetKYCDocumentQuery,
  useUpdateDocumentStatusMutation,
  useGetKYCHistoryQuery,
  useExportKYCDataMutation,
} = adminKycApi
