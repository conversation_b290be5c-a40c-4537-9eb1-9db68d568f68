"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1147],{2933:(e,t,r)=>{r.d(t,{$:()=>c});var o=r(9605),a=r(9585),n=r(8130),i=r(7276),s=r(6994);let l=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:c=!1,...d}=e,u=c?n.DX:"button";return(0,o.jsx)(u,{className:(0,s.cn)(l({variant:a,size:i,className:r})),ref:t,...d})});c.displayName="Button"},3815:(e,t,r)=>{r.d(t,{Ay:()=>v,H$:()=>C,Kc:()=>y,LA:()=>l,PK:()=>n,ri:()=>p,xu:()=>k});var o=r(7895);let a=(0,o.zD)("auth/login",async(e,t)=>{let{rejectWithValue:r}=t;try{console.log("\uD83D\uDD10 User backend login attempt:",e.email);let t=await fetch("".concat("http://localhost:5000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)}),r=await t.json();if(!t.ok)throw Error(r.message||"Login failed");console.log("✅ User backend login successful:",r);let{user:o,accessToken:a,refreshToken:n}=r.data||r;return localStorage.setItem("userProfile",JSON.stringify(o)),console.log("✅ User login successful, backend set HttpOnly cookies:",{user:o.email,role:o.role,note:"Tokens stored in HttpOnly cookies (not accessible from JS)"}),{user:o,accessToken:a,refreshToken:n}}catch(e){return console.error("❌ User backend login failed:",e.message),r(e.message||"Login failed - please check your credentials")}}),n=(0,o.zD)("auth/checkAuth",async(e,t)=>{let{rejectWithValue:r}=t;try{if(console.log("\uD83D\uDD0D User: Checking authentication status..."),!(document.cookie.includes("accessToken=")||document.cookie.includes("token=")))throw console.log("❌ User: No auth cookies found"),Error("No authentication token found");return console.log("✅ User: Authentication cookies found, user data will be fetched by RTK Query"),{authenticated:!0}}catch(e){return console.error("❌ User: Authentication check failed:",e.message),r(e.message||"Authentication check failed")}}),i=(0,o.zD)("auth/register",async(e,t)=>{let{rejectWithValue:r}=t;try{console.log("\uD83D\uDCDD User backend registration attempt:",e.email);let t=await fetch("http://localhost:5000/api",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)}),r=await t.json();if(!t.ok)throw Error(r.message||"Registration failed");console.log("✅ User backend registration successful:",r);let{user:o,accessToken:a,refreshToken:n}=r.data||r;return localStorage.setItem("userProfile",JSON.stringify(o)),{user:o,accessToken:a,refreshToken:n}}catch(e){return console.error("❌ User backend registration failed:",e.message),r(e.message||"Registration failed")}}),s=(0,o.Z0)({name:"auth",initialState:{user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null},reducers:{setCredentials:(e,t)=>{let{user:r,token:o,refreshToken:a}=t.payload;e.user=r,e.token=o,e.refreshToken=a||null,e.isAuthenticated=!0,e.error=null},setUser:(e,t)=>{e.user=t.payload},clearUser:e=>{e.user=null},setToken:(e,t)=>{e.token=t.payload},setLoading:(e,t)=>{e.isLoading=t.payload},setError:(e,t)=>{e.error=t.payload},logout:e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.error=null},clearError:e=>{e.error=null},updateUserProfile:(e,t)=>{e.user&&(e.user={...e.user,...t.payload})}},extraReducers:e=>{e.addCase(n.pending,e=>{e.isLoading=!0,e.error=null}).addCase(n.fulfilled,(e,t)=>{e.isLoading=!1,e.isAuthenticated=t.payload.authenticated,e.error=null}).addCase(n.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null}).addCase(a.pending,e=>{e.isLoading=!0,e.error=null}).addCase(a.fulfilled,(e,t)=>{e.isLoading=!1,e.user=t.payload.user,e.token=t.payload.accessToken,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null}).addCase(a.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null}).addCase(i.pending,e=>{e.isLoading=!0,e.error=null}).addCase(i.fulfilled,(e,t)=>{e.isLoading=!1,e.user=t.payload.user,e.token=t.payload.accessToken,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null}).addCase(i.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null})}}),{setCredentials:l,setUser:c,clearUser:d,setToken:u,setLoading:h,setError:f,logout:p,clearError:g,updateUserProfile:m}=s.actions,k=e=>e.auth.user,y=e=>e.auth.isAuthenticated,C=e=>e.auth.isLoading,v=s.reducer},6965:(e,t,r)=>{r.d(t,{q:()=>c});var o=r(6597),a=r(2713),n=r(3815),i=r(7578);let s=(0,o.cw)({baseUrl:"http://localhost:5000/api",credentials:"include",prepareHeaders:(e,t)=>{let{getState:r}=t,o=r().auth.token,a=i.s.getAuthToken(),n=o||a;return n&&e.set("authorization","Bearer ".concat(n)),e.set("Content-Type","application/json"),e}}),l=async(e,t,r)=>{let o=await s(e,t,r);if(o.error&&401===o.error.status){let i=await s({url:"/auth/refresh",method:"POST"},t,r);if(i.data){var a;let l=i.data;l.success&&(null==(a=l.data)?void 0:a.user)?(t.dispatch((0,n.LA)({user:l.data.user,token:"cookie-based",refreshToken:"cookie-based"})),o=await s(e,t,r)):t.dispatch((0,n.ri)())}else t.dispatch((0,n.ri)())}return o},c=(0,a.xP)({reducerPath:"baseApi",baseQuery:l,tagTypes:["User","Property","Investment","Transaction","Wallet","Referral","SupportTicket","Notification","Dashboard","Activity","Wishlist","Featured","KYC","Lead","PropertyOwner","Auth","Settings","FAQ","FAQCategories","StockHoldings","StockCertificate","StockTransaction"],endpoints:()=>({})})},6994:(e,t,r)=>{r.d(t,{Ee:()=>s,IM:()=>d,Yq:()=>l,cn:()=>n,lW:()=>u,r6:()=>c,vv:()=>i});var o=r(8330),a=r(4291);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,o.$)(t))}function i(e){return null==e||isNaN(e)?"₹0":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}function s(e){return null==e||isNaN(e)?"0%":"".concat(e>0?"+":"").concat(e.toFixed(1),"%")}function l(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric"}).format(t)}function c(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}function d(e,t){return e?e.charAt(0).toUpperCase()+(t?t.charAt(0).toUpperCase():""):""}async function u(e){if(navigator.clipboard)await navigator.clipboard.writeText(e);else{let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select(),document.execCommand("copy"),document.body.removeChild(t)}}},7578:(e,t,r)=>{r.d(t,{s:()=>o});class o{static getCookie(e){if("undefined"==typeof document)return null;let t="; ".concat(document.cookie).split("; ".concat(e,"="));if(2===t.length){var r;let e=null==(r=t.pop())?void 0:r.split(";").shift();return e?decodeURIComponent(e):null}return null}static setCookie(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("undefined"==typeof document)return;let o="".concat(e,"=").concat(encodeURIComponent(t));r.expires&&("string"==typeof r.expires?o+="; expires=".concat(r.expires):o+="; expires=".concat(r.expires.toUTCString())),r.maxAge&&(o+="; max-age=".concat(r.maxAge)),r.path?o+="; path=".concat(r.path):o+="; path=/",r.domain&&(o+="; domain=".concat(r.domain)),r.secure&&(o+="; secure"),r.sameSite&&(o+="; samesite=".concat(r.sameSite)),document.cookie=o}static deleteCookie(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.setCookie(e,"",{...t,expires:new Date(0)})}static hasCookie(e){return null!==this.getCookie(e)}static getAllCookies(){if("undefined"==typeof document)return{};let e={};return document.cookie.split(";").forEach(t=>{let[r,o]=t.trim().split("=");r&&o&&(e[r]=decodeURIComponent(o))}),e}static getAuthToken(){return this.getCookie("accessToken")||this.getCookie("token")}static getRefreshToken(){return this.getCookie("refreshToken")}static isAuthenticated(){return this.hasCookie("accessToken")||this.hasCookie("token")}static clearAuthCookies(){this.deleteCookie("accessToken"),this.deleteCookie("refreshToken"),this.deleteCookie("token")}static getRedirectUrl(){return this.getCookie("redirectTo")}static setRedirectUrl(e){this.setCookie("redirectTo",e,{maxAge:300,sameSite:"lax"})}static clearRedirectUrl(){this.deleteCookie("redirectTo")}static parseCookieString(e){let t={};return e.split(";").forEach(e=>{let[r,o]=e.trim().split("=");r&&o&&(t[r]=decodeURIComponent(o))}),t}static setSessionCookie(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.setCookie(e,t,{...r,expires:void 0,maxAge:void 0})}static setPersistentCookie(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:7,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=new Date;a.setDate(a.getDate()+r),this.setCookie(e,t,{...o,expires:a})}}},8063:(e,t,r)=>{r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>s});var o=r(9605),a=r(9585),n=r(6994);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,o.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,o.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});s.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,o.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});l.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,o.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,o.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,o.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"}}]);