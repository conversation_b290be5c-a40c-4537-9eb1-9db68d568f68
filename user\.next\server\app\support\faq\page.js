(()=>{var e={};e.id=3345,e.ids=[3345],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4942:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(62544);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31874:(e,r,t)=>{Promise.resolve().then(t.bind(t,32995))},32995:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\support\\\\faq\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\support\\faq\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},42353:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(40969);t(73356);var o=t(12011);function a(){return(0,o.useRouter)(),(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Redirecting to FAQ page..."})]})})}},50026:(e,r,t)=>{Promise.resolve().then(t.bind(t,42353))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},87707:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var s=t(10557),o=t(68490),a=t(13172),n=t.n(a),i=t(68835),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let p={children:["",{children:["support",{children:["faq",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,32995)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\support\\faq\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\support\\faq\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/support/faq/page",pathname:"/support/faq",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[755,3777,2544,2487],()=>t(87707));module.exports=s})();