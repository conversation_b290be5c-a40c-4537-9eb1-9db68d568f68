(()=>{var e={};e.id=5281,e.ids=[5281],e.modules={1254:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("ArrowDownRight",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5568:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11779:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24689:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29655:(e,t,s)=>{"use strict";s.d(t,{AR:()=>r,rx:()=>a});let{useGetWalletBalanceQuery:a,useGetWalletTransactionsQuery:r,useAddMoneyToWalletMutation:l,useWithdrawMoneyMutation:i,useGetPaymentMethodsQuery:n,useAddPaymentMethodMutation:o,useUpdatePaymentMethodMutation:d,useDeletePaymentMethodMutation:c,useVerifyPaymentMethodMutation:p,useGetTransactionByIdQuery:m,useGetWalletAnalyticsQuery:u,useExportWalletStatementMutation:x,useSetTransactionAlertsMutation:h,useGetWalletLimitsQuery:y,useRequestLimitIncreaseMutation:g}=s(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getWalletBalance:e.query({query:()=>"/wallet",providesTags:[{type:"Wallet",id:"BALANCE"}],keepUnusedDataFor:120}),getWalletTransactions:e.query({query:e=>({url:"/wallet/transactions",params:{page:e.page||1,limit:e.limit||20,...e.type&&{type:e.type},...e.status&&{status:e.status},...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),transformResponse:e=>e?.success&&e?.data?.data?.length?e:{success:!0,message:"Transactions retrieved successfully",data:{data:[{_id:"demo-txn-1",type:"stock_purchase",amount:25e3,status:"completed",description:"Property Investment - Luxury Apartments",createdAt:new Date().toISOString(),reference:"TXN001"},{_id:"demo-txn-2",type:"deposit",amount:5e4,status:"completed",description:"Wallet Deposit",createdAt:new Date(Date.now()-864e5).toISOString(),reference:"TXN002"},{_id:"demo-txn-3",type:"investment",amount:1e5,status:"completed",description:"Property Investment - Commercial Complex",createdAt:new Date(Date.now()-1728e5).toISOString(),reference:"TXN003"}],pagination:{page:1,limit:20,total:3,pages:1}}},providesTags:e=>e?.data?.data?[...e.data.data.map(({_id:e})=>({type:"Transaction",id:e})),{type:"Transaction",id:"LIST"}]:[{type:"Transaction",id:"LIST"}],keepUnusedDataFor:300}),addMoneyToWallet:e.mutation({query:e=>({url:"/wallet/add-funds",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),withdrawMoney:e.mutation({query:e=>({url:"/wallet/withdraw",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),getPaymentMethods:e.query({query:()=>"/wallet/payment-methods",providesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}],keepUnusedDataFor:600}),addPaymentMethod:e.mutation({query:e=>({url:"/wallet/payment-methods",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),updatePaymentMethod:e.mutation({query:({id:e,...t})=>({url:`/wallet/payment-methods/${e}`,method:"PUT",body:t}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),deletePaymentMethod:e.mutation({query:e=>({url:`/wallet/payment-methods/${e}`,method:"DELETE"}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),verifyPaymentMethod:e.mutation({query:({id:e,verificationData:t})=>({url:`/wallet/payment-methods/${e}/verify`,method:"POST",body:t}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),getTransactionById:e.query({query:e=>`/wallet/transactions/${e}`,providesTags:(e,t,s)=>[{type:"Transaction",id:s}],keepUnusedDataFor:1800}),getWalletAnalytics:e.query({query:({period:e="1Y"})=>({url:"/wallet/analytics",params:{period:e}}),providesTags:[{type:"Wallet",id:"ANALYTICS"}],keepUnusedDataFor:900}),exportWalletStatement:e.mutation({query:e=>({url:"/wallet/export-statement",method:"POST",body:e})}),setTransactionAlerts:e.mutation({query:e=>({url:"/wallet/alerts",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"}]}),getWalletLimits:e.query({query:()=>"/wallet/limits",providesTags:[{type:"Wallet",id:"LIMITS"}],keepUnusedDataFor:3600}),requestLimitIncrease:e.mutation({query:e=>({url:"/wallet/request-limit-increase",method:"POST",body:e})})})})},33873:e=>{"use strict";e.exports=require("path")},37079:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},48687:(e,t,s)=>{Promise.resolve().then(s.bind(s,91753))},51228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},55641:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("Briefcase",[["rect",{width:"20",height:"14",x:"2",y:"7",rx:"2",ry:"2",key:"eto64e"}],["path",{d:"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"zwj3tp"}]])},56021:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},56525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63407:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(99024).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},70499:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\portfolio\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\portfolio\\page.tsx","default")},78587:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=s(10557),r=s(68490),l=s(13172),i=s.n(l),n=s(68835),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["portfolio",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,70499)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\portfolio\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\portfolio\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/portfolio/page",pathname:"/portfolio",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},91753:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var a=s(40969),r=s(73356),l=s(37020),i=s(66949),n=s(46411),o=s(55641),d=s(45548),c=s(56021),p=s(14216),m=s(56525),u=s(31745),x=s(11779),h=s(1254),y=s(67278),g=s(51228),v=s(24689),j=s(37079),f=s(63407),b=s(5568),w=s(21764),A=s(29655);function N(){let[e,t]=(0,r.useState)("all"),[s,N]=(0,r.useState)("purchaseDate"),[T,k]=(0,r.useState)("desc"),{data:P,isLoading:M}=(0,A.rx)(),{data:q,isLoading:D}=(0,A.AR)({type:"all"!==e?e:void 0,sortBy:s,sortOrder:T,limit:50}),S=q?.data?.data||[],C=P?.data||{},_=C?.totalInvested||0,E=C?.totalReturns||0,I=_+E,W=_>0?E/_*100:0,B=S.filter(e=>"investment"===e.type&&"completed"===e.status).length,O=[{title:"Total Invested",value:(0,w.vv)(_),icon:o.A,color:"text-blue-600",bgColor:"bg-blue-100"},{title:"Current Value",value:(0,w.vv)(I),icon:d.A,color:"text-green-600",bgColor:"bg-green-100",change:E>0?`+${(0,w.vv)(E)}`:(0,w.vv)(E),changeColor:E>0?"text-green-600":"text-red-600"},{title:"Total Returns",value:(0,w.vv)(E),icon:c.A,color:E>0?"text-green-600":"text-red-600",bgColor:E>0?"bg-green-100":"bg-red-100",change:(0,w.Ee)(W),changeColor:W>0?"text-green-600":"text-red-600"},{title:"Active Investments",value:B.toString(),icon:p.A,color:"text-purple-600",bgColor:"bg-purple-100"}];return M||D?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6",children:[1,2,3,4].map(e=>(0,a.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg"},e))}),(0,a.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg"})]})})}):(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Portfolio"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Track your real estate investments and performance"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)(n.$,{variant:"outline",className:"flex items-center space-x-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Export"})]}),(0,a.jsxs)(n.$,{variant:"outline",className:"flex items-center space-x-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Analytics"})]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:O.map((e,t)=>{let s=e.icon;return(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:e.title}),(0,a.jsx)("div",{className:`p-2 rounded-full ${e.bgColor}`,children:(0,a.jsx)(s,{className:`h-4 w-4 ${e.color}`})})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.value}),e.change&&(0,a.jsxs)("p",{className:`text-xs ${e.changeColor} flex items-center mt-1`,children:["text-green-600"===e.changeColor?(0,a.jsx)(x.A,{className:"h-3 w-3 mr-1"}):(0,a.jsx)(h.A,{className:"h-3 w-3 mr-1"}),e.change]})]})]},t)})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(i.Zp,{className:"lg:col-span-2",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 mr-2"}),"Asset Allocation"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,a.jsx)(y.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"Asset allocation will be available once you make investments"})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Top Performer"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,a.jsx)(v.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"Top performer will be shown once you have multiple investments"})]})})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(i.ZB,{children:"Investment Holdings"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("select",{value:e,onChange:e=>t(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[{value:"all",label:"All Investments"},{value:"active",label:"Active"},{value:"matured",label:"Matured"},{value:"cancelled",label:"Cancelled"}].map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Filter"]})]})]})}),(0,a.jsx)(i.Wu,{children:S.length>0?(0,a.jsx)("div",{className:"space-y-4",children:S.filter(e=>"investment"===e.type||"stock_purchase"===e.type).map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg",children:e.description||"Property Investment"}),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:e.status})]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-600 text-sm mt-1",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"stock_purchase"===e.type?"Stock Purchase":"Investment"]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-600 text-sm mt-1",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-1"}),(0,w.Yq)(e.createdAt)]})]}),(0,a.jsxs)("div",{className:"text-right space-y-1",children:[(0,a.jsx)("div",{className:"text-lg font-semibold",children:(0,w.vv)(e.amount)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"stock_purchase"===e.type?"Stock Purchase":"Investment"}),(0,a.jsx)("div",{className:`text-sm font-medium ${"completed"===e.status?"text-green-600":"text-yellow-600"}`,children:"completed"===e.status?"Completed":"Pending"})]}),(0,a.jsx)("div",{className:"ml-4",children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"View"]})})]})},e._id))}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(o.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No transactions found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"all"===e?"You haven't made any investments yet. Start building your portfolio today!":`No ${e} investments found. Try changing the filter.`}),(0,a.jsx)(n.$,{children:"Browse Properties"})]})})]})]})})}},95543:(e,t,s)=>{Promise.resolve().then(s.bind(s,70499))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[755,3777,2544,7092,7555,2487,3427],()=>s(78587));module.exports=a})();