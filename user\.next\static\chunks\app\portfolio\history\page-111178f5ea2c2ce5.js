(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9781],{95:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},1470:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2403:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},2790:(e,t,a)=>{"use strict";a.d(t,{E:()=>i});var s=a(9605);a(9585);var r=a(7276),l=a(6994);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{className:(0,l.cn)(n({variant:a}),t),...r})}},3119:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},5457:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},5594:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>c,T0:()=>o,lX:()=>m});var s=a(9605);a(9585);var r=a(2933),l=a(2790),n=a(6994),i=a(9644),d=a(5935);function c(e){let{title:t,description:a,icon:c,badge:o,actions:m,breadcrumbs:x,showBackButton:u=!1,className:y,gradient:h=!1}=e,g=(0,d.useRouter)();return(0,s.jsxs)("div",{className:(0,n.cn)("relative overflow-hidden",h&&"bg-gradient-to-r from-sky-50 via-blue-50 to-indigo-50",!h&&"bg-white",y),children:[h&&(0,s.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,s.jsx)("div",{className:"relative px-4 sm:px-6 lg:px-8 py-6 sm:py-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[x&&x.length>0&&(0,s.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,s.jsx)("ol",{className:"flex items-center space-x-2 text-sm",children:x.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,s.jsx)("span",{className:"mx-2 text-gray-400",children:"/"}),e.href?(0,s.jsx)("button",{onClick:()=>g.push(e.href),className:"text-gray-600 hover:text-sky-600 transition-colors",children:e.label}):(0,s.jsx)("span",{className:"text-gray-900 font-medium",children:e.label})]},t))})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[u&&(0,s.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>g.back(),className:"flex-shrink-0 mt-1",children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}),c&&(0,s.jsx)("div",{className:(0,n.cn)("flex-shrink-0 p-3 rounded-xl",h?"bg-white/80 backdrop-blur-sm shadow-lg":"bg-sky-50","text-sky-600"),children:(0,s.jsx)(c,{className:"h-6 w-6"})}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center flex-wrap gap-3 mb-2",children:[(0,s.jsx)("h1",{className:(0,n.cn)("text-2xl sm:text-3xl font-bold text-gray-900 leading-tight",h&&"bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent"),children:t}),o&&(0,s.jsx)(l.E,{variant:o.variant||"default",className:(0,n.cn)("text-xs font-medium",o.className),children:o.text})]}),a&&(0,s.jsx)("p",{className:"text-gray-600 text-sm sm:text-base max-w-2xl leading-relaxed",children:a})]})]}),m&&(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"flex items-center space-x-3",children:m})})]})]})}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"})]})}function o(e){let{stats:t,className:a}=e;return(0,s.jsx)("div",{className:(0,n.cn)("grid grid-cols-2 sm:grid-cols-4 gap-4 mt-6",a),children:t.map((e,t)=>(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50",children:[(0,s.jsx)("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide",children:e.label}),(0,s.jsx)("p",{className:"text-lg font-bold text-gray-900 mt-1",children:e.value}),e.change&&(0,s.jsx)("p",{className:(0,n.cn)("text-xs font-medium mt-1","up"===e.trend&&"text-green-600","down"===e.trend&&"text-red-600","neutral"===e.trend&&"text-gray-600"),children:e.change})]},t))})}let m={Primary:e=>{let{children:t,...a}=e;return(0,s.jsx)(r.$,{className:"bg-sky-600 hover:bg-sky-700 text-white shadow-lg",...a,children:t})},Secondary:e=>{let{children:t,...a}=e;return(0,s.jsx)(r.$,{variant:"outline",className:"border-sky-200 text-sky-700 hover:bg-sky-50",...a,children:t})},Ghost:e=>{let{children:t,...a}=e;return(0,s.jsx)(r.$,{variant:"ghost",className:"text-gray-600 hover:text-sky-600 hover:bg-sky-50",...a,children:t})}}},7900:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var s=a(9605),r=a(9585),l=a(3005),n=a(5594),i=a(8063),d=a(2790),c=a(2933),o=a(8408),m=a(2403),x=a(5457),u=a(95),y=a(3119),h=a(1470),g=a(9849),p=a(6994);function v(){var e;let[t,a]=(0,r.useState)("all"),[v,j]=(0,r.useState)("createdAt"),[b,N]=(0,r.useState)("desc"),{data:f,isLoading:w,error:T}=(0,g.AR)({type:"all"!==t?t:void 0,sortBy:v,sortOrder:b,limit:100}),k=Array.isArray(null==f?void 0:f.data)?f.data:(null==f||null==(e=f.data)?void 0:e.data)||[],A=e=>{switch(e){case"investment":return"bg-blue-100 text-blue-800";case"return":return"bg-green-100 text-green-800";case"withdrawal":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},S=e=>{switch(e){case"investment":return"↗️";case"return":return"\uD83D\uDCB0";case"withdrawal":return"↙️";default:return"\uD83D\uDCC4"}},D=k.reduce((e,t)=>e+(t.amount||0),0),q=k.filter(e=>{let t=new Date(e.createdAt),a=new Date;return t.getMonth()===a.getMonth()&&t.getFullYear()===a.getFullYear()}).length,E=k.length>0?Math.round(k.filter(e=>"completed"===e.status).length/k.length*100):0,M=[{label:"Total Transactions",value:k.length.toString(),trend:"neutral"},{label:"This Month",value:q.toString(),trend:"neutral"},{label:"Total Volume",value:(0,p.vv)(D),trend:"neutral"},{label:"Success Rate",value:"".concat(E,"%"),trend:"neutral"}];return w?(0,s.jsx)(l.A,{children:(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[1,2,3,4].map(e=>(0,s.jsx)("div",{className:"h-24 bg-gray-200 rounded-lg"},e))}),(0,s.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg"})]})})}):(0,s.jsx)(l.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(n.Ay,{title:"Transaction History",description:"View all your investment transactions and activities with detailed tracking",icon:o.A,gradient:!0,breadcrumbs:[{label:"Portfolio",href:"/portfolio"},{label:"History"}],actions:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(n.lX.Secondary,{children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Filter"]}),(0,s.jsxs)(n.lX.Primary,{children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Export History"]})]})}),(0,s.jsx)(n.T0,{stats:M}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsx)(i.Zp,{className:"border-0 shadow-lg",children:(0,s.jsx)(i.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Transactions"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:k.length})]}),(0,s.jsx)("div",{className:"p-3 rounded-full bg-blue-50 text-blue-600",children:(0,s.jsx)(o.A,{className:"h-6 w-6"})})]})})}),(0,s.jsx)(i.Zp,{className:"border-0 shadow-lg",children:(0,s.jsx)(i.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"This Month"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:"3"})]}),(0,s.jsx)("div",{className:"p-3 rounded-full bg-green-50 text-green-600",children:(0,s.jsx)(u.A,{className:"h-6 w-6"})})]})})}),(0,s.jsx)(i.Zp,{className:"border-0 shadow-lg",children:(0,s.jsx)(i.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Volume"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:"₹4,48,500"})]}),(0,s.jsx)("div",{className:"p-3 rounded-full bg-sky-50 text-sky-600",children:(0,s.jsx)(y.A,{className:"h-6 w-6"})})]})})})]}),(0,s.jsxs)(i.Zp,{className:"border-0 shadow-lg",children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(o.A,{className:"h-5 w-5 text-sky-600"}),(0,s.jsx)("span",{children:"Recent Transactions"})]})}),(0,s.jsxs)(i.Wu,{children:[(0,s.jsx)("div",{className:"space-y-4",children:k.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"text-2xl",children:S(e.type)}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("p",{className:"font-medium text-gray-900",children:e.description||"Transaction"}),(0,s.jsx)(d.E,{className:A(e.type),children:e.type})]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(0,p.Yq)(e.createdAt)," • ID: ",e._id.slice(-8)]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"font-bold ".concat("withdrawal"===e.type?"text-red-600":"text-green-600"),children:["withdrawal"===e.type?"-":"+","₹",e.amount.toLocaleString()]}),(0,s.jsx)(d.E,{variant:"secondary",className:"bg-green-100 text-green-800 text-xs",children:e.status})]}),(0,s.jsx)(c.$,{variant:"ghost",size:"sm",children:(0,s.jsx)(h.A,{className:"h-4 w-4"})})]})]},e._id))}),(0,s.jsx)("div",{className:"flex justify-center mt-6",children:(0,s.jsx)(c.$,{variant:"outline",className:"border-sky-200 text-sky-600 hover:bg-sky-50",children:"Load More Transactions"})})]})]})]})})}},7935:(e,t,a)=>{Promise.resolve().then(a.bind(a,7900))},9644:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9849:(e,t,a)=>{"use strict";a.d(t,{AR:()=>r,rx:()=>s});let{useGetWalletBalanceQuery:s,useGetWalletTransactionsQuery:r,useAddMoneyToWalletMutation:l,useWithdrawMoneyMutation:n,useGetPaymentMethodsQuery:i,useAddPaymentMethodMutation:d,useUpdatePaymentMethodMutation:c,useDeletePaymentMethodMutation:o,useVerifyPaymentMethodMutation:m,useGetTransactionByIdQuery:x,useGetWalletAnalyticsQuery:u,useExportWalletStatementMutation:y,useSetTransactionAlertsMutation:h,useGetWalletLimitsQuery:g,useRequestLimitIncreaseMutation:p}=a(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getWalletBalance:e.query({query:()=>"/wallet",providesTags:[{type:"Wallet",id:"BALANCE"}],keepUnusedDataFor:120}),getWalletTransactions:e.query({query:e=>({url:"/wallet/transactions",params:{page:e.page||1,limit:e.limit||20,...e.type&&{type:e.type},...e.status&&{status:e.status},...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),transformResponse:e=>{var t,a;return(null==e?void 0:e.success)&&(null==e||null==(a=e.data)||null==(t=a.data)?void 0:t.length)?e:{success:!0,message:"Transactions retrieved successfully",data:{data:[{_id:"demo-txn-1",type:"stock_purchase",amount:25e3,status:"completed",description:"Property Investment - Luxury Apartments",createdAt:new Date().toISOString(),reference:"TXN001"},{_id:"demo-txn-2",type:"deposit",amount:5e4,status:"completed",description:"Wallet Deposit",createdAt:new Date(Date.now()-864e5).toISOString(),reference:"TXN002"},{_id:"demo-txn-3",type:"investment",amount:1e5,status:"completed",description:"Property Investment - Commercial Complex",createdAt:new Date(Date.now()-1728e5).toISOString(),reference:"TXN003"}],pagination:{page:1,limit:20,total:3,pages:1}}}},providesTags:e=>{var t;return(null==e||null==(t=e.data)?void 0:t.data)?[...e.data.data.map(e=>{let{_id:t}=e;return{type:"Transaction",id:t}}),{type:"Transaction",id:"LIST"}]:[{type:"Transaction",id:"LIST"}]},keepUnusedDataFor:300}),addMoneyToWallet:e.mutation({query:e=>({url:"/wallet/add-funds",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),withdrawMoney:e.mutation({query:e=>({url:"/wallet/withdraw",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),getPaymentMethods:e.query({query:()=>"/wallet/payment-methods",providesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}],keepUnusedDataFor:600}),addPaymentMethod:e.mutation({query:e=>({url:"/wallet/payment-methods",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),updatePaymentMethod:e.mutation({query:e=>{let{id:t,...a}=e;return{url:"/wallet/payment-methods/".concat(t),method:"PUT",body:a}},invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),deletePaymentMethod:e.mutation({query:e=>({url:"/wallet/payment-methods/".concat(e),method:"DELETE"}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),verifyPaymentMethod:e.mutation({query:e=>{let{id:t,verificationData:a}=e;return{url:"/wallet/payment-methods/".concat(t,"/verify"),method:"POST",body:a}},invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),getTransactionById:e.query({query:e=>"/wallet/transactions/".concat(e),providesTags:(e,t,a)=>[{type:"Transaction",id:a}],keepUnusedDataFor:1800}),getWalletAnalytics:e.query({query:e=>{let{period:t="1Y"}=e;return{url:"/wallet/analytics",params:{period:t}}},providesTags:[{type:"Wallet",id:"ANALYTICS"}],keepUnusedDataFor:900}),exportWalletStatement:e.mutation({query:e=>({url:"/wallet/export-statement",method:"POST",body:e})}),setTransactionAlerts:e.mutation({query:e=>({url:"/wallet/alerts",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"}]}),getWalletLimits:e.query({query:()=>"/wallet/limits",providesTags:[{type:"Wallet",id:"LIMITS"}],keepUnusedDataFor:3600}),requestLimitIncrease:e.mutation({query:e=>({url:"/wallet/request-limit-increase",method:"POST",body:e})})})})}},e=>{var t=t=>e(e.s=t);e.O(0,[2094,5315,7436,7693,1147,7627,3005,390,110,7358],()=>t(7935)),_N_E=e.O()}]);