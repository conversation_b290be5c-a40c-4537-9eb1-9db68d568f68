"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2094],{2713:(e,t,r)=>{r.d(t,{xP:()=>_});var n=r(6597),i=r(9559),a=r(3030),o=r(7895),u=r(9585);function s(e){return e.replace(e[0],e[0].toUpperCase())}function c(e){return"infinitequery"===e.type}function l(e,...t){return Object.assign(e,...t)}r(5733);var f=Symbol();function d(e,t,r,n){let i=(0,u.useMemo)(()=>({queryArgs:e,serialized:"object"==typeof e?t({queryArgs:e,endpointDefinition:r,endpointName:n}):e}),[e,t,r,n]),a=(0,u.useRef)(i);return(0,u.useEffect)(()=>{a.current.serialized!==i.serialized&&(a.current=i)},[i]),a.current.serialized===i.serialized?a.current.queryArgs:e}function p(e){let t=(0,u.useRef)(e);return(0,u.useEffect)(()=>{(0,i.bN)(t.current,e)||(t.current=e)},[e]),(0,i.bN)(t.current,e)?t.current:e}var y="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,h="undefined"!=typeof navigator&&"ReactNative"===navigator.product,m=y||h?u.useLayoutEffect:u.useEffect,g=e=>e.isUninitialized?{...e,isUninitialized:!1,isFetching:!0,isLoading:void 0===e.data,status:n.RE.pending}:e;function b(e,...t){let r={};return t.forEach(t=>{r[t]=e[t]}),r}var v=["data","status","isLoading","isSuccess","isError","error"],w=Symbol(),_=(0,n.l0)((0,n.m7)(),(({batch:e=i.vA,hooks:t={useDispatch:i.wA,useSelector:i.d4,useStore:i.Pj},createSelector:r=a.Mz,unstable__sideEffectsInRender:y=!1,...h}={})=>({name:w,init(a,{serializeQueryArgs:h},w){let{buildQueryHooks:_,buildInfiniteQueryHooks:S,buildMutationHook:O,usePrefetch:E}=function({api:e,moduleOptions:{batch:t,hooks:{useDispatch:r,useSelector:a,useStore:s},unstable__sideEffectsInRender:l,createSelector:y},serializeQueryArgs:h,context:w}){let _=l?e=>e():u.useEffect;return{buildQueryHooks:function(i){let a=(e,t={})=>{let[r]=E(i,e,t);return j(r),(0,u.useMemo)(()=>({refetch:()=>A(r)}),[r])},o=({refetchOnReconnect:n,refetchOnFocus:a,pollingInterval:o=0,skipPollingIfUnfocused:s=!1}={})=>{let{initiate:c}=e.endpoints[i],l=r(),[d,y]=(0,u.useState)(f),h=(0,u.useRef)(void 0),m=p({refetchOnReconnect:n,refetchOnFocus:a,pollingInterval:o,skipPollingIfUnfocused:s});_(()=>{m!==h.current?.subscriptionOptions&&h.current?.updateSubscriptionOptions(m)},[m]);let g=(0,u.useRef)(m);_(()=>{g.current=m},[m]);let b=(0,u.useCallback)(function(e,r=!1){let n;return t(()=>{h.current?.unsubscribe(),h.current=n=l(c(e,{subscriptionOptions:g.current,forceRefetch:!r})),y(e)}),n},[l,c]),v=(0,u.useCallback)(()=>{h.current?.queryCacheKey&&l(e.internalActions.removeQueryResult({queryCacheKey:h.current?.queryCacheKey}))},[l]);return(0,u.useEffect)(()=>()=>{h?.current?.unsubscribe()},[]),(0,u.useEffect)(()=>{d===f||h.current||b(d,!0)},[d,b]),(0,u.useMemo)(()=>[b,d,{reset:v}],[b,d,v])},s=P(i,S);return{useQueryState:s,useQuerySubscription:a,useLazyQuerySubscription:o,useLazyQuery(e){let[t,r,{reset:n}]=o(e),i=s(r,{...e,skip:r===f}),a=(0,u.useMemo)(()=>({lastArg:r}),[r]);return(0,u.useMemo)(()=>[t,{...i,reset:n},a],[t,i,n,a])},useQuery(e,t){let r=a(e,t),i=s(e,{selectFromResult:e===n.hT||t?.skip?void 0:g,...t}),o=b(i,...v);return(0,u.useDebugValue)(o),(0,u.useMemo)(()=>({...i,...r}),[i,r])}}},buildInfiniteQueryHooks:function(e){let r=(r,i={})=>{let[a,o,s,c]=E(e,r,i),l=(0,u.useRef)(c);_(()=>{l.current=c},[c]);let f=(0,u.useCallback)(function(e,r){let n;return t(()=>{a.current?.unsubscribe(),a.current=n=o(s(e,{subscriptionOptions:l.current,direction:r}))}),n},[a,o,s]);j(a);let p=d(i.skip?n.hT:r,n.lE,w.endpointDefinitions[e],e),y=(0,u.useCallback)(()=>A(a),[a]);return(0,u.useMemo)(()=>({trigger:f,refetch:y,fetchNextPage:()=>f(p,"forward"),fetchPreviousPage:()=>f(p,"backward")}),[y,f,p])},i=P(e,O);return{useInfiniteQueryState:i,useInfiniteQuerySubscription:r,useInfiniteQuery(e,t){let{refetch:a,fetchNextPage:o,fetchPreviousPage:s}=r(e,t),c=i(e,{selectFromResult:e===n.hT||t?.skip?void 0:g,...t}),l=b(c,...v,"hasNextPage","hasPreviousPage");return(0,u.useDebugValue)(l),(0,u.useMemo)(()=>({...c,fetchNextPage:o,fetchPreviousPage:s,refetch:a}),[c,o,s,a])}}},buildMutationHook:function(n){return({selectFromResult:o,fixedCacheKey:s}={})=>{let{select:c,initiate:l}=e.endpoints[n],f=r(),[d,p]=(0,u.useState)();(0,u.useEffect)(()=>()=>{d?.arg.fixedCacheKey||d?.reset()},[d]);let h=(0,u.useCallback)(function(e){let t=f(l(e,{fixedCacheKey:s}));return p(t),t},[f,l,s]),{requestId:m}=d||{},g=(0,u.useMemo)(()=>c({fixedCacheKey:s,requestId:d?.requestId}),[s,d,c]),w=a((0,u.useMemo)(()=>o?y([g],o):g,[o,g]),i.bN),_=null==s?d?.arg.originalArgs:void 0,S=(0,u.useCallback)(()=>{t(()=>{d&&p(void 0),s&&f(e.internalActions.removeMutationResult({requestId:m,fixedCacheKey:s}))})},[f,s,d,m]),O=b(w,...v,"endpointName");(0,u.useDebugValue)(O);let E=(0,u.useMemo)(()=>({...w,originalArgs:_,reset:S}),[w,_,S]);return(0,u.useMemo)(()=>[h,E],[h,E])}},usePrefetch:function(t,n){let i=r(),a=p(n);return(0,u.useCallback)((r,n)=>i(e.util.prefetch(t,r,{...a,...n})),[t,i,a])}};function S(e,t,r){if(t?.endpointName&&e.isUninitialized){let{endpointName:e}=t,i=w.endpointDefinitions[e];r!==n.hT&&h({queryArgs:t.originalArgs,endpointDefinition:i,endpointName:e})===h({queryArgs:r,endpointDefinition:i,endpointName:e})&&(t=void 0)}let i=e.isSuccess?e.data:t?.data;void 0===i&&(i=e.data);let a=void 0!==i,o=e.isLoading,u=(!t||t.isLoading||t.isUninitialized)&&!a&&o,s=e.isSuccess||a&&(o&&!t?.isError||e.isUninitialized);return{...e,data:i,currentData:e.data,isFetching:o,isLoading:u,isSuccess:s}}function O(e,t,r){if(t?.endpointName&&e.isUninitialized){let{endpointName:e}=t,i=w.endpointDefinitions[e];r!==n.hT&&h({queryArgs:t.originalArgs,endpointDefinition:i,endpointName:e})===h({queryArgs:r,endpointDefinition:i,endpointName:e})&&(t=void 0)}let i=e.isSuccess?e.data:t?.data;void 0===i&&(i=e.data);let a=void 0!==i,o=e.isLoading,u=(!t||t.isLoading||t.isUninitialized)&&!a&&o,s=e.isSuccess||o&&a;return{...e,data:i,currentData:e.data,isFetching:o,isLoading:u,isSuccess:s}}function E(t,i,{refetchOnReconnect:a,refetchOnFocus:o,refetchOnMountOrArgChange:s,skip:l=!1,pollingInterval:f=0,skipPollingIfUnfocused:y=!1,...h}={}){let{initiate:m}=e.endpoints[t],g=r(),b=(0,u.useRef)(void 0);b.current||(b.current=g(e.internalActions.internal_getRTKQSubscriptions()));let v=d(l?n.hT:i,n.lE,w.endpointDefinitions[t],t),S=p({refetchOnReconnect:a,refetchOnFocus:o,pollingInterval:f,skipPollingIfUnfocused:y}),O=p(h.initialPageParam),P=(0,u.useRef)(void 0),{queryCacheKey:j,requestId:A}=P.current||{},R=!1;j&&A&&(R=b.current.isRequestSubscribed(j,A));let T=!R&&void 0!==P.current;return _(()=>{T&&(P.current=void 0)},[T]),_(()=>{let e=P.current;if(v===n.hT){e?.unsubscribe(),P.current=void 0;return}let r=P.current?.subscriptionOptions;e&&e.arg===v?S!==r&&e.updateSubscriptionOptions(S):(e?.unsubscribe(),P.current=g(m(v,{subscriptionOptions:S,forceRefetch:s,...c(w.endpointDefinitions[t])?{initialPageParam:O}:{}})))},[g,m,s,v,S,T,O,t]),[P,g,m,S]}function P(t,r){return(o,{skip:c=!1,selectFromResult:l}={})=>{let{select:f}=e.endpoints[t],p=d(c?n.hT:o,h,w.endpointDefinitions[t],t),g=(0,u.useRef)(void 0),b=(0,u.useMemo)(()=>y([f(p),(e,t)=>t,e=>p],r,{memoizeOptions:{resultEqualityCheck:i.bN}}),[f,p]),v=(0,u.useMemo)(()=>l?y([b],l,{devModeChecks:{identityFunctionCheck:"never"}}):b,[b,l]),_=a(e=>v(e,g.current),i.bN),S=b(s().getState(),g.current);return m(()=>{g.current=S},[S]),_}}function j(e){(0,u.useEffect)(()=>()=>{e.current?.unsubscribe?.(),e.current=void 0},[e])}function A(e){if(!e.current)throw Error((0,o.gk)(38));return e.current.refetch()}}({api:a,moduleOptions:{batch:e,hooks:t,unstable__sideEffectsInRender:y,createSelector:r},serializeQueryArgs:h,context:w});return l(a,{usePrefetch:E}),l(w,{batch:e}),{injectEndpoint(e,t){if("query"===t.type){let{useQuery:t,useLazyQuery:r,useLazyQuerySubscription:n,useQueryState:i,useQuerySubscription:o}=_(e);l(a.endpoints[e],{useQuery:t,useLazyQuery:r,useLazyQuerySubscription:n,useQueryState:i,useQuerySubscription:o}),a[`use${s(e)}Query`]=t,a[`useLazy${s(e)}Query`]=r}if("mutation"===t.type){let t=O(e);l(a.endpoints[e],{useMutation:t}),a[`use${s(e)}Mutation`]=t}else if(c(t)){let{useInfiniteQuery:t,useInfiniteQuerySubscription:r,useInfiniteQueryState:n}=S(e);l(a.endpoints[e],{useInfiniteQuery:t,useInfiniteQuerySubscription:r,useInfiniteQueryState:n}),a[`use${s(e)}InfiniteQuery`]=t}}}}}))())},3030:(e,t,r)=>{r.d(t,{Mz:()=>_,X4:()=>w});var n=e=>Array.isArray(e)?e:[e],i=0,a=null,o=class{revision=i;_value;_lastValue;_isEqual=u;constructor(e,t=u){this._value=this._lastValue=e,this._isEqual=t}get value(){return a?.add(this),this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++i)}};function u(e,t){return e===t}function s(e){return e instanceof o||console.warn("Not a valid cell! ",e),e.value}var c=(e,t)=>!1;function l(){return function(e,t=u){return new o(null,t)}(0,c)}var f=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=l()),s(t)};Symbol();var d=0,p=Object.getPrototypeOf({}),y=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,h);tag=l();tags={};children={};collectionTag=null;id=d++},h={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in p)return n;if("object"==typeof n&&null!==n){let r=e.children[t];return void 0===r&&(r=e.children[t]=function(e){return Array.isArray(e)?new m(e):new y(e)}(n)),r.tag&&s(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=l()).value=n),s(r),n}})(),ownKeys:e=>(f(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},m=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],g);tag=l();tags={};children={};collectionTag=null;id=d++},g={get:([e],t)=>("length"===t&&f(e),h.get(e,t)),ownKeys:([e])=>h.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>h.getOwnPropertyDescriptor(e,t),has:([e],t)=>h.has(e,t)},b="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function v(){return{s:0,v:void 0,o:null,p:null}}function w(e,t={}){let r,n=v(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:u}=arguments;for(let e=0;e<u;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=v(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=v(),e.set(t,o)):o=r}}let s=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new b(t):t}return s.s=1,s.v=t,t}return o.clearCache=()=>{n=v(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var _=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,i=(...e)=>{let t,i=0,a=0,o={},u=e.pop();"object"==typeof u&&(o=u,u=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(u,`createSelector expects an output function after the inputs, but received: [${typeof u}]`);let{memoize:s,memoizeOptions:c=[],argsMemoize:l=w,argsMemoizeOptions:f=[],devModeChecks:d={}}={...r,...o},p=n(c),y=n(f),h=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),m=s(function(){return i++,u.apply(null,arguments)},...p);return Object.assign(l(function(){a++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(h,arguments);return t=m.apply(null,e)},...y),{resultFunc:u,memoizedResultFunc:m,dependencies:h,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>t,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:s,argsMemoize:l})};return Object.assign(i,{withTypes:()=>i}),i}(w),S=Object.assign((e,t=_)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>S})},4605:(e,t,r)=>{r.d(t,{$i:()=>X,Qx:()=>c,YT:()=>W,a6:()=>l,c2:()=>p,jM:()=>V,vI:()=>B});var n,i=Symbol.for("immer-nothing"),a=Symbol.for("immer-draftable"),o=Symbol.for("immer-state");function u(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var s=Object.getPrototypeOf;function c(e){return!!e&&!!e[o]}function l(e){return!!e&&(d(e)||Array.isArray(e)||!!e[a]||!!e.constructor?.[a]||v(e)||w(e))}var f=Object.prototype.constructor.toString();function d(e){if(!e||"object"!=typeof e)return!1;let t=s(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function p(e){return c(e)||u(15,e),e[o].base_}function y(e,t){0===h(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function h(e){let t=e[o];return t?t.type_:Array.isArray(e)?1:v(e)?2:3*!!w(e)}function m(e,t){return 2===h(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function g(e,t){return 2===h(e)?e.get(t):e[t]}function b(e,t,r){let n=h(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function v(e){return e instanceof Map}function w(e){return e instanceof Set}function _(e){return e.copy_||e.base_}function S(e,t){if(v(e))return new Map(e);if(w(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=d(e);if(!0!==t&&("class_only"!==t||r)){let t=s(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[o];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(s(e),t)}}function O(e,t=!1){return P(e)||c(e)||!l(e)||(h(e)>1&&(e.set=e.add=e.clear=e.delete=E),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>O(t,!0))),e}function E(){u(2)}function P(e){return Object.isFrozen(e)}var j={};function A(e){let t=j[e];return t||u(0,e),t}function R(e,t){t&&(A("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function T(e){C(e),e.drafts_.forEach(q),e.drafts_=null}function C(e){e===n&&(n=e.parent_)}function N(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function q(e){let t=e[o];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function k(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[o].modified_&&(T(t),u(4)),l(e)&&(e=x(t,e),t.parent_||D(t,e)),t.patches_&&A("Patches").generateReplacementPatches_(r[o].base_,e,t.patches_,t.inversePatches_)):e=x(t,r,[]),T(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==i?e:void 0}function x(e,t,r){if(P(t))return t;let n=t[o];if(!n)return y(t,(i,a)=>M(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return D(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),y(i,(i,o)=>M(e,n,t,i,o,r,a)),D(e,t,!1),r&&e.patches_&&A("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function M(e,t,r,n,i,a,o){if(c(i)){let o=x(e,i,a&&t&&3!==t.type_&&!m(t.assigned_,n)?a.concat(n):void 0);if(b(r,n,o),!c(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(l(i)&&!P(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;x(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&D(e,i)}}function D(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&O(t,r)}var I={get(e,t){if(t===o)return e;let r=_(e);if(!m(r,t)){var n=e,i=r,a=t;let o=Q(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let u=r[t];return e.finalized_||!l(u)?u:u===$(e.base_,t)?(F(e),e.copy_[t]=L(u,e)):u},has:(e,t)=>t in _(e),ownKeys:e=>Reflect.ownKeys(_(e)),set(e,t,r){let n=Q(_(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=$(_(e),t),i=n?.[o];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||m(e.base_,t)))return!0;F(e),K(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==$(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,F(e),K(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=_(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){u(11)},getPrototypeOf:e=>s(e.base_),setPrototypeOf(){u(12)}},z={};function $(e,t){let r=e[o];return(r?_(r):e)[t]}function Q(e,t){if(!(t in e))return;let r=s(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=s(r)}}function K(e){!e.modified_&&(e.modified_=!0,e.parent_&&K(e.parent_))}function F(e){e.copy_||(e.copy_=S(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function L(e,t){let r=v(e)?A("MapSet").proxyMap_(e,t):w(e)?A("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),i={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},a=i,o=I;r&&(a=[i],o=z);let{revoke:u,proxy:s}=Proxy.revocable(a,o);return i.draft_=s,i.revoke_=u,s}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function W(){var e;let t="replace",r="remove";function n(e){if(!l(e))return e;if(Array.isArray(e))return e.map(n);if(v(e))return new Map(Array.from(e.entries()).map(([e,t])=>[e,n(t)]));if(w(e))return new Set(Array.from(e).map(n));let t=Object.create(s(e));for(let r in e)t[r]=n(e[r]);return m(e,a)&&(t[a]=e[a]),t}function o(e){return c(e)?n(e):e}e="Patches",j[e]||(j[e]={applyPatches_:function(e,i){return i.forEach(i=>{let{path:a,op:o}=i,s=e;for(let e=0;e<a.length-1;e++){let t=h(s),r=a[e];"string"!=typeof r&&"number"!=typeof r&&(r=""+r),(0===t||1===t)&&("__proto__"===r||"constructor"===r)&&u(19),"function"==typeof s&&"prototype"===r&&u(19),"object"!=typeof(s=g(s,r))&&u(18,a.join("/"))}let c=h(s),l=n(i.value),f=a[a.length-1];switch(o){case t:switch(c){case 2:return s.set(f,l);case 3:u(16);default:return s[f]=l}case"add":switch(c){case 1:return"-"===f?s.push(l):s.splice(f,0,l);case 2:return s.set(f,l);case 3:return s.add(l);default:return s[f]=l}case r:switch(c){case 1:return s.splice(f,1);case 2:return s.delete(f);case 3:return s.delete(i.value);default:return delete s[f]}default:u(17,o)}}),e},generatePatches_:function(e,n,i,a){switch(e.type_){case 0:case 2:var u=e,s=n,c=i,l=a;let{base_:f,copy_:d}=u;y(u.assigned_,(e,n)=>{let i=g(f,e),a=g(d,e),u=n?m(f,e)?t:"add":r;if(i===a&&u===t)return;let p=s.concat(e);c.push(u===r?{op:u,path:p}:{op:u,path:p,value:a}),l.push("add"===u?{op:r,path:p}:u===r?{op:"add",path:p,value:o(i)}:{op:t,path:p,value:o(i)})});return;case 1:return function(e,n,i,a){let{base_:u,assigned_:s}=e,c=e.copy_;c.length<u.length&&([u,c]=[c,u],[i,a]=[a,i]);for(let e=0;e<u.length;e++)if(s[e]&&c[e]!==u[e]){let r=n.concat([e]);i.push({op:t,path:r,value:o(c[e])}),a.push({op:t,path:r,value:o(u[e])})}for(let e=u.length;e<c.length;e++){let t=n.concat([e]);i.push({op:"add",path:t,value:o(c[e])})}for(let e=c.length-1;u.length<=e;--e){let t=n.concat([e]);a.push({op:r,path:t})}}(e,n,i,a);case 3:return function(e,t,n,i){let{base_:a,copy_:o}=e,u=0;a.forEach(e=>{if(!o.has(e)){let a=t.concat([u]);n.push({op:r,path:a,value:e}),i.unshift({op:"add",path:a,value:e})}u++}),u=0,o.forEach(e=>{if(!a.has(e)){let a=t.concat([u]);n.push({op:"add",path:a,value:e}),i.unshift({op:r,path:a,value:e})}u++})}(e,n,i,a)}},generateReplacementPatches_:function(e,r,n,a){n.push({op:t,path:[],value:r===i?void 0:r}),a.push({op:t,path:[],value:e})}})}y(I,(e,t)=>{z[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),z.deleteProperty=function(e,t){return z.set.call(this,e,t,void 0)},z.set=function(e,t,r){return I.set.call(this,e[0],t,r,e[0])};var U=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&u(6),void 0!==r&&"function"!=typeof r&&u(7),l(e)){let i=N(this),a=L(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?T(i):C(i)}return R(i,r),k(n,i)}if(e&&"object"==typeof e)u(1,e);else{if(void 0===(n=t(e))&&(n=e),n===i&&(n=void 0),this.autoFreeze_&&O(n,!0),r){let t=[],i=[];A("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){var t;l(e)||u(8),c(e)&&(c(t=e)||u(10,t),e=function e(t){let r;if(!l(t)||P(t))return t;let n=t[o];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=S(t,n.scope_.immer_.useStrictShallowCopy_)}else r=S(t,!0);return y(r,(t,n)=>{b(r,t,e(n))}),n&&(n.finalized_=!1),r}(t));let r=N(this),n=L(e,void 0);return n[o].isManual_=!0,C(r),n}finishDraft(e,t){let r=e&&e[o];r&&r.isManual_||u(9);let{scope_:n}=r;return R(n,t),k(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=A("Patches").applyPatches_;return c(e)?n(e,t):this.produce(e,e=>n(e,t))}},V=U.produce,B=U.produceWithPatches.bind(U);U.setAutoFreeze.bind(U),U.setUseStrictShallowCopy.bind(U);var X=U.applyPatches.bind(U);U.createDraft.bind(U),U.finishDraft.bind(U)},4631:(e,t,r)=>{e.exports=r(6061)},5050:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(9585),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),o=(e,t)=>{let r=(0,n.forwardRef)((r,o)=>{let{color:u="currentColor",size:s=24,strokeWidth:c=2,absoluteStrokeWidth:l,className:f="",children:d,...p}=r;return(0,n.createElement)("svg",{ref:o,...i,width:s,height:s,stroke:u,strokeWidth:l?24*Number(c)/Number(s):c,className:["lucide","lucide-".concat(a(e)),f].join(" "),...p},[...t.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])});return r.displayName="".concat(e),r}},5340:(e,t,r)=>{function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{HY:()=>c,Qd:()=>u,Tw:()=>f,Zz:()=>l,ve:()=>d,y$:()=>s});var i="function"==typeof Symbol&&Symbol.observable||"@@observable",a=()=>Math.random().toString(36).substring(7).split("").join("."),o={INIT:`@@redux/INIT${a()}`,REPLACE:`@@redux/REPLACE${a()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${a()}`};function u(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function s(e,t,r){if("function"!=typeof e)throw Error(n(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(s)(e,t)}let a=e,c=t,l=new Map,f=l,d=0,p=!1;function y(){f===l&&(f=new Map,l.forEach((e,t)=>{f.set(t,e)}))}function h(){if(p)throw Error(n(3));return c}function m(e){if("function"!=typeof e)throw Error(n(4));if(p)throw Error(n(5));let t=!0;y();let r=d++;return f.set(r,e),function(){if(t){if(p)throw Error(n(6));t=!1,y(),f.delete(r),l=null}}}function g(e){if(!u(e))throw Error(n(7));if(void 0===e.type)throw Error(n(8));if("string"!=typeof e.type)throw Error(n(17));if(p)throw Error(n(9));try{p=!0,c=a(c,e)}finally{p=!1}return(l=f).forEach(e=>{e()}),e}return g({type:o.INIT}),{dispatch:g,subscribe:m,getState:h,replaceReducer:function(e){if("function"!=typeof e)throw Error(n(10));a=e,g({type:o.REPLACE})},[i]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(n(11));function t(){e.next&&e.next(h())}return t(),{unsubscribe:m(t)}},[i](){return this}}}}}function c(e){let t,r=Object.keys(e),i={};for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof e[n]&&(i[n]=e[n])}let a=Object.keys(i);try{Object.keys(i).forEach(e=>{let t=i[e];if(void 0===t(void 0,{type:o.INIT}))throw Error(n(12));if(void 0===t(void 0,{type:o.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let o=!1,u={};for(let t=0;t<a.length;t++){let s=a[t],c=i[s],l=e[s],f=c(l,r);if(void 0===f)throw r&&r.type,Error(n(14));u[s]=f,o=o||f!==l}return(o=o||a.length!==Object.keys(e).length)?u:e}}function l(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function f(...e){return t=>(r,i)=>{let a=t(r,i),o=()=>{throw Error(n(15))},u={getState:a.getState,dispatch:(e,...t)=>o(e,...t)};return o=l(...e.map(e=>e(u)))(a.dispatch),{...a,dispatch:o}}}function d(e){return u(e)&&"type"in e&&"string"==typeof e.type}},6061:(e,t,r)=>{var n=r(9585),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useSyncExternalStore,o=n.useRef,u=n.useEffect,s=n.useMemo,c=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,l){var f=o(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=a(e,(f=s(function(){function e(e){if(!u){if(u=!0,a=e,e=n(e),void 0!==l&&d.hasValue){var t=d.value;if(l(t,e))return o=t}return o=e}if(t=o,i(a,e))return t;var r=n(e);return void 0!==l&&l(t,r)?(a=e,t):(a=e,o=r)}var a,o,u=!1,s=void 0===r?null:r;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,r,n,l]))[0],f[1]);return u(function(){d.hasValue=!0,d.value=p},[p]),c(p),p}},6597:(e,t,r)=>{r.d(t,{RE:()=>s,l0:()=>ee,m7:()=>ed,lE:()=>G,cw:()=>w,$k:()=>T,hT:()=>X});var n=r(5340),i=r(7895),a=r(4605),o=r(3030),u=class extends Error{issues;constructor(e){super(e[0].message),this.name="SchemaError",this.issues=e}},s=(r(5733),(e=>(e.uninitialized="uninitialized",e.pending="pending",e.fulfilled="fulfilled",e.rejected="rejected",e))(s||{}));function c(e){return{status:e,isUninitialized:"uninitialized"===e,isLoading:"pending"===e,isSuccess:"fulfilled"===e,isError:"rejected"===e}}var l=n.Qd;function f(e){let t=0;for(let r in e)t++;return t}var d=e=>[].concat(...e);function p(e){return null!=e}var y=e=>e.replace(/\/$/,""),h=e=>e.replace(/^\//,""),m=(...e)=>fetch(...e),g=e=>e.status>=200&&e.status<=299,b=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function v(e){if(!(0,n.Qd)(e))return e;let t={...e};for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return t}function w({baseUrl:e,prepareHeaders:t=e=>e,fetchFn:r=m,paramsSerializer:i,isJsonContentType:a=b,jsonContentType:o="application/json",jsonReplacer:u,timeout:s,responseHandler:c,validateStatus:l,...f}={}){return"undefined"==typeof fetch&&r===m&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(p,m,b)=>{let w,_,{getState:S,extra:O,endpoint:E,forced:P,type:j}=m,{url:A,headers:R=new Headers(f.headers),params:T,responseHandler:C=c??"json",validateStatus:N=l??g,timeout:q=s,...k}="string"==typeof p?{url:p}:p,x,M=m.signal;q&&(x=new AbortController,m.signal.addEventListener("abort",x.abort),M=x.signal);let D={...f,signal:M,...k};R=new Headers(v(R)),D.headers=await t(R,{getState:S,arg:p,extra:O,endpoint:E,forced:P,type:j,extraOptions:b})||R;let I=e=>"object"==typeof e&&((0,n.Qd)(e)||Array.isArray(e)||"function"==typeof e.toJSON);if(!D.headers.has("content-type")&&I(D.body)&&D.headers.set("content-type",o),I(D.body)&&a(D.headers)&&(D.body=JSON.stringify(D.body,u)),T){let e=~A.indexOf("?")?"&":"?";A+=e+(i?i(T):new URLSearchParams(v(T)))}let z=new Request(A=function(e,t){var r;if(!e)return t;if(!t)return e;if(r=t,RegExp("(^|:)//").test(r))return t;let n=e.endsWith("/")||!t.startsWith("?")?"/":"";return e=y(e),t=h(t),`${e}${n}${t}`}(e,A),D);w={request:new Request(A,D)};let $,Q=!1,K=x&&setTimeout(()=>{Q=!0,x.abort()},q);try{$=await r(z)}catch(e){return{error:{status:Q?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(e)},meta:w}}finally{K&&clearTimeout(K),x?.signal.removeEventListener("abort",x.abort)}let F=$.clone();w.response=F;let L="";try{let e;if(await Promise.all([d($,C).then(e=>_=e,t=>e=t),F.text().then(e=>L=e,()=>{})]),e)throw e}catch(e){return{error:{status:"PARSING_ERROR",originalStatus:$.status,data:L,error:String(e)},meta:w}}return N($,_)?{data:_,meta:w}:{error:{status:$.status,data:_},meta:w}};async function d(e,t){if("function"==typeof t)return t(e);if("content-type"===t&&(t=a(e.headers)?"json":"text"),"json"===t){let t=await e.text();return t.length?JSON.parse(t):null}return e.text()}}var _=class{constructor(e,t){this.value=e,this.meta=t}};async function S(e=0,t=5){let r=~~((Math.random()+.4)*(300<<Math.min(e,t)));await new Promise(e=>setTimeout(t=>e(t),r))}var O={},E=(0,i.VP)("__rtkq/focused"),P=(0,i.VP)("__rtkq/unfocused"),j=(0,i.VP)("__rtkq/online"),A=(0,i.VP)("__rtkq/offline"),R=!1;function T(e,t){return t?t(e,{onFocus:E,onFocusLost:P,onOffline:A,onOnline:j}):function(){let t=()=>e(E()),r=()=>e(P()),n=()=>e(j()),i=()=>e(A()),a=()=>{"visible"===window.document.visibilityState?t():r()};return!R&&"undefined"!=typeof window&&window.addEventListener&&(window.addEventListener("visibilitychange",a,!1),window.addEventListener("focus",t,!1),window.addEventListener("online",n,!1),window.addEventListener("offline",i,!1),R=!0),()=>{window.removeEventListener("focus",t),window.removeEventListener("visibilitychange",a),window.removeEventListener("online",n),window.removeEventListener("offline",i),R=!1}}()}function C(e){return"query"===e.type}function N(e){return"infinitequery"===e.type}function q(e){return C(e)||N(e)}function k(e,t,r,n,i,a){return"function"==typeof e?e(t,r,n,i).filter(p).map(x).map(a):Array.isArray(e)?e.map(x).map(a):[]}function x(e){return"string"==typeof e?{type:e}:e}var M=Symbol("forceQueryFn"),D=e=>"function"==typeof e[M],I=class extends u{constructor(e,t,r,n){super(e),this.value=t,this.schemaName=r,this._bqMeta=n}};async function z(e,t,r,n){let i=await e["~standard"].validate(t);if(i.issues)throw new I(i.issues,t,r,n);return i.value}function $(e){return e}var Q=(e={})=>({...e,[i.cN]:!0});function K(e,{pages:t,pageParams:r},n){let i=t.length-1;return e.getNextPageParam(t[i],t,r[i],r,n)}function F(e,{pages:t,pageParams:r},n){return e.getPreviousPageParam?.(t[0],t,r[0],r,n)}function L(e,t,r,n){return k(r[e.meta.arg.endpointName][t],(0,i.sf)(e)?e.payload:void 0,(0,i.WA)(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,n)}function W(e,t,r){let n=e[t];n&&r(n)}function U(e){return("arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)??e.requestId}function V(e,t,r){let n=e[U(t)];n&&r(n)}var B={},X=Symbol.for("RTKQ/skipToken"),Z={status:"uninitialized"},H=(0,a.jM)(Z,()=>{}),J=(0,a.jM)(Z,()=>{}),Y=WeakMap?new WeakMap:void 0,G=({endpointName:e,queryArgs:t})=>{let r="",i=Y?.get(t);if("string"==typeof i)r=i;else{let e=JSON.stringify(t,(e,t)=>(t="bigint"==typeof t?{$bigint:t.toString()}:t,t=(0,n.Qd)(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t));(0,n.Qd)(t)&&Y?.set(t,e),r=e}return`${e}(${r})`};function ee(...e){return function(t){let r=(0,o.X4)(e=>t.extractRehydrationInfo?.(e,{reducerPath:t.reducerPath??"api"})),n={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...t,extractRehydrationInfo:r,serializeQueryArgs(e){let r=G;if("serializeQueryArgs"in e.endpointDefinition){let t=e.endpointDefinition.serializeQueryArgs;r=e=>{let r=t(e);return"string"==typeof r?r:G({...e,queryArgs:r})}}else t.serializeQueryArgs&&(r=t.serializeQueryArgs);return r(e)},tagTypes:[...t.tagTypes||[]]},a={endpointDefinitions:{},batch(e){e()},apiUid:(0,i.Ak)(),extractRehydrationInfo:r,hasRehydrationInfo:(0,o.X4)(e=>null!=r(e))},u={injectEndpoints:function(e){for(let[t,r]of Object.entries(e.endpoints({query:e=>({...e,type:"query"}),mutation:e=>({...e,type:"mutation"}),infiniteQuery:e=>({...e,type:"infinitequery"})}))){if(!0!==e.overrideExisting&&t in a.endpointDefinitions){if("throw"===e.overrideExisting)throw Error((0,i.gk)(39));continue}for(let e of(a.endpointDefinitions[t]=r,s))e.injectEndpoint(t,r)}return u},enhanceEndpoints({addTagTypes:e,endpoints:t}){if(e)for(let t of e)n.tagTypes.includes(t)||n.tagTypes.push(t);if(t)for(let[e,r]of Object.entries(t))"function"==typeof r?r(a.endpointDefinitions[e]):Object.assign(a.endpointDefinitions[e]||{},r);return u}},s=e.map(e=>e.init(u,n,a));return u.injectEndpoints({endpoints:t.endpoints})}}function et(e,...t){return Object.assign(e,...t)}var er=({api:e,queryThunk:t,internalState:r})=>{let n=`${e.reducerPath}/subscriptions`,i=null,o=null,{updateSubscriptionOptions:u,unsubscribeQueryResult:s}=e.internalActions,c=(r,n)=>{if(u.match(n)){let{queryCacheKey:e,requestId:t,options:i}=n.payload;return r?.[e]?.[t]&&(r[e][t]=i),!0}if(s.match(n)){let{queryCacheKey:e,requestId:t}=n.payload;return r[e]&&delete r[e][t],!0}if(e.internalActions.removeQueryResult.match(n))return delete r[n.payload.queryCacheKey],!0;if(t.pending.match(n)){let{meta:{arg:e,requestId:t}}=n,i=r[e.queryCacheKey]??={};return i[`${t}_running`]={},e.subscribe&&(i[t]=e.subscriptionOptions??i[t]??{}),!0}let i=!1;if(t.fulfilled.match(n)||t.rejected.match(n)){let e=r[n.meta.arg.queryCacheKey]||{},t=`${n.meta.requestId}_running`;i||=!!e[t],delete e[t]}if(t.rejected.match(n)){let{meta:{condition:e,arg:t,requestId:a}}=n;if(e&&t.subscribe){let e=r[t.queryCacheKey]??={};e[a]=t.subscriptionOptions??e[a]??{},i=!0}}return i},l=()=>r.currentSubscriptions,d={getSubscriptions:l,getSubscriptionCount:e=>f(l()[e]??{}),isRequestSubscribed:(e,t)=>{let r=l();return!!r?.[e]?.[t]}};return(u,s)=>{if(i||(i=JSON.parse(JSON.stringify(r.currentSubscriptions))),e.util.resetApiState.match(u))return i=r.currentSubscriptions={},o=null,[!0,!1];if(e.internalActions.internal_getRTKQSubscriptions.match(u))return[!1,d];let l=c(r.currentSubscriptions,u),f=!0;if(l){o||(o=setTimeout(()=>{let t=JSON.parse(JSON.stringify(r.currentSubscriptions)),[,n]=(0,a.vI)(i,()=>t);s.next(e.internalActions.subscriptionsUpdated(n)),i=t,o=null},500));let c="string"==typeof u.type&&!!u.type.startsWith(n),l=t.rejected.match(u)&&u.meta.condition&&!!u.meta.arg.subscribe;f=!c&&!l}return[f,!1]}},en=({reducerPath:e,api:t,queryThunk:r,context:n,internalState:a,selectors:{selectQueryEntry:o,selectConfig:u}})=>{let{removeQueryResult:s,unsubscribeQueryResult:c,cacheEntriesUpserted:l}=t.internalActions,f=(0,i.i0)(c.match,r.fulfilled,r.rejected,l.match);function d(e){let t=a.currentSubscriptions[e];return!!t&&!function(e){for(let t in e)return!1;return!0}(t)}let p={};function y(e,t,r){let i=t.getState();for(let a of e){let e=o(i,a);!function(e,t,r,i){let a=n.endpointDefinitions[t],o=a?.keepUnusedDataFor??i.keepUnusedDataFor;if(o===1/0)return;let u=Math.max(0,Math.min(o,2147482.647));if(!d(e)){let t=p[e];t&&clearTimeout(t),p[e]=setTimeout(()=>{d(e)||r.dispatch(s({queryCacheKey:e})),delete p[e]},1e3*u)}}(a,e?.endpointName,t,r)}}return(e,r,i)=>{let a=u(r.getState());if(f(e)){let t;if(l.match(e))t=e.payload.map(e=>e.queryDescription.queryCacheKey);else{let{queryCacheKey:r}=c.match(e)?e.payload:e.meta.arg;t=[r]}y(t,r,a)}if(t.util.resetApiState.match(e))for(let[e,t]of Object.entries(p))t&&clearTimeout(t),delete p[e];if(n.hasRehydrationInfo(e)){let{queries:t}=n.extractRehydrationInfo(e);y(Object.keys(t),r,a)}}},ei=Error("Promise never resolved before cacheEntryRemoved."),ea=({api:e,reducerPath:t,context:r,queryThunk:n,mutationThunk:a,internalState:o,selectors:{selectQueryEntry:u,selectApiState:s}})=>{let c=(0,i.$S)(n),l=(0,i.$S)(a),f=(0,i.sf)(n,a),d={};function p(e,t,r){let n=d[e];n?.valueResolved&&(n.valueResolved({data:t,meta:r}),delete n.valueResolved)}function y(e){let t=d[e];t&&(delete d[e],t.cacheEntryRemoved())}function h(t,n,i,a,o){let u=r.endpointDefinitions[t],s=u?.onCacheEntryAdded;if(!s)return;let c={},l=new Promise(e=>{c.cacheEntryRemoved=e}),f=Promise.race([new Promise(e=>{c.valueResolved=e}),l.then(()=>{throw ei})]);f.catch(()=>{}),d[i]=c;let p=e.endpoints[t].select(q(u)?n:i),y=a.dispatch((e,t,r)=>r),h={...a,getCacheEntry:()=>p(a.getState()),requestId:o,extra:y,updateCachedData:q(u)?r=>a.dispatch(e.util.updateQueryData(t,n,r)):void 0,cacheDataLoaded:f,cacheEntryRemoved:l};Promise.resolve(s(n,h)).catch(e=>{if(e!==ei)throw e})}return(r,i,o)=>{let s=function(t){return c(t)?t.meta.arg.queryCacheKey:l(t)?t.meta.arg.fixedCacheKey??t.meta.requestId:e.internalActions.removeQueryResult.match(t)?t.payload.queryCacheKey:e.internalActions.removeMutationResult.match(t)?U(t.payload):""}(r);function m(e,t,r,n){let a=u(o,t),s=u(i.getState(),t);!a&&s&&h(e,n,t,i,r)}if(n.pending.match(r))m(r.meta.arg.endpointName,s,r.meta.requestId,r.meta.arg.originalArgs);else if(e.internalActions.cacheEntriesUpserted.match(r))for(let{queryDescription:e,value:t}of r.payload){let{endpointName:n,originalArgs:i,queryCacheKey:a}=e;m(n,a,r.meta.requestId,i),p(a,t,{})}else if(a.pending.match(r))i.getState()[t].mutations[s]&&h(r.meta.arg.endpointName,r.meta.arg.originalArgs,s,i,r.meta.requestId);else if(f(r))p(s,r.payload,r.meta.baseQueryMeta);else if(e.internalActions.removeQueryResult.match(r)||e.internalActions.removeMutationResult.match(r))y(s);else if(e.util.resetApiState.match(r))for(let e of Object.keys(d))y(e)}},eo=({api:e,context:{apiUid:t},reducerPath:r})=>(r,n)=>{e.util.resetApiState.match(r)&&n.dispatch(e.internalActions.middlewareRegistered(t))},eu=({reducerPath:e,context:t,context:{endpointDefinitions:r},mutationThunk:n,queryThunk:a,api:o,assertTagType:u,refetchQuery:s,internalState:c})=>{let{removeQueryResult:l}=o.internalActions,d=(0,i.i0)((0,i.sf)(n),(0,i.WA)(n)),p=(0,i.i0)((0,i.sf)(n,a),(0,i.TK)(n,a)),y=[];function h(r,n){let i=n.getState(),a=i[e];if(y.push(...r),"delayed"===a.config.invalidationBehavior&&function(e){let{queries:t,mutations:r}=e;for(let e of[t,r])for(let t in e)if(e[t]?.status==="pending")return!0;return!1}(a))return;let u=y;if(y=[],0===u.length)return;let d=o.util.selectInvalidatedBy(i,u);t.batch(()=>{for(let{queryCacheKey:e}of Array.from(d.values())){let t=a.queries[e],r=c.currentSubscriptions[e]??{};t&&(0===f(r)?n.dispatch(l({queryCacheKey:e})):"uninitialized"!==t.status&&n.dispatch(s(t)))}})}return(e,t)=>{d(e)?h(L(e,"invalidatesTags",r,u),t):p(e)?h([],t):o.util.invalidateTags.match(e)&&h(k(e.payload,void 0,void 0,void 0,void 0,u),t)}},es=({reducerPath:e,queryThunk:t,api:r,refetchQuery:n,internalState:i})=>{let a={};function o({queryCacheKey:t},r){let u=r.getState()[e],s=u.queries[t],l=i.currentSubscriptions[t];if(!s||"uninitialized"===s.status)return;let{lowestPollingInterval:f,skipPollingIfUnfocused:d}=c(l);if(!Number.isFinite(f))return;let p=a[t];p?.timeout&&(clearTimeout(p.timeout),p.timeout=void 0);let y=Date.now()+f;a[t]={nextPollTimestamp:y,pollingInterval:f,timeout:setTimeout(()=>{(u.config.focused||!d)&&r.dispatch(n(s)),o({queryCacheKey:t},r)},f)}}function u({queryCacheKey:t},r){let n=r.getState()[e].queries[t],u=i.currentSubscriptions[t];if(!n||"uninitialized"===n.status)return;let{lowestPollingInterval:l}=c(u);if(!Number.isFinite(l))return void s(t);let f=a[t],d=Date.now()+l;(!f||d<f.nextPollTimestamp)&&o({queryCacheKey:t},r)}function s(e){let t=a[e];t?.timeout&&clearTimeout(t.timeout),delete a[e]}function c(e={}){let t=!1,r=Number.POSITIVE_INFINITY;for(let n in e)e[n].pollingInterval&&(r=Math.min(e[n].pollingInterval,r),t=e[n].skipPollingIfUnfocused||t);return{lowestPollingInterval:r,skipPollingIfUnfocused:t}}return(e,n)=>{(r.internalActions.updateSubscriptionOptions.match(e)||r.internalActions.unsubscribeQueryResult.match(e))&&u(e.payload,n),(t.pending.match(e)||t.rejected.match(e)&&e.meta.condition)&&u(e.meta.arg,n),(t.fulfilled.match(e)||t.rejected.match(e)&&!e.meta.condition)&&o(e.meta.arg,n),r.util.resetApiState.match(e)&&function(){for(let e of Object.keys(a))s(e)}()}},ec=({api:e,context:t,queryThunk:r,mutationThunk:n})=>{let a=(0,i.mm)(r,n),o=(0,i.TK)(r,n),u=(0,i.sf)(r,n),s={};return(r,n)=>{if(a(r)){let{requestId:i,arg:{endpointName:a,originalArgs:o}}=r.meta,u=t.endpointDefinitions[a],c=u?.onQueryStarted;if(c){let t={},r=new Promise((e,r)=>{t.resolve=e,t.reject=r});r.catch(()=>{}),s[i]=t;let l=e.endpoints[a].select(q(u)?o:i),f=n.dispatch((e,t,r)=>r),d={...n,getCacheEntry:()=>l(n.getState()),requestId:i,extra:f,updateCachedData:q(u)?t=>n.dispatch(e.util.updateQueryData(a,o,t)):void 0,queryFulfilled:r};c(o,d)}}else if(u(r)){let{requestId:e,baseQueryMeta:t}=r.meta;s[e]?.resolve({data:r.payload,meta:t}),delete s[e]}else if(o(r)){let{requestId:e,rejectedWithValue:t,baseQueryMeta:n}=r.meta;s[e]?.reject({error:r.payload??r.error,isUnhandledError:!t,meta:n}),delete s[e]}}},el=({reducerPath:e,context:t,api:r,refetchQuery:n,internalState:i})=>{let{removeQueryResult:a}=r.internalActions;function o(r,o){let u=r.getState()[e],s=u.queries,c=i.currentSubscriptions;t.batch(()=>{for(let e of Object.keys(c)){let t=s[e],i=c[e];i&&t&&(Object.values(i).some(e=>!0===e[o])||Object.values(i).every(e=>void 0===e[o])&&u.config[o])&&(0===f(i)?r.dispatch(a({queryCacheKey:e})):"uninitialized"!==t.status&&r.dispatch(n(t)))}})}return(e,t)=>{E.match(e)&&o(t,"refetchOnFocus"),j.match(e)&&o(t,"refetchOnReconnect")}},ef=Symbol(),ed=({createSelector:e=o.Mz}={})=>({name:ef,init(t,{baseQuery:r,tagTypes:o,reducerPath:u,serializeQueryArgs:s,keepUnusedDataFor:y,refetchOnMountOrArgChange:h,refetchOnFocus:m,refetchOnReconnect:g,invalidationBehavior:b,onSchemaFailure:v,catchSchemaFailure:w,skipSchemaValidation:S},O){(0,a.YT)();let R=e=>e;Object.assign(t,{reducerPath:u,endpoints:{},internalActions:{onOnline:j,onOffline:A,onFocus:E,onFocusLost:P},util:{}});let T=function({serializeQueryArgs:e,reducerPath:t,createSelector:r}){let n=e=>H,i=e=>J;return{buildQuerySelector:function(e,t){return l(e,t,a)},buildInfiniteQuerySelector:function(e,t){let{infiniteQueryOptions:r}=t;return l(e,t,function(e){var t,n,i,a,o,u;let s={...e,...c(e.status)},{isLoading:l,isError:f,direction:d}=s,p="forward"===d,y="backward"===d;return{...s,hasNextPage:(t=r,n=s.data,i=s.originalArgs,!!n&&null!=K(t,n,i)),hasPreviousPage:(a=r,o=s.data,u=s.originalArgs,!!o&&!!a.getPreviousPageParam&&null!=F(a,o,u)),isFetchingNextPage:l&&p,isFetchingPreviousPage:l&&y,isFetchNextPageError:f&&p,isFetchPreviousPageError:f&&y}})},buildMutationSelector:function(){return e=>{let n;return r((n="object"==typeof e?U(e)??X:e)===X?i:e=>(function(e){return e[t]})(e)?.mutations?.[n]??J,a)}},selectInvalidatedBy:function(e,r){let n=e[t],i=new Set;for(let e of r.filter(p).map(x)){let t=n.provided.tags[e.type];if(t)for(let r of(void 0!==e.id?t[e.id]:d(Object.values(t)))??[])i.add(r)}return d(Array.from(i.values()).map(e=>{let t=n.queries[e];return t?[{queryCacheKey:e,endpointName:t.endpointName,originalArgs:t.originalArgs}]:[]}))},selectCachedArgsForQuery:function(e,t){return Object.values(u(e)).filter(e=>e?.endpointName===t&&"uninitialized"!==e.status).map(e=>e.originalArgs)},selectApiState:o,selectQueries:u,selectMutations:function(e){return function(e){return e[t]}(e)?.mutations},selectQueryEntry:s,selectConfig:function(e){return function(e){return e[t]}(e)?.config}};function a(e){return{...e,...c(e.status)}}function o(e){return e[t]}function u(e){return e[t]?.queries}function s(e,t){return u(e)?.[t]}function l(t,i,a){return o=>{if(o===X)return r(n,a);let u=e({queryArgs:o,endpointDefinition:i,endpointName:t});return r(e=>s(e,u)??H,a)}}}({serializeQueryArgs:s,reducerPath:u,createSelector:e}),{selectInvalidatedBy:q,selectCachedArgsForQuery:Z,buildQuerySelector:Y,buildInfiniteQuerySelector:G,buildMutationSelector:ee}=T;et(t.util,{selectInvalidatedBy:q,selectCachedArgsForQuery:Z});let{queryThunk:ei,infiniteQueryThunk:ed,mutationThunk:ep,patchQueryData:ey,updateQueryData:eh,upsertQueryData:em,prefetch:eg,buildMatchThunkActions:eb}=function({reducerPath:e,baseQuery:t,context:{endpointDefinitions:r},serializeQueryArgs:n,api:o,assertTagType:u,selectors:s,onSchemaFailure:c,catchSchemaFailure:l,skipSchemaValidation:f}){function d(e,t,r=0){let n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}function p(e,t,r=0){let n=[...e,t];return r&&n.length>r?n.slice(1):n}let y=(e,t)=>e.query&&e[t]?e[t]:$,h=async(e,{signal:n,abort:i,rejectWithValue:a,fulfillWithValue:o,dispatch:u,getState:h,extra:g})=>{let b=r[e.endpointName],{metaSchema:v,skipSchemaValidation:w=f}=b;try{let r,a=y(b,"transformResponse"),c={signal:n,abort:i,dispatch:u,getState:h,extra:g,endpoint:e.endpointName,type:e.type,forced:"query"===e.type?m(e,h()):void 0,queryCacheKey:"query"===e.type?e.queryCacheKey:void 0},l="query"===e.type?e[M]:void 0,f=async(t,r,n,i)=>{if(null==r&&t.pages.length)return Promise.resolve({data:t});let a={queryArg:e.originalArgs,pageParam:r},o=await S(a),u=i?d:p;return{data:{pages:u(t.pages,o.data,n),pageParams:u(t.pageParams,r,n)},meta:o.meta}};async function S(e){let r,{extraOptions:n,argSchema:i,rawResponseSchema:o,responseSchema:u}=b;if(i&&!w&&(e=await z(i,e,"argSchema",{})),(r=l?l():b.query?await t(b.query(e),c,n):await b.queryFn(e,c,n,e=>t(e,c,n))).error)throw new _(r.error,r.meta);let{data:s}=r;o&&!w&&(s=await z(o,r.data,"rawResponseSchema",r.meta));let f=await a(s,r.meta,e);return u&&!w&&(f=await z(u,f,"responseSchema",r.meta)),{...r,data:f}}if("query"===e.type&&"infiniteQueryOptions"in b){let t,{infiniteQueryOptions:n}=b,{maxPages:i=1/0}=n,a=s.selectQueryEntry(h(),e.queryCacheKey)?.data,o=(!m(e,h())||e.direction)&&a?a:{pages:[],pageParams:[]};if("direction"in e&&e.direction&&o.pages.length){let r="backward"===e.direction,a=(r?F:K)(n,o,e.originalArgs);t=await f(o,a,i,r)}else{let{initialPageParam:r=n.initialPageParam}=e,u=a?.pageParams??[],s=u[0]??r,c=u.length;t=await f(o,s,i),l&&(t={data:t.data.pages[0]});for(let r=1;r<c;r++){let r=K(n,t.data,e.originalArgs);t=await f(t.data,r,i)}}r=t}else r=await S(e.originalArgs);return v&&!w&&r.meta&&(r.meta=await z(v,r.meta,"metaSchema",r.meta)),o(r.data,Q({fulfilledTimeStamp:Date.now(),baseQueryMeta:r.meta}))}catch(r){let t=r;if(t instanceof _){let r=y(b,"transformErrorResponse"),{rawErrorResponseSchema:n,errorResponseSchema:i}=b,{value:o,meta:u}=t;try{n&&!w&&(o=await z(n,o,"rawErrorResponseSchema",u)),v&&!w&&(u=await z(v,u,"metaSchema",u));let t=await r(o,u,e.originalArgs);return i&&!w&&(t=await z(i,t,"errorResponseSchema",u)),a(t,Q({baseQueryMeta:u}))}catch(e){t=e}}try{if(t instanceof I){let r={endpoint:e.endpointName,arg:e.originalArgs,type:e.type,queryCacheKey:"query"===e.type?e.queryCacheKey:void 0};b.onSchemaFailure?.(t,r),c?.(t,r);let{catchSchemaFailure:n=l}=b;if(n)return a(n(t,r),Q({baseQueryMeta:t._bqMeta}))}}catch(e){t=e}throw console.error(t),t}};function m(e,t){let r=s.selectQueryEntry(t,e.queryCacheKey),n=s.selectConfig(t).refetchOnMountOrArgChange,i=r?.fulfilledTimeStamp,a=e.forceRefetch??(e.subscribe&&n);return!!a&&(!0===a||(Number(new Date)-Number(i))/1e3>=a)}let g=()=>(0,i.zD)(`${e}/executeQuery`,h,{getPendingMeta({arg:e}){let t=r[e.endpointName];return Q({startedTimeStamp:Date.now(),...N(t)?{direction:e.direction}:{}})},condition(e,{getState:t}){let n=t(),i=s.selectQueryEntry(n,e.queryCacheKey),a=i?.fulfilledTimeStamp,o=e.originalArgs,u=i?.originalArgs,c=r[e.endpointName],l=e.direction;return!!D(e)||i?.status!=="pending"&&(!!(m(e,n)||C(c)&&c?.forceRefetch?.({currentArg:o,previousArg:u,endpointState:i,state:n}))||!a||!!l)},dispatchConditionRejection:!0}),b=g(),v=g(),w=(0,i.zD)(`${e}/executeMutation`,h,{getPendingMeta:()=>Q({startedTimeStamp:Date.now()})}),S=e=>"force"in e,O=e=>"ifOlderThan"in e;function E(e){return t=>t?.meta?.arg?.endpointName===e}return{queryThunk:b,mutationThunk:w,infiniteQueryThunk:v,prefetch:(e,t,r)=>(n,i)=>{let a=S(r)&&r.force,u=O(r)&&r.ifOlderThan,s=(r=!0)=>o.endpoints[e].initiate(t,{forceRefetch:r,isPrefetch:!0}),c=o.endpoints[e].select(t)(i());if(a)n(s());else if(u){let e=c?.fulfilledTimeStamp;if(!e)return void n(s());(Number(new Date)-Number(new Date(e)))/1e3>=u&&n(s())}else n(s(!1))},updateQueryData:(e,t,r,n=!0)=>(i,u)=>{let s,c=o.endpoints[e].select(t)(u()),l={patches:[],inversePatches:[],undo:()=>i(o.util.patchQueryData(e,t,l.inversePatches,n))};if("uninitialized"===c.status)return l;if("data"in c)if((0,a.a6)(c.data)){let[e,t,n]=(0,a.vI)(c.data,r);l.patches.push(...t),l.inversePatches.push(...n),s=e}else s=r(c.data),l.patches.push({op:"replace",path:[],value:s}),l.inversePatches.push({op:"replace",path:[],value:c.data});return 0===l.patches.length||i(o.util.patchQueryData(e,t,l.patches,n)),l},upsertQueryData:(e,t,r)=>n=>n(o.endpoints[e].initiate(t,{subscribe:!1,forceRefetch:!0,[M]:()=>({data:r})})),patchQueryData:(e,t,i,a)=>(s,c)=>{let l=r[e],f=n({queryArgs:t,endpointDefinition:l,endpointName:e});if(s(o.internalActions.queryResultPatched({queryCacheKey:f,patches:i})),!a)return;let d=o.endpoints[e].select(t)(c()),p=k(l.providesTags,d.data,void 0,t,{},u);s(o.internalActions.updateProvidedBy([{queryCacheKey:f,providedTags:p}]))},buildMatchThunkActions:function(e,t){return{matchPending:(0,i.f$)((0,i.mm)(e),E(t)),matchFulfilled:(0,i.f$)((0,i.sf)(e),E(t)),matchRejected:(0,i.f$)((0,i.TK)(e),E(t))}}}}({baseQuery:r,reducerPath:u,context:O,api:t,serializeQueryArgs:s,assertTagType:R,selectors:T,onSchemaFailure:v,catchSchemaFailure:w,skipSchemaValidation:S}),{reducer:ev,actions:ew}=function({reducerPath:e,queryThunk:t,mutationThunk:r,serializeQueryArgs:o,context:{endpointDefinitions:u,apiUid:s,extractRehydrationInfo:c,hasRehydrationInfo:f},assertTagType:d,config:p}){let y=(0,i.VP)(`${e}/resetApiState`);function h(e,t,r,n){e[t.queryCacheKey]??={status:"uninitialized",endpointName:t.endpointName},W(e,t.queryCacheKey,e=>{e.status="pending",e.requestId=r&&e.requestId?e.requestId:n.requestId,void 0!==t.originalArgs&&(e.originalArgs=t.originalArgs),e.startedTimeStamp=n.startedTimeStamp,N(u[n.arg.endpointName])&&"direction"in t&&(e.direction=t.direction)})}function m(e,t,r,n){W(e,t.arg.queryCacheKey,e=>{if(e.requestId!==t.requestId&&!n)return;let{merge:i}=u[t.arg.endpointName];if(e.status="fulfilled",i)if(void 0!==e.data){let{fulfilledTimeStamp:n,arg:o,baseQueryMeta:u,requestId:s}=t,c=(0,a.jM)(e.data,e=>i(e,r,{arg:o.originalArgs,baseQueryMeta:u,fulfilledTimeStamp:n,requestId:s}));e.data=c}else e.data=r;else e.data=u[t.arg.endpointName].structuralSharing??!0?function e(t,r){if(t===r||!(l(t)&&l(r)||Array.isArray(t)&&Array.isArray(r)))return r;let n=Object.keys(r),i=Object.keys(t),a=n.length===i.length,o=Array.isArray(r)?[]:{};for(let i of n)o[i]=e(t[i],r[i]),a&&(a=t[i]===o[i]);return a?t:o}((0,a.Qx)(e.data)?(0,a.c2)(e.data):e.data,r):r;delete e.error,e.fulfilledTimeStamp=t.fulfilledTimeStamp})}let g=(0,i.Z0)({name:`${e}/queries`,initialState:B,reducers:{removeQueryResult:{reducer(e,{payload:{queryCacheKey:t}}){delete e[t]},prepare:(0,i.aA)()},cacheEntriesUpserted:{reducer(e,t){for(let r of t.payload){let{queryDescription:n,value:i}=r;h(e,n,!0,{arg:n,requestId:t.meta.requestId,startedTimeStamp:t.meta.timestamp}),m(e,{arg:n,requestId:t.meta.requestId,fulfilledTimeStamp:t.meta.timestamp,baseQueryMeta:{}},i,!0)}},prepare:e=>({payload:e.map(e=>{let{endpointName:t,arg:r,value:n}=e,i=u[t];return{queryDescription:{type:"query",endpointName:t,originalArgs:e.arg,queryCacheKey:o({queryArgs:r,endpointDefinition:i,endpointName:t})},value:n}}),meta:{[i.cN]:!0,requestId:(0,i.Ak)(),timestamp:Date.now()}})},queryResultPatched:{reducer(e,{payload:{queryCacheKey:t,patches:r}}){W(e,t,e=>{e.data=(0,a.$i)(e.data,r.concat())})},prepare:(0,i.aA)()}},extraReducers(e){e.addCase(t.pending,(e,{meta:t,meta:{arg:r}})=>{let n=D(r);h(e,r,n,t)}).addCase(t.fulfilled,(e,{meta:t,payload:r})=>{let n=D(t.arg);m(e,t,r,n)}).addCase(t.rejected,(e,{meta:{condition:t,arg:r,requestId:n},error:i,payload:a})=>{W(e,r.queryCacheKey,e=>{if(t);else{if(e.requestId!==n)return;e.status="rejected",e.error=a??i}})}).addMatcher(f,(e,t)=>{let{queries:r}=c(t);for(let[t,n]of Object.entries(r))(n?.status==="fulfilled"||n?.status==="rejected")&&(e[t]=n)})}}),b=(0,i.Z0)({name:`${e}/mutations`,initialState:B,reducers:{removeMutationResult:{reducer(e,{payload:t}){let r=U(t);r in e&&delete e[r]},prepare:(0,i.aA)()}},extraReducers(e){e.addCase(r.pending,(e,{meta:t,meta:{requestId:r,arg:n,startedTimeStamp:i}})=>{n.track&&(e[U(t)]={requestId:r,status:"pending",endpointName:n.endpointName,startedTimeStamp:i})}).addCase(r.fulfilled,(e,{payload:t,meta:r})=>{r.arg.track&&V(e,r,e=>{e.requestId===r.requestId&&(e.status="fulfilled",e.data=t,e.fulfilledTimeStamp=r.fulfilledTimeStamp)})}).addCase(r.rejected,(e,{payload:t,error:r,meta:n})=>{n.arg.track&&V(e,n,e=>{e.requestId===n.requestId&&(e.status="rejected",e.error=t??r)})}).addMatcher(f,(e,t)=>{let{mutations:r}=c(t);for(let[t,n]of Object.entries(r))(n?.status==="fulfilled"||n?.status==="rejected")&&t!==n?.requestId&&(e[t]=n)})}}),v=(0,i.Z0)({name:`${e}/invalidation`,initialState:{tags:{},keys:{}},reducers:{updateProvidedBy:{reducer(e,t){for(let{queryCacheKey:r,providedTags:n}of t.payload){for(let{type:t,id:i}of(w(e,r),n)){let n=(e.tags[t]??={})[i||"__internal_without_id"]??=[];n.includes(r)||n.push(r)}e.keys[r]=n}},prepare:(0,i.aA)()}},extraReducers(e){e.addCase(g.actions.removeQueryResult,(e,{payload:{queryCacheKey:t}})=>{w(e,t)}).addMatcher(f,(e,t)=>{let{provided:r}=c(t);for(let[t,n]of Object.entries(r))for(let[r,i]of Object.entries(n)){let n=(e.tags[t]??={})[r||"__internal_without_id"]??=[];for(let e of i)n.includes(e)||n.push(e)}}).addMatcher((0,i.i0)((0,i.sf)(t),(0,i.WA)(t)),(e,t)=>{_(e,[t])}).addMatcher(g.actions.cacheEntriesUpserted.match,(e,t)=>{_(e,t.payload.map(({queryDescription:e,value:t})=>({type:"UNKNOWN",payload:t,meta:{requestStatus:"fulfilled",requestId:"UNKNOWN",arg:e}})))})}});function w(e,t){for(let r of e.keys[t]??[]){let n=r.type,i=r.id??"__internal_without_id",a=e.tags[n]?.[i];a&&(e.tags[n][i]=a.filter(e=>e!==t))}delete e.keys[t]}function _(e,t){let r=t.map(e=>{let t=L(e,"providesTags",u,d),{queryCacheKey:r}=e.meta.arg;return{queryCacheKey:r,providedTags:t}});v.caseReducers.updateProvidedBy(e,v.actions.updateProvidedBy(r))}let S=(0,i.Z0)({name:`${e}/subscriptions`,initialState:B,reducers:{updateSubscriptionOptions(e,t){},unsubscribeQueryResult(e,t){},internal_getRTKQSubscriptions(){}}}),O=(0,i.Z0)({name:`${e}/internalSubscriptions`,initialState:B,reducers:{subscriptionsUpdated:{reducer:(e,t)=>(0,a.$i)(e,t.payload),prepare:(0,i.aA)()}}}),R=(0,i.Z0)({name:`${e}/config`,initialState:{online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine,focused:"undefined"==typeof document||"hidden"!==document.visibilityState,middlewareRegistered:!1,...p},reducers:{middlewareRegistered(e,{payload:t}){e.middlewareRegistered="conflict"!==e.middlewareRegistered&&s===t||"conflict"}},extraReducers:e=>{e.addCase(j,e=>{e.online=!0}).addCase(A,e=>{e.online=!1}).addCase(E,e=>{e.focused=!0}).addCase(P,e=>{e.focused=!1}).addMatcher(f,e=>({...e}))}}),T=(0,n.HY)({queries:g.reducer,mutations:b.reducer,provided:v.reducer,subscriptions:O.reducer,config:R.reducer});return{reducer:(e,t)=>T(y.match(t)?void 0:e,t),actions:{...R.actions,...g.actions,...S.actions,...O.actions,...b.actions,...v.actions,resetApiState:y}}}({context:O,queryThunk:ei,infiniteQueryThunk:ed,mutationThunk:ep,serializeQueryArgs:s,reducerPath:u,assertTagType:R,config:{refetchOnFocus:m,refetchOnReconnect:g,refetchOnMountOrArgChange:h,keepUnusedDataFor:y,reducerPath:u,invalidationBehavior:b}});et(t.util,{patchQueryData:ey,updateQueryData:eh,upsertQueryData:em,prefetch:eg,resetApiState:ew.resetApiState,upsertQueryEntries:ew.cacheEntriesUpserted}),et(t.internalActions,ew);let{middleware:e_,actions:eS}=function(e){let{reducerPath:t,queryThunk:r,api:a,context:o}=e,{apiUid:u}=o,s={invalidateTags:(0,i.VP)(`${t}/invalidateTags`)},c=e=>e.type.startsWith(`${t}/`),l=[eo,en,eu,es,ea,ec];return{middleware:r=>{let i=!1,s={...e,internalState:{currentSubscriptions:{}},refetchQuery:f,isThisApiSliceAction:c},d=l.map(e=>e(s)),p=er(s),y=el(s);return e=>s=>{let l;if(!(0,n.ve)(s))return e(s);i||(i=!0,r.dispatch(a.internalActions.middlewareRegistered(u)));let f={...r,next:e},h=r.getState(),[m,g]=p(s,f,h);if(l=m?e(s):g,r.getState()[t]&&(y(s,f,h),c(s)||o.hasRehydrationInfo(s)))for(let e of d)e(s,f,h);return l}},actions:s};function f(t){return e.api.endpoints[t.endpointName].initiate(t.originalArgs,{subscribe:!1,forceRefetch:!0})}}({reducerPath:u,context:O,queryThunk:ei,mutationThunk:ep,infiniteQueryThunk:ed,api:t,assertTagType:R,selectors:T});et(t.util,eS),et(t,{reducer:ev,middleware:e_});let{buildInitiateQuery:eO,buildInitiateInfiniteQuery:eE,buildInitiateMutation:eP,getRunningMutationThunk:ej,getRunningMutationsThunk:eA,getRunningQueriesThunk:eR,getRunningQueryThunk:eT}=function({serializeQueryArgs:e,queryThunk:t,infiniteQueryThunk:r,mutationThunk:n,api:i,context:a}){let o=new Map,u=new Map,{unsubscribeQueryResult:s,removeMutationResult:c,updateSubscriptionOptions:l}=i.internalActions;return{buildInitiateQuery:function(e,t){return y(e,t)},buildInitiateInfiniteQuery:function(e,t){return y(e,t)},buildInitiateMutation:function(e){return(t,{track:r=!0,fixedCacheKey:i}={})=>(a,o)=>{var s,l;let d=a(n({type:"mutation",endpointName:e,originalArgs:t,track:r,fixedCacheKey:i})),{requestId:p,abort:y,unwrap:h}=d,m=Object.assign((s=d.unwrap().then(e=>({data:e})),l=e=>({error:e}),s.catch(l)),{arg:d.arg,requestId:p,abort:y,unwrap:h,reset:()=>{a(c({requestId:p,fixedCacheKey:i}))}}),g=u.get(a)||{};return u.set(a,g),g[p]=m,m.then(()=>{delete g[p],f(g)||u.delete(a)}),i&&(g[i]=m,m.then(()=>{g[i]===m&&(delete g[i],f(g)||u.delete(a))})),m}},getRunningQueryThunk:function(t,r){return n=>{let i=e({queryArgs:r,endpointDefinition:a.endpointDefinitions[t],endpointName:t});return o.get(n)?.[i]}},getRunningMutationThunk:function(e,t){return e=>u.get(e)?.[t]},getRunningQueriesThunk:function(){return e=>Object.values(o.get(e)||{}).filter(p)},getRunningMutationsThunk:function(){return e=>Object.values(u.get(e)||{}).filter(p)}};function d(e){}function y(n,a){let u=(c,{subscribe:d=!0,forceRefetch:p,subscriptionOptions:y,[M]:h,...m}={})=>(g,b)=>{let v,w=e({queryArgs:c,endpointDefinition:a,endpointName:n}),_={...m,type:"query",subscribe:d,forceRefetch:p,subscriptionOptions:y,endpointName:n,originalArgs:c,queryCacheKey:w,[M]:h};if(C(a))v=t(_);else{let{direction:e,initialPageParam:t}=m;v=r({..._,direction:e,initialPageParam:t})}let S=i.endpoints[n].select(c),O=g(v),E=S(b()),{requestId:P,abort:j}=O,A=E.requestId!==P,R=o.get(g)?.[w],T=()=>S(b()),N=Object.assign(h?O.then(T):A&&!R?Promise.resolve(E):Promise.all([R,O]).then(T),{arg:c,requestId:P,subscriptionOptions:y,queryCacheKey:w,abort:j,async unwrap(){let e=await N;if(e.isError)throw e.error;return e.data},refetch:()=>g(u(c,{subscribe:!1,forceRefetch:!0})),unsubscribe(){d&&g(s({queryCacheKey:w,requestId:P}))},updateSubscriptionOptions(e){N.subscriptionOptions=e,g(l({endpointName:n,requestId:P,queryCacheKey:w,options:e}))}});if(!R&&!A&&!h){var q;let e=(q={},o.has(g)?o.get(g):o.set(g,q).get(g));e[w]=N,N.then(()=>{delete e[w],f(e)||o.delete(g)})}return N};return u}}({queryThunk:ei,mutationThunk:ep,infiniteQueryThunk:ed,api:t,serializeQueryArgs:s,context:O});return et(t.util,{getRunningMutationThunk:ej,getRunningMutationsThunk:eA,getRunningQueryThunk:eT,getRunningQueriesThunk:eR}),{name:ef,injectEndpoint(e,r){let n=t.endpoints[e]??={};C(r)&&et(n,{name:e,select:Y(e,r),initiate:eO(e,r)},eb(ei,e)),"mutation"===r.type&&et(n,{name:e,select:ee(),initiate:eP(e)},eb(ep,e)),N(r)&&et(n,{name:e,select:G(e,r),initiate:eE(e,r)},eb(ei,e))}}}});ed()},7895:(e,t,r)=>{r.d(t,{cN:()=>h,U1:()=>w,VP:()=>c,zD:()=>x,Z0:()=>z,gk:()=>ed,f$:()=>E,i0:()=>O,$S:()=>function e(...t){return 0===t.length?e=>P(e,["pending","fulfilled","rejected"]):j(t)?O(...t.flatMap(e=>[e.pending,e.rejected,e.fulfilled])):e()(t[0])},sf:()=>function e(...t){return 0===t.length?e=>P(e,["fulfilled"]):j(t)?O(...t.map(e=>e.fulfilled)):e()(t[0])},mm:()=>function e(...t){return 0===t.length?e=>P(e,["pending"]):j(t)?O(...t.map(e=>e.pending)):e()(t[0])},TK:()=>A,WA:()=>function e(...t){let r=e=>e&&e.meta&&e.meta.rejectedWithValue;return 0===t.length||j(t)?E(A(...t),r):e()(t[0])},Ak:()=>R,aA:()=>m});var n=r(5340);function i(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var a=i(),o=r(4605),u=(r(5733),"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)});"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var s=e=>e&&"function"==typeof e.match;function c(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(ed(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(0,n.ve)(t)&&t.type===e,r}function l(e){return["type","payload","error","meta"].indexOf(e)>-1}var f=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function d(e){return(0,o.a6)(e)?(0,o.jM)(e,()=>{}):e}function p(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var y=()=>function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{},u=new f;return t&&("boolean"==typeof t?u.push(a):u.push(i(t.extraArgument))),u},h="RTK_autoBatch",m=()=>e=>({payload:e,meta:{[h]:!0}}),g=e=>t=>{setTimeout(t,e)},b=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,u=new Set,s="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:g(10):"callback"===e.type?e.queueNotification:g(e.timeout),c=()=>{o=!1,a&&(a=!1,u.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return u.add(e),()=>{t(),u.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.[h]))&&!o&&(o=!0,s(c)),n.dispatch(e)}finally{i=!0}}})},v=e=>function(t){let{autoBatch:r=!0}=t??{},n=new f(e);return r&&n.push(b("object"==typeof r?r:void 0)),n};function w(e){let t,r,i=y(),{reducer:a,middleware:o,devTools:s=!0,duplicateMiddlewareCheck:c=!0,preloadedState:l,enhancers:f}=e||{};if("function"==typeof a)t=a;else if((0,n.Qd)(a))t=(0,n.HY)(a);else throw Error(ed(1));r="function"==typeof o?o(i):i();let d=n.Zz;s&&(d=u({trace:!1,..."object"==typeof s&&s}));let p=v((0,n.Tw)(...r)),h=d(..."function"==typeof f?f(p):p());return(0,n.y$)(t,l,h)}function _(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(ed(28));if(n in r)throw Error(ed(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var S=(e,t)=>s(e)?e.match(t):e(t);function O(...e){return t=>e.some(e=>S(e,t))}function E(...e){return t=>e.every(e=>S(e,t))}function P(e,t){if(!e||!e.meta)return!1;let r="string"==typeof e.meta.requestId,n=t.indexOf(e.meta.requestStatus)>-1;return r&&n}function j(e){return"function"==typeof e[0]&&"pending"in e[0]&&"fulfilled"in e[0]&&"rejected"in e[0]}function A(...e){return 0===e.length?e=>P(e,["rejected"]):j(e)?O(...e.map(e=>e.rejected)):A()(e[0])}var R=(e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},T=["name","message","stack","code"],C=class{constructor(e,t){this.payload=e,this.meta=t}_type},N=class{constructor(e,t){this.payload=e,this.meta=t}_type},q=e=>{if("object"==typeof e&&null!==e){let t={};for(let r of T)"string"==typeof e[r]&&(t[r]=e[r]);return t}return{message:String(e)}},k="External signal was aborted",x=(()=>{function e(e,t,r){let n=c(e+"/fulfilled",(e,t,r,n)=>({payload:e,meta:{...n||{},arg:r,requestId:t,requestStatus:"fulfilled"}})),i=c(e+"/pending",(e,t,r)=>({payload:void 0,meta:{...r||{},arg:t,requestId:e,requestStatus:"pending"}})),a=c(e+"/rejected",(e,t,n,i,a)=>({payload:i,error:(r&&r.serializeError||q)(e||"Rejected"),meta:{...a||{},arg:n,requestId:t,rejectedWithValue:!!i,requestStatus:"rejected",aborted:e?.name==="AbortError",condition:e?.name==="ConditionError"}}));return Object.assign(function(e,{signal:o}={}){return(u,s,c)=>{let l,f,d=r?.idGenerator?r.idGenerator(e):R(),p=new AbortController;function y(e){f=e,p.abort()}o&&(o.aborted?y(k):o.addEventListener("abort",()=>y(k),{once:!0}));let h=async function(){let o;try{var h;let a=r?.condition?.(e,{getState:s,extra:c});if(h=a,null!==h&&"object"==typeof h&&"function"==typeof h.then&&(a=await a),!1===a||p.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};let m=new Promise((e,t)=>{l=()=>{t({name:"AbortError",message:f||"Aborted"})},p.signal.addEventListener("abort",l)});u(i(d,e,r?.getPendingMeta?.({requestId:d,arg:e},{getState:s,extra:c}))),o=await Promise.race([m,Promise.resolve(t(e,{dispatch:u,getState:s,extra:c,requestId:d,signal:p.signal,abort:y,rejectWithValue:(e,t)=>new C(e,t),fulfillWithValue:(e,t)=>new N(e,t)})).then(t=>{if(t instanceof C)throw t;return t instanceof N?n(t.payload,d,e,t.meta):n(t,d,e)})])}catch(t){o=t instanceof C?a(null,d,e,t.payload,t.meta):a(t,d,e)}finally{l&&p.signal.removeEventListener("abort",l)}return r&&!r.dispatchConditionRejection&&a.match(o)&&o.meta.condition||u(o),o}();return Object.assign(h,{abort:y,requestId:d,arg:e,unwrap:()=>h.then(M)})}},{pending:i,rejected:a,fulfilled:n,settled:O(a,n),typePrefix:e})}return e.withTypes=()=>e,e})();function M(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var D=Symbol.for("rtk-slice-createasyncthunk"),I=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(I||{}),z=function({creators:e}={}){let t=e?.asyncThunk?.[D];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(ed(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},u=Object.keys(a),s={},l={},f={},y=[],h={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(ed(12));if(r in l)throw Error(ed(13));return l[r]=t,h},addMatcher:(e,t)=>(y.push({matcher:e,reducer:t}),h),exposeAction:(e,t)=>(f[e]=t,h),exposeCaseReducer:(e,t)=>(s[e]=t,h)};function m(){let[t={},r=[],n]="function"==typeof e.extraReducers?_(e.extraReducers):[e.extraReducers],i={...t,...l};return function(e,t){let r,[n,i,a]=_(t);if("function"==typeof e)r=()=>d(e());else{let t=d(e);r=()=>t}function u(e=r(),t){let s=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===s.filter(e=>!!e).length&&(s=[a]),s.reduce((e,r)=>{if(r)if((0,o.Qx)(e)){let n=r(e,t);return void 0===n?e:n}else{if((0,o.a6)(e))return(0,o.jM)(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return u.getInitialState=r,u}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of y)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}u.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(ed(18));let{payloadCreator:a,fulfilled:o,pending:u,rejected:s,settled:c,options:l}=r,f=i(e,a,l);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),u&&n.addCase(f.pending,u),s&&n.addCase(f.rejected,s),c&&n.addMatcher(f.settled,c),n.exposeCaseReducer(t,{fulfilled:o||$,pending:u||$,rejected:s||$,settled:c||$})}(o,i,h,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(ed(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?c(e,o):c(e))}(o,i,h)});let g=e=>e,b=new Map,v=new WeakMap;function w(e,t){return r||(r=m()),r(e,t)}function S(){return r||(r=m()),r.getInitialState()}function O(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=p(v,n,S)),i}function i(t=g){let n=p(b,r,()=>new WeakMap);return p(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let u=t(a);return void 0===u&&n&&(u=r()),e(u,...o)}return i.unwrapped=e,i}(a,t,()=>p(v,t,S),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let E={name:n,reducer:w,actions:f,caseReducers:s,getInitialState:S,...O(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:w},r),{...E,...O(n,!0)}}};return E}}();function $(){}function Q(e){return function(t,r){let n=t=>{isAction(r)&&Object.keys(r).every(l)?e(r.payload,t):e(r,t)};return(null)(t)?(n(t),t):createNextState3(t,n)}}function K(e,t){return t(e)}function F(e){return Array.isArray(e)||(e=Object.values(e)),e}var L=class{constructor(e){this.code=e,this.message=`task cancelled (reason: ${e})`}name="TaskAbortError";message},W=(e,t)=>{if("function"!=typeof e)throw TypeError(ed(32))},U=()=>{},V=(e,t=U)=>(e.catch(t),e),B=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),X=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},Z=e=>{if(e.aborted){let{reason:t}=e;throw new L(t)}};function H(e,t){let r=U;return new Promise((n,i)=>{let a=()=>i(new L(e.reason));if(e.aborted)return void a();r=B(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=U})}var J=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof L?"cancelled":"rejected",error:e}}finally{t?.()}},Y=e=>t=>V(H(e,t).then(t=>(Z(e),t))),G=e=>{let t=Y(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:ee}=Object,et="listenerMiddleware",er=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=c(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(ed(21));return W(a,"options.listener"),{predicate:i,type:t,effect:a}},en=ee(e=>{let{type:t,predicate:r,effect:n}=er(e);return{id:R(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(ed(22))}}},{withTypes:()=>en}),ei=e=>{e.pending.forEach(e=>{X(e,null)})},ea=ee(c(`${et}/add`),{withTypes:()=>ea}),eo=ee(c(`${et}/remove`),{withTypes:()=>eo}),eu=e=>"reducerPath"in e&&"string"==typeof e.reducerPath,es=Symbol.for("rtk-state-proxy-original"),ec=e=>!!e&&!!e[es],el=new WeakMap,ef={};function ed(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}},9559:(e,t,r)=>{r.d(t,{Kq:()=>R,Pj:()=>q,bN:()=>p,d4:()=>M,vA:()=>D,wA:()=>k});var n=r(9585),i=r(4631),a=Symbol.for("react.forward_ref"),o=Symbol.for("react.memo");function u(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}var s={notify(){},get:()=>[]},c="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,l="undefined"!=typeof navigator&&"ReactNative"===navigator.product,f=c||l?n.useLayoutEffect:n.useEffect;function d(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function p(e,t){if(d(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;let r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(let n=0;n<r.length;n++)if(!Object.prototype.hasOwnProperty.call(t,r[n])||!d(e[r[n]],t[r[n]]))return!1;return!0}var y={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},h={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},m={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},g={[a]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[o]:m};function b(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case null:switch(e=e.type){case null:case null:case null:case null:case null:return e;default:switch(e=e&&e.$$typeof){case null:case a:case null:case o:case null:return e;default:return t}}case null:return t}}}(e)===o?m:g[e.$$typeof]||y}var v=Object.defineProperty,w=Object.getOwnPropertyNames,_=Object.getOwnPropertySymbols,S=Object.getOwnPropertyDescriptor,O=Object.getPrototypeOf,E=Object.prototype,P=Symbol.for("react-redux-context"),j="undefined"!=typeof globalThis?globalThis:{},A=function(){if(!n.createContext)return{};let e=j[P]??=new Map,t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}(),R=function(e){let{children:t,context:r,serverState:i,store:a}=e,o=n.useMemo(()=>{let e=function(e,t){let r,n=s,i=0,a=!1;function o(){l.onStateChange&&l.onStateChange()}function u(){if(i++,!r){let t,i;r=e.subscribe(o),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function c(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=s)}let l={addNestedSub:function(e){u();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),c())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,u())},tryUnsubscribe:function(){a&&(a=!1,c())},getListeners:()=>n};return l}(a);return{store:a,subscription:e,getServerState:i?()=>i:void 0}},[a,i]),u=n.useMemo(()=>a.getState(),[a]);return f(()=>{let{subscription:e}=o;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),u!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[o,u]),n.createElement((r||A).Provider,{value:o},t)};function T(e=A){return function(){return n.useContext(e)}}var C=T();function N(e=A){let t=e===A?C:T(e),r=()=>{let{store:e}=t();return e};return Object.assign(r,{withTypes:()=>r}),r}var q=N(),k=function(e=A){let t=e===A?q:N(e),r=()=>t().dispatch;return Object.assign(r,{withTypes:()=>r}),r}(),x=(e,t)=>e===t,M=function(e=A){let t=e===A?C:T(e),r=(e,r={})=>{let{equalityFn:a=x}="function"==typeof r?{equalityFn:r}:r,{store:o,subscription:u,getServerState:s}=t();n.useRef(!0);let c=n.useCallback({[e.name]:t=>e(t)}[e.name],[e]),l=(0,i.useSyncExternalStoreWithSelector)(u.addNestedSub,o.getState,s||o.getState,c,a);return n.useDebugValue(l),l};return Object.assign(r,{withTypes:()=>r}),r}(),D=function(e){e()}}}]);