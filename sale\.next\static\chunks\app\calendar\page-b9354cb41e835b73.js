(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[85],{2023:(e,s,t)=>{Promise.resolve().then(t.bind(t,8243))},2731:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(6501).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3831:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var a=t(9605);function l(e){let{title:s,subtitle:t,icon:l,greeting:n,userName:c,actions:r,children:i}=e;return(0,a.jsx)("div",{className:"bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg p-6 text-white shadow-lg",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center gap-3",children:[l&&(0,a.jsx)(l,{className:"h-8 w-8"}),n&&c?"".concat(n,", ").concat(c,"! \uD83D\uDC4B"):s]}),(0,a.jsx)("p",{className:"text-sky-100",children:t||"Manage and track your SGM sales activities with real-time insights"})]}),(r||i)&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[r,i]})]})})}t(9585)},8243:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var a=t(9605),l=t(9585),n=t(6440),c=t(3831),r=t(8063),i=t(2933),d=t(2790),x=t(4835),m=t(6901),h=t(5857),o=t(7229),j=t(2910),g=t(1713),u=t(8164),p=t(8333),N=t(2731),y=t(8347),v=t(3500),f=t(4965),w=t(3815),b=t(7555);function k(){let[e,s]=(0,l.useState)(new Date),[t,k]=(0,l.useState)("month"),A=(0,f.G)(w.mB),C=(0,f.j)(),D=(null==A?void 0:A.role)==="sales"||(null==A?void 0:A.role)==="sales_manager"||(null==A?void 0:A.role)==="admin",{data:S,isLoading:M,error:_}=(0,v.Z4)(),E=new Date(e.getFullYear(),e.getMonth(),1).toISOString(),Z=new Date(e.getFullYear(),e.getMonth()+1,0).toISOString(),{data:$,isLoading:z}=(0,v.xc)({startDate:E,endDate:Z}),[B]=(0,v.dy)(),[T]=(0,v.eV)(),[U]=(0,v.Yp)();if(!D)return(0,a.jsx)(n.A,{title:"Access Denied",children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(x.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access the calendar."})]})})});let W=(null==S?void 0:S.data)||[],L=e=>{switch(e){case"meeting":return(0,a.jsx)(m.A,{className:"w-4 h-4"});case"call":return(0,a.jsx)(h.A,{className:"w-4 h-4"});case"site_visit":return(0,a.jsx)(o.A,{className:"w-4 h-4"});case"demo":return(0,a.jsx)(j.A,{className:"w-4 h-4"});default:return(0,a.jsx)(g.A,{className:"w-4 h-4"})}},O=e=>{switch(e){case"meeting":return"bg-blue-100 text-blue-800 border-blue-200";case"call":return"bg-green-100 text-green-800 border-green-200";case"site_visit":return"bg-purple-100 text-purple-800 border-purple-200";case"demo":return"bg-orange-100 text-orange-800 border-orange-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},Y=a=>{let l=new Date(e);"month"===t?l.setMonth(l.getMonth()+("next"===a?1:-1)):"week"===t?l.setDate(l.getDate()+("next"===a?7:-7)):l.setDate(l.getDate()+("next"===a?1:-1)),s(l)};return(0,a.jsxs)(n.A,{title:"Calendar",children:[(0,a.jsx)(c.A,{title:"Sales Calendar",subtitle:"Manage your meetings, calls, and appointments",icon:x.A}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(r.Zp,{children:(0,a.jsx)(r.aR,{children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>Y("prev"),children:(0,a.jsx)(u.A,{className:"w-4 h-4"})}),(0,a.jsx)("h2",{className:"text-xl font-semibold",children:e.toLocaleDateString("en-US",{month:"long",year:"numeric"})}),(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>Y("next"),children:(0,a.jsx)(p.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"flex rounded-lg border",children:[(0,a.jsx)(i.$,{variant:"month"===t?"default":"ghost",size:"sm",onClick:()=>k("month"),className:"rounded-r-none",children:"Month"}),(0,a.jsx)(i.$,{variant:"week"===t?"default":"ghost",size:"sm",onClick:()=>k("week"),className:"rounded-none border-x-0",children:"Week"}),(0,a.jsx)(i.$,{variant:"day"===t?"default":"ghost",size:"sm",onClick:()=>k("day"),className:"rounded-l-none",children:"Day"})]}),(0,a.jsxs)(i.$,{className:"flex items-center space-x-2",onClick:()=>C((0,b.qf)({id:"create-event",type:"create-event",data:{}})),children:[(0,a.jsx)(N.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"New Event"})]})]})]})})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsx)(r.Zp,{children:(0,a.jsx)(r.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(x.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Calendar View"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Full calendar component will be implemented here"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:["Currently showing ",t," view for ",e.toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})]})]})})})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Today's Events"})]})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[W.map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg ".concat(O(e.type)),children:L(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 truncate",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1 text-sm text-gray-600",children:[(0,a.jsx)(y.A,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[e.startTime," - ",e.endTime]})]}),e.location&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1 text-sm text-gray-600",children:[(0,a.jsx)(o.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"truncate",children:e.location})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,a.jsx)(d.E,{variant:"default",className:"text-xs",children:e.type.replace("_"," ")}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>C((0,b.qf)({id:"edit-event",type:"edit-event",data:{event:e}})),className:"border-gray-300 text-black hover:bg-green-100 text-xs px-2 py-1",children:"Edit"})})]}),e.createdByName&&(0,a.jsx)("div",{className:"flex items-center space-x-2 mt-1 text-xs text-gray-500",children:(0,a.jsxs)("span",{children:["Created by: ",e.createdByName]})}),e.assignedUsers&&e.assignedUsers.length>0&&(0,a.jsx)("div",{className:"flex items-center space-x-2 mt-1 text-xs text-gray-500",children:(0,a.jsxs)("span",{children:["Assigned to: ",e.assignedUsers.map(e=>e.name).join(", ")]})})]})]})},e.id)),0===W.length&&(0,a.jsxs)("div",{className:"text-center py-6",children:[(0,a.jsx)(g.A,{className:"w-8 h-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-gray-600",children:"No events today"})]})]})})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"This Week"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Total Events"}),(0,a.jsx)("span",{className:"font-medium",children:"12"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Meetings"}),(0,a.jsx)("span",{className:"font-medium",children:"5"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Site Visits"}),(0,a.jsx)("span",{className:"font-medium",children:"3"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Calls"}),(0,a.jsx)("span",{className:"font-medium",children:"4"})]})]})})]})]})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[399,995,713,668,663,75,440,390,110,358],()=>s(2023)),_N_E=e.O()}]);