import { baseApi } from './baseApi'

// Types
export interface AdminUser {
  _id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  role: string
  status: 'active' | 'inactive' | 'blocked' | 'suspended'
  createdAt: string
  updatedAt: string
  kyc: {
    status: 'not_submitted' | 'pending' | 'approved' | 'rejected' | 'under_review'
    verificationLevel: 'none' | 'basic' | 'intermediate' | 'advanced'
    documents?: any[]
  }
  wallet: {
    balance: number
  }
  walletBalance: number // For backward compatibility
}

export interface UserDetails {
  user: AdminUser & {
    emailVerified: boolean
    phoneVerified: boolean
    kycStatus: string
    referredBy?: string
    avatar?: {
      uploadedAt: string
    }
    referralCode: string
    lastLogin?: string
  }
  kyc: {
    _id: string
    userId: string
    country: string
    documents: Array<{
      _id?: string
      type: string
      category: string
      documentNumber?: string
      documentUrl: string
      uploadedAt: string
      status: string
      subType?: string
      rejectionReason?: string
    }>
    address: {
      street: string
      city: string
      state: string
      postalCode: string
      country: string
      addressType: string
      residenceSince: string
    }
    status: string
    riskLevel: string
    complianceFlags: any[]
    completedSteps: string[]
    createdAt: string
    updatedAt: string
    personalInfo: {
      nationality: string
      placeOfBirth: string
      gender: string
      maritalStatus: string
    }
    submittedAt?: string
    level?: string
    reviewedAt?: string
    reviewedBy?: {
      firstName: string
      lastName: string
    }
    rejectionReason?: string
    identityInfo?: {
      aadharNumber?: string
      panNumber?: string
      passportNumber?: string
      drivingLicenseNumber?: string
    }
    bankInfo?: {
      accountNumber?: string
      ifscCode?: string
      bankName?: string
      accountType?: string
      accountHolderName?: string
    }
  }
  wallet: {
    _id: string
    userId: string
    balance: number
    totalInvested: number
    totalReturns: number
    totalReferralEarnings: number
    totalDeposited: number
    totalWithdrawn: number
    pendingWithdrawals: number
    lifetimeEarnings: number
    currency: string
    isActive: boolean
    createdAt: string
    updatedAt: string
    lastTransactionAt?: string
  }
  recentTransactions: any[]
  transactionStats: {
    totalTransactions: number
    totalCredit: number
    totalDebit: number
  }
}

export interface UserKYC {
  user: {
    _id: string
    firstName: string
    lastName: string
    email: string
    phone: string
  }
  kyc: {
    status: string
    verificationLevel: string
    documents: any[]
    personalInfo: any
    addressInfo: any
    employmentInfo: any
    createdAt?: string
    updatedAt?: string
  }
}

export interface UserStats {
  totalUsers: number
  activeUsers: number
  inactiveUsers: number
  blockedUsers: number
}

export interface PaginationInfo {
  page: number
  limit: number
  total: number
  pages: number
}

export interface UserFilters {
  page?: number
  limit?: number
  search?: string
  status?: string
  kycStatus?: string
  role?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface UpdateUserStatusRequest {
  userId: string
  status: 'active' | 'inactive' | 'blocked' | 'suspended'
  reason?: string
}

export const adminUserApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get all users
    getAllUsers: builder.query<{
      users: AdminUser[]
      pagination: PaginationInfo
      statistics: UserStats
    }, UserFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, value.toString())
          }
        })
        return `/admin/users?${params.toString()}`
      },
      transformResponse: (response: any) => {
        // Debug logging
        console.log('Get All Users API Response:', response)
        
        // Handle the API response structure
        if (response.success && response.data) {
          return response.data
        }
        return { 
          users: [], 
          pagination: { page: 1, limit: 20, total: 0, pages: 0 }, 
          statistics: { totalUsers: 0, activeUsers: 0, inactiveUsers: 0, blockedUsers: 0 } 
        }
      },
      providesTags: ['User'],
    }),

    // Get user details
    getUserDetails: builder.query<UserDetails, string>({
      query: (userId) => `/admin/users/${userId}`,
      transformResponse: (response: any) => {
        // Debug logging
        console.log('Get User Details API Response:', response)
        
        // Handle the API response structure
        if (response.success && response.data) {
          return response.data
        }
        return null
      },
      providesTags: (result, error, userId) => [{ type: 'User', id: userId }],
    }),

    // Get user KYC details
    getUserKYC: builder.query<UserKYC, string>({
      query: (userId) => `/admin/users/${userId}/kyc`,
      transformResponse: (response: any) => {
        // Debug logging
        console.log('Get User KYC API Response:', response)
        
        // Handle the API response structure
        if (response.success && response.data) {
          return response.data
        }
        return null
      },
      providesTags: (result, error, userId) => [{ type: 'User', id: userId }],
    }),

    // Update user status
    updateUserStatus: builder.mutation<any, UpdateUserStatusRequest>({
      query: ({ userId, ...data }) => ({
        url: `/admin/users/${userId}/status`,
        method: 'PUT',
        body: data,
      }),
      transformResponse: (response: any) => {
        // Debug logging
        console.log('Update User Status API Response:', response)
        
        // Handle the API response structure
        if (response.success && response.data) {
          return response.data
        }
        return response
      },
      invalidatesTags: (result, error, { userId }) => [
        'User',
        { type: 'User', id: userId }
      ],
    }),

    // Search users (for payment management)
    searchUsersForPayments: builder.query<{
      users: AdminUser[]
      total: number
    }, { query: string; limit?: number }>({
      query: ({ query, limit = 10 }) => 
        `/admin/payments/users/search?query=${encodeURIComponent(query)}&limit=${limit}`,
      transformResponse: (response: any) => {
        // Debug logging
        console.log('Search Users for Payments API Response:', response)
        
        // Handle the API response structure
        if (response.success && response.data) {
          return response.data
        }
        return { users: [], total: 0 }
      },
      providesTags: ['User'],
    }),

    // Admin Password Reset for User
    adminResetUserPassword: builder.mutation<
      { success: boolean; message: string },
      { userId: string; sendEmail?: boolean }
    >({
      query: ({ userId, sendEmail = true }) => ({
        url: `/admin/users/${userId}/reset-password`,
        method: 'POST',
        body: { sendEmail },
      }),
      invalidatesTags: ['User'],
    }),
  }),
  overrideExisting: false,
})

export const {
  useGetAllUsersQuery,
  useGetUserDetailsQuery,
  useGetUserKYCQuery,
  useUpdateUserStatusMutation,
  useSearchUsersForPaymentsQuery,
  useAdminResetUserPasswordMutation,
} = adminUserApi
