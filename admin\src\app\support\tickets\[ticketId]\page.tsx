'use client'

import React, { useState, useEffect, useRef } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import {
  ArrowLeft,
  Send,
  Clock,
  User,
  MessageCircle,
  CheckCircle,
  AlertTriangle,
  Settings,
  UserCheck,
  Mail,
  Phone,
  Calendar,
  Users,
  UserPlus,
  MoreVertical,
  Paperclip,
  Eye,
  EyeOff
} from 'lucide-react'
import {
  useGetSupportTicketByIdQuery,
  useAddTicketMessageMutation,
  useUpdateSupportTicketMutation,
  useAssignTicketMutation,
  useGetSupportAgentsQuery
} from '@/store/api/supportApi'
import { useGetUsersQuery } from '@/store/api/usersApi'
import { useAppSelector } from '@/store'
import { selectUser } from '@/store/slices/authSlice'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { formatDate, formatTime } from '@/lib/utils'

export default function AdminTicketDetailPage() {
  const params = useParams()
  const router = useRouter()
  const ticketId = params?.ticketId as string
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const [newMessage, setNewMessage] = useState('')
  const [ticketStatus, setTicketStatus] = useState('')
  const [ticketPriority, setTicketPriority] = useState('')
  const [assignedTo, setAssignedTo] = useState('')
  const [showInternalNotes, setShowInternalNotes] = useState(false)
  const [isInternalMessage, setIsInternalMessage] = useState(false)
  const currentUser = useAppSelector(selectUser)

  const {
    data: ticketResponse,
    isLoading: isLoadingTicket,
    error: ticketError,
    refetch: refetchTicket
  } = useGetSupportTicketByIdQuery(ticketId)

  // Get support agents for assignment
  const {
    data: agentsResponse,
    isLoading: isLoadingAgents
  } = useGetSupportAgentsQuery()

  // Get all users (admin, subadmin, sales) for assignment
  const {
    data: usersResponse,
    isLoading: isLoadingUsers
  } = useGetUsersQuery({
    role: 'admin,subadmin,sales',
    status: 'active',
    limit: 100
  })

  const [addMessage, { isLoading: isSendingMessage }] = useAddTicketMessageMutation()
  const [updateStatus, { isLoading: isUpdatingStatus }] = useUpdateSupportTicketMutation()
  const [assignTicketMutation, { isLoading: isAssigning }] = useAssignTicketMutation()

  const ticket = ticketResponse?.data
  const messages = ticket?.messages || []
  const agents = agentsResponse?.data || []
  const users = usersResponse?.data || []

  // Combine agents and users for assignment dropdown
  const assignableMembers = [
    ...agents.map(agent => ({
      id: agent.id,
      name: agent.name,
      email: agent.email,
      role: 'Support Agent',
      isOnline: agent.isOnline,
      assignedTickets: agent.assignedTickets
    })),
    ...users.filter(user => ['admin', 'subadmin', 'sales'].includes(user.role)).map(user => ({
      id: user.id,
      name: `${user.firstName} ${user.lastName}`,
      email: user.email,
      role: user.role,
      isOnline: false, // We don't have online status for regular users
      assignedTickets: 0
    }))
  ]

  useEffect(() => {
    if (ticket) {
      setTicketStatus(ticket.status)
      setTicketPriority(ticket.priority)
      setAssignedTo(ticket.assignedTo || '')
    }
  }, [ticket])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return

    try {
      await addMessage({
        ticketId,
        content: newMessage,
        isInternal: isInternalMessage
      }).unwrap()

      setNewMessage('')
      setIsInternalMessage(false)
      refetchTicket()
      toast.success('Message sent successfully')
    } catch (error: any) {
      console.error('Error sending message:', error)
      toast.error(error?.data?.message || 'Failed to send message')
    }
  }

  const handleAssignTicket = async (memberId: string) => {
    if (!ticket) return

    try {
      await assignTicketMutation({
        ticketId: ticket.id,
        assignedTo: memberId
      }).unwrap()

      setAssignedTo(memberId)
      refetchTicket()

      const member = assignableMembers.find(m => m.id === memberId)
      toast.success(`Ticket assigned to ${member?.name || 'team member'}`)
    } catch (error: any) {
      console.error('Error assigning ticket:', error)
      toast.error(error?.data?.message || 'Failed to assign ticket')
    }
  }

  const handleStatusUpdate = async (newStatus: string) => {
    try {
      await updateStatus({
        id: ticketId,
        ticketData: { status: newStatus as 'open' | 'in_progress' | 'resolved' | 'closed' | 'pending_user' }
      }).unwrap()

      setTicketStatus(newStatus)
      refetchTicket()
      toast.success('Ticket status updated successfully')
    } catch (error: any) {
      console.error('Error updating status:', error)
      toast.error(error?.data?.message || 'Failed to update status')
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return <Badge className="bg-red-100 text-red-800"><AlertTriangle className="h-3 w-3 mr-1" />Open</Badge>
      case 'in_progress':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />In Progress</Badge>
      case 'resolved':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Resolved</Badge>
      case 'closed':
        return <Badge className="bg-gray-100 text-gray-800">Closed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge className="bg-red-100 text-red-800">High Priority</Badge>
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">Medium Priority</Badge>
      case 'low':
        return <Badge className="bg-green-100 text-green-800">Low Priority</Badge>
      default:
        return <Badge variant="outline">{priority}</Badge>
    }
  }

  if (isLoadingTicket) {
    return (
      <DashboardLayout>
        <div className="max-w-6xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <div className="h-96 bg-gray-200 rounded"></div>
                <div className="h-24 bg-gray-200 rounded"></div>
              </div>
              <div className="h-96 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (ticketError || !ticket) {
    return (
      <DashboardLayout>
        <div className="max-w-6xl mx-auto">
          <Button
            variant="ghost"
            onClick={() => router.push('/support')}
            className="mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Support
          </Button>
          <Card>
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Ticket Not Found</h2>
              <p className="text-gray-600">The support ticket you're looking for doesn't exist.</p>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => router.push('/support')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Support
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">#{ticket.ticketNumber}</h1>
              <p className="text-gray-600">{ticket.subject}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {getStatusBadge(ticket.status)}
            {getPriorityBadge(ticket.priority)}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Chat Area */}
          <div className="lg:col-span-2 space-y-6">
            {/* Chat Messages */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageCircle className="h-5 w-5 mr-2" />
                  Conversation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg bg-gray-50 p-4 max-h-96 overflow-y-auto space-y-4">
                  {/* Initial ticket message */}
                  <div className="flex justify-start mb-4">
                    <div className="max-w-xs lg:max-w-md">
                      <div className="bg-blue-100 text-blue-900 px-4 py-3 rounded-lg rounded-bl-sm border border-blue-200">
                        <div className="flex items-center space-x-2 mb-1">
                          <User className="h-3 w-3" />
                          <span className="text-xs font-medium">{ticket.userName}</span>
                          <span className="text-xs opacity-75">
                            {formatDate(ticket.createdAt)} {formatTime(ticket.createdAt)}
                          </span>
                        </div>
                        <p className="text-sm font-medium mb-1">{ticket.subject}</p>
                        <p className="text-sm">{ticket.description}</p>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        Ticket #{ticket.ticketNumber} • {ticket.category}
                      </div>
                    </div>
                  </div>

                  {/* Chat messages */}
                  {messages.filter(message => showInternalNotes || !message.isInternal).map((message: any, index: number) => {
                    // Determine if message is from current admin user
                    const messageSenderId = message.senderId?._id || message.senderId?.id || message.senderId
                    const currentUserId = currentUser?.id
                    const isFromCurrentUser = messageSenderId === currentUserId

                    // Determine sender type
                    const senderRole = message.senderId?.role || message.senderType
                    const isFromUser = senderRole === 'user'
                    const isFromAgent = ['admin', 'subadmin', 'support', 'sales'].includes(senderRole)

                    return (
                      <div key={message.id || message._id || index} className="mb-6">
                        {/* User messages - Left side */}
                        {isFromUser && (
                          <div className="flex items-start space-x-3">
                            <Avatar className="w-8 h-8 flex-shrink-0">
                              <AvatarImage src="" />
                              <AvatarFallback className="bg-blue-100 text-blue-600 text-sm">
                                {ticket.userName?.charAt(0)?.toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 max-w-md">
                              <div className="bg-gray-100 rounded-lg rounded-tl-sm px-4 py-3">
                                <div className="flex items-center space-x-2 mb-1">
                                  <span className="text-xs font-medium text-gray-700">{ticket.userName}</span>
                                  <span className="text-xs text-gray-500">{formatTime(message.createdAt)}</span>
                                </div>
                                <p className="text-sm text-gray-900 whitespace-pre-wrap">
                                  {message.content || message.message}
                                </p>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Agent messages - Right side */}
                        {isFromAgent && (
                          <div className="flex items-start space-x-3 justify-end">
                            <div className="flex-1 max-w-md">
                              <div className={`rounded-lg rounded-tr-sm px-4 py-3 ${
                                message.isInternal
                                  ? 'bg-yellow-50 border border-yellow-200'
                                  : isFromCurrentUser
                                    ? 'bg-blue-600 text-white'
                                    : 'bg-green-600 text-white'
                              }`}>
                                <div className="flex items-center space-x-2 mb-1">
                                  <span className={`text-xs font-medium ${
                                    message.isInternal ? 'text-yellow-700' : 'text-white opacity-90'
                                  }`}>
                                    {isFromCurrentUser ? 'You' : message.senderName || 'Support Team'}
                                  </span>
                                  {message.isInternal && (
                                    <Badge variant="outline" className="text-xs bg-yellow-100 text-yellow-700 border-yellow-300">
                                      Internal Note
                                    </Badge>
                                  )}
                                  <span className={`text-xs ${
                                    message.isInternal ? 'text-yellow-600' : 'text-white opacity-75'
                                  }`}>
                                    {formatTime(message.createdAt)}
                                  </span>
                                </div>
                                <p className={`text-sm whitespace-pre-wrap ${
                                  message.isInternal ? 'text-yellow-800' : 'text-white'
                                }`}>
                                  {message.content || message.message}
                                </p>
                              </div>
                            </div>
                            <Avatar className="w-8 h-8 flex-shrink-0">
                              <AvatarImage src={message.senderAvatar} />
                              <AvatarFallback className={`text-sm ${
                                isFromCurrentUser ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'
                              }`}>
                                {(message.senderName || 'ST')?.charAt(0)?.toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                          </div>
                        )}
                      </div>
                    )
                  })}
                  <div ref={messagesEndRef} />
                </div>
              </CardContent>
            </Card>

            {/* Enhanced Reply Box */}
            {ticket.status !== 'closed' && (
              <Card>
                <CardContent className="p-4 space-y-4">
                  {/* Message Type Toggle */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Button
                        variant={showInternalNotes ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowInternalNotes(!showInternalNotes)}
                        className="text-xs"
                      >
                        {showInternalNotes ? <EyeOff className="h-3 w-3 mr-1" /> : <Eye className="h-3 w-3 mr-1" />}
                        {showInternalNotes ? 'Hide Internal' : 'Show Internal'}
                      </Button>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="internal-message"
                          checked={isInternalMessage}
                          onChange={(e) => setIsInternalMessage(e.target.checked)}
                          className="rounded border-gray-300"
                        />
                        <label htmlFor="internal-message" className="text-sm text-gray-600">
                          Internal note (not visible to customer)
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Message Input */}
                  <div className="space-y-3">
                    <Textarea
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder={isInternalMessage ? "Add an internal note..." : "Type your response to the customer..."}
                      rows={4}
                      className={`resize-none ${isInternalMessage ? 'border-yellow-300 bg-yellow-50' : ''}`}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault()
                          handleSendMessage()
                        }
                      }}
                    />

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm" disabled>
                          <Paperclip className="h-4 w-4 mr-1" />
                          Attach File
                        </Button>
                        <span className="text-xs text-gray-500">
                          Press Enter to send, Shift+Enter for new line
                        </span>
                      </div>

                      <Button
                        onClick={handleSendMessage}
                        disabled={isSendingMessage || !newMessage.trim()}
                        className={isInternalMessage ? 'bg-yellow-600 hover:bg-yellow-700' : ''}
                      >
                        {isSendingMessage ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        ) : (
                          <Send className="h-4 w-4 mr-2" />
                        )}
                        {isInternalMessage ? 'Add Note' : 'Send Reply'}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* User Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <UserCheck className="h-5 w-5 mr-2" />
                  Customer Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold">
                      {ticket.userName?.charAt(0)}{ticket.userName?.split(' ')[1]?.charAt(0) || ''}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium">{ticket.userName}</p>
                    <p className="text-sm text-gray-600">User</p>
                  </div>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-2 text-gray-400" />
                    {ticket.userEmail}
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-2 text-gray-400" />
                    Contact via email
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                    Ticket created {formatDate(ticket.createdAt)}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Ticket Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  Ticket Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Status</label>
                  <Select value={ticketStatus} onValueChange={handleStatusUpdate}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="open">Open</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Assignment Section */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Assign To</label>
                  <Select
                    value={assignedTo}
                    onValueChange={handleAssignTicket}
                    disabled={isAssigning || isLoadingAgents || isLoadingUsers}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select team member..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Unassigned</SelectItem>
                      {assignableMembers.map((member) => (
                        <SelectItem key={member.id} value={member.id}>
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center space-x-2">
                              <div className={`w-2 h-2 rounded-full ${member.isOnline ? 'bg-green-500' : 'bg-gray-300'}`} />
                              <span>{member.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {member.role}
                              </Badge>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {ticket.assignedToName && (
                    <p className="text-xs text-gray-500 mt-1">
                      Currently assigned to: {ticket.assignedToName}
                    </p>
                  )}
                </div>

                <div className="pt-4 border-t">
                  <div className="text-sm text-gray-600 space-y-1">
                    <p><strong>Created:</strong> {formatDate(ticket.createdAt)} {formatTime(ticket.createdAt)}</p>
                    <p><strong>Last Updated:</strong> {formatDate(ticket.updatedAt)} {formatTime(ticket.updatedAt)}</p>
                    <p><strong>Category:</strong> {ticket.category}</p>
                    <p><strong>Priority:</strong> {ticket.priority}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
