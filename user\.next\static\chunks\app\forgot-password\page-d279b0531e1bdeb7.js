(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2162],{360:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},684:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var r=t(9605),a=t(9585),i=t(6762),l=t.n(i),d=t(5935),n=t(2407),o=t(7730),c=t(5706),m=t(8049),u=t(360),h=t(6793),x=t(3089),y=t(9644),p=t(1158),g=t(2933),f=t(8063),v=t(1128),b=t(6845);let j=c.Ik({email:c.Yj().email("Please enter a valid email address")}),N=c.Ik({otp:c.Yj().min(6,"OTP must be 6 digits").max(6,"OTP must be 6 digits")});function w(){let e=(0,d.useRouter)(),[s,t]=(0,a.useState)("email"),[i,c]=(0,a.useState)(""),[w,k]=(0,a.useState)(0),[T,{isLoading:P}]=(0,v.nd)(),[S,{isLoading:A}]=(0,v.tl)(),O=(0,n.mN)({resolver:(0,o.u)(j)}),q=(0,n.mN)({resolver:(0,o.u)(N)});(0,a.useEffect)(()=>{let e;return w>0&&(e=setInterval(()=>{k(e=>e-1)},1e3)),()=>clearInterval(e)},[w]);let E=async e=>{try{(await T(e).unwrap()).success&&(c(e.email),t("otp"),k(60),b.toast.success("Password reset code sent to your email!"))}catch(e){var s,r;(null==(s=e.data)?void 0:s.error)==="PASSWORD_RESET_DISABLED"?b.toast.error("Password reset is currently disabled. Please contact support for assistance."):b.toast.error((null==(r=e.data)?void 0:r.message)||"Failed to send reset code")}},C=async s=>{var t,r;try{let r=await S({email:i,otp:s.otp}).unwrap();r.success&&(null==(t=r.data)?void 0:t.resetToken)&&(sessionStorage.setItem("resetToken",r.data.resetToken),e.push("/reset-password"),b.toast.success("Code verified! Redirecting to reset password..."))}catch(e){b.toast.error((null==(r=e.data)?void 0:r.message)||"Invalid or expired code")}},M=async()=>{if(!(w>0))try{(await T({email:i}).unwrap()).success&&(k(60),b.toast.success("New code sent to your email!"))}catch(s){var e;b.toast.error((null==(e=s.data)?void 0:e.message)||"Failed to resend code")}};return"otp"===s?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,r.jsx)(m.A,{className:"h-12 w-12 text-blue-600"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"PropertyInvest"})]}),(0,r.jsxs)(f.Zp,{className:"shadow-xl border-0",children:[(0,r.jsxs)(f.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(u.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsx)(f.ZB,{className:"text-2xl font-bold",children:"Enter Verification Code"}),(0,r.jsxs)(f.BT,{children:["We've sent a 6-digit code to ",i]})]}),(0,r.jsxs)(f.Wu,{className:"space-y-6",children:[(0,r.jsxs)("form",{onSubmit:q.handleSubmit(C),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"otp",className:"text-sm font-medium text-gray-700",children:"Verification Code"}),(0,r.jsx)("input",{...q.register("otp"),type:"text",id:"otp",placeholder:"Enter 6-digit code",maxLength:6,className:"w-full px-4 py-3 text-center text-2xl font-mono border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent tracking-widest"}),q.formState.errors.otp&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:q.formState.errors.otp.message})]}),(0,r.jsx)(g.$,{type:"submit",disabled:A,className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors",children:A?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Verifying..."]}):"Verify Code"})]}),(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:"bg-amber-50 p-4 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 text-amber-600 mr-2"}),(0,r.jsx)("p",{className:"text-sm text-amber-700",children:"Code expires in 15 minutes"})]})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(g.$,{onClick:M,disabled:w>0||P,className:"w-full",variant:"outline",children:P?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending..."]}):w>0?"Resend in ".concat(w,"s"):"Resend Code"}),(0,r.jsxs)(g.$,{onClick:()=>t("email"),variant:"outline",className:"w-full",children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Change Email"]})]})]})]})]})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,r.jsx)(m.A,{className:"h-12 w-12 text-blue-600"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"PropertyInvest"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Reset your password"})]}),(0,r.jsxs)(f.Zp,{className:"shadow-xl border-0",children:[(0,r.jsxs)(f.aR,{className:"space-y-1",children:[(0,r.jsx)(f.ZB,{className:"text-2xl font-bold text-center",children:"Forgot Password?"}),(0,r.jsx)(f.BT,{className:"text-center",children:"Enter your email address and we'll send you a verification code to reset your password"})]}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsxs)("form",{onSubmit:O.handleSubmit(E),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{...O.register("email"),type:"email",id:"email",placeholder:"Enter your email address",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),O.formState.errors.email&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:O.formState.errors.email.message})]}),(0,r.jsx)(g.$,{type:"submit",disabled:P,className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors",children:P?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending code..."]}):"Send Verification Code"})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)(l(),{href:"/login",className:"text-blue-600 hover:text-blue-500 font-medium flex items-center justify-center",children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Back to Login"]})})]})]}),(0,r.jsx)("div",{className:"mt-8 text-center text-sm text-gray-600",children:(0,r.jsxs)("p",{children:["Remember your password?"," ",(0,r.jsx)(l(),{href:"/login",className:"text-blue-600 hover:text-blue-500",children:"Sign in here"})]})})]})})}},1128:(e,s,t)=>{"use strict";t.d(s,{B3:()=>n,Lv:()=>m,Ng:()=>i,_L:()=>r,ac:()=>y,ge:()=>a,nd:()=>o,pv:()=>p,tl:()=>c,uU:()=>d});let{useLoginMutation:r,useRegisterMutation:a,useLogoutMutation:i,useRefreshTokenMutation:l,useVerifyEmailMutation:d,useResendVerificationEmailMutation:n,useForgotPasswordMutation:o,useVerifyPasswordResetOTPMutation:c,useResetPasswordMutation:m,useChangePasswordMutation:u,useVerifyPhoneMutation:h,useSendPhoneOTPMutation:x,useSendEmailOTPMutation:y,useVerifyEmailOTPMutation:p,useGetCurrentUserQuery:g,useGetUserProfileQuery:f,useCheckAuthStatusQuery:v}=t(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({login:e.mutation({query:e=>({url:"/auth/login",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),register:e.mutation({query:e=>({url:"/auth/register",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),logout:e.mutation({query:()=>({url:"/auth/logout",method:"POST"}),invalidatesTags:["Auth","User"]}),refreshToken:e.mutation({query:e=>({url:"/auth/refresh",method:"POST",body:e})}),verifyEmail:e.mutation({query:e=>({url:"/auth/verify-email",method:"POST",body:e}),invalidatesTags:["User"]}),resendVerificationEmail:e.mutation({query:e=>({url:"/auth/send-verification",method:"POST",body:e})}),forgotPassword:e.mutation({query:e=>({url:"/auth/forgot-password",method:"POST",body:e})}),verifyPasswordResetOTP:e.mutation({query:e=>({url:"/auth/verify-reset-otp",method:"POST",body:e})}),resetPassword:e.mutation({query:e=>({url:"/auth/reset-password",method:"POST",body:e})}),changePassword:e.mutation({query:e=>({url:"/auth/change-password",method:"POST",body:e}),invalidatesTags:["User"]}),verifyPhone:e.mutation({query:e=>({url:"/auth/verify-phone",method:"POST",body:e}),invalidatesTags:["User"]}),sendPhoneOTP:e.mutation({query:e=>({url:"/auth/send-phone-otp",method:"POST",body:e})}),sendEmailOTP:e.mutation({query:e=>({url:"/auth/send-otp",method:"POST",body:e})}),verifyEmailOTP:e.mutation({query:e=>({url:"/auth/verify-otp",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),getCurrentUser:e.query({query:()=>"/auth/me",providesTags:["User"]}),checkAuthStatus:e.query({query:()=>"/auth/status",providesTags:["Auth"]}),getUserProfile:e.query({query:()=>"/auth/profile",providesTags:["User","Auth"]})})})},1158:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},2282:(e,s,t)=>{Promise.resolve().then(t.bind(t,684))},3089:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},8049:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},9644:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(5050).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[2094,5315,7436,3403,1147,390,110,7358],()=>s(2282)),_N_E=e.O()}]);