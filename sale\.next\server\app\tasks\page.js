(()=>{var e={};e.id=147,e.ids=[147],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23679:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var a=t(40969),r=t(73356),l=t(88251),i=t(89073),c=t(66949),n=t(46411),d=t(76650),x=t(57387),o=t(2223),m=t(77994),h=t(79201),p=t(21857),u=t(77060),j=t(78182),v=t(8713),g=t(31435),y=t(71727),f=t(77272),N=t(86400),b=t(21764),w=t(48567),k=t(61631),A=t(95003);function C(){let[e,s]=(0,r.useState)(""),[t,C]=(0,r.useState)("all"),[_,P]=(0,r.useState)("all"),[T,q]=(0,r.useState)(1),B=(0,w.G)(k.mB),S=(0,w.j)(),$=B?.role==="sales"||B?.role==="sales_manager"||B?.role==="admin",[M]=(0,N.S3)(),[R]=(0,N.Zz)(),[z]=(0,N.R2)(),[D]=(0,N.rh)();if(!$)return(0,a.jsx)(l.A,{title:"Access Denied",children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access task management."})]})})});let{data:G,isLoading:E,error:Z}=(0,N.AG)({page:1,limit:20,status:"all"!==t?t:void 0,priority:"all"!==_?_:void 0,search:e||void 0}),{data:U,isLoading:W}=(0,N.mc)({days:7}),L=async e=>{try{await D({id:e,notes:"Task completed from dashboard"}).unwrap()}catch(e){console.error("Failed to complete task:",e)}},F=async e=>{if(confirm("Are you sure you want to delete this task?"))try{await z(e).unwrap()}catch(e){console.error("Failed to delete task:",e)}},O=e=>{switch(e){case"completed":return(0,a.jsx)(h.A,{className:"w-4 h-4 text-green-600"});case"in_progress":return(0,a.jsx)(p.A,{className:"w-4 h-4 text-blue-600"});case"overdue":return(0,a.jsx)(u.A,{className:"w-4 h-4 text-red-600"});case"cancelled":return(0,a.jsx)(j.A,{className:"w-4 h-4 text-gray-600"});default:return(0,a.jsx)(p.A,{className:"w-4 h-4 text-yellow-600"})}},Y=e=>{switch(e){case"completed":return"success";case"in_progress":return"default";case"overdue":return"destructive";case"cancelled":return"secondary";default:return"warning"}},I=e=>{switch(e){case"urgent":return"destructive";case"high":return"warning";case"medium":return"default";default:return"secondary"}};return(0,a.jsxs)(l.A,{title:"Sales Tasks",children:[(0,a.jsx)(i.A,{title:"Sales Tasks",subtitle:"Manage your daily tasks and activities",icon:m.A}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Pending"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:G?.data?.tasks?.filter(e=>"pending"===e.status).length||0})]})]})})}),(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.A,{className:"w-5 h-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Completed"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:G?.data?.tasks?.filter(e=>"completed"===e.status).length||0})]})]})})}),(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 text-red-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Overdue"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:G?.data?.tasks?.filter(e=>"overdue"===e.status).length||0})]})]})})}),(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 text-purple-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"This Week"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:U?.data?.length||0})]})]})})})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,a.jsx)(c.ZB,{children:"All Tasks"}),(0,a.jsxs)(n.$,{className:"flex items-center space-x-2",onClick:()=>S((0,A.qf)({id:"create-task",type:"create-task",data:{}})),children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"New Task"})]})]})}),(0,a.jsxs)(c.Wu,{children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 mb-6",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)(x.p,{placeholder:"Search tasks...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(o.l6,{value:t,onValueChange:C,children:[(0,a.jsx)(o.bq,{className:"w-full sm:w-40",children:(0,a.jsx)(o.yv,{placeholder:"Status"})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"all",children:"All Status"}),(0,a.jsx)(o.eb,{value:"pending",children:"Pending"}),(0,a.jsx)(o.eb,{value:"in_progress",children:"In Progress"}),(0,a.jsx)(o.eb,{value:"completed",children:"Completed"}),(0,a.jsx)(o.eb,{value:"overdue",children:"Overdue"})]})]}),(0,a.jsxs)(o.l6,{value:_,onValueChange:P,children:[(0,a.jsx)(o.bq,{className:"w-full sm:w-40",children:(0,a.jsx)(o.yv,{placeholder:"Priority"})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"all",children:"All Priority"}),(0,a.jsx)(o.eb,{value:"urgent",children:"Urgent"}),(0,a.jsx)(o.eb,{value:"high",children:"High"}),(0,a.jsx)(o.eb,{value:"medium",children:"Medium"}),(0,a.jsx)(o.eb,{value:"low",children:"Low"})]})]})]}),E?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-sky-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading tasks..."})]}):Z?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(u.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Failed to load tasks"})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[G?.data?.tasks?.map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[O(e.status),(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:e.title}),(0,a.jsx)(d.E,{variant:Y(e.status),children:e.status.replace("_"," ")}),(0,a.jsx)(d.E,{variant:I(e.priority),children:e.priority})]}),e.description&&(0,a.jsx)("p",{className:"text-gray-600 mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(v.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:(0,b.Yq)(e.dueDate)})]}),e.dueTime&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(p.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.dueTime})]}),e.assignedTo&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(f.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["Assigned: ",e.assignedToName||`${e.assignedTo.firstName} ${e.assignedTo.lastName}`]})]}),e.createdBy&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(f.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["Created by: ",e.createdByName||`${e.createdBy.firstName} ${e.createdBy.lastName}`]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>S((0,A.qf)({id:"edit-task",type:"edit-task",data:{task:e}})),className:"border-gray-300 text-black hover:bg-sky-100",children:"Edit"}),"completed"!==e.status&&(0,a.jsx)(n.$,{variant:"default",size:"sm",onClick:()=>L(e._id),children:"Complete"}),(0,a.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>F(e._id),children:"Delete"})]})]})},e._id)),(!G?.data?.tasks||0===G.data.tasks.length)&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(m.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"No tasks found"}),(0,a.jsxs)(n.$,{className:"mt-4",children:[(0,a.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Create Your First Task"]})]})]})]})]})]})]})}},28133:(e,s,t)=>{Promise.resolve().then(t.bind(t,43337))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31435:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98085).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33873:e=>{"use strict";e.exports=require("path")},43337:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\tasks\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\tasks\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64581:(e,s,t)=>{Promise.resolve().then(t.bind(t,23679))},67971:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>d});var a=t(10557),r=t(68490),l=t(13172),i=t.n(l),c=t(68835),n={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let d={children:["",{children:["tasks",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,43337)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\tasks\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\tasks\\page.tsx"],o={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/tasks/page",pathname:"/tasks",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},77060:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98085).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},78182:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98085).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},79201:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98085).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},79551:e=>{"use strict";e.exports=require("url")},89073:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(40969);function r({title:e,subtitle:s,icon:t,greeting:r,userName:l,actions:i,children:c}){return(0,a.jsx)("div",{className:"bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg p-6 text-white shadow-lg",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center gap-3",children:[t&&(0,a.jsx)(t,{className:"h-8 w-8"}),r&&l?`${r}, ${l}! 👋`:e]}),(0,a.jsx)("p",{className:"text-sky-100",children:s||"Manage and track your SGM sales activities with real-time insights"})]}),(i||c)&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[i,c]})]})})}t(73356)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[755,598,544,29,796,286,447,512],()=>t(67971));module.exports=a})();