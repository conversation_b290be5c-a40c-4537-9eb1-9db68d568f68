# Server Configuration
NODE_ENV=production
PORT=1729


# Database Configuration
# MONGODB_URI=mongodb://localhost:27017/builder_farm
MONGODB_URI=mongodb+srv://dev2brtmultisoftware:<EMAIL>/builder?retryWrites=true&w=majority&appName=Cluster0

# h9QTHZkGucZlO6St
# JWT Configuration
# JWT Configuration
JWT_SECRET=4a8c0c83c997a97f8f5e734e682bd35a78dbb3f7c38ac1f8452eebc3449d7f90
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=b41e93cb6e504d4cfde13b02b99914e2fc5fda6e9c43a3c1cabc9a4f20eb1a6c
JWT_REFRESH_EXPIRES_IN=30d

# Cookie Configuration
COOKIE_SECRET=7f9384d708eaad50c22c8d06b8b4dc4fcfd28a79acac69c510e3a4b4ea97b5a2
COOKIE_EXPIRES_IN=7


# Security Configuration
BCRYPT_SALT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# CORS Configuration
CORS_ORIGIN=*
CORS_CREDENTIALS=true

# File Upload Configuration
MAX_FILE_SIZE=104857600
UPLOAD_PATH=./uploads

# S3 File Upload Limits (in bytes)
S3_MAX_IMAGE_SIZE=10485760
S3_MAX_DOCUMENT_SIZE=5242880
S3_MAX_VIDEO_SIZE=104857600
S3_MAX_AVATAR_SIZE=2097152

# Email Configuration (for future use)
EMAIL_FROM=<EMAIL>
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# SMS Configuration (for future use)
SMS_API_KEY=your-sms-api-key
SMS_SENDER_ID=BFARM

# Payment Gateway Configuration (for future use)
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# API Configuration
API_VERSION=v1
API_PREFIX=/api

# Development Configuration
DEBUG=true
VERBOSE_LOGGING=true

# AWS S3 Configuration (for file storage)
# Real AWS credentials from s3keys.txt
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=6BLaIRsuxohJYbdfPVHHVZQcCN+rjoJ6ARduSmjX
AWS_REGION=us-east-1
AWS_S3_BUCKET=sgmbrt
AWS_S3_BUCKET_DEV=sgmbrt
AWS_S3_BUCKET_PROD=sgmbrt

# S3 Presigned URL Configuration
S3_PRESIGNED_URL_EXPIRES=3600
S3_UPLOAD_ACL=public-read
S3_DEFAULT_PUBLIC=true

# Additional Security Configuration
SESSION_SECRET=bf-super-secret-session-key-2024-change-in-production
ADMIN_EMAIL=<EMAIL>
SUBADMIN_EMAIL=<EMAIL>

# File Upload Security
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,application/pdf,video/mp4
MAX_UPLOAD_SIZE=104857600

# Rate Limiting Configuration
LOGIN_RATE_LIMIT=20
LOGIN_RATE_WINDOW=900000

# Environment Status
SETUP_COMPLETE=true
SUBADMIN_FIXED=true
S3_CONFIGURED=true



# Production Security Headers
HELMET_ENABLED=true
TRUST_PROXY=true

# SSL Configuration
SSL_ENABLED=false
FORCE_HTTPS=false

# Cookie Configuration for Production
COOKIE_DOMAIN=.stcdeal.com


# Monitoring Configuration
# ⚠️  CHANGE THESE: Add production monitoring services
SENTRY_DSN=CHANGE_THIS_TO_PRODUCTION_SENTRY_DSN
NEW_RELIC_LICENSE_KEY=CHANGE_THIS_TO_PRODUCTION_NEW_RELIC_KEY

# Database Connection Pool
DB_MAX_POOL_SIZE=10
DB_MIN_POOL_SIZE=2
DB_CONNECTION_TIMEOUT=30000

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30



