{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/adminKycApi.ts"], "sourcesContent": ["import { base<PERSON><PERSON>, <PERSON>piR<PERSON>po<PERSON>, BaseQueryParams, createQueryParams } from './baseApi'\n\n// KYC Document Interface\nexport interface KYCDocument {\n  _id: string\n  type: 'identity' | 'address' | 'income' | 'bank' | 'photo' | 'signature' | 'other'\n  subType: string\n  documentNumber?: string\n  issuedBy?: string\n  issuedDate?: string\n  expiryDate?: string\n  status: 'pending' | 'verified' | 'rejected'\n  rejectionReason?: string\n  fileUrl?: string\n  uploadedAt: string\n  verifiedAt?: string\n}\n\n// KYC Status Interface\nexport interface KYCStatus {\n  _id: string\n  userId: {\n    _id: string\n    firstName: string\n    lastName: string\n    email: string\n    phone?: string\n    profileImage?: string\n  }\n  status: 'not_started' | 'pending' | 'approved' | 'rejected' | 'under_review'\n  level: 'basic' | 'advanced' | 'premium'\n  submittedAt?: string\n  reviewedAt?: string\n  reviewedBy?: {\n    _id: string\n    firstName: string\n    lastName: string\n  }\n  rejectionReason?: string\n  documents: KYCDocument[]\n  personalInfo: {\n    nationality?: string\n    placeOfBirth?: string\n    gender?: string\n    maritalStatus?: string\n  }\n  address: {\n    street?: string\n    city?: string\n    state?: string\n    postalCode?: string\n    country?: string\n    addressType?: 'permanent' | 'current' | 'mailing'\n    residenceSince?: string\n  }\n  identityInfo: {\n    aadharNumber?: string\n    panNumber?: string\n    passportNumber?: string\n    drivingLicenseNumber?: string\n  }\n  bankInfo: {\n    accountNumber?: string\n    ifscCode?: string\n    bankName?: string\n    accountType?: string\n    accountHolderName?: string\n  }\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface KYCQueryParams extends BaseQueryParams {\n  status?: string\n  level?: string\n  userId?: string\n}\n\nexport interface KYCApprovalRequest {\n  status: 'approved' | 'rejected'\n  rejectionReason?: string\n  level?: 'basic' | 'advanced' | 'premium'\n  notes?: string\n}\n\nexport const adminKycApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // Get all KYC submissions for admin review\n    getAllKYCSubmissions: builder.query<ApiResponse<{\n      kycs: KYCStatus[]\n      total: number\n      page: number\n      limit: number\n      totalPages: number\n    }>, KYCQueryParams>({\n      query: (params = {}) => ({\n        url: '/admin/kyc',\n        params: createQueryParams(params)\n      }),\n      providesTags: ['KYC'],\n    }),\n\n    // Get KYC by ID for detailed review\n    getKYCById: builder.query<ApiResponse<KYCStatus>, string>({\n      query: (id) => `/admin/kyc/${id}`,\n      providesTags: (_, __, id) => [{ type: 'KYC', id }],\n    }),\n\n    // Get KYC by User ID\n    getKYCByUserId: builder.query<ApiResponse<KYCStatus>, string>({\n      query: (userId) => `/kyc/user/${userId}`,\n      providesTags: (_, __, userId) => [{ type: 'KYC', id: userId }],\n    }),\n\n    // Approve or reject KYC\n    updateKYCStatus: builder.mutation<ApiResponse<KYCStatus>, { id: string; data: KYCApprovalRequest }>({\n      query: ({ id, data }) => ({\n        url: `/kyc/${id}/status`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (_, __, { id }) => [{ type: 'KYC', id }, 'KYC', 'User'],\n    }),\n\n    // Get KYC statistics for admin dashboard\n    getKYCStats: builder.query<ApiResponse<{\n      totalSubmissions: number\n      pendingReview: number\n      approved: number\n      rejected: number\n      underReview: number\n      basicLevel: number\n      advancedLevel: number\n      premiumLevel: number\n      recentSubmissions: Array<{\n        _id: string\n        userId: {\n          firstName: string\n          lastName: string\n          email: string\n        }\n        status: string\n        submittedAt: string\n      }>\n    }>, void>({\n      query: () => '/admin/kyc/stats',\n      providesTags: ['KYC'],\n    }),\n\n    // Bulk approve/reject KYCs\n    bulkUpdateKYCStatus: builder.mutation<ApiResponse<{ updated: number }>, {\n      ids: string[]\n      data: KYCApprovalRequest\n    }>({\n      query: ({ ids, data }) => ({\n        url: '/admin/kyc/bulk-update',\n        method: 'PUT',\n        body: { ids, ...data },\n      }),\n      invalidatesTags: ['KYC', 'User'],\n    }),\n\n    // Get KYC document by ID\n    getKYCDocument: builder.query<ApiResponse<KYCDocument>, string>({\n      query: (documentId) => `/admin/kyc/document/${documentId}`,\n      providesTags: (_, __, id) => [{ type: 'KYC', id }],\n    }),\n\n    // Verify/reject specific document\n    updateDocumentStatus: builder.mutation<ApiResponse<KYCDocument>, {\n      documentId: string\n      status: 'verified' | 'rejected'\n      rejectionReason?: string\n    }>({\n      query: ({ documentId, ...data }) => ({\n        url: `/admin/kyc/document/${documentId}/status`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: ['KYC'],\n    }),\n\n    // Get KYC history/audit trail\n    getKYCHistory: builder.query<ApiResponse<Array<{\n      action: string\n      status: string\n      timestamp: string\n      details?: string\n      performedBy: {\n        _id: string\n        firstName: string\n        lastName: string\n      }\n    }>>, string>({\n      query: (kycId) => `/admin/kyc/${kycId}/history`,\n      providesTags: (_, __, id) => [{ type: 'KYC', id }],\n    }),\n\n    // Download KYC PDF\n    downloadKYCPDF: builder.mutation<Blob, string>({\n      query: (kycId) => ({\n        url: `/kyc/${kycId}/download-pdf`,\n        method: 'GET',\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Export KYC data\n    exportKYCData: builder.mutation<ApiResponse<{ downloadUrl: string }>, {\n      format: 'csv' | 'excel' | 'pdf'\n      filters?: KYCQueryParams\n    }>({\n      query: (data) => ({\n        url: '/admin/kyc/export',\n        method: 'POST',\n        body: data,\n      }),\n    }),\n  }),\n})\n\nexport const {\n  useGetAllKYCSubmissionsQuery,\n  useGetKYCByIdQuery,\n  useGetKYCByUserIdQuery,\n  useUpdateKYCStatusMutation,\n  useDownloadKYCPDFMutation,\n  useGetKYCStatsQuery,\n  useBulkUpdateKYCStatusMutation,\n  useGetKYCDocumentQuery,\n  useUpdateDocumentStatusMutation,\n  useGetKYCHistoryQuery,\n  useExportKYCDataMutation,\n} = adminKycApi\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAqFO,MAAM,cAAc,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACjD,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,2CAA2C;YAC3C,sBAAsB,QAAQ,KAAK,CAMf;gBAClB,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE;oBAC5B,CAAC;gBACD,cAAc;oBAAC;iBAAM;YACvB;YAEA,oCAAoC;YACpC,YAAY,QAAQ,KAAK,CAAiC;gBACxD,OAAO,CAAC,KAAO,CAAC,WAAW,EAAE,IAAI;gBACjC,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;qBAAE;YACpD;YAEA,qBAAqB;YACrB,gBAAgB,QAAQ,KAAK,CAAiC;gBAC5D,OAAO,CAAC,SAAW,CAAC,UAAU,EAAE,QAAQ;gBACxC,cAAc,CAAC,GAAG,IAAI,SAAW;wBAAC;4BAAE,MAAM;4BAAO,IAAI;wBAAO;qBAAE;YAChE;YAEA,wBAAwB;YACxB,iBAAiB,QAAQ,QAAQ,CAAmE;gBAClG,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC;wBACxB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;wBAAG;wBAAO;qBAAO;YAC1E;YAEA,yCAAyC;YACzC,aAAa,QAAQ,KAAK,CAmBhB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAM;YACvB;YAEA,2BAA2B;YAC3B,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;wBACzB,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAK,GAAG,IAAI;wBAAC;oBACvB,CAAC;gBACD,iBAAiB;oBAAC;oBAAO;iBAAO;YAClC;YAEA,yBAAyB;YACzB,gBAAgB,QAAQ,KAAK,CAAmC;gBAC9D,OAAO,CAAC,aAAe,CAAC,oBAAoB,EAAE,YAAY;gBAC1D,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;qBAAE;YACpD;YAEA,kCAAkC;YAClC,sBAAsB,QAAQ,QAAQ,CAInC;gBACD,OAAO,CAAC,EAAE,UAAU,EAAE,GAAG,MAAM,GAAK,CAAC;wBACnC,KAAK,CAAC,oBAAoB,EAAE,WAAW,OAAO,CAAC;wBAC/C,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAM;YAC1B;YAEA,8BAA8B;YAC9B,eAAe,QAAQ,KAAK,CAUf;gBACX,OAAO,CAAC,QAAU,CAAC,WAAW,EAAE,MAAM,QAAQ,CAAC;gBAC/C,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;qBAAE;YACpD;YAEA,mBAAmB;YACnB,gBAAgB,QAAQ,QAAQ,CAAe;gBAC7C,OAAO,CAAC,QAAU,CAAC;wBACjB,KAAK,CAAC,KAAK,EAAE,MAAM,aAAa,CAAC;wBACjC,QAAQ;wBACR,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,kBAAkB;YAClB,eAAe,QAAQ,QAAQ,CAG5B;gBACD,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,4BAA4B,EAC5B,kBAAkB,EAClB,sBAAsB,EACtB,0BAA0B,EAC1B,yBAAyB,EACzB,mBAAmB,EACnB,8BAA8B,EAC9B,sBAAsB,EACtB,+BAA+B,EAC/B,qBAAqB,EACrB,wBAAwB,EACzB,GAAG", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format currency\nexport function formatCurrency(amount: number, currency = \"₹\"): string {\n  return `${currency}${amount.toLocaleString('en-IN', { \n    minimumFractionDigits: 2, \n    maximumFractionDigits: 2 \n  })}`\n}\n\n// Format date\nexport function formatDate(date: string | Date, format = \"dd/MM/yyyy\"): string {\n  const d = new Date(date)\n  \n  if (format === \"dd/MM/yyyy\") {\n    return d.toLocaleDateString('en-GB')\n  }\n  \n  if (format === \"relative\") {\n    const now = new Date()\n    const diffInMs = now.getTime() - d.getTime()\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n    \n    if (diffInDays === 0) return \"Today\"\n    if (diffInDays === 1) return \"Yesterday\"\n    if (diffInDays < 7) return `${diffInDays} days ago`\n    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`\n    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`\n    return `${Math.floor(diffInDays / 365)} years ago`\n  }\n  \n  return d.toLocaleDateString()\n}\n\n// Truncate text\nexport function truncateText(text: string, length = 50): string {\n  if (text.length <= length) return text\n  return text.substring(0, length) + \"...\"\n}\n\n// Generate initials\nexport function getInitials(firstName?: string, lastName?: string, email?: string): string {\n  if (firstName && lastName) {\n    return `${firstName[0]}${lastName[0]}`.toUpperCase()\n  }\n  \n  if (firstName) {\n    return firstName.substring(0, 2).toUpperCase()\n  }\n  \n  if (email) {\n    return email.substring(0, 2).toUpperCase()\n  }\n  \n  return \"U\"\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Sleep function\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n// Generate random ID\nexport function generateId(prefix = \"\"): string {\n  const timestamp = Date.now().toString(36)\n  const random = Math.random().toString(36).substring(2, 8)\n  return `${prefix}${timestamp}${random}`.toUpperCase()\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// Validate phone\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\n// Format phone number\nexport function formatPhone(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '')\n  \n  if (cleaned.length === 10) {\n    return `+91 ${cleaned.substring(0, 5)} ${cleaned.substring(5)}`\n  }\n  \n  if (cleaned.length === 12 && cleaned.startsWith('91')) {\n    return `+${cleaned.substring(0, 2)} ${cleaned.substring(2, 7)} ${cleaned.substring(7)}`\n  }\n  \n  return phone\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0\n  return Math.round((value / total) * 100)\n}\n\n// Get status color\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    active: \"text-green-600 bg-green-100\",\n    inactive: \"text-gray-600 bg-gray-100\",\n    pending: \"text-yellow-600 bg-yellow-100\",\n    suspended: \"text-red-600 bg-red-100\",\n    completed: \"text-green-600 bg-green-100\",\n    failed: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    new: \"text-blue-600 bg-blue-100\",\n    contacted: \"text-purple-600 bg-purple-100\",\n    qualified: \"text-indigo-600 bg-indigo-100\",\n    converted: \"text-green-600 bg-green-100\",\n    lost: \"text-red-600 bg-red-100\",\n  }\n  \n  return statusColors[status.toLowerCase()] || \"text-gray-600 bg-gray-100\"\n}\n\n// Copy to clipboard\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error)\n    return false\n  }\n}\n\n// Download file\nexport function downloadFile(data: any, filename: string, type = 'application/json'): void {\n  const blob = new Blob([typeof data === 'string' ? data : JSON.stringify(data, null, 2)], { type })\n  const url = URL.createObjectURL(blob)\n  const link = document.createElement('a')\n  link.href = url\n  link.download = filename\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  URL.revokeObjectURL(url)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Get file extension\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)\n}\n\n// Check if mobile device\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// Scroll to element\nexport function scrollToElement(elementId: string, offset = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({ top, behavior: 'smooth' })\n  }\n}\n\n// Local storage helpers\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null\n    \n    try {\n      const item = localStorage.getItem(key)\n      return item ? JSON.parse(item) : defaultValue || null\n    } catch (error) {\n      console.error(`Error getting item from localStorage:`, error)\n      return defaultValue || null\n    }\n  },\n  \n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.setItem(key, JSON.stringify(value))\n    } catch (error) {\n      console.error(`Error setting item in localStorage:`, error)\n    }\n  },\n  \n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.removeItem(key)\n    } catch (error) {\n      console.error(`Error removing item from localStorage:`, error)\n    }\n  },\n  \n  clear: (): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.clear()\n    } catch (error) {\n      console.error(`Error clearing localStorage:`, error)\n    }\n  }\n}\n\n// Format time\nexport function formatTime(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAW,GAAG;IAC3D,OAAO,GAAG,WAAW,OAAO,cAAc,CAAC,SAAS;QAClD,uBAAuB;QACvB,uBAAuB;IACzB,IAAI;AACN;AAGO,SAAS,WAAW,IAAmB,EAAE,SAAS,YAAY;IACnE,MAAM,IAAI,IAAI,KAAK;IAEnB,IAAI,WAAW,cAAc;QAC3B,OAAO,EAAE,kBAAkB,CAAC;IAC9B;IAEA,IAAI,WAAW,YAAY;QACzB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,EAAE,OAAO;QAC1C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,SAAS,CAAC;QACnD,IAAI,aAAa,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;QACrE,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,WAAW,CAAC;QACxE,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,UAAU,CAAC;IACpD;IAEA,OAAO,EAAE,kBAAkB;AAC7B;AAGO,SAAS,aAAa,IAAY,EAAE,SAAS,EAAE;IACpD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,UAAU;AACrC;AAGO,SAAS,YAAY,SAAkB,EAAE,QAAiB,EAAE,KAAc;IAC/E,IAAI,aAAa,UAAU;QACzB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW;IACpD;IAEA,IAAI,WAAW;QACb,OAAO,UAAU,SAAS,CAAC,GAAG,GAAG,WAAW;IAC9C;IAEA,IAAI,OAAO;QACT,OAAO,MAAM,SAAS,CAAC,GAAG,GAAG,WAAW;IAC1C;IAEA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,WAAW,SAAS,EAAE;IACpC,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,OAAO,GAAG,SAAS,YAAY,QAAQ,CAAC,WAAW;AACrD;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,YAAY,KAAa;IACvC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,CAAC,IAAI,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACjE;IAEA,IAAI,QAAQ,MAAM,KAAK,MAAM,QAAQ,UAAU,CAAC,OAAO;QACrD,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACzF;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;QACR,WAAW;QACX,KAAK;QACL,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;IACR;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,SAAS,aAAa,IAAS,EAAE,QAAgB,EAAE,OAAO,kBAAkB;IACjF,MAAM,OAAO,IAAI,KAAK;QAAC,OAAO,SAAS,WAAW,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM;KAAG,EAAE;QAAE;IAAK;IAChG,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAGO,SAAS;IACd,uCAAmC;;IAAW;IAC9C,OAAO,OAAO,UAAU,GAAG;AAC7B;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAS,CAAC;IAC3D,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YAAE;YAAK,UAAU;QAAS;IAC5C;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,uCAAmC;;QAA0B;QAE7D,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ,gBAAgB;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,CAAC,EAAE;YACvD,OAAO,gBAAgB;QACzB;IACF;IAEA,KAAK,CAAI,KAAa;QACpB,uCAAmC;;QAAK;QAExC,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,CAAC,EAAE;QACvD;IACF;IAEA,QAAQ,CAAC;QACP,uCAAmC;;QAAK;QAExC,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,CAAC,EAAE;QAC1D;IACF;IAEA,OAAO;QACL,uCAAmC;;QAAK;QAExC,IAAI;YACF,aAAa,KAAK;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAChD;IACF;AACF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"card-base text-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"btn-primary\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600\",\n        outline: \"btn-outline\",\n        secondary: \"btn-secondary\",\n        ghost: \"hover:bg-primary-100 text-primary\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,mQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,4TAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"input-field flex h-10 w-full rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,4TAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2RACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,iRAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,iRAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kRAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,kRAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,4TAAC,kRAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,4TAAC,2SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,uSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,kRAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,2SAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,kRAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,4TAAC,kRAAA,CAAA,SAAsB;kBACrB,cAAA,4TAAC,kRAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,4TAAC;;;;;8BACD,4TAAC,kRAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,4TAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,kRAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,kRAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC,kRAAA,CAAA,gBAA6B;8BAC5B,cAAA,4TAAC,2RAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,4TAAC,kRAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kRAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,kRAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = \"Avatar\"\n\nconst AvatarImage = React.forwardRef<\n  HTMLImageElement,\n  React.ImgHTMLAttributes<HTMLImageElement>\n>(({ className, ...props }, ref) => (\n  <img\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = \"AvatarImage\"\n\nconst AvatarFallback = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = \"AvatarFallback\"\n\nconst AvatarInitials = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & { name: string }\n>(({ className, name, ...props }, ref) => {\n  const initials = name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"flex h-full w-full items-center justify-center rounded-full bg-sky-500 text-white text-sm font-medium\",\n        className\n      )}\n      {...props}\n    >\n      {initials}\n    </div>\n  )\n})\nAvatarInitials.displayName = \"AvatarInitials\"\n\nexport { Avatar, AvatarImage, AvatarFallback, AvatarInitials }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAEA,MAAM,uBAAS,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,+BAAiB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAChC,MAAM,WAAW,KACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IAEZ,qBACE,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yGACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AACA,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/logo.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'full' | 'icon' | 'text'\n  className?: string\n}\n\nconst Logo: React.FC<LogoProps> = ({ \n  size = 'md', \n  variant = 'full', \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'h-6 w-6',\n    md: 'h-8 w-8', \n    lg: 'h-10 w-10',\n    xl: 'h-12 w-12'\n  }\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl', \n    xl: 'text-3xl'\n  }\n\n  const LogoIcon = () => (\n    <div className={`${sizeClasses[size]} bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center shadow-lg ${className}`}>\n      <div className=\"relative\">\n        {/* S */}\n        <div className=\"absolute -left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5 ml-1\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n        </div>\n        \n        {/* G */}\n        <div className=\"absolute left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-4 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"absolute right-0 top-2 w-1 h-2 bg-white rounded-full\"></div>\n          <div className=\"absolute right-0 top-3 w-1.5 h-1 bg-white rounded-full\"></div>\n        </div>\n        \n        {/* M */}\n        <div className=\"absolute left-4 top-0\">\n          <div className=\"w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-0.5 top-1 w-1 h-1 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1 top-0 w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1.5 top-0 w-1 h-5 bg-white rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const LogoText = () => (\n    <span className={`font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent ${textSizeClasses[size]} ${className}`}>\n      SGM\n    </span>\n  )\n\n  if (variant === 'icon') {\n    return <LogoIcon />\n  }\n\n  if (variant === 'text') {\n    return <LogoText />\n  }\n\n  return (\n    <div className={`flex items-center gap-3 ${className}`}>\n      <LogoIcon />\n      <LogoText />\n    </div>\n  )\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;;AAQA,MAAM,OAA4B,CAAC,EACjC,OAAO,IAAI,EACX,UAAU,MAAM,EAChB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,WAAW,kBACf,4TAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,mGAAmG,EAAE,WAAW;sBACnJ,cAAA,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,WAAW,kBACf,4TAAC;YAAK,WAAW,CAAC,mFAAmF,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;sBAAE;;;;;;IAK/I,IAAI,YAAY,QAAQ;QACtB,qBAAO,4TAAC;;;;;IACV;IAEA,IAAI,YAAY,QAAQ;QACtB,qBAAO,4TAAC;;;;;IACV;IAEA,qBACE,4TAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,4TAAC;;;;;0BACD,4TAAC;;;;;;;;;;;AAGP;KAvEM;uCAyES", "debugId": null}}, {"offset": {"line": 1193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAppSelector } from '@/store'\nimport { selectUser } from '@/store/slices/authSlice'\nimport { UserRole } from '@/types'\nimport { cn } from '@/lib/utils'\nimport Logo from '@/components/ui/logo'\nimport {\n  LayoutDashboard,\n  Users,\n  Building,\n  TrendingUp,\n  Target,\n  DollarSign,\n  Headphones,\n  Settings,\n  Plus,\n  UserPlus,\n  UserCheck,\n  BarChart3,\n  Shield,\n  Bell,\n  PieChart,\n  CheckSquare,\n  CalendarDays,\n  UserCog,\n  Award,\n  Minus,\n  Receipt,\n  CreditCard\n} from 'lucide-react'\n\ninterface SidebarProps {\n  collapsed?: boolean\n  onToggle?: () => void\n}\n\nexport default function Sidebar({ collapsed = false, onToggle }: SidebarProps) {\n  const pathname = usePathname()\n  const user = useAppSelector((state) => selectUser(state as any))\n\n  console.log('Sidebar rendering...', { collapsed, pathname, user })\n\n  const menuSections = [\n    {\n      title: \"Dashboard\",\n      items: [\n        {\n          id: \"main-dashboard\",\n          label: \"Overview\",\n          icon: LayoutDashboard,\n          href: \"/dashboard\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES, UserRole.USER]\n        }\n        \n      ]\n    },\n    {\n      title: \"User Management\",\n      items: [\n        {\n          id: \"users-overview\",\n          label: \"All Users\",\n          icon: Users,\n          href: \"/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-management-comprehensive\",\n          label: \"User Management\",\n          icon: Users,\n          href: \"/user-management\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-user\",\n          label: \"Add User\",\n          icon: UserPlus,\n          href: \"/users/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"role-management\",\n          label: \"Role Management\",\n          icon: UserCheck,\n          href: \"/users/roles\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Property Management\",\n      items: [\n        {\n          id: \"properties-overview\",\n          label: \"All Properties\",\n          icon: Building,\n          href: \"/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-property\",\n          label: \"Add Property\",\n          icon: Plus,\n          href: \"/properties/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-owners\",\n          label: \"Property Owners\",\n          icon: UserCheck,\n          href: \"/property-owners\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Stock Investments\",\n      items: [\n        {\n          id: \"stocks-overview\",\n          label: \"All Stocks\",\n          icon: TrendingUp,\n          href: \"/stocks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"create-stock\",\n          label: \"Create Stock\",\n          icon: Plus,\n          href: \"/stocks/create\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Lead Management\",\n      items: [\n        {\n          id: \"leads-overview\",\n          label: \"Lead Management\",\n          icon: Target,\n          href: \"/leads\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES]\n        },\n        {\n          id: \"sales-analytics\",\n          label: \"Sales Analytics\",\n          icon: BarChart3,\n          href: \"/sales-analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Sales Management\",\n      items: [\n        {\n          id: \"sales-team\",\n          label: \"Sales Team\",\n          icon: UserCog,\n          href: \"/sales-team\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-tasks\",\n          label: \"Sales Tasks\",\n          icon: CheckSquare,\n          href: \"/sales-tasks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-calendar\",\n          label: \"Sales Calendar\",\n          icon: CalendarDays,\n          href: \"/sales-calendar\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-targets\",\n          label: \"Sales Targets\",\n          icon: Award,\n          href: \"/sales-targets\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"commissions\",\n          label: \"Commissions\",\n          icon: DollarSign,\n          href: \"/commissions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Financial Management\",\n      items: [\n        {\n          id: \"finance-overview\",\n          label: \"Financial Management\",\n          icon: DollarSign,\n          href: \"/finance\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-funds\",\n          label: \"Add Funds\",\n          icon: Plus,\n          href: \"/add-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"deduct-funds\",\n          label: \"Deduct Funds\",\n          icon: Minus,\n          href: \"/deduct-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"admin-transactions\",\n          label: \"All Transactions\",\n          icon: Receipt,\n          href: \"/admin-transactions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"withdrawal-requests\",\n          label: \"Withdrawal Requests\",\n          icon: CreditCard,\n          href: \"/withdrawal-requests\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Support Management\",\n      items: [\n        {\n          id: \"support-dashboard\",\n          label: \"Support Management\",\n          icon: Headphones,\n          href: \"/support\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"System & Settings\",\n      items: [\n        {\n          id: \"system-settings\",\n          label: \"Settings Management\",\n          icon: Settings,\n          href: \"/settings\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Reports & Analytics\",\n      items: [\n        {\n          id: \"analytics-dashboard\",\n          label: \"Analytics Dashboard\",\n          icon: BarChart3,\n          href: \"/analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-reports\",\n          label: \"User Reports\",\n          icon: Users,\n          href: \"/reports/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-reports\",\n          label: \"Property Reports\",\n          icon: Building,\n          href: \"/reports/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"financial-reports\",\n          label: \"Financial Reports\",\n          icon: DollarSign,\n          href: \"/reports/financial\",\n          roles: [UserRole.ADMIN]\n        },\n       \n      ]\n    }\n  ]\n\n  // Filter menu sections based on user role\n  const userRole = user?.role as UserRole || UserRole.USER\n  const filteredSections = menuSections.map(section => ({\n    ...section,\n    items: section.items.filter(item => item.roles.includes(userRole))\n  })).filter(section => section.items.length > 0)\n\n  // Debug logging\n  console.log('User:', user)\n  console.log('User Role:', user?.role)\n  console.log('UserRole enum:', userRole)\n  console.log('Menu Sections:', menuSections.length)\n  console.log('Filtered Sections:', filteredSections.length)\n\n  // Use filtered sections based on user role\n  const sectionsToShow = filteredSections\n\n  const isActive = (href: string) => {\n    if (!pathname) return false\n    if (href === '/dashboard') {\n      return pathname === '/dashboard'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <div className={cn(\n      \"h-screen bg-white border-r border-sky-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg\",\n      collapsed ? \"w-20\" : \"w-72\"\n    )}>\n      {/* Logo */}\n      <div className=\"flex items-center space-x-3 p-4 border-b border-sky-200 flex-shrink-0 bg-gradient-to-r from-sky-50 to-sky-100\">\n        <Logo size={collapsed ? \"lg\" : \"xl\"} variant={collapsed ? \"icon\" : \"full\"} />\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-300 scrollbar-track-sky-100\">\n        <div className=\"space-y-2\">\n          {sectionsToShow.length === 0 ? (\n            <div className=\"text-center text-red-500 p-4\">\n              <p>No menu items found!</p>\n              <p>User: {user?.firstName || 'Not logged in'}</p>\n              <p>Role: {user?.role || 'No role'}</p>\n            </div>\n          ) : (\n            sectionsToShow.map((section) => (\n              <div key={section.title} className=\"space-y-1 mb-6\">\n                {!collapsed && (\n                  <h3 className=\"px-3 py-2 text-xs font-semibold text-sky-600 uppercase tracking-wider\">\n                    {section.title}\n                  </h3>\n                )}\n\n                {section.items.map((item) => (\n                  <Link\n                    key={item.id}\n                    href={item.href || '#'}\n                    className={cn(\n                      \"flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200\",\n                      isActive(item.href || '')\n                        ? \"bg-sky-600 text-white shadow-md\"\n                        : \"text-gray-700 hover:text-sky-600 hover:bg-sky-50\"\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                    {!collapsed && (\n                      <span className=\"flex-1 truncate\">{item.label}</span>\n                    )}\n                  </Link>\n                ))}\n              </div>\n            ))\n          )}\n        </div>\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-sky-200 bg-gradient-to-r from-sky-50 to-sky-100\">\n        {!collapsed && (\n          <div className=\"text-xs text-sky-600 text-center font-medium\">\n            © 2025 SGM. All rights reserved.\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;AAwCe,SAAS,QAAQ,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAgB;;IAC3E,MAAM,WAAW,CAAA,GAAA,oQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,OAAO,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;wCAAE,CAAC,QAAU,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;;IAElD,QAAQ,GAAG,CAAC,wBAAwB;QAAE;QAAW;QAAU;IAAK;IAEhE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,mTAAA,CAAA,kBAAe;oBACrB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,IAAI;qBAAC;gBAC3E;aAED;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,qSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,uSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,iSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,yRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,uSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,yRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,6RAAA,CAAA,SAAM;oBACZ,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBAC5D;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,mSAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kTAAA,CAAA,cAAW;oBACjB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,6SAAA,CAAA,eAAY;oBAClB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,yRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,+RAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,qSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,iSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,iSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aAED;QACH;KACD;IAED,0CAA0C;IAC1C,MAAM,WAAW,MAAM,QAAoB,wHAAA,CAAA,WAAQ,CAAC,IAAI;IACxD,MAAM,mBAAmB,aAAa,GAAG,CAAC,CAAA,UAAW,CAAC;YACpD,GAAG,OAAO;YACV,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;QAC1D,CAAC,GAAG,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,CAAC,MAAM,GAAG;IAE7C,gBAAgB;IAChB,QAAQ,GAAG,CAAC,SAAS;IACrB,QAAQ,GAAG,CAAC,cAAc,MAAM;IAChC,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,kBAAkB,aAAa,MAAM;IACjD,QAAQ,GAAG,CAAC,sBAAsB,iBAAiB,MAAM;IAEzD,2CAA2C;IAC3C,MAAM,iBAAiB;IAEvB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,4TAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,6GACA,YAAY,SAAS;;0BAGrB,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,mIAAA,CAAA,UAAI;oBAAC,MAAM,YAAY,OAAO;oBAAM,SAAS,YAAY,SAAS;;;;;;;;;;;0BAIrE,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;8BACZ,eAAe,MAAM,KAAK,kBACzB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;0CAAE;;;;;;0CACH,4TAAC;;oCAAE;oCAAO,MAAM,aAAa;;;;;;;0CAC7B,4TAAC;;oCAAE;oCAAO,MAAM,QAAQ;;;;;;;;;;;;+BAG1B,eAAe,GAAG,CAAC,CAAC,wBAClB,4TAAC;4BAAwB,WAAU;;gCAChC,CAAC,2BACA,4TAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAIjB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,4TAAC,8RAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI,IAAI;wCACnB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA,SAAS,KAAK,IAAI,IAAI,MAClB,oCACA;;0DAGN,4TAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,CAAC,2BACA,4TAAC;gDAAK,WAAU;0DAAmB,KAAK,KAAK;;;;;;;uCAX1C,KAAK,EAAE;;;;;;2BATR,QAAQ,KAAK;;;;;;;;;;;;;;;0BA+B/B,4TAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,4TAAC;oBAAI,WAAU;8BAA+C;;;;;;;;;;;;;;;;;AAOxE;GAvVwB;;QACL,oQAAA,CAAA,cAAW;QACf,wHAAA,CAAA,iBAAc;;;KAFL", "debugId": null}}, {"offset": {"line": 1744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector, useAppDispatch } from '@/store'\nimport { selectUser, logoutAsync } from '@/store/slices/authSlice'\nimport { Button } from '@/components/ui/button'\nimport Sidebar from './Sidebar'\nimport { Menu, Bell, Search, LogOut, User, Settings, HelpCircle, ChevronDown } from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n  const [userMenuOpen, setUserMenuOpen] = useState(false)\n  const router = useRouter()\n  const dispatch = useAppDispatch()\n  const user = useAppSelector(selectUser)\n\n  const handleLogout = async () => {\n    await dispatch(logoutAsync())\n    router.push('/login')\n  }\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed)\n  }\n\n  return (\n    <div className=\"h-screen bg-white overflow-hidden\">\n      <div className=\"flex h-full\">\n        {/* Sidebar */}\n        <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col h-full overflow-hidden\">\n          {/* Header */}\n          <header className=\"bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg border-b border-sky-700 flex-shrink-0 z-40\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex justify-between items-center py-4\">\n                {/* Left side */}\n                <div className=\"flex items-center space-x-4\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={toggleSidebar}\n                    className=\"lg:hidden hover:bg-white/20 text-white\"\n                  >\n                    <Menu className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Search Bar */}\n                  <div className=\"hidden md:flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2.5 min-w-[350px] border border-white/30\">\n                    <Search className=\"h-4 w-4 text-white/70\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search users, properties, transactions...\"\n                      className=\"bg-transparent border-none outline-none flex-1 text-sm placeholder:text-white/70 text-white\"\n                    />\n                    <kbd className=\"hidden lg:inline-flex items-center px-2 py-1 text-xs font-medium text-sky-600 bg-white rounded border border-white/30\">\n                      ⌘K\n                    </kbd>\n                  </div>\n                </div>\n\n                {/* Right side */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Quick Actions */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"hover:bg-white/20 text-white\">\n                    <HelpCircle className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Notifications */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"relative hover:bg-white/20 text-white\">\n                    <Bell className=\"h-5 w-5\" />\n                    <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-yellow-500 text-black text-xs rounded-full flex items-center justify-center font-semibold\">\n                      5\n                    </span>\n                  </Button>\n\n                  {/* User Menu */}\n                  <div className=\"relative\">\n                    <Button\n                      variant=\"ghost\"\n                      onClick={() => setUserMenuOpen(!userMenuOpen)}\n                      className=\"flex items-center space-x-3 px-3 py-2 hover:bg-white/20 rounded-lg text-white\"\n                    >\n                      <div className=\"hidden sm:block text-right\">\n                        <p className=\"text-sm font-medium text-white\">\n                          {user?.firstName} {user?.lastName}\n                        </p>\n                        <div className=\"flex items-center justify-end space-x-1\">\n                          <div className=\"bg-yellow-500 px-2 py-0.5 rounded-full\">\n                            <span className=\"text-xs font-medium text-black capitalize\">\n                              {user?.role}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"h-10 w-10 bg-white/20 rounded-full flex items-center justify-center shadow-sm border border-white/30\">\n                        <User className=\"h-5 w-5 text-white\" />\n                      </div>\n\n                      <ChevronDown className=\"h-4 w-4 text-white/70\" />\n                    </Button>\n\n                    {/* User Dropdown Menu */}\n                    {userMenuOpen && (\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sky-200 py-2 z-50\">\n                        <div className=\"px-4 py-2 border-b border-sky-100\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {user?.firstName} {user?.lastName}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                        </div>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <User className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Profile\n                        </Button>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <Settings className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Settings\n                        </Button>\n\n                        <div className=\"border-t border-sky-100 mt-2 pt-2\">\n                          <Button\n                            variant=\"ghost\"\n                            className=\"w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                            onClick={handleLogout}\n                          >\n                            <LogOut className=\"h-4 w-4 mr-3\" />\n                            Logout\n                          </Button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Main Content Area */}\n          <main className=\"flex-1 overflow-y-auto bg-gray-50\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAce,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,OAAO,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,aAAU;IAEtC,MAAM,eAAe;QACnB,MAAM,SAAS,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YAAI,WAAU;;8BAEb,4TAAC,0IAAA,CAAA,UAAO;oBAAC,WAAW;oBAAkB,UAAU;;;;;;8BAGhD,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC;4BAAO,WAAU;sCAChB,cAAA,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDAEb,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,4TAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAIlB,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,6RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,4TAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,4TAAC;4DAAI,WAAU;sEAAwH;;;;;;;;;;;;;;;;;;sDAO3I,4TAAC;4CAAI,WAAU;;8DAEb,4TAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;8DAC5C,cAAA,4TAAC,qTAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAIxB,4TAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;;sEAC5C,4TAAC,yRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,4TAAC;4DAAK,WAAU;sEAAgI;;;;;;;;;;;;8DAMlJ,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;;8EAEV,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,4TAAC;4EAAI,WAAU;sFACb,cAAA,4TAAC;gFAAI,WAAU;0FACb,cAAA,4TAAC;oFAAK,WAAU;8FACb,MAAM;;;;;;;;;;;;;;;;;;;;;;8EAMf,4TAAC;oEAAI,WAAU;8EACb,cAAA,4TAAC,yRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGlB,4TAAC,2SAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;wDAIxB,8BACC,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,4TAAC;4EAAE,WAAU;sFAAyB,MAAM;;;;;;;;;;;;8EAG9C,4TAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,4TAAC,yRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIhD,4TAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,4TAAC,iSAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIpD,4TAAC;oEAAI,WAAU;8EACb,cAAA,4TAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,WAAU;wEACV,SAAS;;0FAET,4TAAC,iSAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAarD,4TAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb;GArJwB;;QAGP,oQAAA,CAAA,YAAS;QACP,wHAAA,CAAA,iBAAc;QAClB,wHAAA,CAAA,iBAAc;;;KALL", "debugId": null}}, {"offset": {"line": 2166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kRAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kRAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kRAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gLACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC;;0BACC,4TAAC;;;;;0BACD,4TAAC,kRAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,4TAAC,kRAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,4TAAC,mRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,4TAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,4TAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,4TAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,kRAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,kRAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\nimport { Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst RadioGroup = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Root\n      className={cn(\"grid gap-2\", className)}\n      {...props}\n      ref={ref}\n    />\n  )\n})\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\n\nconst RadioGroupItem = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  )\n})\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\n\nexport { RadioGroup, RadioGroupItem }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,4TAAC,oRAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;;AACA,WAAW,WAAW,GAAG,oRAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,4TAAC,oRAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4OACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,oRAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,4TAAC,6RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;;AACA,eAAe,WAAW,GAAG,oRAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2422, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/modals/KYCReviewModal.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport { Separator } from '@/components/ui/separator'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport {\n  CheckCircle,\n  XCircle,\n  AlertTriangle,\n  User,\n  Mail,\n  Phone,\n  MapPin,\n  CreditCard,\n  Building,\n  FileText,\n  Download,\n  Eye,\n  Calendar,\n  Award,\n  Shield\n} from 'lucide-react'\nimport { toast } from 'sonner'\n\ninterface KYCReviewModalProps {\n  isOpen: boolean\n  onClose: () => void\n  user: any\n  onKYCUpdate: (action: 'approve' | 'reject', data: any) => Promise<void>\n  isLoading?: boolean\n}\n\nexport default function KYCReviewModal({\n  isOpen,\n  onClose,\n  user,\n  onKYCUpdate,\n  isLoading = false\n}: KYCReviewModalProps) {\n  const [action, setAction] = useState<'approve' | 'reject' | null>(null)\n  const [kycLevel, setKycLevel] = useState<'basic' | 'advanced' | 'premium'>('basic')\n  const [rejectionReason, setRejectionReason] = useState('')\n  const [customReason, setCustomReason] = useState('')\n  const [adminNotes, setAdminNotes] = useState('')\n\n  const predefinedReasons = [\n    'Document quality is poor or unclear',\n    'Information mismatch between documents',\n    'Expired or invalid documents',\n    'Missing required documents',\n    'Suspicious or fraudulent documents',\n    'Address proof is insufficient',\n    'Identity verification failed',\n    'Bank details verification failed',\n    'Other (specify below)'\n  ]\n\n  const handleSubmit = async () => {\n    if (!action) {\n      toast.error('Please select an action')\n      return\n    }\n\n    if (action === 'reject' && !rejectionReason && !customReason) {\n      toast.error('Please provide a rejection reason')\n      return\n    }\n\n    try {\n      const finalReason = rejectionReason === 'Other (specify below)' ? customReason : rejectionReason\n\n      await onKYCUpdate(action, {\n        status: action === 'approve' ? 'approved' : 'rejected',\n        level: action === 'approve' ? kycLevel : undefined,\n        rejectionReason: action === 'reject' ? finalReason : undefined,\n        notes: adminNotes || undefined\n      })\n\n      // Reset form\n      setAction(null)\n      setKycLevel('basic')\n      setRejectionReason('')\n      setCustomReason('')\n      setAdminNotes('')\n      onClose()\n    } catch (error) {\n      // Error handled by parent component\n    }\n  }\n\n  const getKYCStatusColor = (status: string) => {\n    switch (status) {\n      case 'approved': return 'bg-green-100 text-green-800'\n      case 'pending': return 'bg-yellow-100 text-yellow-800'\n      case 'rejected': return 'bg-red-100 text-red-800'\n      case 'under_review': return 'bg-blue-100 text-blue-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Shield className=\"h-5 w-5\" />\n            KYC Review - {user?.firstName} {user?.lastName}\n          </DialogTitle>\n          <DialogDescription>\n            Review and approve or reject the KYC submission for this user\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* User Summary */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">User Information</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex items-start gap-4\">\n                <Avatar className=\"h-16 w-16\">\n                  <AvatarImage src={typeof user?.avatar === 'object' && user?.avatar !== null ? user?.avatar.url : user?.profileImage} />\n                  <AvatarFallback className=\"bg-blue-100 text-blue-600 text-xl font-bold\">\n                    {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}\n                  </AvatarFallback>\n                </Avatar>\n                <div className=\"flex-1\">\n                  <h3 className=\"text-xl font-semibold\">{user?.firstName} {user?.lastName}</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3\">\n                    <div className=\"flex items-center gap-2\">\n                      <Mail className=\"h-4 w-4 text-gray-500\" />\n                      <span className=\"text-sm\">{user?.email}</span>\n                      {user?.emailVerified && <CheckCircle className=\"h-4 w-4 text-green-600\" />}\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <Phone className=\"h-4 w-4 text-gray-500\" />\n                      <span className=\"text-sm\">{user?.phone || 'Not provided'}</span>\n                      {user?.phoneVerified && <CheckCircle className=\"h-4 w-4 text-green-600\" />}\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <Calendar className=\"h-4 w-4 text-gray-500\" />\n                      <span className=\"text-sm\">Joined {new Date(user?.createdAt).toLocaleDateString()}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <Badge className={getKYCStatusColor(user?.kycStatus)}>\n                        Current Status: {user?.kycStatus?.replace('_', ' ')}\n                      </Badge>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* KYC Documents Preview */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">KYC Documents</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                {/* Mock documents - replace with real data */}\n                {[\n                  { type: 'Aadhar Card', status: 'uploaded', url: '#' },\n                  { type: 'PAN Card', status: 'uploaded', url: '#' },\n                  { type: 'Address Proof', status: 'uploaded', url: '#' },\n                  { type: 'Bank Statement', status: 'uploaded', url: '#' },\n                  { type: 'Selfie Photo', status: 'uploaded', url: '#' }\n                ].map((doc, index) => (\n                  <div key={index} className=\"p-3 border border-gray-200 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"font-medium text-sm\">{doc.type}</h4>\n                      <Badge className=\"bg-green-100 text-green-800 text-xs\">\n                        {doc.status}\n                      </Badge>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <Button size=\"sm\" variant=\"outline\" className=\"text-xs\">\n                        <Eye className=\"h-3 w-3 mr-1\" />\n                        View\n                      </Button>\n                      <Button size=\"sm\" variant=\"outline\" className=\"text-xs\">\n                        <Download className=\"h-3 w-3 mr-1\" />\n                        Download\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* KYC Action Selection */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">Review Decision</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div>\n                <Label className=\"text-base font-medium\">Select Action</Label>\n                <RadioGroup value={action || ''} onValueChange={(value) => setAction(value as 'approve' | 'reject')}>\n                  <div className=\"flex items-center space-x-2 p-3 border border-green-200 rounded-lg bg-green-50\">\n                    <RadioGroupItem value=\"approve\" id=\"approve\" />\n                    <Label htmlFor=\"approve\" className=\"flex items-center gap-2 cursor-pointer\">\n                      <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                      <span className=\"font-medium text-green-800\">Approve KYC</span>\n                    </Label>\n                  </div>\n                  <div className=\"flex items-center space-x-2 p-3 border border-red-200 rounded-lg bg-red-50\">\n                    <RadioGroupItem value=\"reject\" id=\"reject\" />\n                    <Label htmlFor=\"reject\" className=\"flex items-center gap-2 cursor-pointer\">\n                      <XCircle className=\"h-4 w-4 text-red-600\" />\n                      <span className=\"font-medium text-red-800\">Reject KYC</span>\n                    </Label>\n                  </div>\n                </RadioGroup>\n              </div>\n\n              {/* KYC Level Selection (for approval) */}\n              {action === 'approve' && (\n                <div>\n                  <Label htmlFor=\"kyc-level\" className=\"text-base font-medium\">KYC Level</Label>\n                  <Select value={kycLevel} onValueChange={(value) => setKycLevel(value as any)}>\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"Select KYC level\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"basic\">\n                        <div className=\"flex items-center gap-2\">\n                          <Award className=\"h-4 w-4 text-blue-600\" />\n                          Basic Level - Standard verification\n                        </div>\n                      </SelectItem>\n                      <SelectItem value=\"advanced\">\n                        <div className=\"flex items-center gap-2\">\n                          <Award className=\"h-4 w-4 text-purple-600\" />\n                          Advanced Level - Enhanced verification\n                        </div>\n                      </SelectItem>\n                      <SelectItem value=\"premium\">\n                        <div className=\"flex items-center gap-2\">\n                          <Award className=\"h-4 w-4 text-gold-600\" />\n                          Premium Level - Complete verification\n                        </div>\n                      </SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n              )}\n\n              {/* Rejection Reason (for rejection) */}\n              {action === 'reject' && (\n                <div className=\"space-y-3\">\n                  <Label htmlFor=\"rejection-reason\" className=\"text-base font-medium\">Rejection Reason</Label>\n                  <Select value={rejectionReason} onValueChange={setRejectionReason}>\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"Select rejection reason\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {predefinedReasons.map((reason, index) => (\n                        <SelectItem key={index} value={reason}>\n                          {reason}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n\n                  {rejectionReason === 'Other (specify below)' && (\n                    <div>\n                      <Label htmlFor=\"custom-reason\">Custom Reason</Label>\n                      <Textarea\n                        id=\"custom-reason\"\n                        placeholder=\"Please specify the rejection reason...\"\n                        value={customReason}\n                        onChange={(e) => setCustomReason(e.target.value)}\n                        rows={3}\n                      />\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {/* Admin Notes */}\n              <div>\n                <Label htmlFor=\"admin-notes\" className=\"text-base font-medium\">Admin Notes (Optional)</Label>\n                <Textarea\n                  id=\"admin-notes\"\n                  placeholder=\"Add any additional notes for this KYC review...\"\n                  value={adminNotes}\n                  onChange={(e) => setAdminNotes(e.target.value)}\n                  rows={3}\n                />\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Action Buttons */}\n          <div className=\"flex items-center justify-end gap-3 pt-4 border-t\">\n            <Button variant=\"outline\" onClick={onClose} disabled={isLoading}>\n              Cancel\n            </Button>\n            <Button\n              onClick={handleSubmit}\n              disabled={!action || isLoading}\n              className={action === 'approve' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}\n            >\n              {isLoading ? (\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                  Processing...\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-2\">\n                  {action === 'approve' ? (\n                    <>\n                      <CheckCircle className=\"h-4 w-4\" />\n                      Approve KYC\n                    </>\n                  ) : action === 'reject' ? (\n                    <>\n                      <XCircle className=\"h-4 w-4\" />\n                      Reject KYC\n                    </>\n                  ) : (\n                    'Select Action'\n                  )}\n                </div>\n              )}\n            </Button>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;;;AA9BA;;;;;;;;;;;;;AAwCe,SAAS,eAAe,EACrC,MAAM,EACN,OAAO,EACP,IAAI,EACJ,WAAW,EACX,YAAY,KAAK,EACG;;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAA+B;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAoC;IAC3E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,oBAAoB;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ;YACX,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,WAAW,YAAY,CAAC,mBAAmB,CAAC,cAAc;YAC5D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,cAAc,oBAAoB,0BAA0B,eAAe;YAEjF,MAAM,YAAY,QAAQ;gBACxB,QAAQ,WAAW,YAAY,aAAa;gBAC5C,OAAO,WAAW,YAAY,WAAW;gBACzC,iBAAiB,WAAW,WAAW,cAAc;gBACrD,OAAO,cAAc;YACvB;YAEA,aAAa;YACb,UAAU;YACV,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;YAChB,cAAc;YACd;QACF,EAAE,OAAO,OAAO;QACd,oCAAoC;QACtC;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAgB,OAAO;YAC5B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,4TAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,4TAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,4TAAC,qIAAA,CAAA,eAAY;;sCACX,4TAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,4TAAC,6RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;gCAChB,MAAM;gCAAU;gCAAE,MAAM;;;;;;;sCAExC,4TAAC,qIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC,mIAAA,CAAA,OAAI;;8CACH,4TAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,4TAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAU;;;;;;;;;;;8CAEjC,4TAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,4TAAC,qIAAA,CAAA,cAAW;wDAAC,KAAK,OAAO,MAAM,WAAW,YAAY,MAAM,WAAW,OAAO,MAAM,OAAO,MAAM,MAAM;;;;;;kEACvG,4TAAC,qIAAA,CAAA,iBAAc;wDAAC,WAAU;;4DACvB,MAAM,WAAW,OAAO;4DAAI,MAAM,UAAU,OAAO;;;;;;;;;;;;;0DAGxD,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAG,WAAU;;4DAAyB,MAAM;4DAAU;4DAAE,MAAM;;;;;;;kEAC/D,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAI,WAAU;;kFACb,4TAAC,yRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,4TAAC;wEAAK,WAAU;kFAAW,MAAM;;;;;;oEAChC,MAAM,+BAAiB,4TAAC,kTAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;;;;;;;0EAEjD,4TAAC;gEAAI,WAAU;;kFACb,4TAAC,2RAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,4TAAC;wEAAK,WAAU;kFAAW,MAAM,SAAS;;;;;;oEACzC,MAAM,+BAAiB,4TAAC,kTAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;;;;;;;0EAEjD,4TAAC;gEAAI,WAAU;;kFACb,4TAAC,iSAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,4TAAC;wEAAK,WAAU;;4EAAU;4EAAQ,IAAI,KAAK,MAAM,WAAW,kBAAkB;;;;;;;;;;;;;0EAEhF,4TAAC;gEAAI,WAAU;0EACb,cAAA,4TAAC,oIAAA,CAAA,QAAK;oEAAC,WAAW,kBAAkB,MAAM;;wEAAY;wEACnC,MAAM,WAAW,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAU7D,4TAAC,mIAAA,CAAA,OAAI;;8CACH,4TAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,4TAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAU;;;;;;;;;;;8CAEjC,4TAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,4TAAC;wCAAI,WAAU;kDAEZ;4CACC;gDAAE,MAAM;gDAAe,QAAQ;gDAAY,KAAK;4CAAI;4CACpD;gDAAE,MAAM;gDAAY,QAAQ;gDAAY,KAAK;4CAAI;4CACjD;gDAAE,MAAM;gDAAiB,QAAQ;gDAAY,KAAK;4CAAI;4CACtD;gDAAE,MAAM;gDAAkB,QAAQ;gDAAY,KAAK;4CAAI;4CACvD;gDAAE,MAAM;gDAAgB,QAAQ;gDAAY,KAAK;4CAAI;yCACtD,CAAC,GAAG,CAAC,CAAC,KAAK,sBACV,4TAAC;gDAAgB,WAAU;;kEACzB,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAG,WAAU;0EAAuB,IAAI,IAAI;;;;;;0EAC7C,4TAAC,oIAAA,CAAA,QAAK;gEAAC,WAAU;0EACd,IAAI,MAAM;;;;;;;;;;;;kEAGf,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,qIAAA,CAAA,SAAM;gEAAC,MAAK;gEAAK,SAAQ;gEAAU,WAAU;;kFAC5C,4TAAC,uRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,4TAAC,qIAAA,CAAA,SAAM;gEAAC,MAAK;gEAAK,SAAQ;gEAAU,WAAU;;kFAC5C,4TAAC,iSAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;+CAbjC;;;;;;;;;;;;;;;;;;;;;sCAwBlB,4TAAC,mIAAA,CAAA,OAAI;;8CACH,4TAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,4TAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAU;;;;;;;;;;;8CAEjC,4TAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,4TAAC;;8DACC,4TAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAwB;;;;;;8DACzC,4TAAC,6IAAA,CAAA,aAAU;oDAAC,OAAO,UAAU;oDAAI,eAAe,CAAC,QAAU,UAAU;;sEACnE,4TAAC;4DAAI,WAAU;;8EACb,4TAAC,6IAAA,CAAA,iBAAc;oEAAC,OAAM;oEAAU,IAAG;;;;;;8EACnC,4TAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;;sFACjC,4TAAC,kTAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,4TAAC;4EAAK,WAAU;sFAA6B;;;;;;;;;;;;;;;;;;sEAGjD,4TAAC;4DAAI,WAAU;;8EACb,4TAAC,6IAAA,CAAA,iBAAc;oEAAC,OAAM;oEAAS,IAAG;;;;;;8EAClC,4TAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAS,WAAU;;sFAChC,4TAAC,mSAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;sFACnB,4TAAC;4EAAK,WAAU;sFAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAOlD,WAAW,2BACV,4TAAC;;8DACC,4TAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAwB;;;;;;8DAC7D,4TAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAU,eAAe,CAAC,QAAU,YAAY;;sEAC7D,4TAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,4TAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,4TAAC,qIAAA,CAAA,gBAAa;;8EACZ,4TAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAChB,cAAA,4TAAC;wEAAI,WAAU;;0FACb,4TAAC,2RAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAA0B;;;;;;;;;;;;8EAI/C,4TAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAChB,cAAA,4TAAC;wEAAI,WAAU;;0FACb,4TAAC,2RAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAA4B;;;;;;;;;;;;8EAIjD,4TAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAChB,cAAA,4TAAC;wEAAI,WAAU;;0FACb,4TAAC,2RAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAUtD,WAAW,0BACV,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAmB,WAAU;8DAAwB;;;;;;8DACpE,4TAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAiB,eAAe;;sEAC7C,4TAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,4TAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,4TAAC,qIAAA,CAAA,gBAAa;sEACX,kBAAkB,GAAG,CAAC,CAAC,QAAQ,sBAC9B,4TAAC,qIAAA,CAAA,aAAU;oEAAa,OAAO;8EAC5B;mEADc;;;;;;;;;;;;;;;;gDAOtB,oBAAoB,yCACnB,4TAAC;;sEACC,4TAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAgB;;;;;;sEAC/B,4TAAC,uIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,aAAY;4DACZ,OAAO;4DACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/C,MAAM;;;;;;;;;;;;;;;;;;sDAQhB,4TAAC;;8DACC,4TAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAc,WAAU;8DAAwB;;;;;;8DAC/D,4TAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,MAAM;;;;;;;;;;;;;;;;;;;;;;;;sCAOd,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;oCAAS,UAAU;8CAAW;;;;;;8CAGjE,4TAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,CAAC,UAAU;oCACrB,WAAW,WAAW,YAAY,oCAAoC;8CAErE,0BACC,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;;;;;;4CAAkE;;;;;;6DAInF,4TAAC;wCAAI,WAAU;kDACZ,WAAW,0BACV;;8DACE,4TAAC,kTAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAY;;2DAGnC,WAAW,yBACb;;8DACE,4TAAC,mSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAY;;2DAIjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB;GA/SwB;KAAA", "debugId": null}}, {"offset": {"line": 3384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/app/users/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useState, useEffect, useMemo } from 'react'\r\nimport { useRouter } from 'next/navigation'\r\nimport { useAppSelector } from '@/store'\r\nimport { selectIsAuthenticated, selectUser } from '@/store/slices/authSlice'\r\nimport {\r\n  useGetAllUsersQuery,\r\n  useUpdateUserStatusMutation\r\n} from '@/store/api/adminUserApi'\r\nimport { useUpdateKYCStatusMutation } from '@/store/api/adminKycApi'\r\nimport { useGetAllTransactionsQuery } from '@/store/api/adminPaymentApi'\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Label } from '@/components/ui/label'\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\r\nimport DashboardLayout from '@/components/layout/DashboardLayout'\r\nimport KYCReviewModal from '@/components/modals/KYCReviewModal'\r\nimport {\r\n  Users,\r\n  UserPlus,\r\n  Search,\r\n  Download,\r\n  Upload,\r\n  Edit,\r\n  Trash2,\r\n  Eye,\r\n  UserCheck,\r\n  Clock,\r\n  Mail,\r\n  Phone,\r\n  CheckCircle,\r\n  XCircle,\r\n  AlertTriangle,\r\n  FileText,\r\n  Award,\r\n  Calendar,\r\n  Wallet\r\n} from 'lucide-react'\r\nimport { toast } from 'sonner'\r\n\r\nexport default function UsersPage() {\r\n  const router = useRouter()\r\n  const isAuthenticated = useAppSelector(selectIsAuthenticated)\r\n  const currentUser = useAppSelector(selectUser)\r\n\r\n  const [selectedFilter, setSelectedFilter] = useState('all')\r\n  const [searchQuery, setSearchQuery] = useState('')\r\n  const [currentPage, setCurrentPage] = useState(1)\r\n  const [pageSize, setPageSize] = useState(20)\r\n  const [selectedUsers, setSelectedUsers] = useState<string[]>([])\r\n  const [showBulkActions, setShowBulkActions] = useState(false)\r\n  const [kycModalOpen, setKycModalOpen] = useState(false)\r\n  const [selectedUserForKYC, setSelectedUserForKYC] = useState<any>(null)\r\n  const [activeTab, setActiveTab] = useState('all')\r\n\r\n  useEffect(() => {\r\n    setCurrentPage(1)\r\n  }, [selectedFilter, searchQuery])\r\n\r\n  const {\r\n    data: usersResponse,\r\n    isLoading,\r\n    refetch: refetchUsers\r\n  } = useGetAllUsersQuery({\r\n    page: currentPage,\r\n    limit: pageSize,\r\n    search: searchQuery || undefined,\r\n    status: selectedFilter !== 'all' ? selectedFilter : undefined,\r\n    kycStatus: activeTab !== 'all' ? activeTab : undefined\r\n  })\r\n\r\n  const [updateUserStatus] = useUpdateUserStatusMutation()\r\n  const [updateKYCStatus, { isLoading: isUpdatingKYC }] = useUpdateKYCStatusMutation()\r\n\r\n  // Get transaction data for enhanced user details\r\n  const { data: transactionsData } = useGetAllTransactionsQuery({\r\n    page: 1,\r\n    limit: 1000 // Get all transactions for stats calculation\r\n  })\r\n\r\n  const users = usersResponse?.users || []\r\n  const stats = usersResponse?.statistics || {\r\n    totalUsers: 0,\r\n    activeUsers: 0,\r\n    pendingKyc: 0,\r\n    newThisMonth: 0\r\n  }\r\n  const pagination = usersResponse?.pagination || {\r\n    page: 1,\r\n    limit: 20,\r\n    total: 0,\r\n    pages: 0\r\n  }\r\n\r\n  // Handle different API response formats\r\n  const pendingKYC = (stats as any).pendingKYC || (stats as any).pendingKyc || 0\r\n  const adminUsers = (stats as any).adminUsers || 0\r\n\r\n  // For development - allow access without strict authentication\r\n  // if (!isAuthenticated) {\r\n  //   return (\r\n  //     <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\r\n  //       <div className=\"text-center\">\r\n  //         <div className=\"bg-white p-8 rounded-lg shadow-lg\">\r\n  //           <h1 className=\"text-2xl font-bold mb-4 text-gray-900\">Authentication Required</h1>\r\n  //           <p className=\"text-gray-600 mb-4\">Please log in to access the user management system.</p>\r\n  //           <Button onClick={() => router.push('/auth/login')} className=\"bg-blue-600 hover:bg-blue-700\">\r\n  //             Go to Login\r\n  //           </Button>\r\n  //         </div>\r\n  //       </div>\r\n  //     </div>\r\n  //   )\r\n  // }\r\n\r\n  // if (!currentUser || !['ADMIN', 'SUBADMIN'].includes(currentUser.role)) {\r\n  //   return (\r\n  //     <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\r\n  //       <div className=\"text-center\">\r\n  //         <div className=\"bg-white p-8 rounded-lg shadow-lg\">\r\n  //           <h1 className=\"text-2xl font-bold mb-4 text-gray-900\">Access Denied</h1>\r\n  //           <p className=\"text-gray-600 mb-4\">You don't have permission to access user management.</p>\r\n  //           <Button onClick={() => router.push('/dashboard')} className=\"bg-blue-600 hover:bg-blue-700\">\r\n  //             Go to Dashboard\r\n  //           </Button>\r\n  //         </div>\r\n  //       </div>\r\n  //     </div>\r\n  //   )\r\n  // }\r\n\r\n  const getStatusBadge = (status: string) => {\r\n    switch (status) {\r\n      case 'active':\r\n        return <Badge className=\"bg-green-100 text-green-800\">Active</Badge>\r\n      case 'inactive':\r\n        return <Badge className=\"bg-gray-100 text-gray-800\">Inactive</Badge>\r\n      case 'suspended':\r\n        return <Badge className=\"bg-red-100 text-red-800\">Suspended</Badge>\r\n      default:\r\n        return <Badge variant=\"secondary\">{status}</Badge>\r\n    }\r\n  }\r\n\r\n  const getKYCBadge = (user: any) => {\r\n    // Get KYC status from either kycStatus field or kyc.status\r\n    const kycStatus = user.kyc?.status || user.kycStatus || 'not_submitted'\r\n    const documentsCount = user.kyc?.documents?.length || 0\r\n\r\n    switch (kycStatus) {\r\n      case 'approved':\r\n      case 'verified':\r\n        return (\r\n          <Badge className=\"bg-green-100 text-green-800\">\r\n            <CheckCircle className=\"h-3 w-3 mr-1\" />\r\n            KYC Approved {documentsCount > 0 && `(${documentsCount})`}\r\n          </Badge>\r\n        )\r\n      case 'pending':\r\n        return (\r\n          <Badge className=\"bg-yellow-100 text-yellow-800\">\r\n            <Clock className=\"h-3 w-3 mr-1\" />\r\n            KYC Pending {documentsCount > 0 && `(${documentsCount})`}\r\n          </Badge>\r\n        )\r\n      case 'rejected':\r\n        return (\r\n          <Badge className=\"bg-red-100 text-red-800\">\r\n            <XCircle className=\"h-3 w-3 mr-1\" />\r\n            KYC Rejected\r\n          </Badge>\r\n        )\r\n      case 'under_review':\r\n        return (\r\n          <Badge className=\"bg-blue-100 text-blue-800\">\r\n            <Clock className=\"h-3 w-3 mr-1\" />\r\n            Under Review {documentsCount > 0 && `(${documentsCount})`}\r\n          </Badge>\r\n        )\r\n      case 'not_started':\r\n      case 'not_submitted':\r\n        return (\r\n          <Badge className=\"bg-gray-100 text-gray-800\">\r\n            <AlertTriangle className=\"h-3 w-3 mr-1\" />\r\n            KYC Not Started\r\n          </Badge>\r\n        )\r\n      default:\r\n        return <Badge variant=\"secondary\">{kycStatus} {documentsCount > 0 && `(${documentsCount})`}</Badge>\r\n    }\r\n  }\r\n\r\n  const getRoleBadge = (role: string) => {\r\n    switch (role) {\r\n      case 'ADMIN':\r\n        return <Badge className=\"bg-purple-100 text-purple-800\">Admin</Badge>\r\n      case 'SUBADMIN':\r\n        return <Badge className=\"bg-blue-100 text-blue-800\">Sub Admin</Badge>\r\n      case 'SALES':\r\n        return <Badge className=\"bg-orange-100 text-orange-800\">Sales</Badge>\r\n      case 'USER':\r\n        return <Badge className=\"bg-gray-100 text-gray-800\">User</Badge>\r\n      default:\r\n        return <Badge variant=\"secondary\">{role}</Badge>\r\n    }\r\n  }\r\n\r\n  const getKYCActionButtons = (user: any) => {\r\n    if (user.kycStatus === 'pending') {\r\n      return (\r\n        <div className=\"flex items-center gap-2 mt-2\">\r\n          <Button\r\n            size=\"sm\"\r\n            onClick={() => handleKYCApproval(user)}\r\n            disabled={isUpdatingKYC}\r\n            className=\"bg-blue-600 hover:bg-blue-700 text-white\"\r\n          >\r\n            <FileText className=\"h-4 w-4 mr-1\" />\r\n            Review KYC\r\n          </Button>\r\n        </div>\r\n      )\r\n    }\r\n\r\n    if (user.kycStatus === 'under_review') {\r\n      return (\r\n        <div className=\"flex items-center gap-2 mt-2\">\r\n          <Button\r\n            size=\"sm\"\r\n            variant=\"outline\"\r\n            onClick={() => router.push(`/users/${user._id || user.id}`)}\r\n            className=\"border-blue-300 text-blue-700\"\r\n          >\r\n            <FileText className=\"h-4 w-4 mr-1\" />\r\n            Review KYC\r\n          </Button>\r\n        </div>\r\n      )\r\n    }\r\n\r\n    if (user.kycStatus === 'not_started') {\r\n      return (\r\n        <div className=\"mt-2\">\r\n          <Badge className=\"bg-gray-100 text-gray-600\">\r\n            <AlertTriangle className=\"h-3 w-3 mr-1\" />\r\n            KYC Not Started\r\n          </Badge>\r\n        </div>\r\n      )\r\n    }\r\n\r\n    return null\r\n  }\r\n\r\n  const filteredUsers = useMemo(() => {\r\n    if (!searchQuery && selectedFilter === 'all') {\r\n      return users\r\n    }\r\n\r\n    return users.filter((user: any) => {\r\n      const matchesFilter = selectedFilter === 'all' ||\r\n                           user.status === selectedFilter ||\r\n                           (selectedFilter === 'pending' && user.kycStatus === 'pending') ||\r\n                           (selectedFilter === 'admin' && ['ADMIN', 'SUBADMIN'].includes(user.role))\r\n\r\n      const matchesSearch = !searchQuery ||\r\n                           `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n                           user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n                           (user.phone && user.phone.includes(searchQuery))\r\n\r\n      return matchesFilter && matchesSearch\r\n    })\r\n  }, [users, selectedFilter, searchQuery])\r\n\r\n  const handleDeleteUser = async (userId: string) => {\r\n    if (confirm('Are you sure you want to block this user?')) {\r\n      try {\r\n        await updateUserStatus({ userId, status: 'blocked', reason: 'Blocked by admin' }).unwrap()\r\n        toast.success('User blocked successfully')\r\n        refetchUsers()\r\n      } catch (error: any) {\r\n        toast.error(error?.data?.message || 'Failed to block user')\r\n      }\r\n    }\r\n  }\r\n\r\n  const handleKYCApproval = (user: any) => {\r\n    setSelectedUserForKYC(user)\r\n    setKycModalOpen(true)\r\n  }\r\n\r\n  const handleKYCModalUpdate = async (action: 'approve' | 'reject', data: any) => {\r\n    try {\r\n      await updateKYCStatus({\r\n        id: selectedUserForKYC.kycId || selectedUserForKYC._id,\r\n        data\r\n      }).unwrap()\r\n\r\n      toast.success(`KYC ${action}d successfully`)\r\n      refetchUsers()\r\n    } catch (error: any) {\r\n      toast.error(error?.data?.message || `Failed to ${action} KYC`)\r\n      throw error // Re-throw to let modal handle it\r\n    }\r\n  }\r\n\r\n  const handleCloseKYCModal = () => {\r\n    setKycModalOpen(false)\r\n    setSelectedUserForKYC(null)\r\n  }\r\n\r\n  const handleUserSelection = (userId: string, checked: boolean) => {\r\n    if (checked) {\r\n      setSelectedUsers(prev => [...prev, userId])\r\n    } else {\r\n      setSelectedUsers(prev => prev.filter(id => id !== userId))\r\n    }\r\n  }\r\n\r\n  const handleSelectAll = (checked: boolean) => {\r\n    if (checked) {\r\n      const pendingKYCUsers = filteredUsers\r\n        .filter((user: any) => user.kycStatus === 'pending')\r\n        .map((user: any) => user._id || user.id)\r\n      setSelectedUsers(pendingKYCUsers)\r\n    } else {\r\n      setSelectedUsers([])\r\n    }\r\n  }\r\n\r\n  const handleBulkKYCAction = async (action: 'approve' | 'reject') => {\r\n    if (selectedUsers.length === 0) {\r\n      toast.error('Please select users first')\r\n      return\r\n    }\r\n\r\n    const confirmMessage = `Are you sure you want to ${action} KYC for ${selectedUsers.length} selected user(s)?`\r\n    if (confirm(confirmMessage)) {\r\n      try {\r\n        // Note: This would need a bulk update API endpoint\r\n        for (const userId of selectedUsers) {\r\n          await updateKYCStatus({\r\n            id: userId,\r\n            data: {\r\n              status: action === 'approve' ? 'approved' : 'rejected',\r\n              rejectionReason: action === 'reject' ? 'Bulk rejection' : undefined,\r\n              level: action === 'approve' ? 'basic' : undefined\r\n            }\r\n          }).unwrap()\r\n        }\r\n\r\n        toast.success(`Bulk ${action}ed ${selectedUsers.length} KYC(s) successfully`)\r\n        setSelectedUsers([])\r\n        refetchUsers()\r\n      } catch (error: any) {\r\n        toast.error(error?.data?.message || `Failed to bulk ${action} KYCs`)\r\n      }\r\n    }\r\n  }\r\n\r\n  const userFilters = [\r\n    { id: 'all', label: 'All Users', count: stats.totalUsers },\r\n    { id: 'active', label: 'Active', count: stats.activeUsers },\r\n    { id: 'pending', label: 'Pending KYC', count: pendingKYC },\r\n    { id: 'admin', label: 'Admins', count: adminUsers }\r\n  ]\r\n\r\n  const kycTabs = useMemo(() => {\r\n    const notSubmittedCount = users.filter((u: any) => !u.kyc || u.kyc.status === 'not_submitted' || (u as any).kycStatus === 'not_submitted').length\r\n    const pendingCount = users.filter((u: any) => u.kyc?.status === 'pending' || (u as any).kycStatus === 'pending').length\r\n    const underReviewCount = users.filter((u: any) => u.kyc?.status === 'under_review' || (u as any).kycStatus === 'under_review').length\r\n    const approvedCount = users.filter((u: any) => u.kyc?.status === 'approved' || (u as any).kycStatus === 'approved').length\r\n    const rejectedCount = users.filter((u: any) => u.kyc?.status === 'rejected' || (u as any).kycStatus === 'rejected').length\r\n\r\n    return [\r\n      {\r\n        id: 'all',\r\n        label: 'All Users',\r\n        count: stats.totalUsers,\r\n        color: 'bg-gray-100 text-gray-700',\r\n        description: 'All registered users'\r\n      },\r\n      {\r\n        id: 'not_submitted',\r\n        label: 'Not Submitted',\r\n        count: notSubmittedCount,\r\n        color: 'bg-gray-100 text-gray-700',\r\n        description: 'Users who haven\\'t started KYC'\r\n      },\r\n      {\r\n        id: 'pending',\r\n        label: 'Pending Review',\r\n        count: pendingCount,\r\n        color: 'bg-yellow-100 text-yellow-700',\r\n        description: 'KYC submitted, awaiting review'\r\n      },\r\n      {\r\n        id: 'under_review',\r\n        label: 'Under Review',\r\n        count: underReviewCount,\r\n        color: 'bg-blue-100 text-blue-700',\r\n        description: 'KYC currently being reviewed'\r\n      },\r\n      {\r\n        id: 'approved',\r\n        label: 'Approved',\r\n        count: approvedCount,\r\n        color: 'bg-green-100 text-green-700',\r\n        description: 'KYC verified and approved'\r\n      },\r\n      {\r\n        id: 'rejected',\r\n        label: 'Rejected',\r\n        count: rejectedCount,\r\n        color: 'bg-red-100 text-red-700',\r\n        description: 'KYC rejected, needs resubmission'\r\n      }\r\n    ]\r\n  }, [users, stats.totalUsers])\r\n\r\n  return (\r\n    <DashboardLayout>\r\n      <div className=\"p-6 space-y-6\">\r\n        <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-6 text-white\">\r\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n            <div>\r\n              <h1 className=\"text-3xl font-bold mb-2 flex items-center gap-3\">\r\n                <Users className=\"h-8 w-8\" />\r\n                User Management\r\n              </h1>\r\n              <p className=\"text-blue-100\">Manage users, roles, and permissions with advanced analytics</p>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center gap-3\">\r\n              <Button \r\n                variant=\"secondary\"\r\n                className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\r\n              >\r\n                <Download className=\"h-4 w-4 mr-2\" />\r\n                Export Data\r\n              </Button>\r\n              \r\n              <Button \r\n                variant=\"secondary\"\r\n                className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\r\n              >\r\n                <Upload className=\"h-4 w-4 mr-2\" />\r\n                Import Users\r\n              </Button>\r\n              \r\n              <Button \r\n                onClick={() => router.push('/users/add')}\r\n                className=\"bg-yellow-500 hover:bg-yellow-600 text-black font-semibold\"\r\n              >\r\n                <UserPlus className=\"h-4 w-4 mr-2\" />\r\n                Add User\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\r\n          <Card>\r\n            <CardContent className=\"p-6\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Total Users</p>\r\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.totalUsers.toLocaleString()}</p>\r\n                </div>\r\n                <Users className=\"h-8 w-8 text-blue-600\" />\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardContent className=\"p-6\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Active Users</p>\r\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.activeUsers.toLocaleString()}</p>\r\n                </div>\r\n                <UserCheck className=\"h-8 w-8 text-green-600\" />\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardContent className=\"p-6\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Pending KYC</p>\r\n                  <p className=\"text-2xl font-bold text-gray-900\">{pendingKYC.toLocaleString()}</p>\r\n                  <p className=\"text-xs text-yellow-600 mt-1\">Needs Review</p>\r\n                </div>\r\n                <Clock className=\"h-8 w-8 text-yellow-600\" />\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardContent className=\"p-6\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">KYC Approved</p>\r\n                  <p className=\"text-2xl font-bold text-gray-900\">{(stats.totalUsers - pendingKYC).toLocaleString()}</p>\r\n                  <p className=\"text-xs text-green-600 mt-1\">Verified Users</p>\r\n                </div>\r\n                <Award className=\"h-8 w-8 text-green-600\" />\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* KYC Status Tabs */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <FileText className=\"h-5 w-5\" />\r\n              KYC Status Overview\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3\">\r\n              {kycTabs.map((tab) => (\r\n                <button\r\n                  key={tab.id}\r\n                  onClick={() => setActiveTab(tab.id)}\r\n                  className={`p-4 rounded-lg text-left transition-all border-2 ${\r\n                    activeTab === tab.id\r\n                      ? 'bg-blue-600 text-white border-blue-600 shadow-lg'\r\n                      : `${tab.color} border-transparent hover:shadow-md hover:border-gray-200`\r\n                  }`}\r\n                >\r\n                  <div className=\"flex items-center justify-between mb-2\">\r\n                    <span className=\"text-sm font-medium\">{tab.label}</span>\r\n                    <span className={`px-2 py-1 rounded-full text-xs font-semibold ${\r\n                      activeTab === tab.id\r\n                        ? 'bg-white/20 text-white'\r\n                        : 'bg-white/60 text-gray-700'\r\n                    }`}>\r\n                      {tab.count}\r\n                    </span>\r\n                  </div>\r\n                  <p className={`text-xs ${\r\n                    activeTab === tab.id ? 'text-white/80' : 'text-gray-600'\r\n                  }`}>\r\n                    {tab.description}\r\n                  </p>\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* KYC Quick Actions */}\r\n        {pendingKYC > 0 && (\r\n          <Card className=\"border-yellow-200 bg-yellow-50\">\r\n            <CardContent className=\"p-6\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"p-2 bg-yellow-100 rounded-lg\">\r\n                    <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\r\n                  </div>\r\n                  <div>\r\n                    <h3 className=\"font-medium text-yellow-800\">KYC Reviews Pending</h3>\r\n                    <p className=\"text-sm text-yellow-700\">\r\n                      {pendingKYC} user{pendingKYC > 1 ? 's' : ''} waiting for KYC approval\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"border-yellow-300 text-yellow-700 hover:bg-yellow-100\"\r\n                  onClick={() => setActiveTab('pending')}\r\n                >\r\n                  <FileText className=\"h-4 w-4 mr-2\" />\r\n                  Review Pending KYCs\r\n                </Button>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        )}\r\n\r\n        <Card>\r\n          <CardContent className=\"p-6\">\r\n            <div className=\"flex flex-col lg:flex-row gap-4\">\r\n              <div className=\"flex-1\">\r\n                <Label className=\"text-sm font-medium mb-2 block\">Search Users</Label>\r\n                <div className=\"relative\">\r\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\r\n                  <Input\r\n                    placeholder=\"Search by name, email, or phone...\"\r\n                    value={searchQuery}\r\n                    onChange={(e) => setSearchQuery(e.target.value)}\r\n                    className=\"pl-10\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"lg:w-64\">\r\n                <Label className=\"text-sm font-medium mb-2 block\">Filter by Status</Label>\r\n                <Select value={selectedFilter} onValueChange={setSelectedFilter}>\r\n                  <SelectTrigger>\r\n                    <SelectValue />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    {userFilters.map((filter) => (\r\n                      <SelectItem key={filter.id} value={filter.id}>\r\n                        {filter.label} ({filter.count})\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Users List</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            {isLoading ? (\r\n              <div className=\"flex items-center justify-center py-8\">\r\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n                <span className=\"ml-2 text-gray-600\">Loading users...</span>\r\n              </div>\r\n            ) : filteredUsers.length === 0 ? (\r\n              <div className=\"text-center py-8\">\r\n                <Users className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No users found</h3>\r\n                <p className=\"text-gray-600\">Try adjusting your search or filter criteria.</p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"space-y-4\">\r\n                {filteredUsers.map((user: any) => (\r\n                  <div key={user._id || user.id} className=\"p-6 border border-gray-200 rounded-lg hover:shadow-lg transition-all duration-200 bg-white\">\r\n                    <div className=\"flex items-start justify-between\">\r\n                      <div className=\"flex items-start gap-4 flex-1\">\r\n                        <Avatar className=\"h-14 w-14\">\r\n                          <AvatarImage src={user.profileImage || user.avatar} />\r\n                          <AvatarFallback className=\"bg-blue-100 text-blue-600 font-semibold text-lg\">\r\n                            {user.firstName?.charAt(0)?.toUpperCase()}{user.lastName?.charAt(0)?.toUpperCase()}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div className=\"flex-1\">\r\n                          {/* Header with name and status */}\r\n                          <div className=\"flex items-center gap-3 mb-3\">\r\n                            <h4 className=\"font-semibold text-gray-900 text-lg\">{user.firstName} {user.lastName}</h4>\r\n                            {getStatusBadge(user.status)}\r\n                            {getRoleBadge(user.role)}\r\n                            {user.emailVerified && (\r\n                              <Badge className=\"bg-blue-100 text-blue-800 text-xs\">\r\n                                <Mail className=\"h-3 w-3 mr-1\" />\r\n                                Verified\r\n                              </Badge>\r\n                            )}\r\n                          </div>\r\n\r\n                          {/* Contact Information */}\r\n                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 mb-4\">\r\n                            <div className=\"flex items-center gap-2 text-sm text-gray-600\">\r\n                              <Mail className=\"h-4 w-4\" />\r\n                              <span className=\"truncate\">{user.email}</span>\r\n                            </div>\r\n                            {user.phone && (\r\n                              <div className=\"flex items-center gap-2 text-sm text-gray-600\">\r\n                                <Phone className=\"h-4 w-4\" />\r\n                                <span>{user.phone}</span>\r\n                              </div>\r\n                            )}\r\n                            <div className=\"flex items-center gap-2 text-sm text-gray-600\">\r\n                              <Calendar className=\"h-4 w-4\" />\r\n                              <span>Joined {new Date(user.createdAt).toLocaleDateString()}</span>\r\n                            </div>\r\n                            <div className=\"flex items-center gap-2 text-sm text-gray-600\">\r\n                              <Wallet className=\"h-4 w-4\" />\r\n                              <span className=\"font-medium\">₹{(user.walletBalance || 0).toLocaleString()}</span>\r\n                            </div>\r\n                          </div>\r\n\r\n                          {/* KYC Status Section */}\r\n                          <div className=\"mb-4\">\r\n                            <div className=\"flex items-center gap-3 mb-2\">\r\n                              <div className=\"flex items-center gap-2\">\r\n                                <FileText className=\"h-4 w-4\" />\r\n                                <span className=\"text-sm font-medium text-gray-700\">KYC Status:</span>\r\n                                {getKYCBadge(user)}\r\n                              </div>\r\n                              {user.kyc?.submittedAt && (\r\n                                <span className=\"text-xs text-gray-500\">\r\n                                  Submitted {new Date(user.kyc.submittedAt).toLocaleDateString()}\r\n                                </span>\r\n                              )}\r\n                            </div>\r\n\r\n                            {/* KYC Documents Info */}\r\n                            {(user.kyc?.documents && user.kyc.documents.length > 0) && (\r\n                              <div className=\"text-xs text-gray-600 bg-gray-50 p-2 rounded\">\r\n                                <span className=\"font-medium\">Documents:</span> {user.kyc.documents.length} uploaded\r\n                                {user.kyc.documents.some((doc: any) => doc.category === 'identity') && (\r\n                                  <span className=\"ml-2 text-green-600\">• ID ✓</span>\r\n                                )}\r\n                                {user.kyc.documents.some((doc: any) => doc.category === 'address') && (\r\n                                  <span className=\"ml-2 text-blue-600\">• Address ✓</span>\r\n                                )}\r\n                                {user.kyc.documents.some((doc: any) => doc.category === 'income') && (\r\n                                  <span className=\"ml-2 text-purple-600\">• Income ✓</span>\r\n                                )}\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n\r\n                          {/* User Statistics */}\r\n                          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\r\n                            <div className=\"bg-blue-50 p-3 rounded-lg\">\r\n                              <div className=\"text-xs font-medium text-blue-700 mb-1\">Investments</div>\r\n                              <div className=\"text-sm font-semibold text-blue-600\">{user.totalInvestments || 0}</div>\r\n                            </div>\r\n                            <div className=\"bg-green-50 p-3 rounded-lg\">\r\n                              <div className=\"text-xs font-medium text-green-700 mb-1\">Transactions</div>\r\n                              <div className=\"text-sm font-semibold text-green-600\">{user.totalTransactions || 0}</div>\r\n                            </div>\r\n                            <div className=\"bg-purple-50 p-3 rounded-lg\">\r\n                              <div className=\"text-xs font-medium text-purple-700 mb-1\">Referrals</div>\r\n                              <div className=\"text-sm font-semibold text-purple-600\">{user.referralCount || 0}</div>\r\n                            </div>\r\n                            <div className=\"bg-orange-50 p-3 rounded-lg\">\r\n                              <div className=\"text-xs font-medium text-orange-700 mb-1\">Last Login</div>\r\n                              <div className=\"text-xs font-semibold text-orange-600\">\r\n                                {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString() : 'Never'}\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"outline\"\r\n                          onClick={() => router.push(`/users/${user._id || user.id}`)}\r\n                          className=\"border-blue-300 text-blue-700 hover:bg-blue-50\"\r\n                        >\r\n                          <Eye className=\"h-4 w-4 mr-2\" />\r\n                          View Details\r\n                        </Button>\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"outline\"\r\n                          onClick={() => router.push(`/users/${user._id || user.id}/edit`)}\r\n                          className=\"border-green-300 text-green-700 hover:bg-green-50\"\r\n                        >\r\n                          <Edit className=\"h-4 w-4 mr-2\" />\r\n                          Edit\r\n                        </Button>\r\n                        {true && (\r\n                          <Button\r\n                            size=\"sm\"\r\n                            variant=\"outline\"\r\n                            onClick={() => handleDeleteUser(user._id || user.id)}\r\n                            className=\"border-red-300 text-red-700 hover:bg-red-50\"\r\n                          >\r\n                            <Trash2 className=\"h-4 w-4\" />\r\n                          </Button>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <div className=\"mt-3 pt-3 border-t border-gray-100\">\r\n                      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\r\n                        <div>\r\n                          <p className=\"text-gray-600\">Joined</p>\r\n                          <p className=\"font-medium\">{new Date(user.createdAt).toLocaleDateString()}</p>\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-gray-600\">Last Active</p>\r\n                          <p className=\"font-medium\">{user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString() : 'Never'}</p>\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-gray-600\">Wallet Balance</p>\r\n                          <p className=\"font-medium\">₹{(user.wallet?.balance || 0).toLocaleString()}</p>\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-gray-600\">Total Invested</p>\r\n                          <p className=\"font-medium text-green-600\">₹{(user.wallet?.totalInvested || 0).toLocaleString()}</p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* KYC Action Buttons */}\r\n                      {getKYCActionButtons(user)}\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            \r\n            {/* Enhanced Pagination */}\r\n            {pagination.total > 0 && (\r\n              <div className=\"mt-6 border-t pt-6\">\r\n                <div className=\"flex flex-col sm:flex-row items-center justify-between gap-4\">\r\n                  <div className=\"flex items-center gap-4\">\r\n                    <div className=\"text-sm text-gray-600\">\r\n                      Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} users\r\n                    </div>\r\n                    <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>\r\n                      <SelectTrigger className=\"w-20\">\r\n                        <SelectValue />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        <SelectItem value=\"10\">10</SelectItem>\r\n                        <SelectItem value=\"20\">20</SelectItem>\r\n                        <SelectItem value=\"50\">50</SelectItem>\r\n                        <SelectItem value=\"100\">100</SelectItem>\r\n                      </SelectContent>\r\n                    </Select>\r\n                    <span className=\"text-sm text-gray-600\">per page</span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => setCurrentPage(1)}\r\n                      disabled={pagination.page === 1}\r\n                    >\r\n                      First\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => setCurrentPage(Math.max(1, pagination.page - 1))}\r\n                      disabled={pagination.page === 1}\r\n                    >\r\n                      Previous\r\n                    </Button>\r\n                    <div className=\"flex items-center gap-1\">\r\n                      {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {\r\n                        const page = Math.max(1, Math.min(pagination.pages - 4, pagination.page - 2)) + i\r\n                        return (\r\n                          <Button\r\n                            key={page}\r\n                            variant={page === pagination.page ? \"default\" : \"outline\"}\r\n                            size=\"sm\"\r\n                            onClick={() => setCurrentPage(page)}\r\n                            className=\"w-8 h-8 p-0\"\r\n                          >\r\n                            {page}\r\n                          </Button>\r\n                        )\r\n                      })}\r\n                    </div>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => setCurrentPage(Math.min(pagination.pages, pagination.page + 1))}\r\n                      disabled={pagination.page >= pagination.pages}\r\n                    >\r\n                      Next\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => setCurrentPage(pagination.pages)}\r\n                      disabled={pagination.page >= pagination.pages}\r\n                    >\r\n                      Last\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* KYC Review Modal */}\r\n        <KYCReviewModal\r\n          isOpen={kycModalOpen}\r\n          onClose={handleCloseKYCModal}\r\n          user={selectedUserForKYC}\r\n          onKYCUpdate={handleKYCModalUpdate}\r\n          isLoading={isUpdatingKYC}\r\n        />\r\n      </div>\r\n    </DashboardLayout>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;;;AA1CA;;;;;;;;;;;;;;;;;;;AA4Ce,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,kBAAkB,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,wBAAqB;IAC5D,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,aAAU;IAE7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAO;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;+BAAE;YACR,eAAe;QACjB;8BAAG;QAAC;QAAgB;KAAY;IAEhC,MAAM,EACJ,MAAM,aAAa,EACnB,SAAS,EACT,SAAS,YAAY,EACtB,GAAG,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD,EAAE;QACtB,MAAM;QACN,OAAO;QACP,QAAQ,eAAe;QACvB,QAAQ,mBAAmB,QAAQ,iBAAiB;QACpD,WAAW,cAAc,QAAQ,YAAY;IAC/C;IAEA,MAAM,CAAC,iBAAiB,GAAG,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD;IACrD,MAAM,CAAC,iBAAiB,EAAE,WAAW,aAAa,EAAE,CAAC,GAAG,CAAA,GAAA,qIAAA,CAAA,6BAA0B,AAAD;IAEjF,iDAAiD;IACjD,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,6BAA0B,AAAD,EAAE;QAC5D,MAAM;QACN,OAAO,KAAK,6CAA6C;IAC3D;IAEA,MAAM,QAAQ,eAAe,SAAS,EAAE;IACxC,MAAM,QAAQ,eAAe,cAAc;QACzC,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,cAAc;IAChB;IACA,MAAM,aAAa,eAAe,cAAc;QAC9C,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;IACT;IAEA,wCAAwC;IACxC,MAAM,aAAa,AAAC,MAAc,UAAU,IAAI,AAAC,MAAc,UAAU,IAAI;IAC7E,MAAM,aAAa,AAAC,MAAc,UAAU,IAAI;IAEhD,+DAA+D;IAC/D,0BAA0B;IAC1B,aAAa;IACb,iFAAiF;IACjF,sCAAsC;IACtC,8DAA8D;IAC9D,+FAA+F;IAC/F,sGAAsG;IACtG,0GAA0G;IAC1G,0BAA0B;IAC1B,sBAAsB;IACtB,iBAAiB;IACjB,eAAe;IACf,aAAa;IACb,MAAM;IACN,IAAI;IAEJ,2EAA2E;IAC3E,aAAa;IACb,iFAAiF;IACjF,sCAAsC;IACtC,8DAA8D;IAC9D,qFAAqF;IACrF,uGAAuG;IACvG,yGAAyG;IACzG,8BAA8B;IAC9B,sBAAsB;IACtB,iBAAiB;IACjB,eAAe;IACf,aAAa;IACb,MAAM;IACN,IAAI;IAEJ,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD,KAAK;gBACH,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD,KAAK;gBACH,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA0B;;;;;;YACpD;gBACE,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QACvC;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,2DAA2D;QAC3D,MAAM,YAAY,KAAK,GAAG,EAAE,UAAU,KAAK,SAAS,IAAI;QACxD,MAAM,iBAAiB,KAAK,GAAG,EAAE,WAAW,UAAU;QAEtD,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBACE,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,4TAAC,kTAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAAiB;wBAC1B,iBAAiB,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;;;;;;;YAG/D,KAAK;gBACH,qBACE,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,4TAAC,2RAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;wBACrB,iBAAiB,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;;;;;;;YAG9D,KAAK;gBACH,qBACE,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,4TAAC,mSAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAI1C,KAAK;gBACH,qBACE,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,4TAAC,2RAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;wBACpB,iBAAiB,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;;;;;;;YAG/D,KAAK;YACL,KAAK;gBACH,qBACE,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,4TAAC,+SAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAIhD;gBACE,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;;wBAAa;wBAAU;wBAAE,iBAAiB,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;;;;;;;QAC9F;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;gBACH,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD,KAAK;gBACH,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;gBACH,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD;gBACE,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QACvC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,KAAK,SAAS,KAAK,WAAW;YAChC,qBACE,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,qIAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAS,IAAM,kBAAkB;oBACjC,UAAU;oBACV,WAAU;;sCAEV,4TAAC,qSAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;QAK7C;QAEA,IAAI,KAAK,SAAS,KAAK,gBAAgB;YACrC,qBACE,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,qIAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAQ;oBACR,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI,KAAK,EAAE,EAAE;oBAC1D,WAAU;;sCAEV,4TAAC,qSAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;QAK7C;QAEA,IAAI,KAAK,SAAS,KAAK,eAAe;YACpC,qBACE,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,4TAAC,+SAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;QAKlD;QAEA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,UAAO,AAAD;4CAAE;YAC5B,IAAI,CAAC,eAAe,mBAAmB,OAAO;gBAC5C,OAAO;YACT;YAEA,OAAO,MAAM,MAAM;oDAAC,CAAC;oBACnB,MAAM,gBAAgB,mBAAmB,SACpB,KAAK,MAAM,KAAK,kBACf,mBAAmB,aAAa,KAAK,SAAS,KAAK,aACnD,mBAAmB,WAAW;wBAAC;wBAAS;qBAAW,CAAC,QAAQ,CAAC,KAAK,IAAI;oBAE5F,MAAM,gBAAgB,CAAC,eACF,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACnF,KAAK,KAAK,EAAE,cAAc,SAAS,YAAY,WAAW,OACzD,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC;oBAExD,OAAO,iBAAiB;gBAC1B;;QACF;2CAAG;QAAC;QAAO;QAAgB;KAAY;IAEvC,MAAM,mBAAmB,OAAO;QAC9B,IAAI,QAAQ,8CAA8C;YACxD,IAAI;gBACF,MAAM,iBAAiB;oBAAE;oBAAQ,QAAQ;oBAAW,QAAQ;gBAAmB,GAAG,MAAM;gBACxF,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF,EAAE,OAAO,OAAY;gBACnB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,MAAM,WAAW;YACtC;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,sBAAsB;QACtB,gBAAgB;IAClB;IAEA,MAAM,uBAAuB,OAAO,QAA8B;QAChE,IAAI;YACF,MAAM,gBAAgB;gBACpB,IAAI,mBAAmB,KAAK,IAAI,mBAAmB,GAAG;gBACtD;YACF,GAAG,MAAM;YAET,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,OAAO,cAAc,CAAC;YAC3C;QACF,EAAE,OAAO,OAAY;YACnB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,MAAM,WAAW,CAAC,UAAU,EAAE,OAAO,IAAI,CAAC;YAC7D,MAAM,MAAM,kCAAkC;;QAChD;IACF;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB;QAChB,sBAAsB;IACxB;IAEA,MAAM,sBAAsB,CAAC,QAAgB;QAC3C,IAAI,SAAS;YACX,iBAAiB,CAAA,OAAQ;uBAAI;oBAAM;iBAAO;QAC5C,OAAO;YACL,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO;QACpD;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS;YACX,MAAM,kBAAkB,cACrB,MAAM,CAAC,CAAC,OAAc,KAAK,SAAS,KAAK,WACzC,GAAG,CAAC,CAAC,OAAc,KAAK,GAAG,IAAI,KAAK,EAAE;YACzC,iBAAiB;QACnB,OAAO;YACL,iBAAiB,EAAE;QACrB;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,iBAAiB,CAAC,yBAAyB,EAAE,OAAO,SAAS,EAAE,cAAc,MAAM,CAAC,kBAAkB,CAAC;QAC7G,IAAI,QAAQ,iBAAiB;YAC3B,IAAI;gBACF,mDAAmD;gBACnD,KAAK,MAAM,UAAU,cAAe;oBAClC,MAAM,gBAAgB;wBACpB,IAAI;wBACJ,MAAM;4BACJ,QAAQ,WAAW,YAAY,aAAa;4BAC5C,iBAAiB,WAAW,WAAW,mBAAmB;4BAC1D,OAAO,WAAW,YAAY,UAAU;wBAC1C;oBACF,GAAG,MAAM;gBACX;gBAEA,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,OAAO,GAAG,EAAE,cAAc,MAAM,CAAC,oBAAoB,CAAC;gBAC5E,iBAAiB,EAAE;gBACnB;YACF,EAAE,OAAO,OAAY;gBACnB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,MAAM,WAAW,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC;YACrE;QACF;IACF;IAEA,MAAM,cAAc;QAClB;YAAE,IAAI;YAAO,OAAO;YAAa,OAAO,MAAM,UAAU;QAAC;QACzD;YAAE,IAAI;YAAU,OAAO;YAAU,OAAO,MAAM,WAAW;QAAC;QAC1D;YAAE,IAAI;YAAW,OAAO;YAAe,OAAO;QAAW;QACzD;YAAE,IAAI;YAAS,OAAO;YAAU,OAAO;QAAW;KACnD;IAED,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,UAAO,AAAD;sCAAE;YACtB,MAAM,oBAAoB,MAAM,MAAM;8CAAC,CAAC,IAAW,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,MAAM,KAAK,mBAAmB,AAAC,EAAU,SAAS,KAAK;6CAAiB,MAAM;YACjJ,MAAM,eAAe,MAAM,MAAM;8CAAC,CAAC,IAAW,EAAE,GAAG,EAAE,WAAW,aAAa,AAAC,EAAU,SAAS,KAAK;6CAAW,MAAM;YACvH,MAAM,mBAAmB,MAAM,MAAM;8CAAC,CAAC,IAAW,EAAE,GAAG,EAAE,WAAW,kBAAkB,AAAC,EAAU,SAAS,KAAK;6CAAgB,MAAM;YACrI,MAAM,gBAAgB,MAAM,MAAM;8CAAC,CAAC,IAAW,EAAE,GAAG,EAAE,WAAW,cAAc,AAAC,EAAU,SAAS,KAAK;6CAAY,MAAM;YAC1H,MAAM,gBAAgB,MAAM,MAAM;8CAAC,CAAC,IAAW,EAAE,GAAG,EAAE,WAAW,cAAc,AAAC,EAAU,SAAS,KAAK;6CAAY,MAAM;YAE1H,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO,MAAM,UAAU;oBACvB,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,OAAO;oBACP,aAAa;gBACf;aACD;QACH;qCAAG;QAAC;QAAO,MAAM,UAAU;KAAC;IAE5B,qBACE,4TAAC,kJAAA,CAAA,UAAe;kBACd,cAAA,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;;kDACC,4TAAC;wCAAG,WAAU;;0DACZ,4TAAC,2RAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG/B,4TAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAG/B,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,4TAAC,iSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIvC,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,4TAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIrC,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;;0DAEV,4TAAC,qSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAO7C,4TAAC;oBAAI,WAAU;;sCACb,4TAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,4TAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;8DAAoC,MAAM,UAAU,CAAC,cAAc;;;;;;;;;;;;sDAElF,4TAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKvB,4TAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,4TAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;8DAAoC,MAAM,WAAW,CAAC,cAAc;;;;;;;;;;;;sDAEnF,4TAAC,uSAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAK3B,4TAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,4TAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;8DAAoC,WAAW,cAAc;;;;;;8DAC1E,4TAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;sDAE9C,4TAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKvB,4TAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,4TAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;8DAAoC,CAAC,MAAM,UAAU,GAAG,UAAU,EAAE,cAAc;;;;;;8DAC/F,4TAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;sDAE7C,4TAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOzB,4TAAC,mIAAA,CAAA,OAAI;;sCACH,4TAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,4TAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,qSAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIpC,4TAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,4TAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,oBACZ,4TAAC;wCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;wCAClC,WAAW,CAAC,iDAAiD,EAC3D,cAAc,IAAI,EAAE,GAChB,qDACA,GAAG,IAAI,KAAK,CAAC,yDAAyD,CAAC,EAC3E;;0DAEF,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAK,WAAU;kEAAuB,IAAI,KAAK;;;;;;kEAChD,4TAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,cAAc,IAAI,EAAE,GAChB,2BACA,6BACJ;kEACC,IAAI,KAAK;;;;;;;;;;;;0DAGd,4TAAC;gDAAE,WAAW,CAAC,QAAQ,EACrB,cAAc,IAAI,EAAE,GAAG,kBAAkB,iBACzC;0DACC,IAAI,WAAW;;;;;;;uCArBb,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;gBA8BpB,aAAa,mBACZ,4TAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,4TAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,+SAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,4TAAC;oDAAE,WAAU;;wDACV;wDAAW;wDAAM,aAAa,IAAI,MAAM;wDAAG;;;;;;;;;;;;;;;;;;;8CAIlD,4TAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,aAAa;;sDAE5B,4TAAC,qSAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAQ/C,4TAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,4TAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAiC;;;;;;sDAClD,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,6RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,4TAAC,oIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAiC;;;;;;sDAClD,4TAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAgB,eAAe;;8DAC5C,4TAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,4TAAC,qIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,4TAAC,qIAAA,CAAA,gBAAa;8DACX,YAAY,GAAG,CAAC,CAAC,uBAChB,4TAAC,qIAAA,CAAA,aAAU;4DAAiB,OAAO,OAAO,EAAE;;gEACzC,OAAO,KAAK;gEAAC;gEAAG,OAAO,KAAK;gEAAC;;2DADf,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWxC,4TAAC,mIAAA,CAAA,OAAI;;sCACH,4TAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,4TAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,4TAAC,mIAAA,CAAA,cAAW;;gCACT,0BACC,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;;;;;;sDACf,4TAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;2CAErC,cAAc,MAAM,KAAK,kBAC3B,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,4TAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,4TAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;yDAG/B,4TAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,4TAAC;4CAA8B,WAAU;;8DACvC,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAI,WAAU;;8EACb,4TAAC,qIAAA,CAAA,SAAM;oEAAC,WAAU;;sFAChB,4TAAC,qIAAA,CAAA,cAAW;4EAAC,KAAK,KAAK,YAAY,IAAI,KAAK,MAAM;;;;;;sFAClD,4TAAC,qIAAA,CAAA,iBAAc;4EAAC,WAAU;;gFACvB,KAAK,SAAS,EAAE,OAAO,IAAI;gFAAe,KAAK,QAAQ,EAAE,OAAO,IAAI;;;;;;;;;;;;;8EAGzE,4TAAC;oEAAI,WAAU;;sFAEb,4TAAC;4EAAI,WAAU;;8FACb,4TAAC;oFAAG,WAAU;;wFAAuC,KAAK,SAAS;wFAAC;wFAAE,KAAK,QAAQ;;;;;;;gFAClF,eAAe,KAAK,MAAM;gFAC1B,aAAa,KAAK,IAAI;gFACtB,KAAK,aAAa,kBACjB,4TAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;;sGACf,4TAAC,yRAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;sFAOvC,4TAAC;4EAAI,WAAU;;8FACb,4TAAC;oFAAI,WAAU;;sGACb,4TAAC,yRAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;sGAChB,4TAAC;4FAAK,WAAU;sGAAY,KAAK,KAAK;;;;;;;;;;;;gFAEvC,KAAK,KAAK,kBACT,4TAAC;oFAAI,WAAU;;sGACb,4TAAC,2RAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;sGACjB,4TAAC;sGAAM,KAAK,KAAK;;;;;;;;;;;;8FAGrB,4TAAC;oFAAI,WAAU;;sGACb,4TAAC,iSAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;sGACpB,4TAAC;;gGAAK;gGAAQ,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;8FAE3D,4TAAC;oFAAI,WAAU;;sGACb,4TAAC,6RAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;sGAClB,4TAAC;4FAAK,WAAU;;gGAAc;gGAAE,CAAC,KAAK,aAAa,IAAI,CAAC,EAAE,cAAc;;;;;;;;;;;;;;;;;;;sFAK5E,4TAAC;4EAAI,WAAU;;8FACb,4TAAC;oFAAI,WAAU;;sGACb,4TAAC;4FAAI,WAAU;;8GACb,4TAAC,qSAAA,CAAA,WAAQ;oGAAC,WAAU;;;;;;8GACpB,4TAAC;oGAAK,WAAU;8GAAoC;;;;;;gGACnD,YAAY;;;;;;;wFAEd,KAAK,GAAG,EAAE,6BACT,4TAAC;4FAAK,WAAU;;gGAAwB;gGAC3B,IAAI,KAAK,KAAK,GAAG,CAAC,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;gFAMhE,KAAK,GAAG,EAAE,aAAa,KAAK,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,mBACnD,4TAAC;oFAAI,WAAU;;sGACb,4TAAC;4FAAK,WAAU;sGAAc;;;;;;wFAAiB;wFAAE,KAAK,GAAG,CAAC,SAAS,CAAC,MAAM;wFAAC;wFAC1E,KAAK,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAa,IAAI,QAAQ,KAAK,6BACtD,4TAAC;4FAAK,WAAU;sGAAsB;;;;;;wFAEvC,KAAK,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAa,IAAI,QAAQ,KAAK,4BACtD,4TAAC;4FAAK,WAAU;sGAAqB;;;;;;wFAEtC,KAAK,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAa,IAAI,QAAQ,KAAK,2BACtD,4TAAC;4FAAK,WAAU;sGAAuB;;;;;;;;;;;;;;;;;;sFAO/C,4TAAC;4EAAI,WAAU;;8FACb,4TAAC;oFAAI,WAAU;;sGACb,4TAAC;4FAAI,WAAU;sGAAyC;;;;;;sGACxD,4TAAC;4FAAI,WAAU;sGAAuC,KAAK,gBAAgB,IAAI;;;;;;;;;;;;8FAEjF,4TAAC;oFAAI,WAAU;;sGACb,4TAAC;4FAAI,WAAU;sGAA0C;;;;;;sGACzD,4TAAC;4FAAI,WAAU;sGAAwC,KAAK,iBAAiB,IAAI;;;;;;;;;;;;8FAEnF,4TAAC;oFAAI,WAAU;;sGACb,4TAAC;4FAAI,WAAU;sGAA2C;;;;;;sGAC1D,4TAAC;4FAAI,WAAU;sGAAyC,KAAK,aAAa,IAAI;;;;;;;;;;;;8FAEhF,4TAAC;oFAAI,WAAU;;sGACb,4TAAC;4FAAI,WAAU;sGAA2C;;;;;;sGAC1D,4TAAC;4FAAI,WAAU;sGACZ,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAMhF,4TAAC;4DAAI,WAAU;;8EACb,4TAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI,KAAK,EAAE,EAAE;oEAC1D,WAAU;;sFAEV,4TAAC,uRAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGlC,4TAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,KAAK,CAAC;oEAC/D,WAAU;;sFAEV,4TAAC,kSAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAGlC,sBACC,4TAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,KAAK,GAAG,IAAI,KAAK,EAAE;oEACnD,WAAU;8EAEV,cAAA,4TAAC,iSAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAM1B,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;;sFACC,4TAAC;4EAAE,WAAU;sFAAgB;;;;;;sFAC7B,4TAAC;4EAAE,WAAU;sFAAe,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;8EAEzE,4TAAC;;sFACC,4TAAC;4EAAE,WAAU;sFAAgB;;;;;;sFAC7B,4TAAC;4EAAE,WAAU;sFAAe,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,KAAK;;;;;;;;;;;;8EAEnG,4TAAC;;sFACC,4TAAC;4EAAE,WAAU;sFAAgB;;;;;;sFAC7B,4TAAC;4EAAE,WAAU;;gFAAc;gFAAE,CAAC,KAAK,MAAM,EAAE,WAAW,CAAC,EAAE,cAAc;;;;;;;;;;;;;8EAEzE,4TAAC;;sFACC,4TAAC;4EAAE,WAAU;sFAAgB;;;;;;sFAC7B,4TAAC;4EAAE,WAAU;;gFAA6B;gFAAE,CAAC,KAAK,MAAM,EAAE,iBAAiB,CAAC,EAAE,cAAc;;;;;;;;;;;;;;;;;;;wDAK/F,oBAAoB;;;;;;;;2CAzJf,KAAK,GAAG,IAAI,KAAK,EAAE;;;;;;;;;;gCAiKlC,WAAW,KAAK,GAAG,mBAClB,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;4DAAwB;4DAC3B,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAI;4DAAE;4DAAK,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,WAAW,KAAK;4DAAE;4DAAK,WAAW,KAAK;4DAAC;;;;;;;kEAEpJ,4TAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO,SAAS,QAAQ;wDAAI,eAAe,CAAC,QAAU,YAAY,OAAO;;0EAC/E,4TAAC,qIAAA,CAAA,gBAAa;gEAAC,WAAU;0EACvB,cAAA,4TAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,4TAAC,qIAAA,CAAA,gBAAa;;kFACZ,4TAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAK;;;;;;kFACvB,4TAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAK;;;;;;kFACvB,4TAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAK;;;;;;kFACvB,4TAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAM;;;;;;;;;;;;;;;;;;kEAG5B,4TAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,4TAAC;gDAAI,WAAU;;kEACb,4TAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,eAAe;wDAC9B,UAAU,WAAW,IAAI,KAAK;kEAC/B;;;;;;kEAGD,4TAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,WAAW,IAAI,GAAG;wDAC5D,UAAU,WAAW,IAAI,KAAK;kEAC/B;;;;;;kEAGD,4TAAC;wDAAI,WAAU;kEACZ,MAAM,IAAI,CAAC;4DAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,WAAW,KAAK;wDAAE,GAAG,CAAC,GAAG;4DACzD,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,WAAW,KAAK,GAAG,GAAG,WAAW,IAAI,GAAG,MAAM;4DAChF,qBACE,4TAAC,qIAAA,CAAA,SAAM;gEAEL,SAAS,SAAS,WAAW,IAAI,GAAG,YAAY;gEAChD,MAAK;gEACL,SAAS,IAAM,eAAe;gEAC9B,WAAU;0EAET;+DANI;;;;;wDASX;;;;;;kEAEF,4TAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,WAAW,KAAK,EAAE,WAAW,IAAI,GAAG;wDAC3E,UAAU,WAAW,IAAI,IAAI,WAAW,KAAK;kEAC9C;;;;;;kEAGD,4TAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,eAAe,WAAW,KAAK;wDAC9C,UAAU,WAAW,IAAI,IAAI,WAAW,KAAK;kEAC9C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWb,4TAAC,iJAAA,CAAA,UAAc;oBACb,QAAQ;oBACR,SAAS;oBACT,MAAM;oBACN,aAAa;oBACb,WAAW;;;;;;;;;;;;;;;;;AAKrB;GA70BwB;;QACP,oQAAA,CAAA,YAAS;QACA,wHAAA,CAAA,iBAAc;QAClB,wHAAA,CAAA,iBAAc;QAoB9B,sIAAA,CAAA,sBAAmB;QAQI,sIAAA,CAAA,8BAA2B;QACE,qIAAA,CAAA,6BAA0B;QAG/C,yIAAA,CAAA,6BAA0B;;;KAnCvC", "debugId": null}}]}