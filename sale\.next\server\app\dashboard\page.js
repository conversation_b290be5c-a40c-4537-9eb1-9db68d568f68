(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4211:(e,s,t)=>{Promise.resolve().then(t.bind(t,51545))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31435:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98085).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33873:e=>{"use strict";e.exports=require("path")},44823:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=t(10557),r=t(68490),l=t(13172),i=t.n(l),d=t(68835),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(s,n);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,71819)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\dashboard\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51545:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>V});var a=t(40969);t(73356);var r=t(88251),l=t(66949),i=t(76650),d=t(98085);let n=(0,d.A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),c=(0,d.A)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]]),o=(0,d.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var x=t(79123),m=t(91798),h=t(83427),u=t(35342),p=t(21764);function y({stats:e,previousStats:s}){let t=(e,s)=>{if(!s||!e||0===s)return null;let t=(0,p.tP)(e,s),r=t>0,l=t<0;return(0,a.jsxs)("div",{className:`flex items-center space-x-1 text-xs ${r?"text-green-600":l?"text-red-600":"text-gray-500"}`,children:[r&&(0,a.jsx)(n,{className:"w-3 h-3"}),l&&(0,a.jsx)(c,{className:"w-3 h-3"}),!r&&!l&&(0,a.jsx)(o,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[Math.abs(t||0).toFixed(1),"%"]})]})},r=e.monthlyTarget>0?e.monthlyAchieved/e.monthlyTarget*100:0;return(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ZB,{className:"text-sm font-medium text-gray-600",children:"Total Leads"}),(0,a.jsx)(x.A,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,p.ZV)(e.totalLeads)}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(0,p.ZV)(e.leadsThisMonth)," this month"]})]}),t(e.totalLeads,s?.totalLeads)]}),(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsxs)(i.E,{variant:"info",className:"text-xs",children:[(0,p.ZV)(e.qualifiedLeads)," qualified"]})})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ZB,{className:"text-sm font-medium text-gray-600",children:"Total Sales"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,p.ZV)(e.totalSales)}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(0,p.ZV)(e.salesThisMonth)," this month"]})]}),t(e.totalSales,s?.totalSales)]}),(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsxs)(i.E,{variant:"success",className:"text-xs",children:[(e.conversionRate||0).toFixed(1),"% conversion"]})})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ZB,{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,a.jsx)(h.A,{className:"h-4 w-4 text-yellow-600"})]}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,p.vv)(e.totalRevenue)}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(0,p.vv)(e.revenueThisMonth)," this month"]})]}),t(e.totalRevenue,s?.totalRevenue)]}),(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsxs)(i.E,{variant:"warning",className:"text-xs",children:[(0,p.vv)(e.averageDealSize)," avg deal"]})})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ZB,{className:"text-sm font-medium text-gray-600",children:"Commission"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-purple-600"})]}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,p.vv)(e.commission)}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Earned this month"})]}),t(e.commission,s?.commission)]}),(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Target Progress"}),(0,a.jsxs)("span",{className:`font-medium ${r>=100?"text-green-600":r>=75?"text-yellow-600":"text-red-600"}`,children:[(r||0).toFixed(0),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-1",children:(0,a.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${r>=100?"bg-green-500":r>=75?"bg-yellow-500":"bg-red-500"}`,style:{width:`${Math.min(r,100)}%`}})})]})]})]})]})}var g=t(46411),j=t(72273),v=t(82039),N=t(8713),f=t(28501),b=t(21857),w=t(53168),A=function(e){return e.NEW="new",e.CONTACTED="contacted",e.QUALIFIED="qualified",e.PROPOSAL="proposal",e.NEGOTIATION="negotiation",e.CLOSED_WON="closed_won",e.CLOSED_LOST="closed_lost",e}({}),T=function(e){return e.LEAD_CREATED="lead_created",e.LEAD_UPDATED="lead_updated",e.LEAD_CONTACTED="lead_contacted",e.SALE_CREATED="sale_created",e.SALE_COMPLETED="sale_completed",e.FOLLOW_UP_SCHEDULED="follow_up_scheduled",e.NOTE_ADDED="note_added",e}({});let E=e=>{switch(e){case T.LEAD_CREATED:return(0,a.jsx)(j.A,{className:"w-4 h-4 text-blue-600"});case T.LEAD_CONTACTED:return(0,a.jsx)(v.A,{className:"w-4 h-4 text-green-600"});case T.SALE_CREATED:case T.SALE_COMPLETED:return(0,a.jsx)(h.A,{className:"w-4 h-4 text-yellow-600"});case T.FOLLOW_UP_SCHEDULED:return(0,a.jsx)(N.A,{className:"w-4 h-4 text-purple-600"});case T.NOTE_ADDED:return(0,a.jsx)(f.A,{className:"w-4 h-4 text-gray-600"});default:return(0,a.jsx)(b.A,{className:"w-4 h-4 text-gray-600"})}},D=e=>{switch(e){case T.LEAD_CREATED:return"bg-blue-50 border-blue-200";case T.LEAD_CONTACTED:return"bg-green-50 border-green-200";case T.SALE_CREATED:case T.SALE_COMPLETED:return"bg-yellow-50 border-yellow-200";case T.FOLLOW_UP_SCHEDULED:return"bg-purple-50 border-purple-200";case T.NOTE_ADDED:default:return"bg-gray-50 border-gray-200"}};function L({activities:e,showAll:s=!1}){let t=s?e:e.slice(0,5);return(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between",children:[(0,a.jsx)(l.ZB,{className:"text-lg font-semibold",children:"Recent Activities"}),(0,a.jsx)(g.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(w.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)(l.Wu,{children:[0===t.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(b.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No recent activities"})]}):(0,a.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,a.jsxs)("div",{className:`flex items-start space-x-3 p-3 rounded-lg border ${D(e.type)}`,children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:E(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),(0,a.jsx)("p",{className:"text-xs text-gray-500 flex-shrink-0 ml-2",children:(0,p.r6)(e.createdAt)})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),(0,a.jsx)("div",{className:"flex items-center justify-between mt-2",children:(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["by ",e.userName]})})]})]},e.id))}),!s&&e.length>5&&(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)(g.$,{variant:"ghost",size:"sm",children:"View All Activities"})})]})]})}var k=t(77272),C=t(56252),_=t(61115);let q=e=>{switch(e){case A.NEW:return"secondary";case A.CONTACTED:return"info";case A.QUALIFIED:return"warning";case A.PROPOSAL:return"default";case A.NEGOTIATION:return"warning";case A.CLOSED_WON:return"success";case A.CLOSED_LOST:return"destructive";default:return"secondary"}},P=e=>e>=80?"text-red-600 bg-red-100":e>=60?"text-yellow-600 bg-yellow-100":e>=40?"text-blue-600 bg-blue-100":"text-gray-600 bg-gray-100";function S({leads:e,showAll:s=!1}){let t=s?e:e.slice(0,6);return(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between",children:[(0,a.jsx)(l.ZB,{className:"text-lg font-semibold",children:"Recent Leads"}),(0,a.jsx)(g.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(w.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)(l.Wu,{children:[0===t.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(k.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No leads found"})]}):(0,a.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(k.A,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:[e.firstName," ",e.lastName]}),(0,a.jsx)(i.E,{variant:q(e.status),className:"text-xs",children:e.status.replace("_"," ")}),(0,a.jsx)("div",{className:`px-2 py-1 rounded-full text-xs font-medium ${P(e.score)}`,children:e.score})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-1 text-xs text-gray-500",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(C.A,{className:"w-3 h-3 mr-1"}),e.email]}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"w-3 h-3 mr-1"}),e.phone]})]}),e.interestedProperty&&(0,a.jsxs)("div",{className:"flex items-center mt-1 text-xs text-gray-500",children:[(0,a.jsx)(_.A,{className:"w-3 h-3 mr-1"}),"Interested in: ",e.interestedProperty]}),e.budget&&(0,a.jsxs)("div",{className:"flex items-center mt-1 text-xs text-gray-500",children:[(0,a.jsx)(h.A,{className:"w-3 h-3 mr-1"}),"Budget: ",(0,p.vv)(e.budget)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[e.nextFollowUp&&(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,a.jsx)(N.A,{className:"w-3 h-3 mr-1"}),"Next follow-up"]}),(0,a.jsx)("div",{className:"text-xs font-medium text-gray-900",children:(0,p.Yq)(e.nextFollowUp)})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)(g.$,{size:"sm",variant:"outline",className:"h-7 px-2",children:(0,a.jsx)(v.A,{className:"w-3 h-3"})}),(0,a.jsx)(g.$,{size:"sm",variant:"outline",className:"h-7 px-2",children:(0,a.jsx)(C.A,{className:"w-3 h-3"})})]})]})]},e.id))}),!s&&e.length>6&&(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)(g.$,{variant:"ghost",size:"sm",children:"View All Leads"})})]})]})}var O=t(89073),R=t(77060),$=t(66282);let M=(0,d.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var U=t(51603),Z=t(31435);let F=(0,d.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var W=t(99206),B=t(86400);let G=e=>({...e,status:e.status,source:e.source,lastContactDate:e.lastContactDate||e.updatedAt,tags:e.tags||[]}),z=e=>({...e,type:e.type.toLowerCase().replace("_","_"),entityType:e.entityType||"lead",metadata:e.metadata||{}}),I=()=>{let e=new Date().getHours();return e<12?"Good morning":e<17?"Good afternoon":"Good evening"};function V(){let{data:e,isLoading:s,error:t,refetch:d}=(0,W.Pb)(),{data:n,isLoading:c}=(0,W.Hu)({limit:10}),{data:o,isLoading:x}=(0,W.$V)({page:1,limit:5,sortBy:"createdAt",sortOrder:"desc"}),{data:h,isLoading:j}=(0,B.mc)({days:1}),v=async()=>{await d()};if(s)return(0,a.jsx)(r.A,{title:"Sales Dashboard",children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)(l.Zp,{className:"animate-pulse",children:(0,a.jsxs)(l.Wu,{className:"p-6",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]})},s))})})});if(t)return(0,a.jsx)(r.A,{title:"Sales Dashboard",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(R.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Failed to load dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Please try refreshing the page"})]})})});let f=e?.data,b=(n?.data||[]).map(z),w=(o?.data?.leads||[]).map(G),A=h?.data||[];return(0,a.jsx)(r.A,{title:"SGM Sales Dashboard",children:(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsx)(O.A,{title:"SGM Sales Dashboard",subtitle:"Complete business overview with real-time analytics, insights, and performance metrics for SGM properties",icon:$.A,greeting:I(),userName:"John Doe",actions:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(g.$,{variant:"secondary",onClick:v,disabled:s,className:"bg-white/20 hover:bg-white/30 text-white border-white/30",children:[(0,a.jsx)(M,{className:`h-4 w-4 mr-2 ${s?"animate-spin":""}`}),"Refresh"]}),(0,a.jsxs)(g.$,{className:"bg-yellow-500 hover:bg-yellow-600 text-black font-semibold",children:[(0,a.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Export Report"]})]})}),f?(0,a.jsx)(y,{stats:f,previousStats:f.previousStats}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)(l.Zp,{className:"animate-pulse",children:(0,a.jsxs)(l.Wu,{className:"p-6",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]})},s))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(g.$,{className:"h-16 flex flex-col items-center justify-center space-y-1 bg-sky-500 hover:bg-sky-600 text-white",children:[(0,a.jsx)(Z.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-sm",children:"Add Lead"})]}),(0,a.jsxs)(g.$,{variant:"outline",className:"h-16 flex flex-col items-center justify-center space-y-1 border-green-500 text-green-600 hover:bg-green-50",children:[(0,a.jsx)(N.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-sm",children:"Schedule Follow-up"})]}),(0,a.jsxs)(g.$,{variant:"outline",className:"h-16 flex flex-col items-center justify-center space-y-1 border-yellow-500 text-yellow-600 hover:bg-yellow-50",children:[(0,a.jsx)(u.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-sm",children:"View Targets"})]}),(0,a.jsxs)(g.$,{variant:"outline",className:"h-16 flex flex-col items-center justify-center space-y-1 border-gray-800 text-gray-800 hover:bg-gray-50",children:[(0,a.jsx)(m.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-sm",children:"Sales Report"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsx)(S,{leads:w})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between",children:[(0,a.jsx)(l.ZB,{className:"text-lg font-semibold",children:"Today's Tasks"}),(0,a.jsx)(i.E,{variant:"secondary",children:A.length})]}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsx)("div",{className:"space-y-3",children:A.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:border-sky-300 transition-colors",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.dueTime||"No time set"})]}),(0,a.jsx)(i.E,{variant:"high"===e.priority?"destructive":"medium"===e.priority?"default":"secondary",className:`text-xs ${"high"===e.priority?"bg-red-500 text-white":"medium"===e.priority?"bg-yellow-500 text-white":"bg-green-500 text-white"}`,children:e.priority})]},e.id))}),(0,a.jsxs)(g.$,{variant:"ghost",size:"sm",className:"w-full mt-3",children:["View All Tasks",(0,a.jsx)(F,{className:"w-4 h-4 ml-2"})]})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{className:"text-lg font-semibold",children:"This Month"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Target"}),(0,a.jsx)("span",{className:"font-medium",children:(0,p.vv)(f?.monthlyTarget||0)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Achieved"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:(0,p.vv)(f?.monthlyAchieved||0)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Remaining"}),(0,a.jsx)("span",{className:"font-medium text-red-600",children:(0,p.vv)((f?.monthlyTarget||0)-(f?.monthlyAchieved||0))})]}),(0,a.jsxs)("div",{className:"pt-2 border-t",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Progress"}),(0,a.jsxs)("span",{className:"font-medium",children:[f?.monthlyTarget?((f.monthlyAchieved||0)/f.monthlyTarget*100).toFixed(0):0,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,a.jsx)("div",{className:"bg-sky-500 h-2 rounded-full transition-all duration-300",style:{width:`${f?.monthlyTarget?(f.monthlyAchieved||0)/f.monthlyTarget*100:0}%`}})})]})]})})]})]})]}),(0,a.jsx)(L,{activities:b})]})})}},51603:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98085).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},53168:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98085).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71819:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\dashboard\\page.tsx","default")},72273:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98085).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},77060:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98085).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},79551:e=>{"use strict";e.exports=require("url")},89073:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(40969);function r({title:e,subtitle:s,icon:t,greeting:r,userName:l,actions:i,children:d}){return(0,a.jsx)("div",{className:"bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg p-6 text-white shadow-lg",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center gap-3",children:[t&&(0,a.jsx)(t,{className:"h-8 w-8"}),r&&l?`${r}, ${l}! 👋`:e]}),(0,a.jsx)("p",{className:"text-sky-100",children:s||"Manage and track your SGM sales activities with real-time insights"})]}),(i||d)&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[i,d]})]})})}t(73356)},93595:(e,s,t)=>{Promise.resolve().then(t.bind(t,71819))},99206:(e,s,t)=>{"use strict";t.d(s,{$V:()=>d,FO:()=>c,Hu:()=>l,OT:()=>E,Pb:()=>r,WD:()=>j,Zu:()=>N,aT:()=>g,cm:()=>v,nK:()=>h,pv:()=>T,ro:()=>f});let a=t(53412).q.injectEndpoints({endpoints:e=>({getDashboardStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getDashboardActivities:e.query({query:e=>({url:"/dashboard/activities",params:e}),providesTags:["Dashboard"]}),getSalesStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getLeads:e.query({query:e=>({url:"/sales/leads",params:e}),providesTags:["Lead"]}),getLeadById:e.query({query:e=>`/sales/leads/${e}`,providesTags:(e,s,t)=>[{type:"Lead",id:t}]}),createLead:e.mutation({query:e=>({url:"/sales/leads",method:"POST",body:e}),invalidatesTags:["Lead","Dashboard"]}),updateLead:e.mutation({query:({id:e,data:s})=>({url:`/sales/leads/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:t})=>[{type:"Lead",id:t},"Lead","Dashboard"]}),deleteLead:e.mutation({query:e=>({url:`/sales/leads/${e}`,method:"DELETE"}),invalidatesTags:["Lead","Dashboard"]}),assignLead:e.mutation({query:({leadId:e,salesRepId:s})=>({url:`/sales/leads/${e}/assign`,method:"POST",body:{salesRepId:s}}),invalidatesTags:["Lead"]}),getCustomers:e.query({query:e=>({url:"/sales/customers",params:e}),providesTags:["Customer"]}),getCustomerById:e.query({query:e=>`/sales/customers/${e}`,providesTags:(e,s,t)=>[{type:"Customer",id:t}]}),createCustomer:e.mutation({query:e=>({url:"/sales/customers",method:"POST",body:e}),invalidatesTags:["Customer","Dashboard"]}),updateCustomer:e.mutation({query:({id:e,data:s})=>({url:`/sales/customers/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:t})=>[{type:"Customer",id:t},"Customer"]}),getCommissions:e.query({query:e=>({url:"/sales/commissions",params:e}),providesTags:["Commission"]}),getSalesTargets:e.query({query:()=>"/sales/targets",providesTags:["Report"]}),createSalesTarget:e.mutation({query:e=>({url:"/sales/targets",method:"POST",body:e}),invalidatesTags:["Report","Dashboard"]}),updateSalesTarget:e.mutation({query:({id:e,data:s})=>({url:`/sales/targets/${e}`,method:"PUT",body:s}),invalidatesTags:["Report","Dashboard"]}),getFollowUps:e.query({query:e=>({url:"/follow-ups",params:e}),providesTags:["Task"]}),createFollowUp:e.mutation({query:e=>({url:"/follow-ups",method:"POST",body:e}),invalidatesTags:["Task","Dashboard"]}),updateFollowUp:e.mutation({query:({id:e,data:s})=>({url:`/follow-ups/${e}`,method:"PUT",body:s}),invalidatesTags:["Task","Dashboard"]}),deleteFollowUp:e.mutation({query:e=>({url:`/follow-ups/${e}`,method:"DELETE"}),invalidatesTags:["Task","Dashboard"]})})}),{useGetDashboardStatsQuery:r,useGetDashboardActivitiesQuery:l,useGetSalesStatsQuery:i,useGetLeadsQuery:d,useGetLeadByIdQuery:n,useCreateLeadMutation:c,useUpdateLeadMutation:o,useDeleteLeadMutation:x,useAssignLeadMutation:m,useGetCustomersQuery:h,useGetCustomerByIdQuery:u,useCreateCustomerMutation:p,useUpdateCustomerMutation:y,useGetCommissionsQuery:g,useGetSalesTargetsQuery:j,useCreateSalesTargetMutation:v,useUpdateSalesTargetMutation:N,useGetFollowUpsQuery:f,useCreateFollowUpMutation:b,useUpdateFollowUpMutation:w,useDeleteFollowUpMutation:A}=a,{useGetCustomersQuery:T,useGetCommissionsQuery:E}=a}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[755,598,544,29,796,286,447,512],()=>t(44823));module.exports=a})();