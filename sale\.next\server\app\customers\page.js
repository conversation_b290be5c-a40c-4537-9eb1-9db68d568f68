(()=>{var e={};e.id=812,e.ids=[812],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16387:(e,s,t)=>{"use strict";e.exports=t(25652)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21478:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Z});var a=t(40969),r=t(73356),l=t(88251),i=t(89073),n=t(66949),o=t(46411),d=t(76650),c=t(57387),u=t(64680),m=t(71060),x=t(7409),p=t(81861),h=t(16387);function v(){return()=>{}}var g="Avatar",[f,y]=(0,u.A)(g),[j,N]=f(g),b=r.forwardRef((e,s)=>{let{__scopeAvatar:t,...l}=e,[i,n]=r.useState("idle");return(0,a.jsx)(j,{scope:t,imageLoadingStatus:i,onImageLoadingStatusChange:n,children:(0,a.jsx)(p.sG.span,{...l,ref:s})})});b.displayName=g;var w="AvatarImage",T=r.forwardRef((e,s)=>{let{__scopeAvatar:t,src:l,onLoadingStatusChange:i=()=>{},...n}=e,o=N(w,t),d=function(e,{referrerPolicy:s,crossOrigin:t}){let a=(0,h.useSyncExternalStore)(v,()=>!0,()=>!1),l=r.useRef(null),i=a?(l.current||(l.current=new window.Image),l.current):null,[n,o]=r.useState(()=>C(i,e));return(0,x.N)(()=>{o(C(i,e))},[i,e]),(0,x.N)(()=>{let e=e=>()=>{o(e)};if(!i)return;let a=e("loaded"),r=e("error");return i.addEventListener("load",a),i.addEventListener("error",r),s&&(i.referrerPolicy=s),"string"==typeof t&&(i.crossOrigin=t),()=>{i.removeEventListener("load",a),i.removeEventListener("error",r)}},[i,t,s]),n}(l,n),c=(0,m.c)(e=>{i(e),o.onImageLoadingStatusChange(e)});return(0,x.N)(()=>{"idle"!==d&&c(d)},[d,c]),"loaded"===d?(0,a.jsx)(p.sG.img,{...n,ref:s,src:l}):null});T.displayName=w;var q="AvatarFallback",A=r.forwardRef((e,s)=>{let{__scopeAvatar:t,delayMs:l,...i}=e,n=N(q,t),[o,d]=r.useState(void 0===l);return r.useEffect(()=>{if(void 0!==l){let e=window.setTimeout(()=>d(!0),l);return()=>window.clearTimeout(e)}},[l]),o&&"loaded"!==n.imageLoadingStatus?(0,a.jsx)(p.sG.span,{...i,ref:s}):null});function C(e,s){return e?s?(e.src!==s&&(e.src=s),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}A.displayName=q;var P=t(21764);let S=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(b,{ref:t,className:(0,P.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...s}));S.displayName=b.displayName;let k=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(T,{ref:t,className:(0,P.cn)("aspect-square h-full w-full",e),...s}));k.displayName=T.displayName;let D=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(A,{ref:t,className:(0,P.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...s}));D.displayName=A.displayName;var E=t(58557),L=t(79123),R=t(91798),$=t(83427),_=t(54076),U=t(71727),G=t(56252),I=t(82039),O=t(8713),M=t(92386),B=t(99206),F=t(48567),W=t(61631);function Z(){let[e,s]=(0,r.useState)(""),[t,u]=(0,r.useState)(1),m=(0,F.G)(W.mB);if(m?.role!=="sales"&&m?.role!=="sales_manager"&&m?.role!=="admin")return(0,a.jsx)(l.A,{title:"Access Denied",children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(E.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access customer management."})]})})});let{data:x,isLoading:p,error:h}=(0,B.pv)({page:t,limit:20,search:e||void 0}),v=(e,s)=>`${e?.charAt(0)||""}${s?.charAt(0)||""}`.toUpperCase();return(0,a.jsxs)(l.A,{title:"Customers",children:[(0,a.jsx)(i.A,{title:"Customer Management",subtitle:"Manage your customer relationships and track their investments",icon:E.A}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(L.A,{className:"w-5 h-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Customers"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:x?.data?.pagination?.totalItems||0})]})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(R.A,{className:"w-5 h-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Active Investors"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:x?.data?.customers?.filter(e=>"active"===e.status).length||0})]})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)($.A,{className:"w-5 h-5 text-yellow-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Investment"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:"₹2.5M"})]})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(_.A,{className:"w-5 h-5 text-purple-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Properties Sold"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:"45"})]})]})})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,a.jsx)(n.ZB,{children:"All Customers"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(U.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)(c.p,{placeholder:"Search customers...",value:e,onChange:e=>s(e.target.value),className:"pl-10 w-full sm:w-80"})]})]})}),(0,a.jsxs)(n.Wu,{children:[p?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-sky-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading customers..."})]}):h?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(E.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Failed to load customers"})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[x?.data?.customers?.map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(S,{className:"h-12 w-12",children:[(0,a.jsx)(k,{src:e.avatar}),(0,a.jsx)(D,{className:"bg-sky-100 text-sky-600",children:v(e.firstName,e.lastName)})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-1",children:[(0,a.jsxs)("h3",{className:"font-medium text-gray-900",children:[e.firstName," ",e.lastName]}),(0,a.jsx)(d.E,{variant:"success",children:"Active"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(G.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.email})]}),e.phone&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(I.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.phone})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(O.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["Joined ",(0,P.Yq)(e.createdAt)]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"text-right mr-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"₹0"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Total Investment"})]}),(0,a.jsxs)(o.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(M.A,{className:"w-4 h-4 mr-2"}),"View Details"]})]})]})},e._id)),(!x?.data?.customers||0===x.data.customers.length)&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(E.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"No customers found"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Customers will appear here once they make their first investment"})]})]}),x?.data?.pagination&&x.data.pagination.totalPages>1&&(0,a.jsxs)("div",{className:"flex justify-center mt-6 space-x-2",children:[(0,a.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>u(e=>Math.max(1,e-1)),disabled:1===t||p,children:"Previous"}),(0,a.jsxs)("span",{className:"flex items-center px-4 text-sm text-gray-600",children:["Page ",t," of ",x.data.pagination.totalPages]}),(0,a.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>u(e=>e+1),disabled:t===x.data.pagination.totalPages||p,children:"Next"})]})]})]})]})]})}},25652:(e,s,t)=>{"use strict";var a=t(73356),r="function"==typeof Object.is?Object.is:function(e,s){return e===s&&(0!==e||1/e==1/s)||e!=e&&s!=s},l=a.useState,i=a.useEffect,n=a.useLayoutEffect,o=a.useDebugValue;function d(e){var s=e.getSnapshot;e=e.value;try{var t=s();return!r(e,t)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,s){return s()}:function(e,s){var t=s(),a=l({inst:{value:t,getSnapshot:s}}),r=a[0].inst,c=a[1];return n(function(){r.value=t,r.getSnapshot=s,d(r)&&c({inst:r})},[e,t,s]),i(function(){return d(r)&&c({inst:r}),e(function(){d(r)&&c({inst:r})})},[e]),o(t),t};s.useSyncExternalStore=void 0!==a.useSyncExternalStore?a.useSyncExternalStore:c},27044:(e,s,t)=>{Promise.resolve().then(t.bind(t,21478))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43497:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=t(10557),r=t(68490),l=t(13172),i=t.n(l),n=t(68835),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d={children:["",{children:["customers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,54422)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\customers\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\customers\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/customers/page",pathname:"/customers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},54422:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\customers\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\customers\\page.tsx","default")},61956:(e,s,t)=>{Promise.resolve().then(t.bind(t,54422))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},89073:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(40969);function r({title:e,subtitle:s,icon:t,greeting:r,userName:l,actions:i,children:n}){return(0,a.jsx)("div",{className:"bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg p-6 text-white shadow-lg",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center gap-3",children:[t&&(0,a.jsx)(t,{className:"h-8 w-8"}),r&&l?`${r}, ${l}! 👋`:e]}),(0,a.jsx)("p",{className:"text-sky-100",children:s||"Manage and track your SGM sales activities with real-time insights"})]}),(i||n)&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[i,n]})]})})}t(73356)},92386:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98085).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},99206:(e,s,t)=>{"use strict";t.d(s,{$V:()=>n,FO:()=>d,Hu:()=>l,OT:()=>A,Pb:()=>r,WD:()=>f,Zu:()=>j,aT:()=>g,cm:()=>y,nK:()=>x,pv:()=>q,ro:()=>N});let a=t(53412).q.injectEndpoints({endpoints:e=>({getDashboardStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getDashboardActivities:e.query({query:e=>({url:"/dashboard/activities",params:e}),providesTags:["Dashboard"]}),getSalesStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getLeads:e.query({query:e=>({url:"/sales/leads",params:e}),providesTags:["Lead"]}),getLeadById:e.query({query:e=>`/sales/leads/${e}`,providesTags:(e,s,t)=>[{type:"Lead",id:t}]}),createLead:e.mutation({query:e=>({url:"/sales/leads",method:"POST",body:e}),invalidatesTags:["Lead","Dashboard"]}),updateLead:e.mutation({query:({id:e,data:s})=>({url:`/sales/leads/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:t})=>[{type:"Lead",id:t},"Lead","Dashboard"]}),deleteLead:e.mutation({query:e=>({url:`/sales/leads/${e}`,method:"DELETE"}),invalidatesTags:["Lead","Dashboard"]}),assignLead:e.mutation({query:({leadId:e,salesRepId:s})=>({url:`/sales/leads/${e}/assign`,method:"POST",body:{salesRepId:s}}),invalidatesTags:["Lead"]}),getCustomers:e.query({query:e=>({url:"/sales/customers",params:e}),providesTags:["Customer"]}),getCustomerById:e.query({query:e=>`/sales/customers/${e}`,providesTags:(e,s,t)=>[{type:"Customer",id:t}]}),createCustomer:e.mutation({query:e=>({url:"/sales/customers",method:"POST",body:e}),invalidatesTags:["Customer","Dashboard"]}),updateCustomer:e.mutation({query:({id:e,data:s})=>({url:`/sales/customers/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:t})=>[{type:"Customer",id:t},"Customer"]}),getCommissions:e.query({query:e=>({url:"/sales/commissions",params:e}),providesTags:["Commission"]}),getSalesTargets:e.query({query:()=>"/sales/targets",providesTags:["Report"]}),createSalesTarget:e.mutation({query:e=>({url:"/sales/targets",method:"POST",body:e}),invalidatesTags:["Report","Dashboard"]}),updateSalesTarget:e.mutation({query:({id:e,data:s})=>({url:`/sales/targets/${e}`,method:"PUT",body:s}),invalidatesTags:["Report","Dashboard"]}),getFollowUps:e.query({query:e=>({url:"/follow-ups",params:e}),providesTags:["Task"]}),createFollowUp:e.mutation({query:e=>({url:"/follow-ups",method:"POST",body:e}),invalidatesTags:["Task","Dashboard"]}),updateFollowUp:e.mutation({query:({id:e,data:s})=>({url:`/follow-ups/${e}`,method:"PUT",body:s}),invalidatesTags:["Task","Dashboard"]}),deleteFollowUp:e.mutation({query:e=>({url:`/follow-ups/${e}`,method:"DELETE"}),invalidatesTags:["Task","Dashboard"]})})}),{useGetDashboardStatsQuery:r,useGetDashboardActivitiesQuery:l,useGetSalesStatsQuery:i,useGetLeadsQuery:n,useGetLeadByIdQuery:o,useCreateLeadMutation:d,useUpdateLeadMutation:c,useDeleteLeadMutation:u,useAssignLeadMutation:m,useGetCustomersQuery:x,useGetCustomerByIdQuery:p,useCreateCustomerMutation:h,useUpdateCustomerMutation:v,useGetCommissionsQuery:g,useGetSalesTargetsQuery:f,useCreateSalesTargetMutation:y,useUpdateSalesTargetMutation:j,useGetFollowUpsQuery:N,useCreateFollowUpMutation:b,useUpdateFollowUpMutation:w,useDeleteFollowUpMutation:T}=a,{useGetCustomersQuery:q,useGetCommissionsQuery:A}=a}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[755,598,544,29,796,286,447,512],()=>t(43497));module.exports=a})();