(()=>{var e={};e.id=879,e.ids=[879],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4942:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(62544);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},7244:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(98085).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12053:(e,r,s)=>{"use strict";s.d(r,{J:()=>d});var t=s(40969),a=s(73356),i=s(2724),n=s(52774),o=s(21764);let l=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(i.b,{ref:s,className:(0,o.cn)(l(),e),...r}));d.displayName=i.b.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21764:(e,r,s)=>{"use strict";s.d(r,{Yq:()=>l,ZV:()=>o,cn:()=>i,r6:()=>d,tP:()=>c,vv:()=>n});var t=s(95534),a=s(86026);function i(...e){return(0,a.QP)((0,t.$)(e))}function n(e){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}function o(e){return new Intl.NumberFormat("en-IN").format(e)}function l(e){if(!e)return"N/A";let r=new Date(e);return isNaN(r.getTime())?"Invalid Date":new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric"}).format(r)}function d(e){if(!e)return"N/A";let r=new Date(e);return isNaN(r.getTime())?"Invalid Date":new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(r)}function c(e,r){return 0===r?100*(e>0):(e-r)/r*100}},23939:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\signup\\page.tsx","default")},23955:(e,r,s)=>{Promise.resolve().then(s.bind(s,23939))},28191:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=s(10557),a=s(68490),i=s(13172),n=s.n(i),o=s(68835),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let d={children:["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,23939)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\signup\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\signup\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46411:(e,r,s)=>{"use strict";s.d(r,{$:()=>d});var t=s(40969),a=s(73356),i=s(19334),n=s(52774),o=s(21764);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:r,size:s,asChild:a=!1,...n},d)=>{let c=a?i.DX:"button";return(0,t.jsx)(c,{className:(0,o.cn)(l({variant:r,size:s,className:e})),ref:d,...n})});d.displayName="Button"},57387:(e,r,s)=>{"use strict";s.d(r,{p:()=>n});var t=s(40969),a=s(73356),i=s(21764);let n=a.forwardRef(({className:e,type:r,...s},a)=>(0,t.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...s}));n.displayName="Input"},60907:(e,r,s)=>{Promise.resolve().then(s.bind(s,61197))},61197:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>b});var t=s(40969),a=s(73356),i=s(12011),n=s(27092),o=s.n(n),l=s(66949),d=s(46411),c=s(57387),u=s(12053),m=s(42605),p=s(54076),f=s(72273),x=s(77272),h=s(56252),g=s(82039),v=s(7244);function b(){let[e,r]=(0,a.useState)({firstName:"",lastName:"",email:"",phone:"",password:"",confirmPassword:""}),[s,n]=(0,a.useState)(!1);(0,i.useRouter)();let b=e=>{r(r=>({...r,[e.target.name]:e.target.value}))},y=async r=>{if(r.preventDefault(),!e.firstName||!e.lastName||!e.email||!e.password)return void m.o.error("Please fill in all required fields");if(e.password!==e.confirmPassword)return void m.o.error("Passwords do not match");if(e.password.length<6)return void m.o.error("Password must be at least 6 characters long");n(!0);try{m.o.info("Signup functionality will be implemented soon. Please contact your administrator for account creation.")}catch(e){m.o.error(e.message||"Signup failed")}finally{n(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"BuilderFarm"})]}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-700",children:"Sales Portal"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Create your sales account"})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5"}),"Create Account"]}),(0,t.jsx)(l.BT,{children:"Join the sales team and start managing leads"})]}),(0,t.jsxs)(l.Wu,{children:[(0,t.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"firstName",children:"First Name"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(x.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(c.p,{id:"firstName",name:"firstName",type:"text",placeholder:"John",value:e.firstName,onChange:b,className:"pl-10",required:!0})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"lastName",children:"Last Name"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(x.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(c.p,{id:"lastName",name:"lastName",type:"text",placeholder:"Doe",value:e.lastName,onChange:b,className:"pl-10",required:!0})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"email",children:"Email"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(h.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(c.p,{id:"email",name:"email",type:"email",placeholder:"<EMAIL>",value:e.email,onChange:b,className:"pl-10",required:!0})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"phone",children:"Phone (Optional)"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(g.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(c.p,{id:"phone",name:"phone",type:"tel",placeholder:"+****************",value:e.phone,onChange:b,className:"pl-10"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"password",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(v.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(c.p,{id:"password",name:"password",type:"password",placeholder:"Enter your password",value:e.password,onChange:b,className:"pl-10",required:!0})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(v.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(c.p,{id:"confirmPassword",name:"confirmPassword",type:"password",placeholder:"Confirm your password",value:e.confirmPassword,onChange:b,className:"pl-10",required:!0})]})]}),(0,t.jsx)(d.$,{type:"submit",className:"w-full h-11 bg-blue-600 hover:bg-blue-700",disabled:s,children:s?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Creating Account..."]}):"\uD83D\uDE80 Create Account"})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,t.jsx)(o(),{href:"/login",className:"text-blue-600 hover:text-blue-700 font-medium",children:"Sign in here"})]})})]})]}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("p",{className:"text-sm text-blue-700",children:[(0,t.jsx)("strong",{children:"Note:"})," Account creation requires administrator approval. Contact your system administrator if you need immediate access."]})})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66949:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>o});var t=s(40969),a=s(73356),i=s(21764);let n=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));n.displayName="Card";let o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},72273:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(98085).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},79551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[755,598,544,29,796,447],()=>s(28191));module.exports=t})();