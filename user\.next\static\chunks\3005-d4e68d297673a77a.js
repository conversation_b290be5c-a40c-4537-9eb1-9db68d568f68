"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3005],{3005:(e,t,s)=>{s.d(t,{A:()=>J});var a=s(9605),r=s(9585),i=s(6994),l=s(6762),o=s.n(l),n=s(5935),d=s(5077);let c=s(9559).d4;var u=s(3815),m=s(7218),x=s(1248),p=s(9508),h=s(470),f=s(8770),y=s(8408),b=s(9405),g=s(5833),v=s(3559),N=s(4621),j=s(7439),w=s(2363),k=s(8391),T=s(4268),S=s(5449),A=s(7),C=s(827),q=s(498),U=s(360),D=s(7412),P=s(362),E=s(2237),Y=s(689),K=s(2311),R=s(7806),F=s(1983),O=s(1878),I=s(2933),L=s(8120);let M=[{title:"Dashboard",items:[{id:"dashboard",label:"Overview",icon:m.A,href:"/dashboard"},{id:"analytics",label:"Analytics",icon:x.A,href:"/dashboard/analytics"}]},{title:"Investments",items:[{id:"portfolio",label:"My Portfolio",icon:p.A,href:"/portfolio"},{id:"performance",label:"Performance",icon:h.A,href:"/portfolio/performance"},{id:"returns",label:"Returns",icon:f.A,href:"/portfolio/returns"},{id:"history",label:"History",icon:y.A,href:"/portfolio/history"}]},{title:"Properties",items:[{id:"browse",label:"Browse All",icon:b.A,href:"/properties"},{id:"featured",label:"Featured",icon:g.A,href:"/properties/featured"},{id:"wishlist",label:"Wishlist",icon:v.A,href:"/wishlist"},{id:"calculator",label:"Calculator",icon:N.A,href:"/calculator"}]},{title:"Referrals",items:[{id:"my-referrals",label:"My Network",icon:j.A,href:"/referrals"},{id:"referral-earnings",label:"Earnings",icon:w.A,href:"/referrals/earnings"},{id:"referral-analytics",label:"Analytics",icon:k.A,href:"/referrals/analytics"}]},{title:"Wallet",items:[{id:"wallet",label:"Balance",icon:T.A,href:"/wallet"},{id:"add-money",label:"Add Money",icon:S.A,href:"/wallet/add"},{id:"withdraw",label:"Withdraw",icon:A.A,href:"/wallet/withdraw"},{id:"transactions",label:"Transactions",icon:C.A,href:"/wallet/transactions"}]},{title:"Account",items:[{id:"profile",label:"My Profile",icon:q.A,href:"/profile"},{id:"kyc",label:"KYC Verification",icon:U.A,href:"/kyc"},{id:"settings",label:"Settings",icon:D.A,href:"/settings"}]},{title:"Support",items:[{id:"support-center",label:"Help Center",icon:P.A,href:"/support"},{id:"my-tickets",label:"My Tickets",icon:E.A,href:"/support/tickets"},{id:"faq",label:"FAQ",icon:Y.A,href:"/support/faq"}]}];function _(e){var t,l;let{className:m}=e,x=(0,n.usePathname)(),[p,h]=(0,r.useState)(!1),[f,y]=(0,r.useState)(!1),b=c(u.xu),{data:g}=(0,d.xs)(),v=(null==g||null==(l=g.data)||null==(t=l.kyc)?void 0:t.status)||(null==b?void 0:b.kycStatus),N=["/kyc","/login","/register","/dashboard","/","/support","/support/tickets","/support/faq"],j="approved"!==v&&"verified"!==v,w=()=>{y(!f)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(I.$,{variant:"ghost",size:"icon",className:"fixed top-4 left-4 z-50 md:hidden",onClick:w,children:f?(0,a.jsx)(K.A,{className:"h-6 w-6"}):(0,a.jsx)(R.A,{className:"h-6 w-6"})}),f&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-40 md:hidden",onClick:w}),(0,a.jsxs)("aside",{className:(0,i.cn)("fixed left-0 top-0 z-50 h-screen bg-white border-r border-gray-200 shadow-lg transition-all duration-300 ease-in-out flex flex-col",p?"w-16":"w-64",f?"translate-x-0":"-translate-x-full md:translate-x-0",m),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-sky-100 flex-shrink-0",children:[(0,a.jsx)(L.A,{variant:p?"icon":"full",size:"md",className:p?"mx-auto":""}),(0,a.jsx)(I.$,{variant:"ghost",size:"icon",onClick:()=>{h(!p)},className:"hidden md:flex text-sky-600 hover:bg-sky-50",children:p?(0,a.jsx)(F.A,{className:"h-4 w-4"}):(0,a.jsx)(O.A,{className:"h-4 w-4"})})]}),j&&!p&&(0,a.jsxs)("div",{className:"mx-4 mt-4 p-4 bg-gradient-to-r from-blue-50 to-sky-50 border border-blue-200 rounded-xl",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-sm font-bold text-blue-800",children:"under_review"===v?"KYC Under Review":"KYC Required"})]}),(0,a.jsx)("p",{className:"text-xs text-blue-700 mb-3",children:"under_review"===v?"Your documents are being verified. Limited access until complete.":"Complete KYC verification to access all features."}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>window.location.href="/kyc",className:"text-xs bg-blue-600 text-white px-3 py-1 rounded-full hover:bg-blue-700 transition-colors",children:"under_review"===v?"View Status":"Start KYC"}),(0,a.jsx)("button",{onClick:()=>window.location.href="/support",className:"text-xs bg-white text-blue-600 px-3 py-1 rounded-full border border-blue-300 hover:bg-blue-50 transition-colors",children:"Support"})]})]}),(0,a.jsx)("nav",{className:"flex-1 overflow-y-auto p-6 space-y-6",children:M.map(e=>(0,a.jsxs)("div",{children:[!p&&(0,a.jsx)("h3",{className:"text-xs font-bold text-black uppercase tracking-wider mb-4 px-2",children:e.title}),(0,a.jsx)("ul",{className:"space-y-2",children:e.items.map(e=>{let t=x===e.href,r=e.icon;return j&&!N.includes(e.href)?(0,a.jsx)("li",{children:(0,a.jsxs)("div",{className:(0,i.cn)("relative flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200","text-gray-400 cursor-not-allowed opacity-50 bg-gray-50 border border-dashed border-gray-300 select-none",p&&"justify-center px-3"),title:"Complete KYC verification to access this feature",onClick:e=>{e.preventDefault(),e.stopPropagation(),Promise.resolve().then(s.bind(s,6845)).then(e=>{let{toast:t}=e;t.error("KYC verification required",{description:"Complete your KYC to access this feature",action:{label:"Go to KYC",onClick:()=>window.location.href="/kyc"}})})},children:[(0,a.jsx)(r,{className:"h-5 w-5 text-gray-400"}),!p&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"flex-1",children:e.label}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"\uD83D\uDD12"})]}),p&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-gray-400 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xs text-white",children:"!"})})]})},e.id):(0,a.jsx)("li",{children:(0,a.jsxs)(o(),{href:e.href,onClick:()=>y(!1),className:(0,i.cn)("flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200",t?"bg-sky-500 text-white shadow-lg transform scale-105":"text-black hover:bg-sky-50 hover:text-sky-700 hover:shadow-md",p&&"justify-center px-3"),children:[(0,a.jsx)(r,{className:(0,i.cn)("h-5 w-5",t?"text-white":"text-sky-600")}),!p&&(0,a.jsx)("span",{children:e.label})]})},e.id)})})]},e.title))}),(0,a.jsx)("div",{className:"p-6 border-t-2 border-sky-200 bg-sky-50",children:!p&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xs font-bold text-black mb-1",children:"SGM"}),(0,a.jsx)("div",{className:"text-xs text-sky-600",children:"User Panel v1.0.0"})]})})]})]})}var z=s(3760),H=s(9695),V=s(8188),G=s(9311),W=s(1128),$=s(7707),B=s(5215),Q=s(6845);function Z(e){var t,s,l,o,c,m;let{className:x}=e,p=(0,n.useRouter)(),h=(0,G.GV)(u.xu),[f]=(0,W.Ng)(),{data:y}=(0,$.FZ)({}),g=(null==y||null==(t=y.data)?void 0:t.data)||[],{data:v}=(0,B.M7)(),N=(null==v?void 0:v.data)||h,{data:j}=(0,d.xs)(),w=(null==j||null==(l=j.data)||null==(s=l.kyc)?void 0:s.status)||(null==N?void 0:N.kycStatus),[k,S]=(0,r.useState)(!1),[A,C]=(0,r.useState)(!1),[P,E]=(0,r.useState)(!1),[K,R]=(0,r.useState)(""),F=g.filter(e=>!e.isRead).length;(0,r.useEffect)(()=>{if(N){var e;console.log("\uD83D\uDD0D Header User Data:",{email:N.email,name:"".concat(N.firstName," ").concat(N.lastName),kycStatus:N.kycStatus,walletBalance:(null==(e=N.wallet)?void 0:e.balance)||0})}},[N]);let O=async()=>{try{await f().unwrap(),Q.toast.success("Logged out successfully"),p.push("/login")}catch(e){console.error("Logout error:",e),Q.toast.error("Failed to logout")}},L=e=>{switch(e){case"success":return"✅";case"warning":return"⚠️";case"error":return"❌";default:return"ℹ️"}};return(0,a.jsxs)("header",{className:(0,i.cn)("bg-white border-b border-gray-200 px-4 md:px-6 py-3 shadow-sm",x),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4 flex-1 max-w-md",children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),K.trim()&&p.push("/search?q=".concat(encodeURIComponent(K)))},className:"relative w-full",children:[(0,a.jsx)(b.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search properties, investments...",value:K,onChange:e=>R(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white text-sm"})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-3 bg-sky-50 px-4 py-2 rounded-xl border border-sky-200",children:[(0,a.jsx)(T.A,{className:"h-5 w-5 text-sky-600"}),(0,a.jsx)("span",{className:"text-sm font-bold text-black",children:(0,i.vv)((null==N||null==(o=N.wallet)?void 0:o.balance)||0)})]}),"approved"!==w&&(0,a.jsxs)("div",{className:(0,i.cn)("hidden md:flex items-center space-x-2 px-3 py-2 rounded-xl text-xs font-bold","under_review"===w||"pending_verification"===w?"bg-yellow-50 text-yellow-700 border border-yellow-200":"rejected"===w?"bg-red-50 text-red-700 border border-red-200":"bg-gray-50 text-gray-700 border border-gray-200"),children:[(0,a.jsx)(U.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"under_review"===w?"⏳ Under Review":"pending_verification"===w?"⏳ Pending":"rejected"===w?"❌ Rejected":"\uD83D\uDCCB KYC Required"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(I.$,{variant:"ghost",size:"icon",onClick:()=>S(!k),className:"relative h-10 w-10 rounded-xl hover:bg-sky-50 text-black",children:[(0,a.jsx)(z.A,{className:"h-5 w-5 text-sky-600"}),F>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-yellow-500 text-black text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold",children:F})]}),k&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50",children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Notifications"})}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:g.length>0?g.map(e=>(0,a.jsx)("div",{className:(0,i.cn)("p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer",!e.isRead&&"bg-blue-50"),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("span",{className:"text-lg",children:L(e.type)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:new Date(e.createdAt).toLocaleDateString()})]}),!e.isRead&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})]})},e._id)):(0,a.jsxs)("div",{className:"p-8 text-center text-gray-500",children:[(0,a.jsx)(z.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"No notifications yet"})]})}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,a.jsx)(I.$,{variant:"ghost",className:"w-full text-blue-600 hover:text-blue-700",onClick:()=>p.push("/notifications"),children:"View All Notifications"})})]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(I.$,{variant:"ghost",onClick:()=>C(!A),className:"flex items-center space-x-3 px-4 py-2 rounded-xl hover:bg-sky-50",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-sky-500 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg",children:(null==N||null==(c=N.avatar)?void 0:c.url)?(0,a.jsx)("img",{src:N.avatar.url,alt:"Profile",className:"w-10 h-10 rounded-full"}):(0,i.IM)((null==N?void 0:N.firstName)||"U",(null==N?void 0:N.lastName)||"U")}),(0,a.jsxs)("div",{className:"hidden md:block text-left",children:[(0,a.jsx)("p",{className:"text-sm font-bold text-black",children:(null==N?void 0:N.firstName)||"User"}),(0,a.jsx)("p",{className:"text-xs text-sky-600",children:(null==N?void 0:N.email)||"<EMAIL>"})]}),(0,a.jsx)(H.A,{className:"h-4 w-4 text-sky-600"})]}),A&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50",children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white text-lg font-medium",children:(null==h?void 0:h.avatar)?(0,a.jsx)("img",{src:"string"==typeof h.avatar?h.avatar:null==(m=h.avatar)?void 0:m.url,alt:"Profile",className:"w-12 h-12 rounded-full"}):(0,i.IM)((null==h?void 0:h.firstName)||"U",(null==h?void 0:h.lastName)||"U")}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-900",children:[null==h?void 0:h.firstName," ",null==h?void 0:h.lastName]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:null==h?void 0:h.email})]})]})}),(0,a.jsxs)("div",{className:"py-2",children:[(0,a.jsxs)("button",{onClick:()=>p.push("/profile"),className:"flex items-center space-x-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(q.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"My Profile"})]}),(0,a.jsxs)("button",{onClick:()=>p.push("/wallet"),className:"flex items-center space-x-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(T.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"My Wallet"})]}),(0,a.jsxs)("button",{onClick:()=>p.push("/settings"),className:"flex items-center space-x-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(D.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Settings"})]}),(0,a.jsxs)("button",{onClick:()=>p.push("/support"),className:"flex items-center space-x-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(Y.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Help & Support"})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 py-2",children:(0,a.jsxs)("button",{onClick:O,className:"flex items-center space-x-3 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50",children:[(0,a.jsx)(V.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Sign Out"})]})})]})]})]})]}),(k||A)&&(0,a.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>{S(!1),C(!1)}})]})}function J(e){let{children:t,className:s,restrictSidebar:r=!1}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[!r&&(0,a.jsx)(_,{}),(0,a.jsxs)("div",{className:(0,i.cn)("transition-all duration-300",r?"ml-0":"ml-0 md:ml-64"),children:[(0,a.jsx)(Z,{className:"sticky top-0 z-30 bg-white border-b border-gray-200"}),(0,a.jsx)("main",{className:(0,i.cn)("p-4 md:p-6",s),children:t})]})]})}},5077:(e,t,s)=>{s.d(t,{Gy:()=>n,VI:()=>o,Yx:()=>x,cs:()=>m,et:()=>y,fW:()=>i,fx:()=>u,i6:()=>d,jY:()=>l,xs:()=>a});let{useGetKYCStatusQuery:a,useGetKYCRequirementsQuery:r,useGetKYCDocumentsQuery:i,useGetKYCPersonalInfoQuery:l,useGetKYCAddressInfoQuery:o,useUploadKYCDocumentMutation:n,useGetKYCPresignedUrlMutation:d,useDeleteKYCDocumentMutation:c,useSubmitKYCVerificationMutation:u,useUpdatePersonalInfoMutation:m,useUpdateAddressInfoMutation:x,useGetKYCHistoryQuery:p,useRequestKYCUpgradeMutation:h,useGetKYCVerificationErrorsQuery:f,useDownloadKYCPDFMutation:y}=s(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getKYCStatus:e.query({query:()=>"/kyc",providesTags:[{type:"KYC",id:"STATUS"}],keepUnusedDataFor:300}),getKYCRequirements:e.query({query:()=>"/kyc/requirements",providesTags:[{type:"KYC",id:"REQUIREMENTS"}],keepUnusedDataFor:3600}),getKYCDocuments:e.query({query:()=>"/kyc/documents",providesTags:[{type:"KYC",id:"DOCUMENTS"}],keepUnusedDataFor:600}),getKYCPersonalInfo:e.query({query:()=>"/kyc/personal-info",providesTags:[{type:"KYC",id:"PERSONAL_INFO"}],keepUnusedDataFor:600}),getKYCAddressInfo:e.query({query:()=>"/kyc/address-info",providesTags:[{type:"KYC",id:"ADDRESS_INFO"}],keepUnusedDataFor:600}),uploadKYCDocument:e.mutation({query:e=>{if(e.file){let t=new FormData;return Object.keys(e).forEach(s=>{"file"===s?t.append("document",e.file):void 0!==e[s]&&t.append(s,String(e[s]))}),{url:"/kyc/document",method:"POST",body:t,formData:!0}}{let{file:t,...s}=e;return{url:"/kyc/document",method:"POST",body:s}}},invalidatesTags:[{type:"KYC",id:"DOCUMENTS"},{type:"KYC",id:"STATUS"}]}),getKYCPresignedUrl:e.mutation({query:e=>({url:"/s3/presigned-url",method:"POST",body:e})}),deleteKYCDocument:e.mutation({query:e=>({url:"/kyc/documents/".concat(e),method:"DELETE"}),invalidatesTags:[{type:"KYC",id:"DOCUMENTS"},{type:"KYC",id:"STATUS"}]}),submitKYCVerification:e.mutation({query:()=>({url:"/kyc/submit",method:"POST"}),invalidatesTags:[{type:"KYC",id:"STATUS"}]}),updatePersonalInfo:e.mutation({query:e=>({url:"/kyc",method:"PUT",body:e}),invalidatesTags:[{type:"KYC",id:"STATUS"}]}),updateAddressInfo:e.mutation({query:e=>({url:"/kyc/address",method:"PUT",body:e}),invalidatesTags:[{type:"KYC",id:"STATUS"}]}),getKYCHistory:e.query({query:()=>"/kyc/history",providesTags:[{type:"KYC",id:"HISTORY"}],keepUnusedDataFor:900}),requestKYCUpgrade:e.mutation({query:e=>({url:"/kyc/upgrade",method:"POST",body:e}),invalidatesTags:[{type:"KYC",id:"STATUS"}]}),getKYCVerificationErrors:e.query({query:()=>"/kyc/verification-errors",providesTags:[{type:"KYC",id:"ERRORS"}],keepUnusedDataFor:600}),downloadKYCPDF:e.mutation({query:()=>({url:"/kyc/download-pdf",method:"GET",responseHandler:e=>e.blob()})})})})},5215:(e,t,s)=>{s.d(t,{M7:()=>a,ik:()=>r});let{useGetUserProfileQuery:a,useUpdateUserProfileMutation:r,useUploadProfileImageMutation:i,useGetUserNotificationsQuery:l,useMarkNotificationAsReadMutation:o,useMarkAllNotificationsAsReadMutation:n,useDeleteNotificationMutation:d,useUpdateUserSettingsMutation:c,useGetUserActivityLogQuery:u,useDeleteUserAccountMutation:m,useExportUserDataMutation:x,useGetUserReferralCodeQuery:p,useGenerateNewReferralCodeMutation:h}=s(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getUserProfile:e.query({query:()=>"/users/profile",providesTags:["User"]}),updateUserProfile:e.mutation({query:e=>({url:"/users/profile",method:"PUT",body:e}),invalidatesTags:["User"]}),uploadProfileImage:e.mutation({query:e=>({url:"/users/upload-avatar",method:"POST",body:e}),invalidatesTags:["User"]}),getUserNotifications:e.query({query:e=>{let{page:t=1,limit:s=20,unreadOnly:a=!1}=e;return{url:"/users/notifications",params:{page:t,limit:s,unreadOnly:a}}},providesTags:["Notification"],keepUnusedDataFor:120}),markNotificationAsRead:e.mutation({query:e=>({url:"/user/notifications/".concat(e,"/read"),method:"PUT"}),invalidatesTags:["Notification"]}),markAllNotificationsAsRead:e.mutation({query:()=>({url:"/user/notifications/mark-all-read",method:"PUT"}),invalidatesTags:["Notification"]}),deleteNotification:e.mutation({query:e=>({url:"/user/notifications/".concat(e),method:"DELETE"}),invalidatesTags:["Notification"]}),updateUserSettings:e.mutation({query:e=>({url:"/user/settings",method:"PUT",body:e}),invalidatesTags:["User"]}),getUserActivityLog:e.query({query:e=>{let{page:t=1,limit:s=20,type:a}=e;return{url:"/user/activity-log",params:{page:t,limit:s,...a&&{type:a}}}},providesTags:["User"],keepUnusedDataFor:300}),deleteUserAccount:e.mutation({query:e=>({url:"/user/delete-account",method:"DELETE",body:e}),invalidatesTags:["User"]}),exportUserData:e.mutation({query:e=>({url:"/user/export-data",method:"POST",body:e})}),getUserReferralCode:e.query({query:()=>"/user/referral-code",providesTags:["User"]}),generateNewReferralCode:e.mutation({query:()=>({url:"/user/referral-code/generate",method:"POST"}),invalidatesTags:["User"]})})})},7707:(e,t,s)=>{s.d(t,{FZ:()=>a,b7:()=>i,d1:()=>l,nC:()=>o});let{useGetUserNotificationsQuery:a,useGetNotificationByIdQuery:r,useMarkNotificationAsReadMutation:i,useMarkAllNotificationsAsReadMutation:l,useDeleteNotificationMutation:o,useDeleteAllReadNotificationsMutation:n,useGetNotificationStatsQuery:d,useGetNotificationPreferencesQuery:c,useUpdateNotificationPreferencesMutation:u,useSubscribeToPushNotificationsMutation:m,useUnsubscribeFromPushNotificationsMutation:x,useTestNotificationMutation:p}=s(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getUserNotifications:e.query({query:e=>({url:"/users/notifications",params:{page:e.page||1,limit:e.limit||20,...void 0!==e.unreadOnly&&{unreadOnly:e.unreadOnly},...e.type&&{type:e.type},...e.category&&{category:e.category},...e.priority&&{priority:e.priority},...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate}}}),providesTags:e=>{var t;return(null==e||null==(t=e.data)?void 0:t.data)?[...e.data.data.map(e=>{let{_id:t}=e;return{type:"Notification",id:t}}),{type:"Notification",id:"LIST"}]:[{type:"Notification",id:"LIST"}]},keepUnusedDataFor:120}),getNotificationById:e.query({query:e=>"/users/notifications/".concat(e),providesTags:(e,t,s)=>[{type:"Notification",id:s}],keepUnusedDataFor:600}),markNotificationAsRead:e.mutation({query:e=>({url:"/users/notifications/".concat(e,"/read"),method:"PATCH"}),invalidatesTags:(e,t,s)=>[{type:"Notification",id:s},{type:"Notification",id:"LIST"},{type:"Notification",id:"STATS"}]}),markAllNotificationsAsRead:e.mutation({query:()=>({url:"/users/notifications/read-all",method:"PATCH"}),invalidatesTags:[{type:"Notification",id:"LIST"},{type:"Notification",id:"STATS"}]}),deleteNotification:e.mutation({query:e=>({url:"/users/notifications/".concat(e),method:"DELETE"}),invalidatesTags:(e,t,s)=>[{type:"Notification",id:s},{type:"Notification",id:"LIST"},{type:"Notification",id:"STATS"}]}),deleteAllReadNotifications:e.mutation({query:()=>({url:"/users/notifications/delete-read",method:"DELETE"}),invalidatesTags:[{type:"Notification",id:"LIST"},{type:"Notification",id:"STATS"}]}),getNotificationStats:e.query({query:()=>"/users/notifications/stats",providesTags:[{type:"Notification",id:"STATS"}],keepUnusedDataFor:300}),getNotificationPreferences:e.query({query:()=>"/users/notifications/preferences",providesTags:[{type:"Notification",id:"PREFERENCES"}],keepUnusedDataFor:1800}),updateNotificationPreferences:e.mutation({query:e=>({url:"/users/notifications/preferences",method:"PUT",body:e}),invalidatesTags:[{type:"Notification",id:"PREFERENCES"}]}),subscribeToPushNotifications:e.mutation({query:e=>({url:"/users/notifications/push/subscribe",method:"POST",body:e})}),unsubscribeFromPushNotifications:e.mutation({query:()=>({url:"/users/notifications/push/unsubscribe",method:"POST"})}),testNotification:e.mutation({query:e=>({url:"/users/notifications/test",method:"POST",body:e})})})})}}]);