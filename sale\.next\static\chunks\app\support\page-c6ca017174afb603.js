(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[174],{192:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(6501).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},2731:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(6501).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3266:(e,s,t)=>{Promise.resolve().then(t.bind(t,7365))},3757:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(6501).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3831:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var l=t(9605);function a(e){let{title:s,subtitle:t,icon:a,greeting:r,userName:i,actions:c,children:n}=e;return(0,l.jsx)("div",{className:"bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg p-6 text-white shadow-lg",children:(0,l.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center gap-3",children:[a&&(0,l.jsx)(a,{className:"h-8 w-8"}),r&&i?"".concat(r,", ").concat(i,"! \uD83D\uDC4B"):s]}),(0,l.jsx)("p",{className:"text-sky-100",children:t||"Manage and track your SGM sales activities with real-time insights"})]}),(c||n)&&(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[c,n]})]})})}t(9585)},7365:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>_});var l=t(9605),a=t(9585),r=t(6440),i=t(3831),c=t(8063),n=t(2933),d=t(2790),x=t(7971),o=t(6467),u=t(9577),h=t(192),m=t(8347),p=t(3757);let j=(0,t(6501).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var v=t(6380),g=t(2731),y=t(7183),N=t(8232),b=t(1713),f=t(4965),k=t(3815),w=t(6994);let{useGetSupportTicketsQuery:T,useGetSupportTicketByIdQuery:C,useCreateSupportTicketMutation:A,useAddTicketMessageMutation:S,useGetSupportStatsQuery:q}=t(6701).q.injectEndpoints({endpoints:e=>({getSupportTickets:e.query({query:e=>({url:"/sales/support/tickets",params:Object.fromEntries(Object.entries(e).filter(e=>{let[s,t]=e;return void 0!==t}))}),providesTags:["Support"]}),getSupportTicketById:e.query({query:e=>"/sales/support/tickets/".concat(e),providesTags:(e,s,t)=>[{type:"Support",id:t}]}),createSupportTicket:e.mutation({query:e=>({url:"/sales/support/tickets",method:"POST",body:e}),invalidatesTags:["Support"]}),addTicketMessage:e.mutation({query:e=>{let{ticketId:s,message:t}=e;return{url:"/sales/support/tickets/".concat(s,"/messages"),method:"POST",body:t}},invalidatesTags:(e,s,t)=>{let{ticketId:l}=t;return[{type:"Support",id:l},"Support"]}}),getSupportStats:e.query({query:()=>"/sales/support/stats",providesTags:["Support"]})})});function _(){var e;let[s,t]=(0,a.useState)(""),[C,S]=(0,a.useState)("all"),[_,L]=(0,a.useState)("all"),[P,M]=(0,a.useState)(!1),[B,O]=(0,a.useState)(1),[Z,E]=(0,a.useState)({subject:"",description:"",priority:"medium",category:"general"});(0,f.G)(k.mB);let{data:D,isLoading:R,error:V}=T({page:B,limit:10,status:"all"!==C?C:void 0,priority:"all"!==_?_:void 0,search:s||void 0}),{data:W,isLoading:I}=q(),[$,{isLoading:z}]=A(),G=(null==D||null==(e=D.data)?void 0:e.tickets)||[],F=(null==W?void 0:W.data)||{totalTickets:0,openTickets:0,inProgressTickets:0,resolvedTickets:0,closedTickets:0,pendingTickets:0},H=e=>{switch(e){case"open":return(0,l.jsx)(h.A,{className:"w-4 h-4 text-red-600"});case"in_progress":return(0,l.jsx)(m.A,{className:"w-4 h-4 text-yellow-600"});case"resolved":return(0,l.jsx)(p.A,{className:"w-4 h-4 text-green-600"});case"closed":return(0,l.jsx)(p.A,{className:"w-4 h-4 text-gray-600"});default:return(0,l.jsx)(j,{className:"w-4 h-4 text-blue-600"})}},U=e=>{switch(e){case"open":return"destructive";case"in_progress":return"warning";case"resolved":return"success";case"closed":return"secondary";default:return"default"}},Y=e=>{switch(e){case"urgent":return"destructive";case"high":return"warning";case"medium":return"default";default:return"secondary"}},J=async()=>{try{await $(Z).unwrap(),M(!1),E({subject:"",description:"",priority:"medium",category:"general"})}catch(e){console.error("Failed to create ticket:",e)}},K=G.filter(e=>{let t=""===s||e.subject.toLowerCase().includes(s.toLowerCase())||e.description.toLowerCase().includes(s.toLowerCase()),l="all"===C||e.status===C,a="all"===_||e.priority===_;return t&&l&&a});return(0,l.jsxs)(r.A,{title:"Support",children:[(0,l.jsx)(i.A,{title:"Support Center",subtitle:"Get help and create support tickets",icon:v.A}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,l.jsx)(c.Zp,{children:(0,l.jsx)(c.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(j,{className:"w-5 h-5 text-blue-600"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Total Tickets"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:F.totalTickets})]})]})})}),(0,l.jsx)(c.Zp,{children:(0,l.jsx)(c.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(h.A,{className:"w-5 h-5 text-red-600"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Open"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:F.openTickets})]})]})})}),(0,l.jsx)(c.Zp,{children:(0,l.jsx)(c.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(m.A,{className:"w-5 h-5 text-yellow-600"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"In Progress"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:F.inProgressTickets})]})]})})}),(0,l.jsx)(c.Zp,{children:(0,l.jsx)(c.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(p.A,{className:"w-5 h-5 text-green-600"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Resolved"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:F.resolvedTickets})]})]})})})]}),P&&(0,l.jsxs)(c.Zp,{children:[(0,l.jsx)(c.aR,{children:(0,l.jsx)(c.ZB,{children:"Create New Support Ticket"})}),(0,l.jsx)(c.Wu,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Subject"}),(0,l.jsx)(x.p,{value:Z.subject,onChange:e=>E({...Z,subject:e.target.value}),placeholder:"Brief description of your issue"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,l.jsx)(o.T,{value:Z.description,onChange:e=>E({...Z,description:e.target.value}),placeholder:"Detailed description of your issue",rows:4})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Priority"}),(0,l.jsxs)(u.l6,{value:Z.priority,onValueChange:e=>E({...Z,priority:e}),children:[(0,l.jsx)(u.bq,{children:(0,l.jsx)(u.yv,{})}),(0,l.jsxs)(u.gC,{children:[(0,l.jsx)(u.eb,{value:"low",children:"Low"}),(0,l.jsx)(u.eb,{value:"medium",children:"Medium"}),(0,l.jsx)(u.eb,{value:"high",children:"High"}),(0,l.jsx)(u.eb,{value:"urgent",children:"Urgent"})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category"}),(0,l.jsxs)(u.l6,{value:Z.category,onValueChange:e=>E({...Z,category:e}),children:[(0,l.jsx)(u.bq,{children:(0,l.jsx)(u.yv,{})}),(0,l.jsxs)(u.gC,{children:[(0,l.jsx)(u.eb,{value:"general",children:"General"}),(0,l.jsx)(u.eb,{value:"technical",children:"Technical"}),(0,l.jsx)(u.eb,{value:"billing",children:"Billing"}),(0,l.jsx)(u.eb,{value:"feature_request",children:"Feature Request"}),(0,l.jsx)(u.eb,{value:"bug_report",children:"Bug Report"})]})]})]})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(n.$,{onClick:J,children:"Create Ticket"}),(0,l.jsx)(n.$,{variant:"outline",onClick:()=>M(!1),children:"Cancel"})]})]})})]}),(0,l.jsxs)(c.Zp,{children:[(0,l.jsx)(c.aR,{children:(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,l.jsx)(c.ZB,{children:"Support Tickets"}),(0,l.jsxs)(n.$,{className:"flex items-center space-x-2",onClick:()=>M(!0),children:[(0,l.jsx)(g.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"New Ticket"})]})]})}),(0,l.jsxs)(c.Wu,{children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 mb-6",children:[(0,l.jsxs)("div",{className:"relative flex-1",children:[(0,l.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,l.jsx)(x.p,{placeholder:"Search tickets...",value:s,onChange:e=>t(e.target.value),className:"pl-10"})]}),(0,l.jsxs)(u.l6,{value:C,onValueChange:S,children:[(0,l.jsx)(u.bq,{className:"w-full sm:w-40",children:(0,l.jsx)(u.yv,{placeholder:"Status"})}),(0,l.jsxs)(u.gC,{children:[(0,l.jsx)(u.eb,{value:"all",children:"All Status"}),(0,l.jsx)(u.eb,{value:"open",children:"Open"}),(0,l.jsx)(u.eb,{value:"in_progress",children:"In Progress"}),(0,l.jsx)(u.eb,{value:"resolved",children:"Resolved"}),(0,l.jsx)(u.eb,{value:"closed",children:"Closed"})]})]}),(0,l.jsxs)(u.l6,{value:_,onValueChange:L,children:[(0,l.jsx)(u.bq,{className:"w-full sm:w-40",children:(0,l.jsx)(u.yv,{placeholder:"Priority"})}),(0,l.jsxs)(u.gC,{children:[(0,l.jsx)(u.eb,{value:"all",children:"All Priority"}),(0,l.jsx)(u.eb,{value:"urgent",children:"Urgent"}),(0,l.jsx)(u.eb,{value:"high",children:"High"}),(0,l.jsx)(u.eb,{value:"medium",children:"Medium"}),(0,l.jsx)(u.eb,{value:"low",children:"Low"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[K.map(e=>{var s,t;return(0,l.jsx)("div",{className:"border rounded-lg p-4 hover:bg-gray-50 transition-colors",children:(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[H(e.status),(0,l.jsx)("h3",{className:"font-medium text-gray-900",children:e.subject}),(0,l.jsx)(d.E,{variant:U(e.status),children:e.status.replace("_"," ")}),(0,l.jsx)(d.E,{variant:Y(e.priority),children:e.priority})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-2",children:e.description}),(0,l.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(N.A,{className:"w-4 h-4"}),(0,l.jsxs)("span",{children:[null==(s=e.userId)?void 0:s.firstName," ",null==(t=e.userId)?void 0:t.lastName]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(b.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:(0,w.Yq)(e.createdAt)})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(j,{className:"w-4 h-4"}),(0,l.jsxs)("span",{children:["#",e.ticketNumber]})]})]})]}),(0,l.jsx)("div",{className:"flex items-center space-x-2",children:(0,l.jsx)(n.$,{variant:"outline",size:"sm",children:"View Details"})})]})},e._id)}),0===K.length&&(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)(v.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,l.jsx)("p",{className:"text-gray-600",children:"No support tickets found"}),(0,l.jsxs)(n.$,{className:"mt-4",onClick:()=>M(!0),children:[(0,l.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Create Your First Ticket"]})]})]})]})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[399,995,713,668,663,75,440,390,110,358],()=>s(3266)),_N_E=e.O()}]);