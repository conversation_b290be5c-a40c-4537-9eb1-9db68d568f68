{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "PVmk5TQbvtQgyuN9AKNKg", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MvRMowirayUMXTqa0JsuzpVeeZhjIfs4Gxrv2knh34Q=", "__NEXT_PREVIEW_MODE_ID": "e8a824219308e4d0e21c2fc35de64914", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9dec97d1f98f2e99f410b1cab3e6e46056345e9270b22019f11c2e337897474d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a62e3a10e554136992af3db8a904697231f562e42bcf8b2c594f8c5974a11d1a"}}}, "functions": {}, "sortedMiddleware": ["/"]}