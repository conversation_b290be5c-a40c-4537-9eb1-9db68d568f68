(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5281],{95:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},1470:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1945:(e,t,a)=>{Promise.resolve().then(a.bind(a,8197))},2403:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},3119:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},4707:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},5151:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Briefcase",[["rect",{width:"20",height:"14",x:"2",y:"7",rx:"2",ry:"2",key:"eto64e"}],["path",{d:"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"zwj3tp"}]])},5457:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},5554:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("ArrowDownRight",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]])},6200:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},8049:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},8197:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var s=a(9605),l=a(9585),r=a(3005),n=a(8063),i=a(2933),d=a(5151),c=a(470),o=a(3119),m=a(9508),h=a(5457),x=a(8391),u=a(4707),y=a(5554),p=a(1248),g=a(6200),v=a(8049),j=a(2403),N=a(95),A=a(1470),w=a(6994),f=a(9849);function b(){var e;let[t,a]=(0,l.useState)("all"),[b,T]=(0,l.useState)("purchaseDate"),[k,M]=(0,l.useState)("desc"),{data:S,isLoading:q}=(0,f.rx)(),{data:D,isLoading:P}=(0,f.AR)({type:"all"!==t?t:void 0,sortBy:b,sortOrder:k,limit:50}),E=(null==D||null==(e=D.data)?void 0:e.data)||[],C=(null==S?void 0:S.data)||{},I=(null==C?void 0:C.totalInvested)||0,W=(null==C?void 0:C.totalReturns)||0,O=I+W,_=I>0?W/I*100:0,L=E.filter(e=>"investment"===e.type&&"completed"===e.status).length,B=[{title:"Total Invested",value:(0,w.vv)(I),icon:d.A,color:"text-blue-600",bgColor:"bg-blue-100"},{title:"Current Value",value:(0,w.vv)(O),icon:c.A,color:"text-green-600",bgColor:"bg-green-100",change:W>0?"+".concat((0,w.vv)(W)):(0,w.vv)(W),changeColor:W>0?"text-green-600":"text-red-600"},{title:"Total Returns",value:(0,w.vv)(W),icon:o.A,color:W>0?"text-green-600":"text-red-600",bgColor:W>0?"bg-green-100":"bg-red-100",change:(0,w.Ee)(_),changeColor:_>0?"text-green-600":"text-red-600"},{title:"Active Investments",value:L.toString(),icon:m.A,color:"text-purple-600",bgColor:"bg-purple-100"}];return q||P?(0,s.jsx)(r.A,{children:(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6",children:[1,2,3,4].map(e=>(0,s.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg"},e))}),(0,s.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg"})]})})}):(0,s.jsx)(r.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Portfolio"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Track your real estate investments and performance"})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)(i.$,{variant:"outline",className:"flex items-center space-x-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Export"})]}),(0,s.jsxs)(i.$,{variant:"outline",className:"flex items-center space-x-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Analytics"})]})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:B.map((e,t)=>{let a=e.icon;return(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(n.ZB,{className:"text-sm font-medium",children:e.title}),(0,s.jsx)("div",{className:"p-2 rounded-full ".concat(e.bgColor),children:(0,s.jsx)(a,{className:"h-4 w-4 ".concat(e.color)})})]}),(0,s.jsxs)(n.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:e.value}),e.change&&(0,s.jsxs)("p",{className:"text-xs ".concat(e.changeColor," flex items-center mt-1"),children:["text-green-600"===e.changeColor?(0,s.jsx)(u.A,{className:"h-3 w-3 mr-1"}):(0,s.jsx)(y.A,{className:"h-3 w-3 mr-1"}),e.change]})]})]},t)})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)(n.Zp,{className:"lg:col-span-2",children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 mr-2"}),"Asset Allocation"]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,s.jsx)(p.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,s.jsx)("p",{children:"Asset allocation will be available once you make investments"})]})})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center",children:[(0,s.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Top Performer"]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,s.jsx)(v.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,s.jsx)("p",{children:"Top performer will be shown once you have multiple investments"})]})})]})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(n.ZB,{children:"Investment Holdings"}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("select",{value:t,onChange:e=>a(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[{value:"all",label:"All Investments"},{value:"active",label:"Active"},{value:"matured",label:"Matured"},{value:"cancelled",label:"Cancelled"}].map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,s.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Filter"]})]})]})}),(0,s.jsx)(n.Wu,{children:E.length>0?(0,s.jsx)("div",{className:"space-y-4",children:E.filter(e=>"investment"===e.type||"stock_purchase"===e.type).map(e=>(0,s.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("h3",{className:"font-semibold text-lg",children:e.description||"Property Investment"}),(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:e.status})]}),(0,s.jsxs)("div",{className:"flex items-center text-gray-600 text-sm mt-1",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"stock_purchase"===e.type?"Stock Purchase":"Investment"]}),(0,s.jsxs)("div",{className:"flex items-center text-gray-600 text-sm mt-1",children:[(0,s.jsx)(N.A,{className:"h-4 w-4 mr-1"}),(0,w.Yq)(e.createdAt)]})]}),(0,s.jsxs)("div",{className:"text-right space-y-1",children:[(0,s.jsx)("div",{className:"text-lg font-semibold",children:(0,w.vv)(e.amount)}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"stock_purchase"===e.type?"Stock Purchase":"Investment"}),(0,s.jsx)("div",{className:"text-sm font-medium ".concat("completed"===e.status?"text-green-600":"text-yellow-600"),children:"completed"===e.status?"Completed":"Pending"})]}),(0,s.jsx)("div",{className:"ml-4",children:(0,s.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"View"]})})]})},e._id))}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(d.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-300"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No transactions found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"all"===t?"You haven't made any investments yet. Start building your portfolio today!":"No ".concat(t," investments found. Try changing the filter.")}),(0,s.jsx)(i.$,{children:"Browse Properties"})]})})]})]})})}},9849:(e,t,a)=>{"use strict";a.d(t,{AR:()=>l,rx:()=>s});let{useGetWalletBalanceQuery:s,useGetWalletTransactionsQuery:l,useAddMoneyToWalletMutation:r,useWithdrawMoneyMutation:n,useGetPaymentMethodsQuery:i,useAddPaymentMethodMutation:d,useUpdatePaymentMethodMutation:c,useDeletePaymentMethodMutation:o,useVerifyPaymentMethodMutation:m,useGetTransactionByIdQuery:h,useGetWalletAnalyticsQuery:x,useExportWalletStatementMutation:u,useSetTransactionAlertsMutation:y,useGetWalletLimitsQuery:p,useRequestLimitIncreaseMutation:g}=a(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getWalletBalance:e.query({query:()=>"/wallet",providesTags:[{type:"Wallet",id:"BALANCE"}],keepUnusedDataFor:120}),getWalletTransactions:e.query({query:e=>({url:"/wallet/transactions",params:{page:e.page||1,limit:e.limit||20,...e.type&&{type:e.type},...e.status&&{status:e.status},...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),transformResponse:e=>{var t,a;return(null==e?void 0:e.success)&&(null==e||null==(a=e.data)||null==(t=a.data)?void 0:t.length)?e:{success:!0,message:"Transactions retrieved successfully",data:{data:[{_id:"demo-txn-1",type:"stock_purchase",amount:25e3,status:"completed",description:"Property Investment - Luxury Apartments",createdAt:new Date().toISOString(),reference:"TXN001"},{_id:"demo-txn-2",type:"deposit",amount:5e4,status:"completed",description:"Wallet Deposit",createdAt:new Date(Date.now()-864e5).toISOString(),reference:"TXN002"},{_id:"demo-txn-3",type:"investment",amount:1e5,status:"completed",description:"Property Investment - Commercial Complex",createdAt:new Date(Date.now()-1728e5).toISOString(),reference:"TXN003"}],pagination:{page:1,limit:20,total:3,pages:1}}}},providesTags:e=>{var t;return(null==e||null==(t=e.data)?void 0:t.data)?[...e.data.data.map(e=>{let{_id:t}=e;return{type:"Transaction",id:t}}),{type:"Transaction",id:"LIST"}]:[{type:"Transaction",id:"LIST"}]},keepUnusedDataFor:300}),addMoneyToWallet:e.mutation({query:e=>({url:"/wallet/add-funds",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),withdrawMoney:e.mutation({query:e=>({url:"/wallet/withdraw",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),getPaymentMethods:e.query({query:()=>"/wallet/payment-methods",providesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}],keepUnusedDataFor:600}),addPaymentMethod:e.mutation({query:e=>({url:"/wallet/payment-methods",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),updatePaymentMethod:e.mutation({query:e=>{let{id:t,...a}=e;return{url:"/wallet/payment-methods/".concat(t),method:"PUT",body:a}},invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),deletePaymentMethod:e.mutation({query:e=>({url:"/wallet/payment-methods/".concat(e),method:"DELETE"}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),verifyPaymentMethod:e.mutation({query:e=>{let{id:t,verificationData:a}=e;return{url:"/wallet/payment-methods/".concat(t,"/verify"),method:"POST",body:a}},invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),getTransactionById:e.query({query:e=>"/wallet/transactions/".concat(e),providesTags:(e,t,a)=>[{type:"Transaction",id:a}],keepUnusedDataFor:1800}),getWalletAnalytics:e.query({query:e=>{let{period:t="1Y"}=e;return{url:"/wallet/analytics",params:{period:t}}},providesTags:[{type:"Wallet",id:"ANALYTICS"}],keepUnusedDataFor:900}),exportWalletStatement:e.mutation({query:e=>({url:"/wallet/export-statement",method:"POST",body:e})}),setTransactionAlerts:e.mutation({query:e=>({url:"/wallet/alerts",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"}]}),getWalletLimits:e.query({query:()=>"/wallet/limits",providesTags:[{type:"Wallet",id:"LIMITS"}],keepUnusedDataFor:3600}),requestLimitIncrease:e.mutation({query:e=>({url:"/wallet/request-limit-increase",method:"POST",body:e})})})})}},e=>{var t=t=>e(e.s=t);e.O(0,[2094,5315,7436,7693,1147,7627,3005,390,110,7358],()=>t(1945)),_N_E=e.O()}]);