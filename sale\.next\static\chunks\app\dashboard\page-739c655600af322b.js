(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{192:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(6501).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},636:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(6501).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2731:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(6501).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3299:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(6501).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},3831:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var a=t(9605);function l(e){let{title:s,subtitle:t,icon:l,greeting:r,userName:i,actions:c,children:d}=e;return(0,a.jsx)("div",{className:"bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg p-6 text-white shadow-lg",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center gap-3",children:[l&&(0,a.jsx)(l,{className:"h-8 w-8"}),r&&i?"".concat(r,", ").concat(i,"! \uD83D\uDC4B"):s]}),(0,a.jsx)("p",{className:"text-sky-100",children:t||"Manage and track your SGM sales activities with real-time insights"})]}),(c||d)&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[c,d]})]})})}t(9585)},4519:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>I});var a=t(9605);t(9585);var l=t(6440),r=t(8063),i=t(2790),c=t(6501);let d=(0,c.A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),n=(0,c.A)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]]),x=(0,c.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var o=t(6901),m=t(5048),h=t(9581),u=t(4842),y=t(6994);function g(e){let{stats:s,previousStats:t}=e,l=(e,s)=>{if(!s||!e||0===s)return null;let t=(0,y.tP)(e,s),l=t>0,r=t<0;return(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs ".concat(l?"text-green-600":r?"text-red-600":"text-gray-500"),children:[l&&(0,a.jsx)(d,{className:"w-3 h-3"}),r&&(0,a.jsx)(n,{className:"w-3 h-3"}),!l&&!r&&(0,a.jsx)(x,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[Math.abs(t||0).toFixed(1),"%"]})]})},c=s.monthlyTarget>0?s.monthlyAchieved/s.monthlyTarget*100:0;return(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium text-gray-600",children:"Total Leads"}),(0,a.jsx)(o.A,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,y.ZV)(s.totalLeads)}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(0,y.ZV)(s.leadsThisMonth)," this month"]})]}),l(s.totalLeads,null==t?void 0:t.totalLeads)]}),(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsxs)(i.E,{variant:"info",className:"text-xs",children:[(0,y.ZV)(s.qualifiedLeads)," qualified"]})})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium text-gray-600",children:"Total Sales"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,y.ZV)(s.totalSales)}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(0,y.ZV)(s.salesThisMonth)," this month"]})]}),l(s.totalSales,null==t?void 0:t.totalSales)]}),(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsxs)(i.E,{variant:"success",className:"text-xs",children:[(s.conversionRate||0).toFixed(1),"% conversion"]})})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,a.jsx)(h.A,{className:"h-4 w-4 text-yellow-600"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,y.vv)(s.totalRevenue)}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(0,y.vv)(s.revenueThisMonth)," this month"]})]}),l(s.totalRevenue,null==t?void 0:t.totalRevenue)]}),(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsxs)(i.E,{variant:"warning",className:"text-xs",children:[(0,y.vv)(s.averageDealSize)," avg deal"]})})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium text-gray-600",children:"Commission"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-purple-600"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,y.vv)(s.commission)}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Earned this month"})]}),l(s.commission,null==t?void 0:t.commission)]}),(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Target Progress"}),(0,a.jsxs)("span",{className:"font-medium ".concat(c>=100?"text-green-600":c>=75?"text-yellow-600":"text-red-600"),children:[(c||0).toFixed(0),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-1",children:(0,a.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(c>=100?"bg-green-500":c>=75?"bg-yellow-500":"bg-red-500"),style:{width:"".concat(Math.min(c,100),"%")}})})]})]})]})]})}var j=t(2933),v=t(3299),p=t(5857),N=t(1713),f=t(6351),b=t(8347),w=t(7456),A=function(e){return e.NEW="new",e.CONTACTED="contacted",e.QUALIFIED="qualified",e.PROPOSAL="proposal",e.NEGOTIATION="negotiation",e.CLOSED_WON="closed_won",e.CLOSED_LOST="closed_lost",e}({}),T=function(e){return e.LEAD_CREATED="lead_created",e.LEAD_UPDATED="lead_updated",e.LEAD_CONTACTED="lead_contacted",e.SALE_CREATED="sale_created",e.SALE_COMPLETED="sale_completed",e.FOLLOW_UP_SCHEDULED="follow_up_scheduled",e.NOTE_ADDED="note_added",e}({});let E=e=>{switch(e){case T.LEAD_CREATED:return(0,a.jsx)(v.A,{className:"w-4 h-4 text-blue-600"});case T.LEAD_CONTACTED:return(0,a.jsx)(p.A,{className:"w-4 h-4 text-green-600"});case T.SALE_CREATED:case T.SALE_COMPLETED:return(0,a.jsx)(h.A,{className:"w-4 h-4 text-yellow-600"});case T.FOLLOW_UP_SCHEDULED:return(0,a.jsx)(N.A,{className:"w-4 h-4 text-purple-600"});case T.NOTE_ADDED:return(0,a.jsx)(f.A,{className:"w-4 h-4 text-gray-600"});default:return(0,a.jsx)(b.A,{className:"w-4 h-4 text-gray-600"})}},D=e=>{switch(e){case T.LEAD_CREATED:return"bg-blue-50 border-blue-200";case T.LEAD_CONTACTED:return"bg-green-50 border-green-200";case T.SALE_CREATED:case T.SALE_COMPLETED:return"bg-yellow-50 border-yellow-200";case T.FOLLOW_UP_SCHEDULED:return"bg-purple-50 border-purple-200";case T.NOTE_ADDED:default:return"bg-gray-50 border-gray-200"}};function L(e){let{activities:s,showAll:t=!1}=e,l=t?s:s.slice(0,5);return(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between",children:[(0,a.jsx)(r.ZB,{className:"text-lg font-semibold",children:"Recent Activities"}),(0,a.jsx)(j.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(w.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)(r.Wu,{children:[0===l.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(b.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No recent activities"})]}):(0,a.jsx)("div",{className:"space-y-4",children:l.map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border ".concat(D(e.type)),children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:E(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),(0,a.jsx)("p",{className:"text-xs text-gray-500 flex-shrink-0 ml-2",children:(0,y.r6)(e.createdAt)})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),(0,a.jsx)("div",{className:"flex items-center justify-between mt-2",children:(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["by ",e.userName]})})]})]},e.id))}),!t&&s.length>5&&(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)(j.$,{variant:"ghost",size:"sm",children:"View All Activities"})})]})]})}var k=t(8232),C=t(1468),S=t(7229);let _=e=>{switch(e){case A.NEW:return"secondary";case A.CONTACTED:return"info";case A.QUALIFIED:return"warning";case A.PROPOSAL:return"default";case A.NEGOTIATION:return"warning";case A.CLOSED_WON:return"success";case A.CLOSED_LOST:return"destructive";default:return"secondary"}},O=e=>e>=80?"text-red-600 bg-red-100":e>=60?"text-yellow-600 bg-yellow-100":e>=40?"text-blue-600 bg-blue-100":"text-gray-600 bg-gray-100";function q(e){let{leads:s,showAll:t=!1}=e,l=t?s:s.slice(0,6);return(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between",children:[(0,a.jsx)(r.ZB,{className:"text-lg font-semibold",children:"Recent Leads"}),(0,a.jsx)(j.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(w.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)(r.Wu,{children:[0===l.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(k.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No leads found"})]}):(0,a.jsx)("div",{className:"space-y-4",children:l.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(k.A,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:[e.firstName," ",e.lastName]}),(0,a.jsx)(i.E,{variant:_(e.status),className:"text-xs",children:e.status.replace("_"," ")}),(0,a.jsx)("div",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(O(e.score)),children:e.score})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-1 text-xs text-gray-500",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(C.A,{className:"w-3 h-3 mr-1"}),e.email]}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"w-3 h-3 mr-1"}),e.phone]})]}),e.interestedProperty&&(0,a.jsxs)("div",{className:"flex items-center mt-1 text-xs text-gray-500",children:[(0,a.jsx)(S.A,{className:"w-3 h-3 mr-1"}),"Interested in: ",e.interestedProperty]}),e.budget&&(0,a.jsxs)("div",{className:"flex items-center mt-1 text-xs text-gray-500",children:[(0,a.jsx)(h.A,{className:"w-3 h-3 mr-1"}),"Budget: ",(0,y.vv)(e.budget)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[e.nextFollowUp&&(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,a.jsx)(N.A,{className:"w-3 h-3 mr-1"}),"Next follow-up"]}),(0,a.jsx)("div",{className:"text-xs font-medium text-gray-900",children:(0,y.Yq)(e.nextFollowUp)})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)(j.$,{size:"sm",variant:"outline",className:"h-7 px-2",children:(0,a.jsx)(p.A,{className:"w-3 h-3"})}),(0,a.jsx)(j.$,{size:"sm",variant:"outline",className:"h-7 px-2",children:(0,a.jsx)(C.A,{className:"w-3 h-3"})})]})]})]},e.id))}),!t&&s.length>6&&(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)(j.$,{variant:"ghost",size:"sm",children:"View All Leads"})})]})]})}var R=t(3831),P=t(192),M=t(7418);let Z=(0,c.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var F=t(636),U=t(2731);let W=(0,c.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var $=t(8642),B=t(3500);let V=e=>({...e,status:e.status,source:e.source,lastContactDate:e.lastContactDate||e.updatedAt,tags:e.tags||[]}),z=e=>({...e,type:e.type.toLowerCase().replace("_","_"),entityType:e.entityType||"lead",metadata:e.metadata||{}}),G=()=>{let e=new Date().getHours();return e<12?"Good morning":e<17?"Good afternoon":"Good evening"};function I(){var e;let{data:s,isLoading:t,error:c,refetch:d}=(0,$.Pb)(),{data:n,isLoading:x}=(0,$.Hu)({limit:10}),{data:o,isLoading:h}=(0,$.$V)({page:1,limit:5,sortBy:"createdAt",sortOrder:"desc"}),{data:v,isLoading:p}=(0,B.mc)({days:1}),f=async()=>{await d()};if(t)return(0,a.jsx)(l.A,{title:"Sales Dashboard",children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)(r.Zp,{className:"animate-pulse",children:(0,a.jsxs)(r.Wu,{className:"p-6",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]})},s))})})});if(c)return(0,a.jsx)(l.A,{title:"Sales Dashboard",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(P.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Failed to load dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Please try refreshing the page"})]})})});let b=null==s?void 0:s.data,w=((null==n?void 0:n.data)||[]).map(z),A=((null==o||null==(e=o.data)?void 0:e.leads)||[]).map(V),T=(null==v?void 0:v.data)||[];return(0,a.jsx)(l.A,{title:"SGM Sales Dashboard",children:(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsx)(R.A,{title:"SGM Sales Dashboard",subtitle:"Complete business overview with real-time analytics, insights, and performance metrics for SGM properties",icon:M.A,greeting:G(),userName:"John Doe",actions:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(j.$,{variant:"secondary",onClick:f,disabled:t,className:"bg-white/20 hover:bg-white/30 text-white border-white/30",children:[(0,a.jsx)(Z,{className:"h-4 w-4 mr-2 ".concat(t?"animate-spin":"")}),"Refresh"]}),(0,a.jsxs)(j.$,{className:"bg-yellow-500 hover:bg-yellow-600 text-black font-semibold",children:[(0,a.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"Export Report"]})]})}),b?(0,a.jsx)(g,{stats:b,previousStats:b.previousStats}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)(r.Zp,{className:"animate-pulse",children:(0,a.jsxs)(r.Wu,{className:"p-6",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]})},s))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(j.$,{className:"h-16 flex flex-col items-center justify-center space-y-1 bg-sky-500 hover:bg-sky-600 text-white",children:[(0,a.jsx)(U.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-sm",children:"Add Lead"})]}),(0,a.jsxs)(j.$,{variant:"outline",className:"h-16 flex flex-col items-center justify-center space-y-1 border-green-500 text-green-600 hover:bg-green-50",children:[(0,a.jsx)(N.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-sm",children:"Schedule Follow-up"})]}),(0,a.jsxs)(j.$,{variant:"outline",className:"h-16 flex flex-col items-center justify-center space-y-1 border-yellow-500 text-yellow-600 hover:bg-yellow-50",children:[(0,a.jsx)(u.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-sm",children:"View Targets"})]}),(0,a.jsxs)(j.$,{variant:"outline",className:"h-16 flex flex-col items-center justify-center space-y-1 border-gray-800 text-gray-800 hover:bg-gray-50",children:[(0,a.jsx)(m.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-sm",children:"Sales Report"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsx)(q,{leads:A})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between",children:[(0,a.jsx)(r.ZB,{className:"text-lg font-semibold",children:"Today's Tasks"}),(0,a.jsx)(i.E,{variant:"secondary",children:T.length})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"space-y-3",children:T.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:border-sky-300 transition-colors",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.dueTime||"No time set"})]}),(0,a.jsx)(i.E,{variant:"high"===e.priority?"destructive":"medium"===e.priority?"default":"secondary",className:"text-xs ".concat("high"===e.priority?"bg-red-500 text-white":"medium"===e.priority?"bg-yellow-500 text-white":"bg-green-500 text-white"),children:e.priority})]},e.id))}),(0,a.jsxs)(j.$,{variant:"ghost",size:"sm",className:"w-full mt-3",children:["View All Tasks",(0,a.jsx)(W,{className:"w-4 h-4 ml-2"})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{className:"text-lg font-semibold",children:"This Month"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Target"}),(0,a.jsx)("span",{className:"font-medium",children:(0,y.vv)((null==b?void 0:b.monthlyTarget)||0)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Achieved"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:(0,y.vv)((null==b?void 0:b.monthlyAchieved)||0)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Remaining"}),(0,a.jsx)("span",{className:"font-medium text-red-600",children:(0,y.vv)(((null==b?void 0:b.monthlyTarget)||0)-((null==b?void 0:b.monthlyAchieved)||0))})]}),(0,a.jsxs)("div",{className:"pt-2 border-t",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Progress"}),(0,a.jsxs)("span",{className:"font-medium",children:[(null==b?void 0:b.monthlyTarget)?((b.monthlyAchieved||0)/b.monthlyTarget*100).toFixed(0):0,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,a.jsx)("div",{className:"bg-sky-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat((null==b?void 0:b.monthlyTarget)?(b.monthlyAchieved||0)/b.monthlyTarget*100:0,"%")}})})]})]})})]})]})]}),(0,a.jsx)(L,{activities:w})]})})}},7456:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(6501).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},8445:(e,s,t)=>{Promise.resolve().then(t.bind(t,4519))},8642:(e,s,t)=>{"use strict";t.d(s,{$V:()=>c,FO:()=>n,Hu:()=>r,OT:()=>E,Pb:()=>l,WD:()=>v,Zu:()=>N,aT:()=>j,cm:()=>p,nK:()=>h,pv:()=>T,ro:()=>f});let a=t(6701).q.injectEndpoints({endpoints:e=>({getDashboardStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getDashboardActivities:e.query({query:e=>({url:"/dashboard/activities",params:e}),providesTags:["Dashboard"]}),getSalesStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getLeads:e.query({query:e=>({url:"/sales/leads",params:e}),providesTags:["Lead"]}),getLeadById:e.query({query:e=>"/sales/leads/".concat(e),providesTags:(e,s,t)=>[{type:"Lead",id:t}]}),createLead:e.mutation({query:e=>({url:"/sales/leads",method:"POST",body:e}),invalidatesTags:["Lead","Dashboard"]}),updateLead:e.mutation({query:e=>{let{id:s,data:t}=e;return{url:"/sales/leads/".concat(s),method:"PUT",body:t}},invalidatesTags:(e,s,t)=>{let{id:a}=t;return[{type:"Lead",id:a},"Lead","Dashboard"]}}),deleteLead:e.mutation({query:e=>({url:"/sales/leads/".concat(e),method:"DELETE"}),invalidatesTags:["Lead","Dashboard"]}),assignLead:e.mutation({query:e=>{let{leadId:s,salesRepId:t}=e;return{url:"/sales/leads/".concat(s,"/assign"),method:"POST",body:{salesRepId:t}}},invalidatesTags:["Lead"]}),getCustomers:e.query({query:e=>({url:"/sales/customers",params:e}),providesTags:["Customer"]}),getCustomerById:e.query({query:e=>"/sales/customers/".concat(e),providesTags:(e,s,t)=>[{type:"Customer",id:t}]}),createCustomer:e.mutation({query:e=>({url:"/sales/customers",method:"POST",body:e}),invalidatesTags:["Customer","Dashboard"]}),updateCustomer:e.mutation({query:e=>{let{id:s,data:t}=e;return{url:"/sales/customers/".concat(s),method:"PUT",body:t}},invalidatesTags:(e,s,t)=>{let{id:a}=t;return[{type:"Customer",id:a},"Customer"]}}),getCommissions:e.query({query:e=>({url:"/sales/commissions",params:e}),providesTags:["Commission"]}),getSalesTargets:e.query({query:()=>"/sales/targets",providesTags:["Report"]}),createSalesTarget:e.mutation({query:e=>({url:"/sales/targets",method:"POST",body:e}),invalidatesTags:["Report","Dashboard"]}),updateSalesTarget:e.mutation({query:e=>{let{id:s,data:t}=e;return{url:"/sales/targets/".concat(s),method:"PUT",body:t}},invalidatesTags:["Report","Dashboard"]}),getFollowUps:e.query({query:e=>({url:"/follow-ups",params:e}),providesTags:["Task"]}),createFollowUp:e.mutation({query:e=>({url:"/follow-ups",method:"POST",body:e}),invalidatesTags:["Task","Dashboard"]}),updateFollowUp:e.mutation({query:e=>{let{id:s,data:t}=e;return{url:"/follow-ups/".concat(s),method:"PUT",body:t}},invalidatesTags:["Task","Dashboard"]}),deleteFollowUp:e.mutation({query:e=>({url:"/follow-ups/".concat(e),method:"DELETE"}),invalidatesTags:["Task","Dashboard"]})})}),{useGetDashboardStatsQuery:l,useGetDashboardActivitiesQuery:r,useGetSalesStatsQuery:i,useGetLeadsQuery:c,useGetLeadByIdQuery:d,useCreateLeadMutation:n,useUpdateLeadMutation:x,useDeleteLeadMutation:o,useAssignLeadMutation:m,useGetCustomersQuery:h,useGetCustomerByIdQuery:u,useCreateCustomerMutation:y,useUpdateCustomerMutation:g,useGetCommissionsQuery:j,useGetSalesTargetsQuery:v,useCreateSalesTargetMutation:p,useUpdateSalesTargetMutation:N,useGetFollowUpsQuery:f,useCreateFollowUpMutation:b,useUpdateFollowUpMutation:w,useDeleteFollowUpMutation:A}=a,{useGetCustomersQuery:T,useGetCommissionsQuery:E}=a}},e=>{var s=s=>e(e.s=s);e.O(0,[399,995,713,668,663,75,440,390,110,358],()=>s(8445)),_N_E=e.O()}]);