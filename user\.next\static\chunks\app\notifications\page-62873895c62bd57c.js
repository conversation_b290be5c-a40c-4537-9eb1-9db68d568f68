(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6173],{95:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},1470:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1645:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},2006:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2549:(e,s,a)=>{Promise.resolve().then(a.bind(a,2611))},2611:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>k});var t=a(9605),l=a(9585),c=a(3005),i=a(8063),n=a(2933),r=a(470),d=a(3119),x=a(5828),m=a(7439),h=a(2006),o=a(9249),u=a(7412),y=a(9742),p=a(95),j=a(1645),N=a(1470),f=a(3760),b=a(6994),g=a(7707),v=a(6845);function k(){var e;let[s,a]=(0,l.useState)("all"),[k,w]=(0,l.useState)([]),{data:A,isLoading:C,refetch:M}=(0,g.FZ)({unreadOnly:"unread"===s,limit:50}),[R]=(0,g.b7)(),[_]=(0,g.d1)(),[S]=(0,g.nC)(),z=(null==A||null==(e=A.data)?void 0:e.data)||[],$=z.filter(e=>!e.isRead).length,P=e=>{switch(e){case"investment":return(0,t.jsx)(r.A,{className:"h-5 w-5 text-blue-600"});case"payment":return(0,t.jsx)(d.A,{className:"h-5 w-5 text-green-600"});case"security":case"error":return(0,t.jsx)(x.A,{className:"h-5 w-5 text-red-600"});case"referral":return(0,t.jsx)(m.A,{className:"h-5 w-5 text-purple-600"});case"success":return(0,t.jsx)(h.A,{className:"h-5 w-5 text-green-600"});case"warning":return(0,t.jsx)(x.A,{className:"h-5 w-5 text-yellow-600"});default:return(0,t.jsx)(o.A,{className:"h-5 w-5 text-blue-600"})}},Z=(e,s)=>{let a=s?"50":"100";switch(e){case"investment":default:return"bg-blue-".concat(a);case"payment":case"success":return"bg-green-".concat(a);case"security":case"error":return"bg-red-".concat(a);case"referral":return"bg-purple-".concat(a);case"warning":return"bg-yellow-".concat(a)}},q=async e=>{try{await R(e).unwrap(),M()}catch(e){v.toast.error("Failed to mark notification as read")}},E=async()=>{try{await _().unwrap(),v.toast.success("All notifications marked as read"),M()}catch(e){v.toast.error("Failed to mark all notifications as read")}},V=async e=>{try{await S(e).unwrap(),v.toast.success("Notification deleted"),M()}catch(e){v.toast.error("Failed to delete notification")}},F=async()=>{try{await Promise.all(k.map(e=>S(e).unwrap())),v.toast.success("".concat(k.length," notifications deleted")),w([]),M()}catch(e){v.toast.error("Failed to delete notifications")}},W=e=>{w(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},H=z.filter(e=>"all"===s||("unread"===s?!e.isRead:e.type===s));return C?(0,t.jsx)(c.A,{children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,t.jsx)("div",{className:"h-20 bg-gray-200 rounded-lg"},e))})]})})}):(0,t.jsx)(c.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Notifications"}),(0,t.jsxs)("p",{className:"text-gray-600 mt-1",children:["Stay updated with your investments and account activity",$>0&&(0,t.jsxs)("span",{className:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[$," unread"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[$>0&&(0,t.jsxs)(n.$,{variant:"outline",onClick:E,className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Mark All Read"})]}),(0,t.jsxs)(n.$,{variant:"outline",className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Settings"})]})]})]}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("select",{value:s,onChange:e=>a(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[{value:"all",label:"All Notifications"},{value:"unread",label:"Unread Only"},{value:"investment",label:"Investment Updates"},{value:"security",label:"Security Alerts"},{value:"system",label:"System Messages"}].map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))}),z.length>0&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",checked:k.length===z.length,onChange:()=>{k.length===z.length?w([]):w(z.map(e=>e._id))},className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:["Select All (",k.length," selected)"]})]})]}),k.length>0&&(0,t.jsx)("div",{className:"flex items-center space-x-3",children:(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:F,className:"flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Delete Selected (",k.length,")"]})]})})]})})}),H.length>0?(0,t.jsx)("div",{className:"space-y-4",children:H.map(e=>(0,t.jsx)(i.Zp,{className:"transition-all hover:shadow-md ".concat(e.isRead?"":"border-l-4 border-l-blue-500 bg-blue-50"),children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("input",{type:"checkbox",checked:k.includes(e._id),onChange:()=>W(e._id),className:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,t.jsx)("div",{className:"p-2 rounded-full ".concat(Z(e.type,e.isRead)),children:P(e.type)}),(0,t.jsx)("div",{className:"flex-1 min-w-0",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium ".concat(e.isRead?"text-gray-700":"text-gray-900"),children:e.title}),(0,t.jsx)("p",{className:"mt-1 text-sm ".concat(e.isRead?"text-gray-600":"text-gray-700"),children:e.message}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-gray-500",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(p.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:(0,b.r6)(e.createdAt)})]}),!e.isRead&&(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"New"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[e.actionUrl&&(0,t.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex items-center space-x-1",children:[(0,t.jsx)(j.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:e.actionText||"View"})]}),!e.isRead&&(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>q(e._id),className:"flex items-center space-x-1",children:[(0,t.jsx)(N.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:"Mark Read"})]}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>V(e._id),className:"flex items-center space-x-1 text-red-600 hover:text-red-700",children:(0,t.jsx)(y.A,{className:"h-3 w-3"})})]})]})})]})})},e._id))}):(0,t.jsx)(i.Zp,{children:(0,t.jsxs)(i.Wu,{className:"text-center py-12",children:[(0,t.jsx)(f.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"all"===s?"No notifications":"No ".concat(s," notifications")}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"all"===s?"You're all caught up! New notifications will appear here.":"No ".concat(s," notifications found. Try changing the filter.")}),"all"!==s&&(0,t.jsx)(n.$,{onClick:()=>a("all"),children:"View All Notifications"})]})}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Notification Preferences"]})}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Email Notifications"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"mr-2"}),(0,t.jsx)("span",{className:"text-sm",children:"Investment updates"})]}),(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"mr-2"}),(0,t.jsx)("span",{className:"text-sm",children:"Payment confirmations"})]}),(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"mr-2"}),(0,t.jsx)("span",{className:"text-sm",children:"Security alerts"})]}),(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",className:"mr-2"}),(0,t.jsx)("span",{className:"text-sm",children:"Marketing emails"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Push Notifications"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"mr-2"}),(0,t.jsx)("span",{className:"text-sm",children:"Real-time updates"})]}),(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"mr-2"}),(0,t.jsx)("span",{className:"text-sm",children:"Price alerts"})]}),(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",className:"mr-2"}),(0,t.jsx)("span",{className:"text-sm",children:"Daily summaries"})]}),(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",className:"mr-2"}),(0,t.jsx)("span",{className:"text-sm",children:"Weekly reports"})]})]})]})]}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)(n.$,{children:"Save Preferences"})})]})]})]})})}},3119:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},5828:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},9249:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},9742:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[2094,5315,7436,7693,1147,7627,3005,390,110,7358],()=>s(2549)),_N_E=e.O()}]);