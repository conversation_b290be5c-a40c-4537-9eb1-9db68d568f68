"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7627],{1128:(e,t,a)=>{a.d(t,{B3:()=>n,Lv:()=>h,Ng:()=>s,_L:()=>i,ac:()=>p,ge:()=>o,nd:()=>d,pv:()=>g,tl:()=>u,uU:()=>l});let{useLoginMutation:i,useRegisterMutation:o,useLogoutMutation:s,useRefreshTokenMutation:r,useVerifyEmailMutation:l,useResendVerificationEmailMutation:n,useForgotPasswordMutation:d,useVerifyPasswordResetOTPMutation:u,useResetPasswordMutation:h,useChangePasswordMutation:c,useVerifyPhoneMutation:f,useSendPhoneOTPMutation:m,useSendEmailOTPMutation:p,useVerifyEmailOTPMutation:g,useGetCurrentUserQuery:y,useGetUserProfileQuery:x,useCheckAuthStatusQuery:v}=a(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({login:e.mutation({query:e=>({url:"/auth/login",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),register:e.mutation({query:e=>({url:"/auth/register",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),logout:e.mutation({query:()=>({url:"/auth/logout",method:"POST"}),invalidatesTags:["Auth","User"]}),refreshToken:e.mutation({query:e=>({url:"/auth/refresh",method:"POST",body:e})}),verifyEmail:e.mutation({query:e=>({url:"/auth/verify-email",method:"POST",body:e}),invalidatesTags:["User"]}),resendVerificationEmail:e.mutation({query:e=>({url:"/auth/send-verification",method:"POST",body:e})}),forgotPassword:e.mutation({query:e=>({url:"/auth/forgot-password",method:"POST",body:e})}),verifyPasswordResetOTP:e.mutation({query:e=>({url:"/auth/verify-reset-otp",method:"POST",body:e})}),resetPassword:e.mutation({query:e=>({url:"/auth/reset-password",method:"POST",body:e})}),changePassword:e.mutation({query:e=>({url:"/auth/change-password",method:"POST",body:e}),invalidatesTags:["User"]}),verifyPhone:e.mutation({query:e=>({url:"/auth/verify-phone",method:"POST",body:e}),invalidatesTags:["User"]}),sendPhoneOTP:e.mutation({query:e=>({url:"/auth/send-phone-otp",method:"POST",body:e})}),sendEmailOTP:e.mutation({query:e=>({url:"/auth/send-otp",method:"POST",body:e})}),verifyEmailOTP:e.mutation({query:e=>({url:"/auth/verify-otp",method:"POST",body:e}),invalidatesTags:["Auth","User"]}),getCurrentUser:e.query({query:()=>"/auth/me",providesTags:["User"]}),checkAuthStatus:e.query({query:()=>"/auth/status",providesTags:["Auth"]}),getUserProfile:e.query({query:()=>"/auth/profile",providesTags:["User","Auth"]})})})},8120:(e,t,a)=>{a.d(t,{A:()=>o});var i=a(9605);a(9585);let o=e=>{let{size:t="md",variant:a="full",className:o=""}=e,s={sm:"h-8 w-8",md:"h-12 w-12",lg:"h-16 w-16",xl:"h-24 w-24"},r={sm:"text-lg",md:"text-2xl",lg:"text-3xl",xl:"text-4xl"},l=()=>(0,i.jsx)("div",{className:"".concat(s[t]," ").concat(o," relative"),children:(0,i.jsxs)("svg",{viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"w-full h-full",children:[(0,i.jsx)("circle",{cx:"50",cy:"50",r:"48",fill:"#0ea5e9",stroke:"#ffffff",strokeWidth:"2"}),(0,i.jsxs)("g",{transform:"translate(20, 25)",children:[(0,i.jsx)("rect",{x:"15",y:"20",width:"30",height:"35",fill:"#ffffff",rx:"2"}),(0,i.jsx)("rect",{x:"20",y:"25",width:"6",height:"6",fill:"#0ea5e9"}),(0,i.jsx)("rect",{x:"34",y:"25",width:"6",height:"6",fill:"#0ea5e9"}),(0,i.jsx)("rect",{x:"20",y:"35",width:"6",height:"6",fill:"#0ea5e9"}),(0,i.jsx)("rect",{x:"34",y:"35",width:"6",height:"6",fill:"#0ea5e9"}),(0,i.jsx)("rect",{x:"27",y:"45",width:"6",height:"10",fill:"#fbbf24"}),(0,i.jsx)("rect",{x:"5",y:"30",width:"8",height:"25",fill:"#ffffff",rx:"1"}),(0,i.jsx)("rect",{x:"47",y:"35",width:"8",height:"20",fill:"#ffffff",rx:"1"}),(0,i.jsx)("path",{d:"M10 15 L20 5 L30 10 L40 2 L50 8",stroke:"#fbbf24",strokeWidth:"3",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"}),(0,i.jsx)("polygon",{points:"45,2 50,8 45,8",fill:"#fbbf24"})]}),(0,i.jsx)("text",{x:"50",y:"75",textAnchor:"middle",fill:"#ffffff",fontSize:"12",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:"SGM"})]})});switch(a){case"icon":return(0,i.jsx)(l,{});case"text":return(0,i.jsx)(()=>(0,i.jsxs)("div",{className:"".concat(o," flex items-center"),children:[(0,i.jsx)("span",{className:"".concat(r[t]," font-bold sgm-accent-text"),children:"SGM"}),(0,i.jsx)("span",{className:"".concat(r[t]," font-light sgm-primary-text ml-1"),children:"Investments"})]}),{});default:return(0,i.jsx)(()=>(0,i.jsxs)("div",{className:"".concat(o," flex items-center space-x-3"),children:[(0,i.jsx)(l,{}),(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("span",{className:"".concat(r[t]," font-bold sgm-accent-text leading-none"),children:"SGM"}),(0,i.jsx)("span",{className:"text-sm sgm-primary-text leading-none",children:"Investments"})]})]}),{})}}},9311:(e,t,a)=>{a.d(t,{M_:()=>Q,jL:()=>W,GV:()=>z});var i=a(7895),o=a(6597),s=a(9559),r=a(6965),l=a(3815);let n={sidebarCollapsed:!1,sidebarMobileOpen:!1,theme:"light",loading:{global:!1,page:!1,component:{}},modals:{investmentModal:!1,withdrawalModal:!1,addMoneyModal:!1,profileModal:!1,supportModal:!1},notifications:{show:!1,unreadCount:0},searchQuery:"",filters:{}},d=(0,i.Z0)({name:"ui",initialState:n,reducers:{toggleSidebar:e=>{e.sidebarCollapsed=!e.sidebarCollapsed},setSidebarCollapsed:(e,t)=>{e.sidebarCollapsed=t.payload},toggleMobileSidebar:e=>{e.sidebarMobileOpen=!e.sidebarMobileOpen},setMobileSidebarOpen:(e,t)=>{e.sidebarMobileOpen=t.payload},setTheme:(e,t)=>{e.theme=t.payload},toggleTheme:e=>{e.theme="light"===e.theme?"dark":"light"},setGlobalLoading:(e,t)=>{e.loading.global=t.payload},setPageLoading:(e,t)=>{e.loading.page=t.payload},setComponentLoading:(e,t)=>{let{component:a,loading:i}=t.payload;e.loading.component[a]=i},openModal:(e,t)=>{e.modals[t.payload]=!0},closeModal:(e,t)=>{e.modals[t.payload]=!1},closeAllModals:e=>{Object.keys(e.modals).forEach(t=>{e.modals[t]=!1})},toggleNotifications:e=>{e.notifications.show=!e.notifications.show},setNotificationsOpen:(e,t)=>{e.notifications.show=t.payload},setUnreadCount:(e,t)=>{e.notifications.unreadCount=t.payload},setSearchQuery:(e,t)=>{e.searchQuery=t.payload},setFilter:(e,t)=>{let{key:a,value:i}=t.payload;e.filters[a]=i},clearFilters:e=>{e.filters={}},resetUI:e=>({...n,theme:e.theme})}}),{toggleSidebar:u,setSidebarCollapsed:h,toggleMobileSidebar:c,setMobileSidebarOpen:f,setTheme:m,toggleTheme:p,setGlobalLoading:g,setPageLoading:y,setComponentLoading:x,openModal:v,closeModal:w,closeAllModals:b,toggleNotifications:T,setNotificationsOpen:j,setUnreadCount:P,setSearchQuery:S,setFilter:C,clearFilters:O,resetUI:q}=d.actions,M=d.reducer,A=(0,i.Z0)({name:"notifications",initialState:{notifications:[],unreadCount:0,loading:!1,error:null},reducers:{setNotifications:(e,t)=>{e.notifications=t.payload,e.unreadCount=t.payload.filter(e=>!e.read).length},addNotification:(e,t)=>{e.notifications.unshift(t.payload),t.payload.read||(e.unreadCount+=1)},markAsRead:(e,t)=>{let a=e.notifications.find(e=>e.id===t.payload);a&&!a.read&&(a.read=!0,e.unreadCount=Math.max(0,e.unreadCount-1))},markAllAsRead:e=>{e.notifications.forEach(e=>{e.read=!0}),e.unreadCount=0},removeNotification:(e,t)=>{let a=e.notifications.findIndex(e=>e.id===t.payload);-1!==a&&(e.notifications[a].read||(e.unreadCount=Math.max(0,e.unreadCount-1)),e.notifications.splice(a,1))},clearAllNotifications:e=>{e.notifications=[],e.unreadCount=0},setLoading:(e,t)=>{e.loading=t.payload},setError:(e,t)=>{e.error=t.payload},receiveNotification:(e,t)=>{if(e.notifications.unshift(t.payload),t.payload.read||(e.unreadCount+=1),e.notifications.length>50){let t=e.notifications.splice(50).filter(e=>!e.read).length;e.unreadCount=Math.max(0,e.unreadCount-t)}}}}),{setNotifications:N,addNotification:k,markAsRead:E,markAllAsRead:U,removeNotification:L,clearAllNotifications:R,setLoading:G,setError:I,receiveNotification:_}=A.actions,F=A.reducer,Q=(0,i.U1)({reducer:{[r.q.reducerPath]:r.q.reducer,auth:l.Ay,ui:M,notifications:F},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE","persist/PAUSE","persist/PURGE","persist/REGISTER"]}}).concat(r.q.middleware),devTools:!1});(0,o.$k)(Q.dispatch);let W=()=>(0,s.wA)(),z=s.d4}}]);