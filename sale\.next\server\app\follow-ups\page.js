(()=>{var e={};e.id=177,e.ids=[177],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3799:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>S});var t=a(40969),r=a(73356),l=a(88251),i=a(66949),d=a(46411),n=a(57387),o=a(76650),c=a(2223),u=a(82039),m=a(56252),x=a(58896),h=a(61115),p=a(8713),g=a(79201),y=a(77060),j=a(21857),b=a(14493),v=a(31435),N=a(71727),f=a(82583),w=a(77272),k=a(22190),A=a(21764),T=a(99206);let q=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"completed":return"bg-green-100 text-green-800";case"missed":return"bg-red-100 text-red-800";case"rescheduled":return"bg-sky-100 text-sky-800";default:return"bg-gray-100 text-gray-800"}},P=e=>{switch(e){case"high":return"bg-red-100 text-red-800";case"medium":return"bg-yellow-100 text-yellow-800";case"low":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},C=e=>{switch(e){case"call":return(0,t.jsx)(u.A,{className:"w-4 h-4"});case"email":return(0,t.jsx)(m.A,{className:"w-4 h-4"});case"meeting":return(0,t.jsx)(x.A,{className:"w-4 h-4"});case"visit":return(0,t.jsx)(h.A,{className:"w-4 h-4"});default:return(0,t.jsx)(p.A,{className:"w-4 h-4"})}},D=e=>{switch(e){case"completed":return(0,t.jsx)(g.A,{className:"w-4 h-4 text-green-600"});case"missed":return(0,t.jsx)(y.A,{className:"w-4 h-4 text-red-600"});case"pending":return(0,t.jsx)(j.A,{className:"w-4 h-4 text-yellow-600"});case"rescheduled":return(0,t.jsx)(p.A,{className:"w-4 h-4 text-sky-600"});default:return(0,t.jsx)(j.A,{className:"w-4 h-4 text-gray-600"})}};function S(){let[e,s]=(0,r.useState)(""),[a,u]=(0,r.useState)("all"),[m,x]=(0,r.useState)("all"),[h,S]=(0,r.useState)("all"),[$,L]=(0,r.useState)(1),{data:U,isLoading:M,error:E,refetch:_}=(0,T.ro)({page:$,limit:20,status:"all"!==a?a:void 0,priority:"all"!==h?h:void 0}),F=U?.data?.followUps||[],R=U?.data?.pagination;return M?(0,t.jsx)(l.A,{title:"Follow-ups",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)(b.A,{className:"w-8 h-8 animate-spin text-sky-500"})})})}):E?(0,t.jsx)(l.A,{title:"Follow-ups",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(y.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Failed to load follow-ups"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Please try refreshing the page"}),(0,t.jsx)(d.$,{onClick:()=>_(),className:"bg-sky-500 hover:bg-sky-600",children:"Try Again"})]})})})}):(0,t.jsx)(l.A,{title:"Follow-ups",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Follow-ups"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage and track your follow-up activities"})]}),(0,t.jsxs)(d.$,{className:"bg-sky-500 hover:bg-sky-600 text-white",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Schedule Follow-up"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,t.jsx)(i.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,t.jsx)(j.A,{className:"w-6 h-6 text-yellow-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:F.filter(e=>"pending"===e.status).length})]})]})})}),(0,t.jsx)(i.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)(g.A,{className:"w-6 h-6 text-green-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:F.filter(e=>"completed"===e.status).length})]})]})})}),(0,t.jsx)(i.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,t.jsx)(y.A,{className:"w-6 h-6 text-red-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Missed"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:F.filter(e=>"missed"===e.status).length})]})]})})}),(0,t.jsx)(i.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-sky-100 rounded-lg",children:(0,t.jsx)(p.A,{className:"w-6 h-6 text-sky-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Today"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:F.filter(e=>{let s=new Date(e.scheduledDate||e.dueDate),a=new Date;return s.toDateString()===a.toDateString()}).length})]})]})})})]}),(0,t.jsx)(i.Zp,{className:"border-gray-200 shadow-sm",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(N.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(n.p,{placeholder:"Search follow-ups by lead name or notes...",value:e,onChange:e=>s(e.target.value),className:"pl-10 border-gray-300 focus:border-sky-500 focus:ring-sky-500"})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(c.l6,{value:a,onValueChange:u,children:[(0,t.jsx)(c.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,t.jsx)(c.yv,{placeholder:"Status"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"all",children:"All Status"}),(0,t.jsx)(c.eb,{value:"pending",children:"Pending"}),(0,t.jsx)(c.eb,{value:"completed",children:"Completed"}),(0,t.jsx)(c.eb,{value:"missed",children:"Missed"}),(0,t.jsx)(c.eb,{value:"rescheduled",children:"Rescheduled"})]})]}),(0,t.jsxs)(c.l6,{value:m,onValueChange:x,children:[(0,t.jsx)(c.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,t.jsx)(c.yv,{placeholder:"Type"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"all",children:"All Types"}),(0,t.jsx)(c.eb,{value:"call",children:"Call"}),(0,t.jsx)(c.eb,{value:"email",children:"Email"}),(0,t.jsx)(c.eb,{value:"meeting",children:"Meeting"}),(0,t.jsx)(c.eb,{value:"visit",children:"Visit"})]})]}),(0,t.jsxs)(c.l6,{value:h,onValueChange:S,children:[(0,t.jsx)(c.bq,{className:"w-40 border-gray-300 focus:border-sky-500",children:(0,t.jsx)(c.yv,{placeholder:"Priority"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"all",children:"All Priorities"}),(0,t.jsx)(c.eb,{value:"high",children:"High"}),(0,t.jsx)(c.eb,{value:"medium",children:"Medium"}),(0,t.jsx)(c.eb,{value:"low",children:"Low"})]})]}),(0,t.jsxs)(d.$,{variant:"outline",className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:[(0,t.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"More Filters"]})]})]})})}),(0,t.jsxs)(i.Zp,{className:"border-gray-200 shadow-sm",children:[(0,t.jsx)(i.aR,{className:"bg-white border-b border-gray-200",children:(0,t.jsxs)(i.ZB,{className:"text-gray-900",children:["Follow-ups (",F.length,")",R&&(0,t.jsxs)("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["Page ",R.currentPage," of ",R.totalPages,"(",R.totalItems," total)"]})]})}),(0,t.jsxs)(i.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"space-y-0",children:F.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-100 hover:bg-sky-50 transition-colors",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,t.jsx)("div",{className:"flex-shrink-0 p-2 bg-gray-100 rounded-lg",children:C(e.type)}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:e.title||`${e.type.charAt(0).toUpperCase()+e.type.slice(1)} Follow-up`}),(0,t.jsx)(o.E,{className:`text-xs ${q(e.status)}`,children:e.status.toUpperCase()}),(0,t.jsx)(o.E,{className:`text-xs ${P(e.priority)}`,children:e.priority.toUpperCase()})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600",children:[(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(w.A,{className:"w-3 h-3 mr-1 text-sky-500"}),e.leadName||e.customerName||"Unknown"]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"w-3 h-3 mr-1 text-yellow-500"}),(0,A.Yq)(e.scheduledDate||e.dueDate)]}),(0,t.jsxs)("span",{className:"flex items-center",children:[D(e.status),(0,t.jsx)("span",{className:"ml-1",children:e.status})]})]}),e.notes&&(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1 truncate",children:e.notes})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,t.jsx)(d.$,{size:"sm",variant:"outline",className:"border-green-300 text-green-600 hover:bg-green-50",children:(0,t.jsx)(g.A,{className:"w-3 h-3"})}),(0,t.jsx)(d.$,{size:"sm",variant:"outline",className:"border-sky-300 text-sky-600 hover:bg-sky-50",children:(0,t.jsx)(k.A,{className:"w-3 h-3"})}),(0,t.jsx)(d.$,{size:"sm",variant:"outline",className:"border-gray-300 text-gray-600 hover:bg-gray-50",children:(0,t.jsx)(p.A,{className:"w-3 h-3"})})]})]},e.id))}),0===F.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(p.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500",children:"No follow-ups found"}),(0,t.jsx)(d.$,{className:"mt-4 bg-sky-500 hover:bg-sky-600 text-white",onClick:()=>_(),children:"Refresh"})]}),R&&R.totalPages>1&&(0,t.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(R.currentPage-1)*R.itemsPerPage+1," to"," ",Math.min(R.currentPage*R.itemsPerPage,R.totalItems)," of"," ",R.totalItems," results"]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>L(R.currentPage-1),disabled:1===R.currentPage,className:"border-gray-300",children:"Previous"}),(0,t.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>L(R.currentPage+1),disabled:R.currentPage===R.totalPages,className:"border-gray-300",children:"Next"})]})]})]})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22190:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31435:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33873:e=>{"use strict";e.exports=require("path")},39917:(e,s,a)=>{Promise.resolve().then(a.bind(a,86157))},44287:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var t=a(10557),r=a(68490),l=a(13172),i=a.n(l),d=a(68835),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);a.d(s,n);let o={children:["",{children:["follow-ups",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,86157)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\follow-ups\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\follow-ups\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/follow-ups/page",pathname:"/follow-ups",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77060:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},79201:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},79551:e=>{"use strict";e.exports=require("url")},82583:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},86157:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\follow-ups\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\follow-ups\\page.tsx","default")},87181:(e,s,a)=>{Promise.resolve().then(a.bind(a,3799))},99206:(e,s,a)=>{"use strict";a.d(s,{$V:()=>d,FO:()=>o,Hu:()=>l,OT:()=>T,Pb:()=>r,WD:()=>j,Zu:()=>v,aT:()=>y,cm:()=>b,nK:()=>x,pv:()=>A,ro:()=>N});let t=a(53412).q.injectEndpoints({endpoints:e=>({getDashboardStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getDashboardActivities:e.query({query:e=>({url:"/dashboard/activities",params:e}),providesTags:["Dashboard"]}),getSalesStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getLeads:e.query({query:e=>({url:"/sales/leads",params:e}),providesTags:["Lead"]}),getLeadById:e.query({query:e=>`/sales/leads/${e}`,providesTags:(e,s,a)=>[{type:"Lead",id:a}]}),createLead:e.mutation({query:e=>({url:"/sales/leads",method:"POST",body:e}),invalidatesTags:["Lead","Dashboard"]}),updateLead:e.mutation({query:({id:e,data:s})=>({url:`/sales/leads/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:a})=>[{type:"Lead",id:a},"Lead","Dashboard"]}),deleteLead:e.mutation({query:e=>({url:`/sales/leads/${e}`,method:"DELETE"}),invalidatesTags:["Lead","Dashboard"]}),assignLead:e.mutation({query:({leadId:e,salesRepId:s})=>({url:`/sales/leads/${e}/assign`,method:"POST",body:{salesRepId:s}}),invalidatesTags:["Lead"]}),getCustomers:e.query({query:e=>({url:"/sales/customers",params:e}),providesTags:["Customer"]}),getCustomerById:e.query({query:e=>`/sales/customers/${e}`,providesTags:(e,s,a)=>[{type:"Customer",id:a}]}),createCustomer:e.mutation({query:e=>({url:"/sales/customers",method:"POST",body:e}),invalidatesTags:["Customer","Dashboard"]}),updateCustomer:e.mutation({query:({id:e,data:s})=>({url:`/sales/customers/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:a})=>[{type:"Customer",id:a},"Customer"]}),getCommissions:e.query({query:e=>({url:"/sales/commissions",params:e}),providesTags:["Commission"]}),getSalesTargets:e.query({query:()=>"/sales/targets",providesTags:["Report"]}),createSalesTarget:e.mutation({query:e=>({url:"/sales/targets",method:"POST",body:e}),invalidatesTags:["Report","Dashboard"]}),updateSalesTarget:e.mutation({query:({id:e,data:s})=>({url:`/sales/targets/${e}`,method:"PUT",body:s}),invalidatesTags:["Report","Dashboard"]}),getFollowUps:e.query({query:e=>({url:"/follow-ups",params:e}),providesTags:["Task"]}),createFollowUp:e.mutation({query:e=>({url:"/follow-ups",method:"POST",body:e}),invalidatesTags:["Task","Dashboard"]}),updateFollowUp:e.mutation({query:({id:e,data:s})=>({url:`/follow-ups/${e}`,method:"PUT",body:s}),invalidatesTags:["Task","Dashboard"]}),deleteFollowUp:e.mutation({query:e=>({url:`/follow-ups/${e}`,method:"DELETE"}),invalidatesTags:["Task","Dashboard"]})})}),{useGetDashboardStatsQuery:r,useGetDashboardActivitiesQuery:l,useGetSalesStatsQuery:i,useGetLeadsQuery:d,useGetLeadByIdQuery:n,useCreateLeadMutation:o,useUpdateLeadMutation:c,useDeleteLeadMutation:u,useAssignLeadMutation:m,useGetCustomersQuery:x,useGetCustomerByIdQuery:h,useCreateCustomerMutation:p,useUpdateCustomerMutation:g,useGetCommissionsQuery:y,useGetSalesTargetsQuery:j,useCreateSalesTargetMutation:b,useUpdateSalesTargetMutation:v,useGetFollowUpsQuery:N,useCreateFollowUpMutation:f,useUpdateFollowUpMutation:w,useDeleteFollowUpMutation:k}=t,{useGetCustomersQuery:A,useGetCommissionsQuery:T}=t}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[755,598,544,29,796,286,447,512],()=>a(44287));module.exports=t})();