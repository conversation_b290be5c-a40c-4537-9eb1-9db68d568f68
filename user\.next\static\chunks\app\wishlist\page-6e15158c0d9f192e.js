(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1720],{1470:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(5050).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3321:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var i=t(9605),l=t(9585),a=t(3005),r=t(8063),c=t(2933),d=t(5768),n=t(8437),h=t(9742),x=t(8049),o=t(5833),m=t(3559),p=t(6751),y=t(1470),u=t(4883);let j=(0,t(5050).A)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);var g=t(470),v=t(6994),f=t(5263),N=t(6845);function w(){var e;let[s,t]=(0,l.useState)("grid"),[w,k]=(0,l.useState)("all"),[b,A]=(0,l.useState)([]),{data:T,isLoading:S,refetch:M}=(0,f.EF)({limit:50}),[W]=(0,f.e6)(),q=(null==T||null==(e=T.data)?void 0:e.data)||[],E=async e=>{try{await W(e).unwrap(),N.toast.success("Property removed from wishlist"),M()}catch(e){N.toast.error("Failed to remove property from wishlist")}},P=async()=>{try{await Promise.all(b.map(e=>W(e).unwrap())),N.toast.success("".concat(b.length," properties removed from wishlist")),A([]),M()}catch(e){N.toast.error("Failed to remove properties from wishlist")}},z=e=>{A(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},L=q.filter(e=>"all"===w||("featured"===w?e.featured:e.type===w));return S?(0,i.jsx)(a.A,{children:(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"animate-pulse",children:[(0,i.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(e=>(0,i.jsx)("div",{className:"h-80 bg-gray-200 rounded-lg"},e))})]})})}):(0,i.jsx)(a.A,{children:(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Wishlist"}),(0,i.jsx)("p",{className:"text-gray-600 mt-1",children:"Properties you've saved for later"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(c.$,{variant:"grid"===s?"default":"outline",size:"sm",onClick:()=>t("grid"),children:(0,i.jsx)(d.A,{className:"h-4 w-4"})}),(0,i.jsx)(c.$,{variant:"list"===s?"default":"outline",size:"sm",onClick:()=>t("list"),children:(0,i.jsx)(n.A,{className:"h-4 w-4"})})]})]}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("select",{value:w,onChange:e=>k(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[{value:"all",label:"All Properties"},{value:"residential",label:"Residential"},{value:"commercial",label:"Commercial"},{value:"mixed",label:"Mixed Use"},{value:"featured",label:"Featured Only"}].map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))}),q.length>0&&(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:b.length===q.length,onChange:()=>{b.length===q.length?A([]):A(q.map(e=>e._id))},className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,i.jsxs)("span",{className:"text-sm text-gray-600",children:["Select All (",b.length," selected)"]})]})]}),b.length>0&&(0,i.jsx)("div",{className:"flex items-center space-x-3",children:(0,i.jsxs)(c.$,{variant:"outline",size:"sm",onClick:P,className:"flex items-center space-x-2",children:[(0,i.jsx)(h.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{children:["Remove Selected (",b.length,")"]})]})})]})})}),(0,i.jsx)("div",{className:"flex items-center justify-between",children:(0,i.jsxs)("p",{className:"text-gray-600",children:[L.length," properties in your wishlist"]})}),L.length>0?(0,i.jsx)("div",{className:"grid"===s?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:L.map(e=>{var s,t;return(0,i.jsxs)(r.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("div",{className:"absolute top-3 left-3 z-10",children:(0,i.jsx)("input",{type:"checkbox",checked:b.includes(e._id),onChange:()=>z(e._id),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded bg-white"})}),e.images&&e.images.length>0?(0,i.jsx)("img",{src:e.images[0].url,alt:e.name,className:"w-full h-48 object-cover rounded-t-lg"}):(0,i.jsx)("div",{className:"w-full h-48 bg-gray-200 rounded-t-lg flex items-center justify-center",children:(0,i.jsx)(x.A,{className:"h-12 w-12 text-gray-400"})}),e.featured&&(0,i.jsxs)("div",{className:"absolute top-3 right-12 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center",children:[(0,i.jsx)(o.A,{className:"h-3 w-3 mr-1"}),"Featured"]}),(0,i.jsx)("button",{onClick:()=>E(e._id),className:"absolute top-3 right-3 p-2 bg-white rounded-full shadow-md hover:bg-red-50 text-red-600",children:(0,i.jsx)(m.A,{className:"h-4 w-4 fill-current"})})]}),(0,i.jsx)(r.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-xl font-semibold",children:e.name}),(0,i.jsxs)("div",{className:"flex items-center text-gray-600 text-sm mt-1",children:[(0,i.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"string"==typeof e.location?e.location:(null==(s=e.location)?void 0:s.address)||(null==(t=e.location)?void 0:t.city)||"Location not specified"]})]}),(0,i.jsx)("p",{className:"text-gray-600 text-sm line-clamp-2",children:e.description}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Stock Price"}),(0,i.jsx)("div",{className:"font-semibold",children:(0,v.vv)(e.pricePerStock)})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Expected Returns"}),(0,i.jsx)("div",{className:"font-semibold text-green-600",children:(0,v.Ee)(e.roi)})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Available"}),(0,i.jsxs)("div",{className:"font-semibold",children:[e.availableStocks," stocks"]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Type"}),(0,i.jsx)("div",{className:"font-semibold capitalize",children:e.type})]})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t space-x-2",children:[(0,i.jsxs)(c.$,{variant:"outline",size:"sm",className:"flex-1 flex items-center justify-center space-x-1",children:[(0,i.jsx)(y.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:"View"})]}),(0,i.jsx)(c.$,{variant:"outline",size:"sm",className:"flex items-center justify-center",children:(0,i.jsx)(u.A,{className:"h-4 w-4"})}),(0,i.jsxs)(c.$,{size:"sm",className:"flex-1 flex items-center justify-center space-x-1",children:[(0,i.jsx)(j,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:"Invest"})]})]})]})})]},e._id)})}):(0,i.jsx)(r.Zp,{children:(0,i.jsxs)(r.Wu,{className:"text-center py-12",children:[(0,i.jsx)(m.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-300"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Your wishlist is empty"}),(0,i.jsx)("p",{className:"text-gray-600 mb-6",children:"all"===w?"You haven't saved any properties yet. Browse properties and add them to your wishlist.":"No ".concat(w," properties in your wishlist. Try changing the filter or browse more properties.")}),(0,i.jsx)(c.$,{children:"Browse Properties"})]})}),q.length>0&&(0,i.jsxs)(r.Zp,{children:[(0,i.jsx)(r.aR,{children:(0,i.jsx)(r.ZB,{className:"text-lg",children:"Wishlist Tips"})}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,i.jsx)(m.A,{className:"h-4 w-4 text-blue-600"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium",children:"Save for Later"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Keep track of properties you're interested in"})]})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,i.jsx)(g.A,{className:"h-4 w-4 text-green-600"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium",children:"Compare Options"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Easily compare different investment opportunities"})]})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,i.jsx)(u.A,{className:"h-4 w-4 text-purple-600"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium",children:"Share with Others"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Get opinions from friends and family"})]})]})]})})]})]})})}},4883:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(5050).A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},5005:(e,s,t)=>{Promise.resolve().then(t.bind(t,3321))},5263:(e,s,t)=>{"use strict";t.d(s,{EF:()=>i,U0:()=>l,e6:()=>a});let{useGetUserWishlistQuery:i,useAddToWishlistMutation:l,useRemoveFromWishlistMutation:a,useCheckWishlistStatusQuery:r,useSetPriceAlertMutation:c,useRemovePriceAlertMutation:d,useGetPopularPropertiesQuery:n,useGetWishlistStatsQuery:h,useClearWishlistMutation:x,useExportWishlistMutation:o}=t(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getUserWishlist:e.query({query:e=>{let{page:s=1,limit:t=12}=e;return{url:"/wishlist",params:{page:s,limit:t}}},providesTags:[{type:"Wishlist",id:"LIST"}],keepUnusedDataFor:300}),addToWishlist:e.mutation({query:e=>({url:"/wishlist",method:"POST",body:{propertyId:e}}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),removeFromWishlist:e.mutation({query:e=>({url:"/wishlist/".concat(e),method:"DELETE"}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),checkWishlistStatus:e.query({query:e=>"/wishlist/check/".concat(e),providesTags:(e,s,t)=>[{type:"Wishlist",id:"check-".concat(t)}],keepUnusedDataFor:600}),setPriceAlert:e.mutation({query:e=>{let{propertyId:s,...t}=e;return{url:"/wishlist/".concat(s,"/price-alert"),method:"POST",body:t}},invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),removePriceAlert:e.mutation({query:e=>({url:"/wishlist/".concat(e,"/price-alert"),method:"DELETE"}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),getPopularProperties:e.query({query:e=>{let{limit:s=10}=e;return{url:"/wishlist/popular",params:{limit:s}}},providesTags:[{type:"Wishlist",id:"POPULAR"}],keepUnusedDataFor:1800}),getWishlistStats:e.query({query:()=>"/wishlist/stats",providesTags:[{type:"Wishlist",id:"STATS"}],keepUnusedDataFor:600}),clearWishlist:e.mutation({query:()=>({url:"/wishlist/clear",method:"DELETE"}),invalidatesTags:[{type:"Wishlist",id:"LIST"}]}),exportWishlist:e.mutation({query:e=>({url:"/wishlist/export",method:"POST",body:e})})})})},5768:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(5050).A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},6751:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(5050).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8049:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(5050).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},8437:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(5050).A)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},9742:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});let i=(0,t(5050).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[2094,5315,7436,7693,1147,7627,3005,390,110,7358],()=>s(5005)),_N_E=e.O()}]);