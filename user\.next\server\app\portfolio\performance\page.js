(()=>{var e={};e.id=5200,e.ids=[5200],e.modules={575:(e,t,r)=>{Promise.resolve().then(r.bind(r,70500))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18661:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(10557),a=r(68490),n=r(13172),l=r.n(n),i=r(68835),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["portfolio",{children:["performance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,91622)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\portfolio\\performance\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\portfolio\\performance\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/portfolio/performance/page",pathname:"/portfolio/performance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28496:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(99024).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29655:(e,t,r)=>{"use strict";r.d(t,{AR:()=>a,rx:()=>s});let{useGetWalletBalanceQuery:s,useGetWalletTransactionsQuery:a,useAddMoneyToWalletMutation:n,useWithdrawMoneyMutation:l,useGetPaymentMethodsQuery:i,useAddPaymentMethodMutation:o,useUpdatePaymentMethodMutation:d,useDeletePaymentMethodMutation:c,useVerifyPaymentMethodMutation:m,useGetTransactionByIdQuery:p,useGetWalletAnalyticsQuery:x,useExportWalletStatementMutation:u,useSetTransactionAlertsMutation:h,useGetWalletLimitsQuery:y,useRequestLimitIncreaseMutation:g}=r(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getWalletBalance:e.query({query:()=>"/wallet",providesTags:[{type:"Wallet",id:"BALANCE"}],keepUnusedDataFor:120}),getWalletTransactions:e.query({query:e=>({url:"/wallet/transactions",params:{page:e.page||1,limit:e.limit||20,...e.type&&{type:e.type},...e.status&&{status:e.status},...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),transformResponse:e=>e?.success&&e?.data?.data?.length?e:{success:!0,message:"Transactions retrieved successfully",data:{data:[{_id:"demo-txn-1",type:"stock_purchase",amount:25e3,status:"completed",description:"Property Investment - Luxury Apartments",createdAt:new Date().toISOString(),reference:"TXN001"},{_id:"demo-txn-2",type:"deposit",amount:5e4,status:"completed",description:"Wallet Deposit",createdAt:new Date(Date.now()-864e5).toISOString(),reference:"TXN002"},{_id:"demo-txn-3",type:"investment",amount:1e5,status:"completed",description:"Property Investment - Commercial Complex",createdAt:new Date(Date.now()-1728e5).toISOString(),reference:"TXN003"}],pagination:{page:1,limit:20,total:3,pages:1}}},providesTags:e=>e?.data?.data?[...e.data.data.map(({_id:e})=>({type:"Transaction",id:e})),{type:"Transaction",id:"LIST"}]:[{type:"Transaction",id:"LIST"}],keepUnusedDataFor:300}),addMoneyToWallet:e.mutation({query:e=>({url:"/wallet/add-funds",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),withdrawMoney:e.mutation({query:e=>({url:"/wallet/withdraw",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),getPaymentMethods:e.query({query:()=>"/wallet/payment-methods",providesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}],keepUnusedDataFor:600}),addPaymentMethod:e.mutation({query:e=>({url:"/wallet/payment-methods",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),updatePaymentMethod:e.mutation({query:({id:e,...t})=>({url:`/wallet/payment-methods/${e}`,method:"PUT",body:t}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),deletePaymentMethod:e.mutation({query:e=>({url:`/wallet/payment-methods/${e}`,method:"DELETE"}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),verifyPaymentMethod:e.mutation({query:({id:e,verificationData:t})=>({url:`/wallet/payment-methods/${e}/verify`,method:"POST",body:t}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),getTransactionById:e.query({query:e=>`/wallet/transactions/${e}`,providesTags:(e,t,r)=>[{type:"Transaction",id:r}],keepUnusedDataFor:1800}),getWalletAnalytics:e.query({query:({period:e="1Y"})=>({url:"/wallet/analytics",params:{period:e}}),providesTags:[{type:"Wallet",id:"ANALYTICS"}],keepUnusedDataFor:900}),exportWalletStatement:e.mutation({query:e=>({url:"/wallet/export-statement",method:"POST",body:e})}),setTransactionAlerts:e.mutation({query:e=>({url:"/wallet/alerts",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"}]}),getWalletLimits:e.query({query:()=>"/wallet/limits",providesTags:[{type:"Wallet",id:"LIMITS"}],keepUnusedDataFor:3600}),requestLimitIncrease:e.mutation({query:e=>({url:"/wallet/request-limit-increase",method:"POST",body:e})})})})},33873:e=>{"use strict";e.exports=require("path")},37079:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(99024).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},56021:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(99024).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},56525:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(99024).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70311:(e,t,r)=>{Promise.resolve().then(r.bind(r,91622))},70500:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(40969);r(73356);var a=r(37020),n=r(85306),l=r(66949),i=r(45548),o=r(56021),d=r(14216),c=r(31745),m=r(37079),p=r(56525);let x=(0,r(99024).A)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);var u=r(67278),h=r(29655),y=r(21764);function g(){let{data:e,isLoading:t}=(0,h.rx)(),r=e?.data||{},g=r?.totalInvested||0,f=r?.totalReturns||0,v=g>0?f/g*100:0,b=[{title:"Total Returns",value:(0,y.vv)(f),change:v>0?(0,y.Ee)(v):"0%",trend:f>0?"up":f<0?"down":"neutral",icon:i.A,color:f>0?"text-green-600":f<0?"text-red-600":"text-gray-600"},{title:"Portfolio Value",value:(0,y.vv)(g+f),change:v>0?(0,y.Ee)(v):"0%",trend:"up",icon:o.A,color:"text-sky-600"},{title:"Risk Score",value:"7.2/10",change:"-0.3",trend:"down",icon:d.A,color:"text-orange-600"}];return(0,s.jsx)(a.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(n.Ay,{title:"Performance Analytics",description:"Track your investment performance and returns with detailed insights",icon:c.A,gradient:!0,breadcrumbs:[{label:"Portfolio",href:"/portfolio"},{label:"Performance"}],actions:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(n.lX.Secondary,{children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Filter"]}),(0,s.jsxs)(n.lX.Primary,{children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Export Report"]})]})}),(0,s.jsx)(n.T0,{stats:[{label:"Total Returns",value:"₹2,45,680",change:"+12.5%",trend:"up"},{label:"Monthly Growth",value:"₹18,450",change:"+8.2%",trend:"up"},{label:"Portfolio Value",value:"₹8,75,000",change:"+15.3%",trend:"up"},{label:"Risk Score",value:"7.2/10",change:"-0.3",trend:"down"}]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:b.map((e,t)=>{let r=e.icon;return(0,s.jsx)(l.Zp,{className:"border-0 shadow-lg",children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:e.value}),(0,s.jsxs)("div",{className:"flex items-center mt-2",children:["up"===e.trend?(0,s.jsx)(i.A,{className:"h-4 w-4 text-green-600 mr-1"}):(0,s.jsx)(x,{className:"h-4 w-4 text-red-600 mr-1"}),(0,s.jsx)("span",{className:`text-sm font-medium ${"up"===e.trend?"text-green-600":"text-red-600"}`,children:e.change})]})]}),(0,s.jsx)("div",{className:`p-3 rounded-full bg-gray-50 ${e.color}`,children:(0,s.jsx)(r,{className:"h-6 w-6"})})]})})},t)})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(l.Zp,{className:"border-0 shadow-lg",children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 text-sky-600"}),(0,s.jsx)("span",{children:"Monthly Performance"})]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 rounded-lg",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Performance chart will be displayed here"})})})]}),(0,s.jsxs)(l.Zp,{className:"border-0 shadow-lg",children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 text-sky-600"}),(0,s.jsx)("span",{children:"Asset Allocation"})]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 rounded-lg",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Asset allocation chart will be displayed here"})})})]})]}),(0,s.jsxs)(l.Zp,{className:"border-0 shadow-lg",children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{children:"Recent Performance"})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"font-medium",children:["Property Investment #",e]}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Last updated 2 hours ago"})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"font-bold text-green-600",children:"+₹12,500"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"+8.5%"})]})]},e))})})]})]})})}},76650:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(40969);r(73356);var a=r(52774),n=r(21764);let l=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return(0,s.jsx)("div",{className:(0,n.cn)(l({variant:t}),e),...r})}},79551:e=>{"use strict";e.exports=require("url")},85306:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d,T0:()=>c,lX:()=>m});var s=r(40969);r(73356);var a=r(46411),n=r(76650),l=r(21764),i=r(28496),o=r(12011);function d({title:e,description:t,icon:r,badge:d,actions:c,breadcrumbs:m,showBackButton:p=!1,className:x,gradient:u=!1}){let h=(0,o.useRouter)();return(0,s.jsxs)("div",{className:(0,l.cn)("relative overflow-hidden",u&&"bg-gradient-to-r from-sky-50 via-blue-50 to-indigo-50",!u&&"bg-white",x),children:[u&&(0,s.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,s.jsx)("div",{className:"relative px-4 sm:px-6 lg:px-8 py-6 sm:py-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[m&&m.length>0&&(0,s.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,s.jsx)("ol",{className:"flex items-center space-x-2 text-sm",children:m.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,s.jsx)("span",{className:"mx-2 text-gray-400",children:"/"}),e.href?(0,s.jsx)("button",{onClick:()=>h.push(e.href),className:"text-gray-600 hover:text-sky-600 transition-colors",children:e.label}):(0,s.jsx)("span",{className:"text-gray-900 font-medium",children:e.label})]},t))})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[p&&(0,s.jsx)(a.$,{variant:"ghost",size:"icon",onClick:()=>h.back(),className:"flex-shrink-0 mt-1",children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}),r&&(0,s.jsx)("div",{className:(0,l.cn)("flex-shrink-0 p-3 rounded-xl",u?"bg-white/80 backdrop-blur-sm shadow-lg":"bg-sky-50","text-sky-600"),children:(0,s.jsx)(r,{className:"h-6 w-6"})}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center flex-wrap gap-3 mb-2",children:[(0,s.jsx)("h1",{className:(0,l.cn)("text-2xl sm:text-3xl font-bold text-gray-900 leading-tight",u&&"bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent"),children:e}),d&&(0,s.jsx)(n.E,{variant:d.variant||"default",className:(0,l.cn)("text-xs font-medium",d.className),children:d.text})]}),t&&(0,s.jsx)("p",{className:"text-gray-600 text-sm sm:text-base max-w-2xl leading-relaxed",children:t})]})]}),c&&(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"flex items-center space-x-3",children:c})})]})]})}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"})]})}function c({stats:e,className:t}){return(0,s.jsx)("div",{className:(0,l.cn)("grid grid-cols-2 sm:grid-cols-4 gap-4 mt-6",t),children:e.map((e,t)=>(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50",children:[(0,s.jsx)("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide",children:e.label}),(0,s.jsx)("p",{className:"text-lg font-bold text-gray-900 mt-1",children:e.value}),e.change&&(0,s.jsx)("p",{className:(0,l.cn)("text-xs font-medium mt-1","up"===e.trend&&"text-green-600","down"===e.trend&&"text-red-600","neutral"===e.trend&&"text-gray-600"),children:e.change})]},t))})}let m={Primary:({children:e,...t})=>(0,s.jsx)(a.$,{className:"bg-sky-600 hover:bg-sky-700 text-white shadow-lg",...t,children:e}),Secondary:({children:e,...t})=>(0,s.jsx)(a.$,{variant:"outline",className:"border-sky-200 text-sky-700 hover:bg-sky-50",...t,children:e}),Ghost:({children:e,...t})=>(0,s.jsx)(a.$,{variant:"ghost",className:"text-gray-600 hover:text-sky-600 hover:bg-sky-50",...t,children:e})}},91622:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\app\\\\portfolio\\\\performance\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\app\\portfolio\\performance\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[755,3777,2544,7092,7555,2487,3427],()=>r(18661));module.exports=s})();