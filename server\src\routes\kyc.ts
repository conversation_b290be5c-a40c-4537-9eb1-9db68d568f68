import { Router, Request, Response } from 'express';
import {
  getUserKYC,
  updateKY<PERSON>,
  updateAddress,
  uploadDocument,
  getPendingKYCs,
  approveKYC,
  rejectKYC,
  getKYCStats,
  getKYCById
} from '../controllers/kycController';
import { KYCService } from '../services';
import {
  validateObjectId,
  validatePagination
} from '../middleware/validation';
import {
  authenticateToken,
  adminOrSubAdmin
} from '../middleware';

const router: any = Router();
const kycService = new KYCService();

/**
 * @route   GET /api/kyc
 * @desc    Get current user's KYC details
 * @access  Private
 */
router.get('/', authenticateToken, getUserKYC);

/**
 * @route   GET /api/kyc/personal-info
 * @desc    Get user's KYC personal information
 * @access  Private
 */
router.get('/personal-info', authenticateToken, async (req: Request, res: Response) => {
  try {
    const kyc = await kycService.getUserKYC(req.user!.id);
    const userDetails = await kycService.getUserDetailsForKYC(req.user!.id);

    const response = {
      success: true,
      message: 'Personal information retrieved successfully',
      data: {
        ...userDetails,
        ...kyc.personalInfo,
        autoFilled: true
      }
    };

    res.json(response);
  } catch (error: any) {
    console.error('Get personal info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve personal information',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/kyc/address-info
 * @desc    Get user's KYC address information
 * @access  Private
 */
router.get('/address-info', authenticateToken, async (req: Request, res: Response) => {
  try {
    const kyc = await kycService.getUserKYC(req.user!.id);

    const response = {
      success: true,
      message: 'Address information retrieved successfully',
      data: kyc.address || {}
    };

    res.json(response);
  } catch (error: any) {
    console.error('Get address info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve address information',
      error: error.message
    });
  }
});

/**
 * @route   PUT /api/kyc
 * @desc    Update KYC personal information (additional info only, user details auto-populated)
 * @access  Private
 */
router.put('/', authenticateToken, updateKYC);

/**
 * @route   PUT /api/kyc/personal-info
 * @desc    Update KYC personal information
 * @access  Private
 */
router.put('/personal-info', authenticateToken, updateKYC);

/**
 * @route   PUT /api/kyc/address-info
 * @desc    Update KYC address information
 * @access  Private
 */
router.put('/address-info', authenticateToken, updateAddress);

/**
 * @route   PUT /api/kyc/address
 * @desc    Update KYC address information (legacy)
 * @access  Private
 */
router.put('/address', authenticateToken, updateAddress);

/**
 * @route   GET /api/kyc/documents
 * @desc    Get user's KYC documents
 * @access  Private
 */
router.get('/documents', authenticateToken, async (req: Request, res: Response) => {
  try {
    const kyc = await kycService.getUserKYC(req.user!.id);

    const response = {
      success: true,
      message: 'Documents retrieved successfully',
      data: kyc.documents || []
    };

    res.json(response);
  } catch (error: any) {
    console.error('Get documents error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve documents',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/kyc/document
 * @desc    Upload KYC document
 * @access  Private
 */
router.post('/document', authenticateToken, uploadDocument);

/**
 * @route   POST /api/kyc/submit
 * @desc    Submit KYC for verification
 * @access  Private
 */
router.post('/submit', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    console.log('🚀 KYC Submit request for user:', userId);

    const kycService = new KYCService();
    const result = await kycService.submitKYC(userId);

    console.log('✅ KYC submitted successfully for user:', userId);
    res.status(200).json({
      success: true,
      message: 'KYC submitted successfully for verification',
      data: result
    });
  } catch (error: any) {
    console.error('❌ KYC submit error:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to submit KYC',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/kyc/download-pdf
 * @desc    Download KYC PDF report with all documents
 * @access  Private
 */
router.get('/download-pdf', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user!.id;
    const kyc = await kycService.getUserKYC(userId);
    const user = await kycService.getUserDetailsForKYC(userId);

    if (!kyc) {
      res.status(404).json({
        success: false,
        message: 'KYC data not found'
      });
      return;
    }

    // Generate PDF with all KYC data and documents
    const pdfBuffer = await kycService.generateKYCPDF(user, kyc);

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="KYC_Report_${user.firstName}_${user.lastName}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    res.send(pdfBuffer);
  } catch (error: any) {
    console.error('PDF generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate PDF report',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/kyc/pending
 * @desc    Get all pending KYC applications
 * @access  Private (Admin/SubAdmin)
 */
router.get('/pending', authenticateToken, adminOrSubAdmin, validatePagination, getPendingKYCs);

/**
 * @route   GET /api/kyc/stats
 * @desc    Get KYC statistics
 * @access  Private (Admin/SubAdmin)
 */
router.get('/stats', authenticateToken, adminOrSubAdmin, getKYCStats);

/**
 * @route   GET /api/kyc/:kycId
 * @desc    Get KYC by ID
 * @access  Private (Admin/SubAdmin)
 */
router.get('/:kycId', authenticateToken, adminOrSubAdmin, validateObjectId('kycId'), getKYCById);

/**
 * @route   POST /api/kyc/:kycId/approve
 * @desc    Approve KYC application
 * @access  Private (Admin/SubAdmin)
 */
router.post('/:kycId/approve', authenticateToken, adminOrSubAdmin, validateObjectId('kycId'), approveKYC);

/**
 * @route   POST /api/kyc/:kycId/reject
 * @desc    Reject KYC application
 * @access  Private (Admin/SubAdmin)
 */
router.post('/:kycId/reject', authenticateToken, adminOrSubAdmin, validateObjectId('kycId'), rejectKYC);

/**
 * @route   GET /api/kyc/user/:userId
 * @desc    Get KYC by user ID (for admin)
 * @access  Private (Admin/SubAdmin)
 */
router.get('/user/:userId', authenticateToken, adminOrSubAdmin, validateObjectId('userId'), async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const kyc = await kycService.getUserKYC(userId);

    const response = {
      success: true,
      message: 'KYC retrieved successfully',
      data: kyc
    };

    res.json(response);
  } catch (error: any) {
    console.error('Get KYC by user ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve KYC',
      error: error.message
    });
  }
});

/**
 * @route   PUT /api/kyc/:kycId/status
 * @desc    Update KYC status (approve/reject with details)
 * @access  Private (Admin/SubAdmin)
 */
router.put('/:kycId/status', authenticateToken, adminOrSubAdmin, validateObjectId('kycId'), async (req: Request, res: Response) => {
  try {
    const { kycId } = req.params;
    const { status, rejectionReason, notes } = req.body;

    // Get the KYC record to find the user ID
    const kyc = await kycService.getKYCById(kycId);
    if (!kyc) {
      res.status(404).json({
        success: false,
        message: 'KYC record not found'
      });
      return;
    }

    // Extract userId properly - handle populated userId
    let userId: string;
    if (typeof kyc.userId === 'object' && kyc.userId._id) {
      // userId is populated with user object
      userId = kyc.userId._id.toString();
    } else {
      // userId is just an ObjectId
      userId = kyc.userId.toString();
    }

    console.log('Extracted userId for KYC status update:', userId);

    let updatedKYC;

    if (status === 'approved') {
      updatedKYC = await kycService.approveKYC(userId, notes);
    } else if (status === 'rejected') {
      if (!rejectionReason) {
        res.status(400).json({
          success: false,
          message: 'Rejection reason is required'
        });
        return;
      }
      updatedKYC = await kycService.rejectKYC(userId, rejectionReason, notes);
    } else {
      res.status(400).json({
        success: false,
        message: 'Invalid status. Must be "approved" or "rejected"'
      });
      return;
    }

    const response = {
      success: true,
      message: `KYC ${status} successfully`,
      data: updatedKYC
    };

    res.json(response);
  } catch (error: any) {
    console.error('Update KYC status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update KYC status',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/kyc/:kycId/download-pdf
 * @desc    Download KYC PDF for admin review
 * @access  Private (Admin/SubAdmin)
 */
router.get('/:kycId/download-pdf', authenticateToken, adminOrSubAdmin, validateObjectId('kycId'), async (req: Request, res: Response) => {
  try {
    const { kycId } = req.params;

    // Get KYC data
    const kyc = await kycService.getKYCById(kycId);
    if (!kyc) {
      res.status(404).json({
        success: false,
        message: 'KYC data not found'
      });
      return;
    }

    // Get user details - handle populated userId
    console.log('KYC userId type:', typeof kyc.userId, 'value:', kyc.userId);
    let userId: string;
    if (typeof kyc.userId === 'object' && kyc.userId._id) {
      // userId is populated with user object
      userId = kyc.userId._id.toString();
    } else {
      // userId is just an ObjectId
      userId = kyc.userId.toString();
    }
    console.log('Processed userId:', userId);
    const user = await kycService.getUserDetailsForKYC(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    // Generate PDF
    const pdfBuffer = await kycService.generateKYCPDF(user, kyc);

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="KYC_Report_${user.firstName}_${user.lastName}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    res.send(pdfBuffer);
  } catch (error: any) {
    console.error('Admin KYC PDF generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate PDF report',
      error: error.message
    });
  }
});



export default router;
