(()=>{var e={};e.id=85,e.ids=[85],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11303:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=t(10557),r=t(68490),n=t(13172),l=t.n(n),i=t(68835),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d={children:["",{children:["calendar",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81305)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\calendar\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\calendar\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/calendar/page",pathname:"/calendar",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31435:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98085).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33873:e=>{"use strict";e.exports=require("path")},47299:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var a=t(40969),r=t(73356),n=t(88251),l=t(89073),i=t(66949),c=t(46411),d=t(76650),o=t(92409),x=t(79123),m=t(82039),h=t(61115),p=t(58896),u=t(8713),j=t(93012),g=t(33763),v=t(31435),f=t(21857),N=t(86400),y=t(48567),b=t(61631),w=t(95003);function A(){let[e,s]=(0,r.useState)(new Date),[t,A]=(0,r.useState)("month"),k=(0,y.G)(b.mB),C=(0,y.j)(),_=k?.role==="sales"||k?.role==="sales_manager"||k?.role==="admin",{data:D,isLoading:P,error:M}=(0,N.Z4)(),S=new Date(e.getFullYear(),e.getMonth(),1).toISOString(),q=new Date(e.getFullYear(),e.getMonth()+1,0).toISOString(),{data:R,isLoading:T}=(0,N.xc)({startDate:S,endDate:q}),[B]=(0,N.dy)(),[E]=(0,N.eV)(),[U]=(0,N.Yp)();if(!_)return(0,a.jsx)(n.A,{title:"Access Denied",children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access the calendar."})]})})});let $=D?.data||[],z=e=>{switch(e){case"meeting":return(0,a.jsx)(x.A,{className:"w-4 h-4"});case"call":return(0,a.jsx)(m.A,{className:"w-4 h-4"});case"site_visit":return(0,a.jsx)(h.A,{className:"w-4 h-4"});case"demo":return(0,a.jsx)(p.A,{className:"w-4 h-4"});default:return(0,a.jsx)(u.A,{className:"w-4 h-4"})}},G=e=>{switch(e){case"meeting":return"bg-blue-100 text-blue-800 border-blue-200";case"call":return"bg-green-100 text-green-800 border-green-200";case"site_visit":return"bg-purple-100 text-purple-800 border-purple-200";case"demo":return"bg-orange-100 text-orange-800 border-orange-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},Z=a=>{let r=new Date(e);"month"===t?r.setMonth(r.getMonth()+("next"===a?1:-1)):"week"===t?r.setDate(r.getDate()+("next"===a?7:-7)):r.setDate(r.getDate()+("next"===a?1:-1)),s(r)};return(0,a.jsxs)(n.A,{title:"Calendar",children:[(0,a.jsx)(l.A,{title:"Sales Calendar",subtitle:"Manage your meetings, calls, and appointments",icon:o.A}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>Z("prev"),children:(0,a.jsx)(j.A,{className:"w-4 h-4"})}),(0,a.jsx)("h2",{className:"text-xl font-semibold",children:e.toLocaleDateString("en-US",{month:"long",year:"numeric"})}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>Z("next"),children:(0,a.jsx)(g.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"flex rounded-lg border",children:[(0,a.jsx)(c.$,{variant:"month"===t?"default":"ghost",size:"sm",onClick:()=>A("month"),className:"rounded-r-none",children:"Month"}),(0,a.jsx)(c.$,{variant:"week"===t?"default":"ghost",size:"sm",onClick:()=>A("week"),className:"rounded-none border-x-0",children:"Week"}),(0,a.jsx)(c.$,{variant:"day"===t?"default":"ghost",size:"sm",onClick:()=>A("day"),className:"rounded-l-none",children:"Day"})]}),(0,a.jsxs)(c.$,{className:"flex items-center space-x-2",onClick:()=>C((0,w.qf)({id:"create-event",type:"create-event",data:{}})),children:[(0,a.jsx)(v.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"New Event"})]})]})]})})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(o.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Calendar View"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Full calendar component will be implemented here"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:["Currently showing ",t," view for ",e.toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})]})]})})})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Today's Events"})]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[$.map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:`p-2 rounded-lg ${G(e.type)}`,children:z(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 truncate",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1 text-sm text-gray-600",children:[(0,a.jsx)(f.A,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[e.startTime," - ",e.endTime]})]}),e.location&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1 text-sm text-gray-600",children:[(0,a.jsx)(h.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"truncate",children:e.location})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,a.jsx)(d.E,{variant:"default",className:"text-xs",children:e.type.replace("_"," ")}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>C((0,w.qf)({id:"edit-event",type:"edit-event",data:{event:e}})),className:"border-gray-300 text-black hover:bg-green-100 text-xs px-2 py-1",children:"Edit"})})]}),e.createdByName&&(0,a.jsx)("div",{className:"flex items-center space-x-2 mt-1 text-xs text-gray-500",children:(0,a.jsxs)("span",{children:["Created by: ",e.createdByName]})}),e.assignedUsers&&e.assignedUsers.length>0&&(0,a.jsx)("div",{className:"flex items-center space-x-2 mt-1 text-xs text-gray-500",children:(0,a.jsxs)("span",{children:["Assigned to: ",e.assignedUsers.map(e=>e.name).join(", ")]})})]})]})},e.id)),0===$.length&&(0,a.jsxs)("div",{className:"text-center py-6",children:[(0,a.jsx)(u.A,{className:"w-8 h-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-gray-600",children:"No events today"})]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"This Week"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Total Events"}),(0,a.jsx)("span",{className:"font-medium",children:"12"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Meetings"}),(0,a.jsx)("span",{className:"font-medium",children:"5"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Site Visits"}),(0,a.jsx)("span",{className:"font-medium",children:"3"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Calls"}),(0,a.jsx)("span",{className:"font-medium",children:"4"})]})]})})]})]})]})]})]})}},48569:(e,s,t)=>{Promise.resolve().then(t.bind(t,81305))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},81305:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\calendar\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\calendar\\page.tsx","default")},85017:(e,s,t)=>{Promise.resolve().then(t.bind(t,47299))},89073:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(40969);function r({title:e,subtitle:s,icon:t,greeting:r,userName:n,actions:l,children:i}){return(0,a.jsx)("div",{className:"bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg p-6 text-white shadow-lg",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center gap-3",children:[t&&(0,a.jsx)(t,{className:"h-8 w-8"}),r&&n?`${r}, ${n}! 👋`:e]}),(0,a.jsx)("p",{className:"text-sky-100",children:s||"Manage and track your SGM sales activities with real-time insights"})]}),(l||i)&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[l,i]})]})})}t(73356)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[755,598,544,29,796,286,447,512],()=>t(11303));module.exports=a})();