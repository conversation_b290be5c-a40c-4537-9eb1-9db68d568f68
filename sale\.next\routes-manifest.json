{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/calendar", "regex": "^/calendar(?:/)?$", "routeKeys": {}, "namedRegex": "^/calendar(?:/)?$"}, {"page": "/commissions", "regex": "^/commissions(?:/)?$", "routeKeys": {}, "namedRegex": "^/commissions(?:/)?$"}, {"page": "/customers", "regex": "^/customers(?:/)?$", "routeKeys": {}, "namedRegex": "^/customers(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/documents", "regex": "^/documents(?:/)?$", "routeKeys": {}, "namedRegex": "^/documents(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/follow-ups", "regex": "^/follow\\-ups(?:/)?$", "routeKeys": {}, "namedRegex": "^/follow\\-ups(?:/)?$"}, {"page": "/leads", "regex": "^/leads(?:/)?$", "routeKeys": {}, "namedRegex": "^/leads(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/properties", "regex": "^/properties(?:/)?$", "routeKeys": {}, "namedRegex": "^/properties(?:/)?$"}, {"page": "/reports", "regex": "^/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/reports(?:/)?$"}, {"page": "/sales", "regex": "^/sales(?:/)?$", "routeKeys": {}, "namedRegex": "^/sales(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/signup", "regex": "^/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/signup(?:/)?$"}, {"page": "/support", "regex": "^/support(?:/)?$", "routeKeys": {}, "namedRegex": "^/support(?:/)?$"}, {"page": "/targets", "regex": "^/targets(?:/)?$", "routeKeys": {}, "namedRegex": "^/targets(?:/)?$"}, {"page": "/tasks", "regex": "^/tasks(?:/)?$", "routeKeys": {}, "namedRegex": "^/tasks(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}