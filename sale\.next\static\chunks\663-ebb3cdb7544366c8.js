"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[663],{944:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("calendar-plus",[["path",{d:"M16 19h6",key:"xwg31i"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M19 16v6",key:"tddt3s"}],["path",{d:"M21 12.598V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8.5",key:"1glfrc"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 2v4",key:"1cmpym"}]])},1432:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},1713:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},1817:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},1843:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},2848:(e,t,n)=>{n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>el,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(9585),o=n(4761),l=n(4455),i=n(8972),a=n(7421),u=n(9728),c=n(7271),s=n(6452),d=n(8036),f=n(4853),p=n(7905),h=n(5790),m=n(4229),v=n(3383),y=n(8130),g=n(9605),w="Dialog",[x,b]=(0,i.A)(w),[k,E]=x(w),A=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:l,onOpenChange:i,modal:c=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.i)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:w});return(0,g.jsx)(k,{scope:t,triggerRef:s,contentRef:d,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};A.displayName=w;var C="DialogTrigger",S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=E(C,n),a=(0,l.s)(t,i.triggerRef);return(0,g.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":U(i.open),...r,ref:a,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});S.displayName=C;var M="DialogPortal",[R,N]=x(M,{forceMount:void 0}),T=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:l}=e,i=E(M,t);return(0,g.jsx)(R,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,g.jsx)(f.C,{present:n||i.open,children:(0,g.jsx)(d.Z,{asChild:!0,container:l,children:e})}))})};T.displayName=M;var L="DialogOverlay",P=r.forwardRef((e,t)=>{let n=N(L,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=E(L,e.__scopeDialog);return l.modal?(0,g.jsx)(f.C,{present:r||l.open,children:(0,g.jsx)(O,{...o,ref:t})}):null});P.displayName=L;var j=(0,y.TL)("DialogOverlay.RemoveScroll"),O=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(L,n);return(0,g.jsx)(m.A,{as:j,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(p.sG.div,{"data-state":U(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),D="DialogContent",I=r.forwardRef((e,t)=>{let n=N(D,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=E(D,e.__scopeDialog);return(0,g.jsx)(f.C,{present:r||l.open,children:l.modal?(0,g.jsx)(F,{...o,ref:t}):(0,g.jsx)(H,{...o,ref:t})})});I.displayName=D;var F=r.forwardRef((e,t)=>{let n=E(D,e.__scopeDialog),i=r.useRef(null),a=(0,l.s)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,v.Eq)(e)},[]),(0,g.jsx)(W,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),H=r.forwardRef((e,t)=>{let n=E(D,e.__scopeDialog),o=r.useRef(!1),l=r.useRef(!1);return(0,g.jsx)(W,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,i;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(i=n.triggerRef.current)||i.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var r,i;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let a=t.target;(null==(i=n.triggerRef.current)?void 0:i.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),W=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...u}=e,d=E(D,n),f=r.useRef(null),p=(0,l.s)(t,f);return(0,h.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,g.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":U(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(Z,{titleId:d.titleId}),(0,g.jsx)($,{contentRef:f,descriptionId:d.descriptionId})]})]})}),_="DialogTitle",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(_,n);return(0,g.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});B.displayName=_;var V="DialogDescription",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(V,n);return(0,g.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});z.displayName=V;var q="DialogClose",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(q,n);return(0,g.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function U(e){return e?"open":"closed"}G.displayName=q;var K="DialogTitleWarning",[Y,X]=(0,i.q)(K,{contentName:D,titleName:_,docsSlug:"dialog"}),Z=e=>{let{titleId:t}=e,n=X(K),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},$=e=>{let{contentRef:t,descriptionId:n}=e,o=X("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(l))},[l,t,n]),null},J=A,Q=S,ee=T,et=P,en=I,er=B,eo=z,el=G},2910:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},3183:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},3188:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},3192:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]])},3383:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,l=new WeakMap,i={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],d=[],f=new Set,p=new Set(c),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,a),s.set(e,u),d.push(e),1===a&&i&&l.set(e,!0),1===u&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,i=s.get(e)-1;o.set(e,t),s.set(e,i),t||(l.has(e)||e.removeAttribute(r),l.delete(e)),i||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,l=new WeakMap,i={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),l=t||r(e);return l?(o.push.apply(o,Array.from(l.querySelectorAll("[aria-live], script"))),c(o,l,n,"aria-hidden")):function(){return null}}},4128:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},4229:(e,t,n)=>{n.d(t,{A:()=>U});var r,o,l=function(){return(l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(9585)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var l=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(l)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return i.options=l({async:!0,ssr:!1},e),i}(),m=function(){},v=a.forwardRef(function(e,t){var n,r,o,u,c=a.useRef(null),p=a.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=p[0],y=p[1],g=e.forwardProps,w=e.children,x=e.className,b=e.removeScrollBar,k=e.enabled,E=e.shards,A=e.sideCar,C=e.noRelative,S=e.noIsolation,M=e.inert,R=e.allowPinchZoom,N=e.as,T=e.gapMode,L=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),j=l(l({},L),v);return a.createElement(a.Fragment,null,k&&a.createElement(A,{sideCar:h,removeScrollBar:b,shards:E,noRelative:C,noIsolation:S,inert:M,setCallbacks:y,allowPinchZoom:!!R,lockRef:c,gapMode:T}),g?a.cloneElement(a.Children.only(w),l(l({},j),{ref:P})):a.createElement(void 0===N?"div":N,l({},j,{className:x,ref:P}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:u};var y=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,l({},n))};y.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var l,i;(l=t).styleSheet?l.styleSheet.cssText=r:l.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=g();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},x=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},k=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[k(n),k(r),k(o)]},A=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},C=x(),S="data-scroll-locked",M=function(e,t,n,r){var o=e.left,l=e.top,i=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},R=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},N=function(){a.useEffect(function(){return document.body.setAttribute(S,(R()+1).toString()),function(){var e=R()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},T=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;N();var l=a.useMemo(function(){return A(o)},[o]);return a.createElement(C,{styles:M(l,!t,o,n?"":"!important")})},L=!1;if("undefined"!=typeof window)try{var P=Object.defineProperty({},"passive",{get:function(){return L=!0,!0}});window.addEventListener("test",P,P),window.removeEventListener("test",P,P)}catch(e){L=!1}var j=!!L&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},H=function(e,t,n,r,o){var l,i=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),a=i*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=F(e,u),m=h[0],v=h[1]-h[2]-i*m;(m||v)&&I(e,u)&&(f+=v,p+=m);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},V=0,z=[];let q=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(V++)[0],l=a.useState(x)[0],i=a.useRef(e);a.useEffect(function(){i.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,l=t.length;o<l;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,l=W(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-l[0],c="deltaY"in e?e.deltaY:a[1]-l[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=D(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return H(p,t,e,"h"===p?u:c,!0)},[]),c=a.useCallback(function(e){if(z.length&&z[z.length-1]===l){var n="deltaY"in e?_(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var l={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),d=a.useCallback(function(e){n.current=W(e),r.current=void 0},[]),f=a.useCallback(function(t){s(t.type,_(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,W(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return z.push(l),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,j),document.addEventListener("touchmove",c,j),document.addEventListener("touchstart",d,j),function(){z=z.filter(function(e){return e!==l}),document.removeEventListener("wheel",c,j),document.removeEventListener("touchmove",c,j),document.removeEventListener("touchstart",d,j)}},[]);var h=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(T,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),y);var G=a.forwardRef(function(e,t){return a.createElement(v,l({},e,{ref:t,sideCar:q}))});G.classNames=v.classNames;let U=G},4281:(e,t,n)=>{n.d(t,{X:()=>l});var r=n(9585),o=n(6921);function l(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},4397:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},4485:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},4721:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4726:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("square-check-big",[["path",{d:"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344",key:"2acyp4"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4761:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},4835:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("calendar-days",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},4842:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},4844:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},4853:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(9585),o=n(4455),l=n(6921),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(u.current);s.current="mounted"===d?e:"none"},[d]),(0,l.N)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=a(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,l.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=a(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(s.current=a(u.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,i(e)},[])}}(t),u="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),c=(0,o.s)(i.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||i.isPresent?r.cloneElement(u,{ref:c}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},5048:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},5790:(e,t,n)=>{n.d(t,{Oh:()=>l});var r=n(9585),o=0;function l(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:i()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},6277:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6351:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},6380:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]])},6452:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(9585),o=n(4455),l=n(7905),i=n(7090),a=n(9605),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:y,...g}=e,[w,x]=r.useState(null),b=(0,i.c)(v),k=(0,i.c)(y),E=r.useRef(null),A=(0,o.s)(t,e=>x(e)),C=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(C.paused||!w)return;let t=e.target;w.contains(t)?E.current=t:h(E.current,{select:!0})},t=function(e){if(C.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||h(E.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,C.paused]),r.useEffect(()=>{if(w){m.add(C);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,b),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,b),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,k),w.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,k),m.remove(C)},0)}}},[w,b,k,C]);let S=r.useCallback(e=>{if(!n&&!d||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,l]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&l?e.shiftKey||r!==l?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(l,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,C.paused]);return(0,a.jsx)(l.sG.div,{tabIndex:-1,...g,ref:A,onKeyDown:S})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=v(e,t)).unshift(t)},remove(t){var n;null==(n=(e=v(e,t))[0])||n.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},6466:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},6901:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},6921:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(9585),o=globalThis?.document?r.useLayoutEffect:()=>{}},6992:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},7090:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(9585);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},7183:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},7229:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},7271:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(9585),l=n(4761),i=n(7905),a=n(4455),u=n(7090),c=n(9605),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:v,onPointerDownOutside:y,onFocusOutside:g,onInteractOutside:w,onDismiss:x,...b}=e,k=o.useContext(d),[E,A]=o.useState(null),C=null!=(f=null==E?void 0:E.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,S]=o.useState({}),M=(0,a.s)(t,e=>A(e)),R=Array.from(k.layers),[N]=[...k.layersWithOutsidePointerEventsDisabled].slice(-1),T=R.indexOf(N),L=E?R.indexOf(E):-1,P=k.layersWithOutsidePointerEventsDisabled.size>0,j=L>=T,O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),l=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!l.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);l.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>l.current=!0}}(e=>{let t=e.target,n=[...k.branches].some(e=>e.contains(t));j&&!n&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==x||x())},C),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),l=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!l.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}(e=>{let t=e.target;![...k.branches].some(e=>e.contains(t))&&(null==g||g(e),null==w||w(e),e.defaultPrevented||null==x||x())},C);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{L===k.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},C),o.useEffect(()=>{if(E)return m&&(0===k.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),k.layersWithOutsidePointerEventsDisabled.add(E)),k.layers.add(E),p(),()=>{m&&1===k.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[E,C,m,k]),o.useEffect(()=>()=>{E&&(k.layers.delete(E),k.layersWithOutsidePointerEventsDisabled.delete(E),p())},[E,k]),o.useEffect(()=>{let e=()=>S({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(i.sG.div,{...b,ref:M,style:{pointerEvents:P?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,l.m)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,l.m)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,l.m)(e.onPointerDownCapture,O.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,l=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&l.addEventListener(e,t,{once:!0}),o?(0,i.hO)(l,a):l.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),l=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(i.sG.div,{...e,ref:l})}).displayName="DismissableLayerBranch"},7418:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},7421:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(9585),l=n(6921),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(i());return(0,l.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},8036:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(9585),o=n(3220),l=n(7905),i=n(6921),a=n(9605),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[d,f]=r.useState(!1);(0,i.N)(()=>f(!0),[]);let p=c||d&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,a.jsx)(l.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"},8069:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(9585);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},8164:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},8333:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},8347:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},8849:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},8881:(e,t,n)=>{n.d(t,{UC:()=>ng,YJ:()=>nx,In:()=>nv,q7:()=>nk,VF:()=>nA,p4:()=>nE,JU:()=>nb,ZL:()=>ny,bL:()=>np,wn:()=>nS,PP:()=>nC,wv:()=>nM,l9:()=>nh,WT:()=>nm,LM:()=>nw});var r,o=n(9585),l=n(3220);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var a=n(4761);function u(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function c(e,t){var n=u(e,t,"get");return n.get?n.get.call(e):n.value}function s(e,t,n){var r=u(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var d=n(8972),f=n(4455),p=n(8130),h=n(9605),m=new WeakMap;function v(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=y(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function y(e){return e!=e||0===e?0:Math.trunc(e)}r=new WeakMap;var g=o.createContext(void 0),w=n(7271),x=n(5790),b=n(6452),k=n(7421);let E=["top","right","bottom","left"],A=Math.min,C=Math.max,S=Math.round,M=Math.floor,R=e=>({x:e,y:e}),N={left:"right",right:"left",bottom:"top",top:"bottom"},T={start:"end",end:"start"};function L(e,t){return"function"==typeof e?e(t):e}function P(e){return e.split("-")[0]}function j(e){return e.split("-")[1]}function O(e){return"x"===e?"y":"x"}function D(e){return"y"===e?"height":"width"}let I=new Set(["top","bottom"]);function F(e){return I.has(P(e))?"y":"x"}function H(e){return e.replace(/start|end/g,e=>T[e])}let W=["left","right"],_=["right","left"],B=["top","bottom"],V=["bottom","top"];function z(e){return e.replace(/left|right|bottom|top/g,e=>N[e])}function q(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function G(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function U(e,t,n){let r,{reference:o,floating:l}=e,i=F(t),a=O(F(t)),u=D(a),c=P(t),s="y"===i,d=o.x+o.width/2-l.width/2,f=o.y+o.height/2-l.height/2,p=o[u]/2-l[u]/2;switch(c){case"top":r={x:d,y:o.y-l.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-l.width,y:f};break;default:r={x:o.x,y:o.y}}switch(j(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let K=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),u=await (null==i.isRTL?void 0:i.isRTL(t)),c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=U(c,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:l,fn:m}=a[n],{x:v,y:y,data:g,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:i,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=y?y:d,p={...p,[l]:{...p[l],...g}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=U(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function Y(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:i,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=L(t,e),h=q(p),m=a[f?"floating"===d?"reference":"floating":d],v=G(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(m)))||n?m:m.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,g=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),w=await (null==l.isElement?void 0:l.isElement(g))&&await (null==l.getScale?void 0:l.getScale(g))||{x:1,y:1},x=G(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:g,strategy:u}):y);return{top:(v.top-x.top+h.top)/w.y,bottom:(x.bottom-v.bottom+h.bottom)/w.y,left:(v.left-x.left+h.left)/w.x,right:(x.right-v.right+h.right)/w.x}}function X(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Z(e){return E.some(t=>e[t]>=0)}let $=new Set(["left","top"]);async function J(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=P(n),a=j(n),u="y"===F(n),c=$.has(i)?-1:1,s=l&&u?-1:1,d=L(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function Q(){return"undefined"!=typeof window}function ee(e){return er(e)?(e.nodeName||"").toLowerCase():"#document"}function et(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function en(e){var t;return null==(t=(er(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function er(e){return!!Q()&&(e instanceof Node||e instanceof et(e).Node)}function eo(e){return!!Q()&&(e instanceof Element||e instanceof et(e).Element)}function el(e){return!!Q()&&(e instanceof HTMLElement||e instanceof et(e).HTMLElement)}function ei(e){return!!Q()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof et(e).ShadowRoot)}let ea=new Set(["inline","contents"]);function eu(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ew(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!ea.has(o)}let ec=new Set(["table","td","th"]),es=[":popover-open",":modal"];function ed(e){return es.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let ef=["transform","translate","scale","rotate","perspective"],ep=["transform","translate","scale","rotate","perspective","filter"],eh=["paint","layout","strict","content"];function em(e){let t=ev(),n=eo(e)?ew(e):e;return ef.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||ep.some(e=>(n.willChange||"").includes(e))||eh.some(e=>(n.contain||"").includes(e))}function ev(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ey=new Set(["html","body","#document"]);function eg(e){return ey.has(ee(e))}function ew(e){return et(e).getComputedStyle(e)}function ex(e){return eo(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eb(e){if("html"===ee(e))return e;let t=e.assignedSlot||e.parentNode||ei(e)&&e.host||en(e);return ei(t)?t.host:t}function ek(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eb(t);return eg(n)?t.ownerDocument?t.ownerDocument.body:t.body:el(n)&&eu(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),i=et(o);if(l){let e=eE(i);return t.concat(i,i.visualViewport||[],eu(o)?o:[],e&&n?ek(e):[])}return t.concat(o,ek(o,[],n))}function eE(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eA(e){let t=ew(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=el(e),l=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=S(n)!==l||S(r)!==i;return a&&(n=l,r=i),{width:n,height:r,$:a}}function eC(e){return eo(e)?e:e.contextElement}function eS(e){let t=eC(e);if(!el(t))return R(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=eA(t),i=(l?S(n.width):n.width)/r,a=(l?S(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),a&&Number.isFinite(a)||(a=1),{x:i,y:a}}let eM=R(0);function eR(e){let t=et(e);return ev()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eM}function eN(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=eC(e),a=R(1);t&&(r?eo(r)&&(a=eS(r)):a=eS(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===et(i))&&o)?eR(i):R(0),c=(l.left+u.x)/a.x,s=(l.top+u.y)/a.y,d=l.width/a.x,f=l.height/a.y;if(i){let e=et(i),t=r&&eo(r)?et(r):r,n=e,o=eE(n);for(;o&&r&&t!==n;){let e=eS(o),t=o.getBoundingClientRect(),r=ew(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=l,s+=i,o=eE(n=et(o))}}return G({width:d,height:f,x:c,y:s})}function eT(e,t){let n=ex(e).scrollLeft;return t?t.left+n:eN(en(e)).left+n}function eL(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eT(e,r)),y:r.top+t.scrollTop}}let eP=new Set(["absolute","fixed"]);function ej(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=et(e),r=en(e),o=n.visualViewport,l=r.clientWidth,i=r.clientHeight,a=0,u=0;if(o){l=o.width,i=o.height;let e=ev();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:l,height:i,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=en(e),n=ex(e),r=e.ownerDocument.body,o=C(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=C(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),i=-n.scrollLeft+eT(e),a=-n.scrollTop;return"rtl"===ew(r).direction&&(i+=C(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:i,y:a}}(en(e));else if(eo(t))r=function(e,t){let n=eN(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=el(e)?eS(e):R(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:r*l.y}}(t,n);else{let n=eR(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return G(r)}function eO(e){return"static"===ew(e).position}function eD(e,t){if(!el(e)||"fixed"===ew(e).position)return null;if(t)return t(e);let n=e.offsetParent;return en(e)===n&&(n=n.ownerDocument.body),n}function eI(e,t){var n;let r=et(e);if(ed(e))return r;if(!el(e)){let t=eb(e);for(;t&&!eg(t);){if(eo(t)&&!eO(t))return t;t=eb(t)}return r}let o=eD(e,t);for(;o&&(n=o,ec.has(ee(n)))&&eO(o);)o=eD(o,t);return o&&eg(o)&&eO(o)&&!em(o)?r:o||function(e){let t=eb(e);for(;el(t)&&!eg(t);){if(em(t))return t;if(ed(t))break;t=eb(t)}return null}(e)||r}let eF=async function(e){let t=this.getOffsetParent||eI,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=el(t),o=en(t),l="fixed"===n,i=eN(e,!0,l,t),a={scrollLeft:0,scrollTop:0},u=R(0);if(r||!r&&!l)if(("body"!==ee(t)||eu(o))&&(a=ex(t)),r){let e=eN(t,!0,l,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eT(o));l&&!r&&o&&(u.x=eT(o));let c=!o||r||l?R(0):eL(o,a);return{x:i.left+a.scrollLeft-u.x-c.x,y:i.top+a.scrollTop-u.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eH={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,i=en(r),a=!!t&&ed(t.floating);if(r===i||a&&l)return n;let u={scrollLeft:0,scrollTop:0},c=R(1),s=R(0),d=el(r);if((d||!d&&!l)&&(("body"!==ee(r)||eu(i))&&(u=ex(r)),el(r))){let e=eN(r);c=eS(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!i||d||l?R(0):eL(i,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:en,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?ed(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=ek(e,[],!1).filter(e=>eo(e)&&"body"!==ee(e)),o=null,l="fixed"===ew(e).position,i=l?eb(e):e;for(;eo(i)&&!eg(i);){let t=ew(i),n=em(i);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&eP.has(o.position)||eu(i)&&!n&&function e(t,n){let r=eb(t);return!(r===n||!eo(r)||eg(r))&&("fixed"===ew(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=eb(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],i=l[0],a=l.reduce((e,n)=>{let r=ej(t,n,o);return e.top=C(r.top,e.top),e.right=A(r.right,e.right),e.bottom=A(r.bottom,e.bottom),e.left=C(r.left,e.left),e},ej(t,i,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eI,getElementRects:eF,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eA(e);return{width:t,height:n}},getScale:eS,isElement:eo,isRTL:function(e){return"rtl"===ew(e).direction}};function eW(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e_=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:i,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=L(e,t)||{};if(null==c)return{};let d=q(s),f={x:n,y:r},p=O(F(o)),h=D(p),m=await i.getDimensions(c),v="y"===p,y=v?"clientHeight":"clientWidth",g=l.reference[h]+l.reference[p]-f[p]-l.floating[h],w=f[p]-l.reference[p],x=await (null==i.getOffsetParent?void 0:i.getOffsetParent(c)),b=x?x[y]:0;b&&await (null==i.isElement?void 0:i.isElement(x))||(b=a.floating[y]||l.floating[h]);let k=b/2-m[h]/2-1,E=A(d[v?"top":"left"],k),S=A(d[v?"bottom":"right"],k),M=b-m[h]-S,R=b/2-m[h]/2+(g/2-w/2),N=C(E,A(R,M)),T=!u.arrow&&null!=j(o)&&R!==N&&l.reference[h]/2-(R<E?E:S)-m[h]/2<0,P=T?R<E?R-E:R-M:0;return{[p]:f[p]+P,data:{[p]:N,centerOffset:R-N-P,...T&&{alignmentOffset:P}},reset:T}}}),eB=(e,t,n)=>{let r=new Map,o={platform:eH,...n},l={...o.platform,_c:r};return K(e,t,{...o,platform:l})};var eV="undefined"!=typeof document?o.useLayoutEffect:function(){};function ez(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ez(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ez(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eq(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eG(e,t){let n=eq(e);return Math.round(t*n)/n}function eU(e){let t=o.useRef(e);return eV(()=>{t.current=e}),t}let eK=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?e_({element:n.current,padding:r}).fn(t):{}:n?e_({element:n,padding:r}).fn(t):{}}}),eY=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:i,middlewareData:a}=t,u=await J(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:l+u.y,data:{...u,placement:i}}}}}(e),options:[e,t]}),eX=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:i=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=L(e,t),c={x:n,y:r},s=await Y(t,u),d=F(P(o)),f=O(d),p=c[f],h=c[d];if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=C(n,A(p,r))}if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=C(n,A(h,r))}let m=a.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:l,[d]:i}}}}}}(e),options:[e,t]}),eZ=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=L(e,t),s={x:n,y:r},d=F(o),f=O(d),p=s[f],h=s[d],m=L(a,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===f?"height":"width",t=l.reference[f]-l.floating[e]+v.mainAxis,n=l.reference[f]+l.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var y,g;let e="y"===f?"width":"height",t=$.has(P(o)),n=l.reference[d]-l.floating[e]+(t&&(null==(y=i.offset)?void 0:y[d])||0)+(t?0:v.crossAxis),r=l.reference[d]+l.reference[e]+(t?0:(null==(g=i.offset)?void 0:g[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),e$=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,i;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:g=!0,...w}=L(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let x=P(a),b=F(s),k=P(s)===s,E=await (null==d.isRTL?void 0:d.isRTL(f.floating)),A=m||(k||!g?[z(s)]:function(e){let t=z(e);return[H(e),t,H(t)]}(s)),C="none"!==y;!m&&C&&A.push(...function(e,t,n,r){let o=j(e),l=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?_:W;return t?W:_;case"left":case"right":return t?B:V;default:return[]}}(P(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(H)))),l}(s,g,y,E));let S=[s,...A],M=await Y(t,w),R=[],N=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&R.push(M[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=j(e),o=O(F(e)),l=D(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=z(i)),[i,z(i)]}(a,c,E);R.push(M[e[0]],M[e[1]])}if(N=[...N,{placement:a,overflows:R}],!R.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=S[e];if(t&&("alignment"!==h||b===F(t)||N.every(e=>e.overflows[0]>0&&F(e.placement)===b)))return{data:{index:e,overflows:N},reset:{placement:t}};let n=null==(l=N.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(v){case"bestFit":{let e=null==(i=N.filter(e=>{if(C){let t=F(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eJ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:i,rects:a,platform:u,elements:c}=t,{apply:s=()=>{},...d}=L(e,t),f=await Y(t,d),p=P(i),h=j(i),m="y"===F(i),{width:v,height:y}=a.floating;"top"===p||"bottom"===p?(o=p,l=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(l=p,o="end"===h?"top":"bottom");let g=y-f.top-f.bottom,w=v-f.left-f.right,x=A(y-f[o],g),b=A(v-f[l],w),k=!t.middlewareData.shift,E=x,S=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(S=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(E=g),k&&!h){let e=C(f.left,0),t=C(f.right,0),n=C(f.top,0),r=C(f.bottom,0);m?S=v-2*(0!==e||0!==t?e+t:C(f.left,f.right)):E=y-2*(0!==n||0!==r?n+r:C(f.top,f.bottom))}await s({...t,availableWidth:S,availableHeight:E});let M=await u.getDimensions(c.floating);return v!==M.width||y!==M.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eQ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=L(e,t);switch(r){case"referenceHidden":{let e=X(await Y(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:Z(e)}}}case"escaped":{let e=X(await Y(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:Z(e)}}}default:return{}}}}}(e),options:[e,t]}),e0=(e,t)=>({...eK(e),options:[e,t]});var e1=n(7905),e2=o.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,h.jsx)(e1.sG.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,h.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e2.displayName="Arrow";var e5=n(7090),e6=n(6921),e4=n(4281),e8="Popper",[e3,e7]=(0,d.A)(e8),[e9,te]=e3(e8),tt=e=>{let{__scopePopper:t,children:n}=e,[r,l]=o.useState(null);return(0,h.jsx)(e9,{scope:t,anchor:r,onAnchorChange:l,children:n})};tt.displayName=e8;var tn="PopperAnchor",tr=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...l}=e,i=te(tn,n),a=o.useRef(null),u=(0,f.s)(t,a);return o.useEffect(()=>{i.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,h.jsx)(e1.sG.div,{...l,ref:u})});tr.displayName=tn;var to="PopperContent",[tl,ti]=e3(to),ta=o.forwardRef((e,t)=>{var n,r,i,a,u,c,s,d;let{__scopePopper:p,side:m="bottom",sideOffset:v=0,align:y="center",alignOffset:g=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:k=0,sticky:E="partial",hideWhenDetached:S=!1,updatePositionStrategy:R="optimized",onPlaced:N,...T}=e,L=te(to,p),[P,j]=o.useState(null),O=(0,f.s)(t,e=>j(e)),[D,I]=o.useState(null),F=(0,e4.X)(D),H=null!=(s=null==F?void 0:F.width)?s:0,W=null!=(d=null==F?void 0:F.height)?d:0,_="number"==typeof k?k:{top:0,right:0,bottom:0,left:0,...k},B=Array.isArray(b)?b:[b],V=B.length>0,z={padding:_,boundary:B.filter(td),altBoundary:V},{refs:q,floatingStyles:G,placement:U,isPositioned:K,middlewareData:Y}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:i,elements:{reference:a,floating:u}={},transform:c=!0,whileElementsMounted:s,open:d}=e,[f,p]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=o.useState(r);ez(h,r)||m(r);let[v,y]=o.useState(null),[g,w]=o.useState(null),x=o.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),b=o.useCallback(e=>{e!==C.current&&(C.current=e,w(e))},[]),k=a||v,E=u||g,A=o.useRef(null),C=o.useRef(null),S=o.useRef(f),M=null!=s,R=eU(s),N=eU(i),T=eU(d),L=o.useCallback(()=>{if(!A.current||!C.current)return;let e={placement:t,strategy:n,middleware:h};N.current&&(e.platform=N.current),eB(A.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};P.current&&!ez(S.current,t)&&(S.current=t,l.flushSync(()=>{p(t)}))})},[h,t,n,N,T]);eV(()=>{!1===d&&S.current.isPositioned&&(S.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let P=o.useRef(!1);eV(()=>(P.current=!0,()=>{P.current=!1}),[]),eV(()=>{if(k&&(A.current=k),E&&(C.current=E),k&&E){if(R.current)return R.current(k,E,L);L()}},[k,E,L,R,M]);let j=o.useMemo(()=>({reference:A,floating:C,setReference:x,setFloating:b}),[x,b]),O=o.useMemo(()=>({reference:k,floating:E}),[k,E]),D=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=eG(O.floating,f.x),r=eG(O.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...eq(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,O.floating,f.x,f.y]);return o.useMemo(()=>({...f,update:L,refs:j,elements:O,floatingStyles:D}),[f,L,j,O,D])}({strategy:"fixed",placement:m+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eC(e),d=l||i?[...s?ek(s):[],...ek(t)]:[];d.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=en(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function i(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),l();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let h=M(d),m=M(o.clientWidth-(s+f)),v={rootMargin:-h+"px "+-m+"px "+-M(o.clientHeight-(d+p))+"px "+-M(s)+"px",threshold:C(0,A(1,u))||1},y=!0;function g(t){let r=t[0].intersectionRatio;if(r!==u){if(!y)return i();r?i(!1,r):n=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==r||eW(c,e.getBoundingClientRect())||i(),y=!1}try{r=new IntersectionObserver(g,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(g,v)}r.observe(e)}(!0),l}(s,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let m=c?eN(e):null;return c&&function t(){let r=eN(e);m&&!eW(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{l&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===R})},elements:{reference:L.anchor},middleware:[eY({mainAxis:v+W,alignmentAxis:g}),x&&eX({mainAxis:!0,crossAxis:!1,limiter:"partial"===E?eZ():void 0,...z}),x&&e$({...z}),eJ({...z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:l,height:i}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),D&&e0({element:D,padding:w}),tf({arrowWidth:H,arrowHeight:W}),S&&eQ({strategy:"referenceHidden",...z})]}),[X,Z]=tp(U),$=(0,e5.c)(N);(0,e6.N)(()=>{K&&(null==$||$())},[K,$]);let J=null==(n=Y.arrow)?void 0:n.x,Q=null==(r=Y.arrow)?void 0:r.y,ee=(null==(i=Y.arrow)?void 0:i.centerOffset)!==0,[et,er]=o.useState();return(0,e6.N)(()=>{P&&er(window.getComputedStyle(P).zIndex)},[P]),(0,h.jsx)("div",{ref:q.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:K?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:et,"--radix-popper-transform-origin":[null==(a=Y.transformOrigin)?void 0:a.x,null==(u=Y.transformOrigin)?void 0:u.y].join(" "),...(null==(c=Y.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,h.jsx)(tl,{scope:p,placedSide:X,onArrowChange:I,arrowX:J,arrowY:Q,shouldHideArrow:ee,children:(0,h.jsx)(e1.sG.div,{"data-side":X,"data-align":Z,...T,ref:O,style:{...T.style,animation:K?void 0:"none"}})})})});ta.displayName=to;var tu="PopperArrow",tc={top:"bottom",right:"left",bottom:"top",left:"right"},ts=o.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ti(tu,n),l=tc[o.placedSide];return(0,h.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,h.jsx)(e2,{...r,ref:t,style:{...r.style,display:"block"}})})});function td(e){return null!==e}ts.displayName=tu;var tf=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,l,i;let{placement:a,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,h]=tp(a),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(l=null==(r=c.arrow)?void 0:r.x)?l:0)+d/2,y=(null!=(i=null==(o=c.arrow)?void 0:o.y)?i:0)+f/2,g="",w="";return"bottom"===p?(g=s?m:"".concat(v,"px"),w="".concat(-f,"px")):"top"===p?(g=s?m:"".concat(v,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),w=s?m:"".concat(y,"px")):"left"===p&&(g="".concat(u.floating.width+f,"px"),w=s?m:"".concat(y,"px")),{data:{x:g,y:w}}}});function tp(e){let[t,n="center"]=e.split("-");return[t,n]}var th=n(8036),tm=n(9728),tv=n(8069),ty=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});o.forwardRef((e,t)=>(0,h.jsx)(e1.sG.span,{...e,ref:t,style:{...ty,...e.style}})).displayName="VisuallyHidden";var tg=n(3383),tw=n(4229),tx=[" ","Enter","ArrowUp","ArrowDown"],tb=[" ","Enter"],tk="Select",[tE,tA,tC]=function(e){let t=e+"CollectionProvider",[n,r]=(0,d.A)(t),[l,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=o.useRef(null),i=o.useRef(new Map).current;return(0,h.jsx)(l,{scope:t,itemMap:i,collectionRef:r,children:n})};a.displayName=t;let u=e+"CollectionSlot",c=(0,p.TL)(u),s=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(u,n),l=(0,f.s)(t,o.collectionRef);return(0,h.jsx)(c,{ref:l,children:r})});s.displayName=u;let m=e+"CollectionItemSlot",v="data-radix-collection-item",y=(0,p.TL)(m),g=o.forwardRef((e,t)=>{let{scope:n,children:r,...l}=e,a=o.useRef(null),u=(0,f.s)(t,a),c=i(m,n);return o.useEffect(()=>(c.itemMap.set(a,{ref:a,...l}),()=>void c.itemMap.delete(a))),(0,h.jsx)(y,{...{[v]:""},ref:u,children:r})});return g.displayName=m,[{Provider:a,Slot:s,ItemSlot:g},function(t){let n=i(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(tk),[tS,tM]=(0,d.A)(tk,[tC,e7]),tR=e7(),[tN,tT]=tS(tk),[tL,tP]=tS(tk),tj=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:l,onOpenChange:i,value:a,defaultValue:u,onValueChange:c,dir:s,name:d,autoComplete:f,disabled:p,required:m,form:v}=e,y=tR(t),[w,x]=o.useState(null),[b,E]=o.useState(null),[A,C]=o.useState(!1),S=function(e){let t=o.useContext(g);return e||t||"ltr"}(s),[M,R]=(0,tm.i)({prop:r,defaultProp:null!=l&&l,onChange:i,caller:tk}),[N,T]=(0,tm.i)({prop:a,defaultProp:u,onChange:c,caller:tk}),L=o.useRef(null),P=!w||v||!!w.closest("form"),[j,O]=o.useState(new Set),D=Array.from(j).map(e=>e.props.value).join(";");return(0,h.jsx)(tt,{...y,children:(0,h.jsxs)(tN,{required:m,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:A,onValueNodeHasChildrenChange:C,contentId:(0,k.B)(),value:N,onValueChange:T,open:M,onOpenChange:R,dir:S,triggerPointerDownPosRef:L,disabled:p,children:[(0,h.jsx)(tE.Provider,{scope:t,children:(0,h.jsx)(tL,{scope:e.__scopeSelect,onNativeOptionAdd:o.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:o.useCallback(e=>{O(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),P?(0,h.jsxs)(nc,{"aria-hidden":!0,required:m,tabIndex:-1,name:d,autoComplete:f,value:N,onChange:e=>T(e.target.value),disabled:p,form:v,children:[void 0===N?(0,h.jsx)("option",{value:""}):null,Array.from(j)]},D):null]})})};tj.displayName=tk;var tO="SelectTrigger",tD=o.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...l}=e,i=tR(n),u=tT(tO,n),c=u.disabled||r,s=(0,f.s)(t,u.onTriggerChange),d=tA(n),p=o.useRef("touch"),[m,v,y]=nd(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=nf(t,e,n);void 0!==r&&u.onValueChange(r.value)}),g=e=>{c||(u.onOpenChange(!0),y()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,h.jsx)(tr,{asChild:!0,...i,children:(0,h.jsx)(e1.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":ns(u.value)?"":void 0,...l,ref:s,onClick:(0,a.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:(0,a.m)(l.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,a.m)(l.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&tx.includes(e.key)&&(g(),e.preventDefault())})})})});tD.displayName=tO;var tI="SelectValue",tF=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:i="",...a}=e,u=tT(tI,n),{onValueNodeHasChildrenChange:c}=u,s=void 0!==l,d=(0,f.s)(t,u.onValueNodeChange);return(0,e6.N)(()=>{c(s)},[c,s]),(0,h.jsx)(e1.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:ns(u.value)?(0,h.jsx)(h.Fragment,{children:i}):l})});tF.displayName=tI;var tH=o.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,h.jsx)(e1.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tH.displayName="SelectIcon";var tW=e=>(0,h.jsx)(th.Z,{asChild:!0,...e});tW.displayName="SelectPortal";var t_="SelectContent",tB=o.forwardRef((e,t)=>{let n=tT(t_,e.__scopeSelect),[r,i]=o.useState();return((0,e6.N)(()=>{i(new DocumentFragment)},[]),n.open)?(0,h.jsx)(tG,{...e,ref:t}):r?l.createPortal((0,h.jsx)(tV,{scope:e.__scopeSelect,children:(0,h.jsx)(tE.Slot,{scope:e.__scopeSelect,children:(0,h.jsx)("div",{children:e.children})})}),r):null});tB.displayName=t_;var[tV,tz]=tS(t_),tq=(0,p.TL)("SelectContent.RemoveScroll"),tG=o.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:i,onPointerDownOutside:u,side:c,sideOffset:s,align:d,alignOffset:p,arrowPadding:m,collisionBoundary:v,collisionPadding:y,sticky:g,hideWhenDetached:k,avoidCollisions:E,...A}=e,C=tT(t_,n),[S,M]=o.useState(null),[R,N]=o.useState(null),T=(0,f.s)(t,e=>M(e)),[L,P]=o.useState(null),[j,O]=o.useState(null),D=tA(n),[I,F]=o.useState(!1),H=o.useRef(!1);o.useEffect(()=>{if(S)return(0,tg.Eq)(S)},[S]),(0,x.Oh)();let W=o.useCallback(e=>{let[t,...n]=D().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&R&&(R.scrollTop=0),n===r&&R&&(R.scrollTop=R.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[D,R]),_=o.useCallback(()=>W([L,S]),[W,L,S]);o.useEffect(()=>{I&&_()},[I,_]);let{onOpenChange:B,triggerPointerDownPosRef:V}=C;o.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{var n,r,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=V.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(l=null==(r=V.current)?void 0:r.y)?l:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||B(!1),document.removeEventListener("pointermove",t),V.current=null};return null!==V.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,B,V]),o.useEffect(()=>{let e=()=>B(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[B]);let[z,q]=nd(e=>{let t=D().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=nf(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),G=o.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==C.value&&C.value===t||r)&&(P(e),r&&(H.current=!0))},[C.value]),U=o.useCallback(()=>null==S?void 0:S.focus(),[S]),K=o.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==C.value&&C.value===t||r)&&O(e)},[C.value]),Y="popper"===r?tK:tU,X=Y===tK?{side:c,sideOffset:s,align:d,alignOffset:p,arrowPadding:m,collisionBoundary:v,collisionPadding:y,sticky:g,hideWhenDetached:k,avoidCollisions:E}:{};return(0,h.jsx)(tV,{scope:n,content:S,viewport:R,onViewportChange:N,itemRefCallback:G,selectedItem:L,onItemLeave:U,itemTextRefCallback:K,focusSelectedItem:_,selectedItemText:j,position:r,isPositioned:I,searchRef:z,children:(0,h.jsx)(tw.A,{as:tq,allowPinchZoom:!0,children:(0,h.jsx)(b.n,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(l,e=>{var t;null==(t=C.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,h.jsx)(w.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,h.jsx)(Y,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...A,...X,onPlaced:()=>F(!0),ref:T,style:{display:"flex",flexDirection:"column",outline:"none",...A.style},onKeyDown:(0,a.m)(A.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||q(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=D().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>W(t)),e.preventDefault()}})})})})})})});tG.displayName="SelectContentImpl";var tU=o.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...l}=e,a=tT(t_,n),u=tz(t_,n),[c,s]=o.useState(null),[d,p]=o.useState(null),m=(0,f.s)(t,e=>p(e)),v=tA(n),y=o.useRef(!1),g=o.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:b,focusSelectedItem:k}=u,E=o.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&d&&w&&x&&b){let e=a.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),o=b.getBoundingClientRect();if("rtl"!==a.dir){let r=o.left-t.left,l=n.left-r,a=e.left-l,u=e.width+a,s=Math.max(u,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=u+"px",c.style.left=d+"px"}else{let r=t.right-o.right,l=window.innerWidth-n.right-r,a=window.innerWidth-e.right-l,u=e.width+a,s=Math.max(u,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=u+"px",c.style.right=d+"px"}let l=v(),u=window.innerHeight-20,s=w.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),h=parseInt(f.paddingTop,10),m=parseInt(f.borderBottomWidth,10),g=p+h+s+parseInt(f.paddingBottom,10)+m,k=Math.min(5*x.offsetHeight,g),E=window.getComputedStyle(w),A=parseInt(E.paddingTop,10),C=parseInt(E.paddingBottom,10),S=e.top+e.height/2-10,M=x.offsetHeight/2,R=p+h+(x.offsetTop+M);if(R<=S){let e=l.length>0&&x===l[l.length-1].ref.current;c.style.bottom="0px";let t=Math.max(u-S,M+(e?C:0)+(d.clientHeight-w.offsetTop-w.offsetHeight)+m);c.style.height=R+t+"px"}else{let e=l.length>0&&x===l[0].ref.current;c.style.top="0px";let t=Math.max(S,p+w.offsetTop+(e?A:0)+M);c.style.height=t+(g-R)+"px",w.scrollTop=R-S+w.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=k+"px",c.style.maxHeight=u+"px",null==r||r(),requestAnimationFrame(()=>y.current=!0)}},[v,a.trigger,a.valueNode,c,d,w,x,b,a.dir,r]);(0,e6.N)(()=>E(),[E]);let[A,C]=o.useState();(0,e6.N)(()=>{d&&C(window.getComputedStyle(d).zIndex)},[d]);let S=o.useCallback(e=>{e&&!0===g.current&&(E(),null==k||k(),g.current=!1)},[E,k]);return(0,h.jsx)(tY,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:y,onScrollButtonChange:S,children:(0,h.jsx)("div",{ref:s,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:A},children:(0,h.jsx)(e1.sG.div,{...l,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tU.displayName="SelectItemAlignedPosition";var tK=o.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,i=tR(n);return(0,h.jsx)(ta,{...i,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tK.displayName="SelectPopperPosition";var[tY,tX]=tS(t_,{}),tZ="SelectViewport",t$=o.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...l}=e,i=tz(tZ,n),u=tX(tZ,n),c=(0,f.s)(t,i.onViewportChange),s=o.useRef(0);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,h.jsx)(tE.Slot,{scope:n,children:(0,h.jsx)(e1.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,a.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if((null==r?void 0:r.current)&&n){let e=Math.abs(s.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,i=Math.min(r,l),a=l-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}s.current=t.scrollTop})})})]})});t$.displayName=tZ;var tJ="SelectGroup",[tQ,t0]=tS(tJ),t1=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,k.B)();return(0,h.jsx)(tQ,{scope:n,id:o,children:(0,h.jsx)(e1.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});t1.displayName=tJ;var t2="SelectLabel",t5=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=t0(t2,n);return(0,h.jsx)(e1.sG.div,{id:o.id,...r,ref:t})});t5.displayName=t2;var t6="SelectItem",[t4,t8]=tS(t6),t3=o.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:l=!1,textValue:i,...u}=e,c=tT(t6,n),s=tz(t6,n),d=c.value===r,[p,m]=o.useState(null!=i?i:""),[v,y]=o.useState(!1),g=(0,f.s)(t,e=>{var t;return null==(t=s.itemRefCallback)?void 0:t.call(s,e,r,l)}),w=(0,k.B)(),x=o.useRef("touch"),b=()=>{l||(c.onValueChange(r),c.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,h.jsx)(t4,{scope:n,value:r,disabled:l,textId:w,isSelected:d,onItemTextChange:o.useCallback(e=>{m(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,h.jsx)(tE.ItemSlot,{scope:n,value:r,disabled:l,textValue:p,children:(0,h.jsx)(e1.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":d&&v,"data-state":d?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...u,ref:g,onFocus:(0,a.m)(u.onFocus,()=>y(!0)),onBlur:(0,a.m)(u.onBlur,()=>y(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{if(x.current=e.pointerType,l){var t;null==(t=s.onItemLeave)||t.call(s)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=s.onItemLeave)||t.call(s)}}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{var t;((null==(t=s.searchRef)?void 0:t.current)===""||" "!==e.key)&&(tb.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});t3.displayName=t6;var t7="SelectItemText",t9=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:i,...a}=e,u=tT(t7,n),c=tz(t7,n),s=t8(t7,n),d=tP(t7,n),[p,m]=o.useState(null),v=(0,f.s)(t,e=>m(e),s.onItemTextChange,e=>{var t;return null==(t=c.itemTextRefCallback)?void 0:t.call(c,e,s.value,s.disabled)}),y=null==p?void 0:p.textContent,g=o.useMemo(()=>(0,h.jsx)("option",{value:s.value,disabled:s.disabled,children:y},s.value),[s.disabled,s.value,y]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=d;return(0,e6.N)(()=>(w(g),()=>x(g)),[w,x,g]),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(e1.sG.span,{id:s.textId,...a,ref:v}),s.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(a.children,u.valueNode):null]})});t9.displayName=t7;var ne="SelectItemIndicator",nt=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return t8(ne,n).isSelected?(0,h.jsx)(e1.sG.span,{"aria-hidden":!0,...r,ref:t}):null});nt.displayName=ne;var nn="SelectScrollUpButton",nr=o.forwardRef((e,t)=>{let n=tz(nn,e.__scopeSelect),r=tX(nn,e.__scopeSelect),[l,i]=o.useState(!1),a=(0,f.s)(t,r.onScrollButtonChange);return(0,e6.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,h.jsx)(ni,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});nr.displayName=nn;var no="SelectScrollDownButton",nl=o.forwardRef((e,t)=>{let n=tz(no,e.__scopeSelect),r=tX(no,e.__scopeSelect),[l,i]=o.useState(!1),a=(0,f.s)(t,r.onScrollButtonChange);return(0,e6.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,h.jsx)(ni,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});nl.displayName=no;var ni=o.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...l}=e,i=tz("SelectScrollButton",n),u=o.useRef(null),c=tA(n),s=o.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return o.useEffect(()=>()=>s(),[s]),(0,e6.N)(()=>{var e;let t=c().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[c]),(0,h.jsx)(e1.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,a.m)(l.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(r,50))}),onPointerMove:(0,a.m)(l.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===u.current&&(u.current=window.setInterval(r,50))}),onPointerLeave:(0,a.m)(l.onPointerLeave,()=>{s()})})}),na=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,h.jsx)(e1.sG.div,{"aria-hidden":!0,...r,ref:t})});na.displayName="SelectSeparator";var nu="SelectArrow";o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tR(n),l=tT(nu,n),i=tz(nu,n);return l.open&&"popper"===i.position?(0,h.jsx)(ts,{...o,...r,ref:t}):null}).displayName=nu;var nc=o.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...l}=e,i=o.useRef(null),a=(0,f.s)(t,i),u=(0,tv.Z)(r);return o.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[u,r]),(0,h.jsx)(e1.sG.select,{...l,style:{...ty,...l.style},ref:a,defaultValue:r})});function ns(e){return""===e||void 0===e}function nd(e){let t=(0,e5.c)(e),n=o.useRef(""),r=o.useRef(0),l=o.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=o.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,l,i]}function nf(e,t,n){var r,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=e,o=Math.max(i,0),r.map((e,t)=>r[(o+t)%r.length]));1===l.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return u!==n?u:void 0}nc.displayName="SelectBubbleInput";var np=tj,nh=tD,nm=tF,nv=tH,ny=tW,ng=tB,nw=t$,nx=t1,nb=t5,nk=t3,nE=t9,nA=nt,nC=nr,nS=nl,nM=na},8972:(e,t,n)=>{n.d(t,{A:()=>i,q:()=>l});var r=n(9585),o=n(9605);function l(e,t){let n=r.createContext(t),l=e=>{let{children:t,...l}=e,i=r.useMemo(()=>l,Object.values(l));return(0,o.jsx)(n.Provider,{value:i,children:t})};return l.displayName=e+"Provider",[l,function(o){let l=r.useContext(n);if(l)return l;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],l=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return l.scopeName=e,[function(t,l){let i=r.createContext(l),a=n.length;n=[...n,l];let u=t=>{let{scope:n,children:l,...u}=t,c=n?.[e]?.[a]||i,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:l})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||i,c=r.useContext(u);if(c)return c;if(void 0!==l)return l;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(l,...t)]}},9581:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},9714:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9722:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},9728:(e,t,n)=>{n.d(t,{i:()=>a});var r,o=n(9585),l=n(6921),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||l.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[l,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),l=o.useRef(n),a=o.useRef(t);return i(()=>{a.current=t},[t]),o.useEffect(()=>{l.current!==n&&(a.current?.(n),l.current=n)},[n,l]),[n,r,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[c,e,a,u])]}Symbol("RADIX:SYNC_STATE")},9798:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},9960:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(6501).A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])}}]);