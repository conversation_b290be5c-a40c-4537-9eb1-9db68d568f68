exports.id=2487,exports.ids=[2487],exports.modules={5316:(e,t,s)=>{Promise.resolve().then(s.bind(s,1507)),Promise.resolve().then(s.bind(s,27430)),Promise.resolve().then(s.bind(s,36709)),Promise.resolve().then(s.bind(s,36536)),Promise.resolve().then(s.bind(s,77038))},21764:(e,t,s)=>{"use strict";s.d(t,{Ee:()=>a,IM:()=>c,Yq:()=>l,cn:()=>i,lW:()=>u,r6:()=>d,vv:()=>n});var r=s(95534),o=s(77175);function i(...e){return(0,o.QP)((0,r.$)(e))}function n(e){return null==e||isNaN(e)?"₹0":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}function a(e){return null==e||isNaN(e)?"0%":`${e>0?"+":""}${e.toFixed(1)}%`}function l(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric"}).format(t)}function d(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}function c(e,t){return e?e.charAt(0).toUpperCase()+(t?t.charAt(0).toUpperCase():""):""}async function u(e){if(navigator.clipboard)await navigator.clipboard.writeText(e);else{let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select(),document.execCommand("copy"),document.body.removeChild(t)}}},22727:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\components\\\\auth\\\\KYCGuard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\components\\auth\\KYCGuard.tsx","default")},27430:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(40969);s(73356);var o=s(92462),i=s(61631);function n({children:e}){return(0,o.jL)(),(0,o.GV)(i.Kc),(0,o.GV)(i.H$),(0,r.jsx)(r.Fragment,{children:e})}},34259:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,89896,23)),Promise.resolve().then(s.t.bind(s,65958,23)),Promise.resolve().then(s.t.bind(s,5958,23)),Promise.resolve().then(s.t.bind(s,45545,23)),Promise.resolve().then(s.t.bind(s,55021,23)),Promise.resolve().then(s.t.bind(s,85621,23)),Promise.resolve().then(s.t.bind(s,16605,23)),Promise.resolve().then(s.t.bind(s,72007,23))},36204:(e,t,s)=>{"use strict";s.d(t,{SettingsProvider:()=>o});var r=s(60605);let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\contexts\\SettingsContext.tsx","SettingsProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\contexts\\SettingsContext.tsx","useSettings"),(0,r.registerClientReference)(function(){throw Error("Attempted to call useSettingCheck() from the server but useSettingCheck is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\contexts\\SettingsContext.tsx","useSettingCheck"),(0,r.registerClientReference)(function(){throw Error("Attempted to call useFeatureFlag() from the server but useFeatureFlag is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\contexts\\SettingsContext.tsx","useFeatureFlag"),(0,r.registerClientReference)(function(){throw Error("Attempted to call useUserFlowSettings() from the server but useUserFlowSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\contexts\\SettingsContext.tsx","useUserFlowSettings")},36536:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(40969);s(73356);var o=s(29079),i=s(92462);function n({children:e}){return(0,r.jsx)(o.Kq,{store:i.M_,children:e})}},36709:(e,t,s)=>{"use strict";s.d(t,{default:()=>C});var r=s(40969),o=s(73356),i=s(12011),n=s(92462),a=s(61631),l=s(77038),d=s(66949),c=s(46411),u=s(42386),h=s(63980),m=s(15423),f=s(2408),p=s(1110),g=s(49610),x=s(58573),y=s(16716),b=s(91447),v=s(23031);function C({children:e}){let t=(0,i.useRouter)();(0,i.usePathname)();let s=(0,n.GV)(a.xu);(0,n.GV)(a.Kc);let{getNextStepForUser:C,kycRequired:k}=(0,l.YK)(),[w,j]=(0,o.useState)(!1);if(w&&s){let e=(()=>{switch(s?.kycStatus||"not_started"){case"not_started":return{icon:(0,r.jsx)(h.A,{className:"h-6 w-6 text-yellow-600"}),title:"KYC Verification Required",description:"Complete your identity verification to access all features and start investing.",color:"border-yellow-200 bg-yellow-50",progress:0};case"pending":return{icon:(0,r.jsx)(m.A,{className:"h-6 w-6 text-sky-600"}),title:"KYC Under Review",description:"Your documents are being reviewed. This typically takes 1-2 business days.",color:"border-sky-200 bg-sky-50",progress:75};case"rejected":return{icon:(0,r.jsx)(f.A,{className:"h-6 w-6 text-red-600"}),title:"KYC Verification Failed",description:"Your KYC verification was rejected. Please review and resubmit your documents.",color:"border-red-200 bg-red-50",progress:50};case"approved":return{icon:(0,r.jsx)(p.A,{className:"h-6 w-6 text-sky-600"}),title:"KYC Verified",description:"Your identity has been successfully verified. You can access all features.",color:"border-sky-200 bg-sky-50",progress:100};default:return{icon:(0,r.jsx)(h.A,{className:"h-6 w-6 text-black"}),title:"KYC Status Unknown",description:"Please contact support for assistance.",color:"border-gray-200 bg-white",progress:0}}})();return(0,r.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center p-6",children:(0,r.jsxs)("div",{className:"max-w-2xl w-full space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto w-16 h-16 bg-sky-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(h.A,{className:"h-8 w-8 text-sky-600"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-black mb-2",children:"Identity Verification Required"}),(0,r.jsx)("p",{className:"text-gray-600",children:"To ensure security and compliance, please complete your KYC verification to access your investment dashboard."})]}),(0,r.jsxs)(d.Zp,{className:`border-2 ${e.color}`,children:[(0,r.jsx)(d.aR,{children:(0,r.jsxs)(d.ZB,{className:"flex items-center space-x-3",children:[e.icon,(0,r.jsx)("span",{children:e.title})]})}),(0,r.jsxs)(d.Wu,{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-700",children:e.description}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Verification Progress"}),(0,r.jsxs)("span",{className:"font-medium",children:[e.progress,"%"]})]}),(0,r.jsx)(u.k,{value:e.progress,className:"h-2"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mt-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-sky-100 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,r.jsx)(g.A,{className:"h-5 w-5 text-sky-600"})}),(0,r.jsx)("p",{className:"text-xs font-medium text-gray-700",children:"Personal Info"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-sky-100 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,r.jsx)(x.A,{className:"h-5 w-5 text-sky-600"})}),(0,r.jsx)("p",{className:"text-xs font-medium text-gray-700",children:"Address"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-sky-100 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,r.jsx)(y.A,{className:"h-5 w-5 text-sky-600"})}),(0,r.jsx)("p",{className:"text-xs font-medium text-gray-700",children:"Documents"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-sky-100 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,r.jsx)(b.A,{className:"h-5 w-5 text-sky-600"})}),(0,r.jsx)("p",{className:"text-xs font-medium text-gray-700",children:"Review"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 mt-6",children:[(0,r.jsxs)(c.$,{onClick:()=>{t.push("/kyc")},className:"flex-1 bg-sky-500 hover:bg-sky-600 text-white",children:["not_started"===s.kycStatus?"Start KYC Verification":"Continue KYC Process",(0,r.jsx)(v.A,{className:"ml-2 h-4 w-4"})]}),(0,r.jsx)(c.$,{variant:"outline",onClick:()=>{j(!1)},className:"flex-1 border-gray-300 text-gray-700 hover:bg-gray-50",children:"Continue Later"})]}),(0,r.jsx)("div",{className:"bg-sky-50 border border-sky-200 rounded-lg p-4 mt-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 text-sky-600 mt-0.5"}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("p",{className:"font-medium text-sky-900 mb-1",children:"Why is KYC required?"}),(0,r.jsx)("p",{className:"text-sky-700",children:"KYC (Know Your Customer) verification helps us comply with financial regulations, prevent fraud, and ensure the security of your investments."})]})]})})]})]})]})})}return(0,r.jsx)(r.Fragment,{children:e})}},40228:(e,t,s)=>{Promise.resolve().then(s.bind(s,1409)),Promise.resolve().then(s.bind(s,58672)),Promise.resolve().then(s.bind(s,22727)),Promise.resolve().then(s.bind(s,58322)),Promise.resolve().then(s.bind(s,36204))},41669:()=>{},42386:(e,t,s)=>{"use strict";s.d(t,{k:()=>a});var r=s(40969),o=s(73356),i=s(14569),n=s(21764);let a=o.forwardRef(({className:e,value:t,...s},o)=>(0,r.jsx)(i.bL,{ref:o,className:(0,n.cn)("relative h-2 w-full overflow-hidden rounded-full bg-gray-200",e),...s,children:(0,r.jsx)(i.C1,{className:"h-full w-full flex-1 bg-blue-600 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));a.displayName=i.bL.displayName},46411:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var r=s(40969),o=s(73356),i=s(19334),n=s(52774),a=s(21764);let l=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef(({className:e,variant:t,size:s,asChild:o=!1,...n},d)=>{let c=o?i.DX:"button";return(0,r.jsx)(c,{className:(0,a.cn)(l({variant:t,size:s,className:e})),ref:d,...n})});d.displayName="Button"},58322:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\components\\\\providers\\\\ReduxProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\components\\providers\\ReduxProvider.tsx","default")},58672:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\user\\\\src\\\\components\\\\auth\\\\AuthProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\user\\src\\components\\auth\\AuthProvider.tsx","default")},61631:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>v,H$:()=>b,Kc:()=>y,LA:()=>l,ri:()=>f,xu:()=>x});var r=s(98571);let o=(0,r.zD)("auth/login",async(e,{rejectWithValue:t})=>{try{console.log("\uD83D\uDD10 User backend login attempt:",e.email);let t=await fetch("http://localhost:5000/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)}),s=await t.json();if(!t.ok)throw Error(s.message||"Login failed");console.log("✅ User backend login successful:",s);let{user:r,accessToken:o,refreshToken:i}=s.data||s;return{user:r,accessToken:o,refreshToken:i}}catch(e){return console.error("❌ User backend login failed:",e.message),t(e.message||"Login failed - please check your credentials")}}),i=(0,r.zD)("auth/checkAuth",async(e,{rejectWithValue:t})=>{try{return console.log("\uD83D\uDD0D User: Checking authentication status..."),console.log("✅ User: Authentication cookies found, user data will be fetched by RTK Query"),{authenticated:!0}}catch(e){return console.error("❌ User: Authentication check failed:",e.message),t(e.message||"Authentication check failed")}}),n=(0,r.zD)("auth/register",async(e,{rejectWithValue:t})=>{try{console.log("\uD83D\uDCDD User backend registration attempt:",e.email);let t=await fetch("http://localhost:5000/api",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)}),s=await t.json();if(!t.ok)throw Error(s.message||"Registration failed");console.log("✅ User backend registration successful:",s);let{user:r,accessToken:o,refreshToken:i}=s.data||s;return{user:r,accessToken:o,refreshToken:i}}catch(e){return console.error("❌ User backend registration failed:",e.message),t(e.message||"Registration failed")}}),a=(0,r.Z0)({name:"auth",initialState:{user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null},reducers:{setCredentials:(e,t)=>{let{user:s,token:r,refreshToken:o}=t.payload;e.user=s,e.token=r,e.refreshToken=o||null,e.isAuthenticated=!0,e.error=null},setUser:(e,t)=>{e.user=t.payload},clearUser:e=>{e.user=null},setToken:(e,t)=>{e.token=t.payload},setLoading:(e,t)=>{e.isLoading=t.payload},setError:(e,t)=>{e.error=t.payload},logout:e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.error=null},clearError:e=>{e.error=null},updateUserProfile:(e,t)=>{e.user&&(e.user={...e.user,...t.payload})}},extraReducers:e=>{e.addCase(i.pending,e=>{e.isLoading=!0,e.error=null}).addCase(i.fulfilled,(e,t)=>{e.isLoading=!1,e.isAuthenticated=t.payload.authenticated,e.error=null}).addCase(i.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null}).addCase(o.pending,e=>{e.isLoading=!0,e.error=null}).addCase(o.fulfilled,(e,t)=>{e.isLoading=!1,e.user=t.payload.user,e.token=t.payload.accessToken,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null}).addCase(o.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null}).addCase(n.pending,e=>{e.isLoading=!0,e.error=null}).addCase(n.fulfilled,(e,t)=>{e.isLoading=!1,e.user=t.payload.user,e.token=t.payload.accessToken,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null}).addCase(n.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null})}}),{setCredentials:l,setUser:d,clearUser:c,setToken:u,setLoading:h,setError:m,logout:f,clearError:p,updateUserProfile:g}=a.actions,x=e=>e.auth.user,y=e=>e.auth.isAuthenticated,b=e=>e.auth.isLoading,v=a.reducer},66949:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>a});var r=s(40969),o=s(73356),i=s(21764);let n=o.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let a=o.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));a.displayName="CardHeader";let l=o.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=o.forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=o.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",o.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},67556:(e,t,s)=>{"use strict";s.d(t,{s:()=>r});class r{static getCookie(e){if("undefined"==typeof document)return null;let t=`; ${document.cookie}`.split(`; ${e}=`);if(2===t.length){let e=t.pop()?.split(";").shift();return e?decodeURIComponent(e):null}return null}static setCookie(e,t,s={}){if("undefined"==typeof document)return;let r=`${e}=${encodeURIComponent(t)}`;s.expires&&("string"==typeof s.expires?r+=`; expires=${s.expires}`:r+=`; expires=${s.expires.toUTCString()}`),s.maxAge&&(r+=`; max-age=${s.maxAge}`),s.path?r+=`; path=${s.path}`:r+="; path=/",s.domain&&(r+=`; domain=${s.domain}`),s.secure&&(r+="; secure"),s.sameSite&&(r+=`; samesite=${s.sameSite}`),document.cookie=r}static deleteCookie(e,t={}){this.setCookie(e,"",{...t,expires:new Date(0)})}static hasCookie(e){return null!==this.getCookie(e)}static getAllCookies(){if("undefined"==typeof document)return{};let e={};return document.cookie.split(";").forEach(t=>{let[s,r]=t.trim().split("=");s&&r&&(e[s]=decodeURIComponent(r))}),e}static getAuthToken(){return this.getCookie("accessToken")||this.getCookie("token")}static getRefreshToken(){return this.getCookie("refreshToken")}static isAuthenticated(){return this.hasCookie("accessToken")||this.hasCookie("token")}static clearAuthCookies(){this.deleteCookie("accessToken"),this.deleteCookie("refreshToken"),this.deleteCookie("token")}static getRedirectUrl(){return this.getCookie("redirectTo")}static setRedirectUrl(e){this.setCookie("redirectTo",e,{maxAge:300,sameSite:"lax"})}static clearRedirectUrl(){this.deleteCookie("redirectTo")}static parseCookieString(e){let t={};return e.split(";").forEach(e=>{let[s,r]=e.trim().split("=");s&&r&&(t[s]=decodeURIComponent(r))}),t}static setSessionCookie(e,t,s={}){this.setCookie(e,t,{...s,expires:void 0,maxAge:void 0})}static setPersistentCookie(e,t,s=7,r={}){let o=new Date;o.setDate(o.getDate()+s),this.setCookie(e,t,{...r,expires:o})}}},77038:(e,t,s)=>{"use strict";s.d(t,{SettingsProvider:()=>a,YK:()=>d});var r=s(40969),o=s(73356);let{useGetPublicSettingsQuery:i}=s(88559).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getPublicSettings:e.query({query:()=>"/settings/public",providesTags:["Settings"]})})}),n=(0,o.createContext)(void 0),a=({children:e})=>{let[t,s]=(0,o.useState)({}),{data:a,isLoading:l,error:d,refetch:c}=i(void 0,{skip:!1,refetchOnMountOrArgChange:!0,refetchOnFocus:!1,refetchOnReconnect:!0});return(0,o.useEffect)(()=>{a?.success&&a.data?.settings&&s(e=>({...e,...a.data?.settings}))},[a]),(0,o.useEffect)(()=>{d&&console.warn("Failed to load settings from server, using defaults:",d)},[d]),(0,o.useEffect)(()=>{let e=setInterval(()=>{c()},3e5);return()=>clearInterval(e)},[c]),(0,r.jsx)(n.Provider,{value:{settings:t,isLoading:l,error:d,isEmailVerificationEnabled:()=>t.EMAIL_VERIFICATION_ENABLED??!0,isPhoneVerificationEnabled:()=>t.PHONE_VERIFICATION_ENABLED??!1,isKYCRequired:()=>t.KYC_REQUIRED_FOR_ACTIVATION??!0,isKYCPriorityOverEmail:()=>t.KYC_PRIORITY_OVER_EMAIL??!0,allowKYCForInactiveUsers:()=>t.ALLOW_KYC_FOR_INACTIVE_USERS??!0,getMinInvestmentAmount:()=>t.MIN_INVESTMENT_AMOUNT??1e3,getMaxInvestmentAmount:()=>t.MAX_INVESTMENT_AMOUNT??1e6,isFeatureEnabled:e=>t[`FEATURE_${e.toUpperCase()}_ENABLED`]??!0},children:e})},l=()=>{let e=(0,o.useContext)(n);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e},d=()=>{let{isEmailVerificationEnabled:e,isKYCRequired:t,isKYCPriorityOverEmail:s,allowKYCForInactiveUsers:r}=l();return{emailVerificationEnabled:e(),kycRequired:t(),kycPriorityOverEmail:s(),allowKYCForInactive:r(),shouldShowEmailFirst:()=>e()&&!s(),shouldShowKYCFirst:()=>t()&&s(),getNextStepForUser:r=>{if(!r)return null;let o=t()&&["not_started","not_submitted","incomplete","rejected"].includes(r.kycStatus||"not_started"),i=e()&&!r.emailVerified;return"pending"===r.kycStatus||"under_review"===r.kycStatus?i?"email_verification":null:o&&s()?"kyc":i&&!s()?"email_verification":o?"kyc":i?"email_verification":null}}}},81115:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,26390,23)),Promise.resolve().then(s.t.bind(s,23904,23)),Promise.resolve().then(s.t.bind(s,13172,23)),Promise.resolve().then(s.t.bind(s,81351,23)),Promise.resolve().then(s.t.bind(s,62735,23)),Promise.resolve().then(s.t.bind(s,82471,23)),Promise.resolve().then(s.t.bind(s,51287,23)),Promise.resolve().then(s.t.bind(s,20737,23))},88559:(e,t,s)=>{"use strict";s.d(t,{q:()=>d});var r=s(99553),o=s(82845),i=s(61631),n=s(67556);let a=(0,r.cw)({baseUrl:"http://localhost:5000/api",credentials:"include",prepareHeaders:(e,{getState:t})=>{let s=t().auth.token,r=n.s.getAuthToken(),o=s||r;return o&&e.set("authorization",`Bearer ${o}`),e.set("Content-Type","application/json"),e}}),l=async(e,t,s)=>{let r=await a(e,t,s);if(r.error&&401===r.error.status){let o=await a({url:"/auth/refresh",method:"POST"},t,s);if(o.data){let n=o.data;n.success&&n.data?.user?(t.dispatch((0,i.LA)({user:n.data.user,token:"cookie-based",refreshToken:"cookie-based"})),r=await a(e,t,s)):t.dispatch((0,i.ri)())}else t.dispatch((0,i.ri)())}return r},d=(0,o.xP)({reducerPath:"baseApi",baseQuery:l,tagTypes:["User","Property","Investment","Transaction","Wallet","Referral","SupportTicket","Notification","Dashboard","Activity","Wishlist","Featured","KYC","Lead","PropertyOwner","Auth","Settings","FAQ","FAQCategories","StockHoldings","StockCertificate","StockTransaction"],endpoints:()=>({})})},92462:(e,t,s)=>{"use strict";s.d(t,{M_:()=>O,jL:()=>$,GV:()=>V});var r=s(98571),o=s(99553),i=s(29079),n=s(88559),a=s(61631);let l={sidebarCollapsed:!1,sidebarMobileOpen:!1,theme:"light",loading:{global:!1,page:!1,component:{}},modals:{investmentModal:!1,withdrawalModal:!1,addMoneyModal:!1,profileModal:!1,supportModal:!1},notifications:{show:!1,unreadCount:0},searchQuery:"",filters:{}},d=(0,r.Z0)({name:"ui",initialState:l,reducers:{toggleSidebar:e=>{e.sidebarCollapsed=!e.sidebarCollapsed},setSidebarCollapsed:(e,t)=>{e.sidebarCollapsed=t.payload},toggleMobileSidebar:e=>{e.sidebarMobileOpen=!e.sidebarMobileOpen},setMobileSidebarOpen:(e,t)=>{e.sidebarMobileOpen=t.payload},setTheme:(e,t)=>{e.theme=t.payload},toggleTheme:e=>{e.theme="light"===e.theme?"dark":"light"},setGlobalLoading:(e,t)=>{e.loading.global=t.payload},setPageLoading:(e,t)=>{e.loading.page=t.payload},setComponentLoading:(e,t)=>{let{component:s,loading:r}=t.payload;e.loading.component[s]=r},openModal:(e,t)=>{e.modals[t.payload]=!0},closeModal:(e,t)=>{e.modals[t.payload]=!1},closeAllModals:e=>{Object.keys(e.modals).forEach(t=>{e.modals[t]=!1})},toggleNotifications:e=>{e.notifications.show=!e.notifications.show},setNotificationsOpen:(e,t)=>{e.notifications.show=t.payload},setUnreadCount:(e,t)=>{e.notifications.unreadCount=t.payload},setSearchQuery:(e,t)=>{e.searchQuery=t.payload},setFilter:(e,t)=>{let{key:s,value:r}=t.payload;e.filters[s]=r},clearFilters:e=>{e.filters={}},resetUI:e=>({...l,theme:e.theme})}}),{toggleSidebar:c,setSidebarCollapsed:u,toggleMobileSidebar:h,setMobileSidebarOpen:m,setTheme:f,toggleTheme:p,setGlobalLoading:g,setPageLoading:x,setComponentLoading:y,openModal:b,closeModal:v,closeAllModals:C,toggleNotifications:k,setNotificationsOpen:w,setUnreadCount:j,setSearchQuery:N,setFilter:A,clearFilters:T,resetUI:R}=d.actions,S=d.reducer,P=(0,r.Z0)({name:"notifications",initialState:{notifications:[],unreadCount:0,loading:!1,error:null},reducers:{setNotifications:(e,t)=>{e.notifications=t.payload,e.unreadCount=t.payload.filter(e=>!e.read).length},addNotification:(e,t)=>{e.notifications.unshift(t.payload),t.payload.read||(e.unreadCount+=1)},markAsRead:(e,t)=>{let s=e.notifications.find(e=>e.id===t.payload);s&&!s.read&&(s.read=!0,e.unreadCount=Math.max(0,e.unreadCount-1))},markAllAsRead:e=>{e.notifications.forEach(e=>{e.read=!0}),e.unreadCount=0},removeNotification:(e,t)=>{let s=e.notifications.findIndex(e=>e.id===t.payload);-1!==s&&(e.notifications[s].read||(e.unreadCount=Math.max(0,e.unreadCount-1)),e.notifications.splice(s,1))},clearAllNotifications:e=>{e.notifications=[],e.unreadCount=0},setLoading:(e,t)=>{e.loading=t.payload},setError:(e,t)=>{e.error=t.payload},receiveNotification:(e,t)=>{if(e.notifications.unshift(t.payload),t.payload.read||(e.unreadCount+=1),e.notifications.length>50){let t=e.notifications.splice(50).filter(e=>!e.read).length;e.unreadCount=Math.max(0,e.unreadCount-t)}}}}),{setNotifications:E,addNotification:U,markAsRead:I,markAllAsRead:F,removeNotification:M,clearAllNotifications:L,setLoading:D,setError:Y,receiveNotification:_}=P.actions,K=P.reducer,O=(0,r.U1)({reducer:{[n.q.reducerPath]:n.q.reducer,auth:a.Ay,ui:S,notifications:K},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE","persist/PAUSE","persist/PURGE","persist/REGISTER"]}}).concat(n.q.middleware),devTools:!1});(0,o.$k)(O.dispatch);let $=()=>(0,i.wA)(),V=i.d4},92603:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h,metadata:()=>u});var r=s(59355),o=s(33799),i=s.n(o);s(41669);var n=s(58322),a=s(58672),l=s(22727),d=s(36204),c=s(1409);let u={title:"SGM  - User Panel",description:"Invest in real estate stocks and build your portfolio with SGM ",keywords:"real estate, investment, stocks, property, portfolio",authors:[{name:"SGM "}],viewport:"width=device-width, initial-scale=1"};function h({children:e}){return(0,r.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,r.jsx)("body",{className:`${i().className} antialiased`,children:(0,r.jsxs)(n.default,{children:[(0,r.jsx)(a.default,{children:(0,r.jsx)(d.SettingsProvider,{children:(0,r.jsx)(l.default,{children:e})})}),(0,r.jsx)(c.Toaster,{position:"top-right",richColors:!0,closeButton:!0,duration:4e3})]})})})}}};