(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[558],{2345:()=>{},3815:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>b,H$:()=>m,Kc:()=>f,PK:()=>n,ZB:()=>k,_v:()=>i,mB:()=>y});var s=a(7895);let o={setCurrentUser:e=>{localStorage.setItem("salesUser",JSON.stringify(e))},clearCurrentUser:()=>{localStorage.removeItem("salesUser")}},i=(0,s.zD)("auth/login",async(e,t)=>{let{rejectWithValue:a}=t;try{console.log("\uD83D\uDD10 Sales backend login attempt:",e.email);let t=await fetch("".concat("http://localhost:5000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Client-Type":"sales-dashboard","X-Client-Version":"1.0.0"},credentials:"include",body:JSON.stringify(e)}),s=await t.json();if(!t.ok)throw Error(s.message||"Login failed");console.log("✅ Sales: Backend login response:",s);let{user:i,accessToken:r,refreshToken:l}=s.data||s;if(!i||!r)return a("Invalid response format");try{return o.setCurrentUser(i),console.log("✅ Sales login successful, backend set HttpOnly cookies:",{user:i.email,role:i.role,note:"Tokens stored in HttpOnly cookies (not accessible from JS)"}),{user:i,accessToken:r,refreshToken:l}}catch(e){return console.error("Sales user storage failed:",e),a("Failed to store user data")}}catch(e){return console.error("Sales login error:",e),a(e.message||"Login failed")}}),r=(0,s.zD)("auth/logout",async()=>{try{await fetch("".concat("http://localhost:5000/api","/auth/logout"),{method:"POST",headers:{"Content-Type":"application/json","X-Client-Type":"sales-dashboard"},credentials:"include"})}catch(e){console.error("Sales logout API error:",e)}finally{o.clearCurrentUser()}}),l=(0,s.zD)("auth/refreshUser",async(e,t)=>{let{rejectWithValue:a}=t;try{let e=await fetch("".concat("http://localhost:5000/api","/auth/profile"),{method:"GET",headers:{"Content-Type":"application/json","X-Client-Type":"sales-dashboard"},credentials:"include"}),t=await e.json();if(!e.ok)throw Error(t.message||"Failed to get profile");let a=t.data||t;return o.setCurrentUser(a),a}catch(e){return a(e.message||"Failed to refresh user")}}),n=(0,s.zD)("auth/checkAuth",async(e,t)=>{let{rejectWithValue:a}=t;try{console.log("\uD83D\uDD0D Sales: Checking auth with backend (HttpOnly cookies)...");let e=await fetch("".concat("http://localhost:5000/api","/auth/check"),{method:"GET",headers:{"Content-Type":"application/json","X-Client-Type":"sales-dashboard"},credentials:"include"}),t=await e.json();if(console.log("✅ Sales: Backend auth response:",t),e.ok&&t.success&&t.data)return o.setCurrentUser(t.data),t.data;return console.log("❌ Sales: Backend auth failed:",t.message),o.clearCurrentUser(),a(t.message||"Authentication failed")}catch(e){return console.error("❌ Sales: Auth check error:",e.message),o.clearCurrentUser(),a(e.message||"Authentication check failed")}}),c=(0,s.Z0)({name:"auth",initialState:{user:null,isAuthenticated:!1,loading:!1,error:null,sessionExpiry:null},reducers:{clearError:e=>{e.error=null},setUser:(e,t)=>{e.user=t.payload,e.isAuthenticated=!0,o.setCurrentUser(t.payload)},clearAuth:e=>{e.user=null,e.isAuthenticated=!1,e.error=null,e.sessionExpiry=null,o.clearCurrentUser()},setSessionExpiry:(e,t)=>{e.sessionExpiry=t.payload},updateUserProfile:(e,t)=>{e.user&&(e.user={...e.user,...t.payload},o.setCurrentUser(e.user))}},extraReducers:e=>{e.addCase(i.pending,e=>{e.loading=!0,e.error=null}).addCase(i.fulfilled,(e,t)=>{e.loading=!1,e.user=t.payload.user||t.payload,e.isAuthenticated=!0,e.error=null}).addCase(i.rejected,(e,t)=>{e.loading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null}),e.addCase(r.pending,e=>{e.loading=!0}).addCase(r.fulfilled,e=>{e.loading=!1,e.user=null,e.isAuthenticated=!1,e.error=null,e.sessionExpiry=null}).addCase(r.rejected,e=>{e.loading=!1,e.user=null,e.isAuthenticated=!1,e.error=null,e.sessionExpiry=null}),e.addCase(l.pending,e=>{e.loading=!0}).addCase(l.fulfilled,(e,t)=>{e.loading=!1,e.user=t.payload.user||t.payload,e.error=null}).addCase(l.rejected,(e,t)=>{e.loading=!1,e.error=t.payload}),e.addCase(n.pending,e=>{e.loading=!0}).addCase(n.fulfilled,(e,t)=>{e.loading=!1,e.user=t.payload.user||t.payload,e.isAuthenticated=!0,e.error=null}).addCase(n.rejected,(e,t)=>{e.loading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null})}}),{clearError:d,setUser:u,clearAuth:h,setSessionExpiry:p,updateUserProfile:g}=c.actions,y=e=>e.auth.user,f=e=>e.auth.isAuthenticated,m=e=>e.auth.loading,k=e=>e.auth.error,b=c.reducer},4965:(e,t,a)=>{"use strict";a.d(t,{G:()=>i,j:()=>o});var s=a(9559);let o=()=>(0,s.wA)(),i=s.d4},6701:(e,t,a)=>{"use strict";a.d(t,{q:()=>n});var s=a(6597),o=a(2713);class i{static getCookie(e){if("undefined"==typeof document)return null;let t="; ".concat(document.cookie).split("; ".concat(e,"="));if(2===t.length){var a;let e=null==(a=t.pop())?void 0:a.split(";").shift();return e?decodeURIComponent(e):null}return null}static setCookie(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("undefined"==typeof document)return;let s="".concat(e,"=").concat(encodeURIComponent(t));a.expires&&("string"==typeof a.expires?s+="; expires=".concat(a.expires):s+="; expires=".concat(a.expires.toUTCString())),a.maxAge&&(s+="; max-age=".concat(a.maxAge)),a.path?s+="; path=".concat(a.path):s+="; path=/",a.domain&&(s+="; domain=".concat(a.domain)),a.secure&&(s+="; secure"),a.sameSite&&(s+="; samesite=".concat(a.sameSite)),document.cookie=s}static deleteCookie(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.setCookie(e,"",{...t,expires:new Date(0)})}static hasCookie(e){return null!==this.getCookie(e)}static getAllCookies(){if("undefined"==typeof document)return{};let e={};return document.cookie.split(";").forEach(t=>{let[a,s]=t.trim().split("=");a&&s&&(e[a]=decodeURIComponent(s))}),e}static getAuthToken(){return this.getCookie("accessToken")||this.getCookie("token")}static getRefreshToken(){return this.getCookie("refreshToken")}static isAuthenticated(){return this.hasCookie("accessToken")||this.hasCookie("token")}static clearAuthCookies(){this.deleteCookie("accessToken"),this.deleteCookie("refreshToken"),this.deleteCookie("token")}static getRedirectUrl(){return this.getCookie("redirectTo")}static setRedirectUrl(e){this.setCookie("redirectTo",e,{maxAge:300,sameSite:"lax"})}static clearRedirectUrl(){this.deleteCookie("redirectTo")}static parseCookieString(e){let t={};return e.split(";").forEach(e=>{let[a,s]=e.trim().split("=");a&&s&&(t[a]=decodeURIComponent(s))}),t}static setSessionCookie(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.setCookie(e,t,{...a,expires:void 0,maxAge:void 0})}static setPersistentCookie(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:7,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=new Date;o.setDate(o.getDate()+a),this.setCookie(e,t,{...s,expires:o})}}let r=(0,s.cw)({baseUrl:"http://localhost:5000/api",credentials:"include",prepareHeaders:(e,t)=>{let{getState:a}=t,s=null;if(!(s=i.getAuthToken()||localStorage.getItem("accessToken"))){var o,r;s=null==(r=a())||null==(o=r.auth)?void 0:o.token}return s&&e.set("authorization","Bearer ".concat(s)),e.set("content-type","application/json"),e.set("accept","application/json"),e.set("x-client-type","sales-dashboard"),e.set("x-client-version","1.0.0"),e}}),l=async(e,t,a)=>{let s=await r(e,t,a);if(s.error&&401===s.error.status){console.log("Access token expired, attempting refresh...");let o=i.getRefreshToken();if(o){let l=await r({url:"/auth/refresh",method:"POST",body:{refreshToken:o}},t,a);if(l.data){let{accessToken:o}=l.data.data;localStorage.setItem("accessToken",o),s=await r(e,t,a)}else i.clearAuthCookies(),localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/login"}else i.clearAuthCookies(),localStorage.removeItem("accessToken"),window.location.href="/login"}return s},n=(0,o.xP)({reducerPath:"api",baseQuery:l,tagTypes:["User","Lead","Customer","Property","Commission","Report","Team","Task","Calendar","Notification","Dashboard"],endpoints:()=>({})})},7555:(e,t,a)=>{"use strict";a.d(t,{$U:()=>d,Ay:()=>B,Oo:()=>l,qf:()=>r});var s=a(7895);let o={modals:[],toasts:[],loading:{global:!1,components:{}},tables:{},search:{query:"",filters:[],suggestions:[],recentSearches:[],isSearching:!1},sidebar:{isOpen:!0,activeSection:null},breadcrumbs:[],pageTitle:"",pageSubtitle:void 0,quickFilters:{},bulkActions:{selectedItems:[],availableActions:[]},preferences:{density:"comfortable",animations:!0,soundEffects:!1,autoSave:!0,confirmActions:!0}},i=(0,s.Z0)({name:"ui",initialState:o,reducers:{openModal:(e,t)=>{let a=e.modals.find(e=>e.id===t.payload.id);a?(a.isOpen=!0,a.data=t.payload.data):e.modals.push({...t.payload,isOpen:!0})},closeModal:(e,t)=>{let a=e.modals.find(e=>e.id===t.payload);a&&(a.isOpen=!1)},closeAllModals:e=>{e.modals.forEach(e=>{e.isOpen=!1})},updateModalData:(e,t)=>{let a=e.modals.find(e=>e.id===t.payload.id);a&&(a.data=t.payload.data)},addToast:(e,t)=>{let a=Date.now().toString();e.toasts.push({id:a,...t.payload})},removeToast:(e,t)=>{e.toasts=e.toasts.filter(e=>e.id!==t.payload)},clearToasts:e=>{e.toasts=[]},setGlobalLoading:(e,t)=>{e.loading.global=t.payload},setComponentLoading:(e,t)=>{e.loading.components[t.payload.component]=t.payload.loading},setTableState:(e,t)=>{let{tableId:a,state:s}=t.payload;e.tables[a]={...e.tables[a],...s}},selectTableRows:(e,t)=>{let{tableId:a,rowIds:s}=t.payload;e.tables[a]||(e.tables[a]={selectedRows:[],filters:[],sort:null,pagination:{page:1,limit:10,total:0},view:"table",columns:[]}),e.tables[a].selectedRows=s},toggleTableRow:(e,t)=>{let{tableId:a,rowId:s}=t.payload;if(!e.tables[a])return;let o=e.tables[a].selectedRows,i=o.indexOf(s);i>-1?o.splice(i,1):o.push(s)},clearTableSelection:(e,t)=>{e.tables[t.payload]&&(e.tables[t.payload].selectedRows=[])},setSearchQuery:(e,t)=>{e.search.query=t.payload},setSearchFilters:(e,t)=>{e.search.filters=t.payload},addSearchFilter:(e,t)=>{let a=e.search.filters.findIndex(e=>e.id===t.payload.id);a>-1?e.search.filters[a]=t.payload:e.search.filters.push(t.payload)},removeSearchFilter:(e,t)=>{e.search.filters=e.search.filters.filter(e=>e.id!==t.payload)},setSearchSuggestions:(e,t)=>{e.search.suggestions=t.payload},addRecentSearch:(e,t)=>{let a=t.payload.trim();a&&!e.search.recentSearches.includes(a)&&(e.search.recentSearches.unshift(a),e.search.recentSearches.length>10&&(e.search.recentSearches=e.search.recentSearches.slice(0,10)))},clearRecentSearches:e=>{e.search.recentSearches=[]},setSearching:(e,t)=>{e.search.isSearching=t.payload},setSidebarOpen:(e,t)=>{e.sidebar.isOpen=t.payload},setActiveSection:(e,t)=>{e.sidebar.activeSection=t.payload},setBreadcrumbs:(e,t)=>{e.breadcrumbs=t.payload},setPageTitle:(e,t)=>{e.pageTitle=t.payload},setPageSubtitle:(e,t)=>{e.pageSubtitle=t.payload},setQuickFilter:(e,t)=>{e.quickFilters[t.payload.key]=t.payload.value},clearQuickFilters:e=>{e.quickFilters={}},setBulkSelectedItems:(e,t)=>{e.bulkActions.selectedItems=t.payload},setBulkAvailableActions:(e,t)=>{e.bulkActions.availableActions=t.payload},clearBulkSelection:e=>{e.bulkActions.selectedItems=[]},setPreferences:(e,t)=>{e.preferences={...e.preferences,...t.payload}},resetUI:e=>({...o,preferences:e.preferences})}}),{openModal:r,closeModal:l,closeAllModals:n,updateModalData:c,addToast:d,removeToast:u,clearToasts:h,setGlobalLoading:p,setComponentLoading:g,setTableState:y,selectTableRows:f,toggleTableRow:m,clearTableSelection:k,setSearchQuery:b,setSearchFilters:v,addSearchFilter:C,removeSearchFilter:S,setSearchSuggestions:w,addRecentSearch:A,clearRecentSearches:x,setSearching:T,setSidebarOpen:U,setActiveSection:R,setBreadcrumbs:j,setPageTitle:I,setPageSubtitle:E,setQuickFilter:P,clearQuickFilters:D,setBulkSelectedItems:q,setBulkAvailableActions:O,clearBulkSelection:N,setPreferences:L,resetUI:F}=i.actions,B=i.reducer},7625:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});var s=a(9605),o=a(9585),i=a(5935),r=a(4965),l=a(3815);function n(e){let{children:t}=e,a=(0,r.j)(),n=(0,r.G)(l.Kc),c=(0,r.G)(l.H$),d="/login"===(0,i.usePathname)();return((0,o.useEffect)(()=>{if(d)return void console.log("\uD83D\uDD0D Sales AuthCheck: Skipping auth check on login page");n||(console.log("\uD83D\uDD0D Sales AuthCheck: Checking authentication status..."),a((0,l.PK)()))},[a,n,d]),c&&!d)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-sky-50 via-white to-sky-100",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-sky-600 mx-auto"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Checking authentication..."})]})}):(0,s.jsx)(s.Fragment,{children:t})}},8817:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,3659,23)),Promise.resolve().then(a.bind(a,8595)),Promise.resolve().then(a.t.bind(a,2345,23)),Promise.resolve().then(a.bind(a,7625)),Promise.resolve().then(a.bind(a,9466))},9466:(e,t,a)=>{"use strict";a.d(t,{default:()=>z});var s=a(9605),o=a(9559),i=a(3281),r=a(7895),l=a(6597),n=a(5027),c=a(2598),d=a(5340),u=a(6701),h=a(3815);let p={filters:{dateRange:{startDate:new Date(Date.now()-2592e6).toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0],preset:"month"}},view:{layout:"grid",widgets:[{id:"stats-overview",type:"stats",position:{x:0,y:0,w:12,h:2},visible:!0},{id:"leads-chart",type:"chart",position:{x:0,y:2,w:6,h:4},visible:!0},{id:"recent-activities",type:"table",position:{x:6,y:2,w:6,h:4},visible:!0},{id:"upcoming-tasks",type:"tasks",position:{x:0,y:6,w:4,h:3},visible:!0},{id:"calendar-events",type:"calendar",position:{x:4,y:6,w:4,h:3},visible:!0},{id:"notifications",type:"notifications",position:{x:8,y:6,w:4,h:3},visible:!0}],refreshInterval:300,autoRefresh:!0},quickActions:{recentLeads:[],recentCustomers:[],recentProperties:[],bookmarkedReports:[],frequentTasks:[]},isLoading:!1,lastUpdated:null,notifications:{show:!0,count:0},sidebarCollapsed:!1,theme:"light"},g=(0,r.Z0)({name:"dashboard",initialState:p,reducers:{setFilters:(e,t)=>{e.filters={...e.filters,...t.payload}},setDateRange:(e,t)=>{e.filters.dateRange=t.payload},updateWidget:(e,t)=>{let{id:a,updates:s}=t.payload,o=e.view.widgets.findIndex(e=>e.id===a);-1!==o&&(e.view.widgets[o]={...e.view.widgets[o],...s})},toggleWidget:(e,t)=>{let a=e.view.widgets.findIndex(e=>e.id===t.payload);-1!==a&&(e.view.widgets[a].visible=!e.view.widgets[a].visible)},reorderWidgets:(e,t)=>{e.view.widgets=t.payload},setLayout:(e,t)=>{e.view.layout=t.payload},setAutoRefresh:(e,t)=>{e.view.autoRefresh=t.payload},setRefreshInterval:(e,t)=>{e.view.refreshInterval=t.payload},addToQuickActions:(e,t)=>{let{type:a,id:s}=t.payload;!e.quickActions[a].includes(s)&&(e.quickActions[a].unshift(s),e.quickActions[a].length>10&&(e.quickActions[a]=e.quickActions[a].slice(0,10)))},removeFromQuickActions:(e,t)=>{let{type:a,id:s}=t.payload;e.quickActions[a]=e.quickActions[a].filter(e=>e!==s)},setLoading:(e,t)=>{e.isLoading=t.payload},setLastUpdated:(e,t)=>{e.lastUpdated=t.payload},setNotificationCount:(e,t)=>{e.notifications.count=t.payload},toggleNotifications:e=>{e.notifications.show=!e.notifications.show},toggleSidebar:e=>{e.sidebarCollapsed=!e.sidebarCollapsed},setSidebarCollapsed:(e,t)=>{e.sidebarCollapsed=t.payload},setTheme:(e,t)=>{e.theme=t.payload},resetDashboard:e=>({...p,theme:e.theme}),saveDashboardLayout:(e,t)=>{e.view=t.payload},loadDashboardLayout:(e,t)=>{e.view=t.payload}}}),{setFilters:y,setDateRange:f,updateWidget:m,toggleWidget:k,reorderWidgets:b,setLayout:v,setAutoRefresh:C,setRefreshInterval:S,addToQuickActions:w,removeFromQuickActions:A,setLoading:x,setLastUpdated:T,setNotificationCount:U,toggleNotifications:R,toggleSidebar:j,setSidebarCollapsed:I,setTheme:E,resetDashboard:P,saveDashboardLayout:D,loadDashboardLayout:q}=g.actions,O=g.reducer;var N=a(7555);let L={key:"sales-root",storage:c.A,whitelist:["auth","dashboard","ui"],blacklist:[u.q.reducerPath]},F=(0,d.HY)({auth:h.Ay,dashboard:O,ui:N.Ay,[u.q.reducerPath]:u.q.reducer}),B=(0,n.rL)(L,F),G=(0,r.U1)({reducer:B,middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE","persist/PAUSE","persist/PURGE","persist/REGISTER"]}}).concat(u.q.middleware),devTools:!1}),H=(0,n.GM)(G);(0,l.$k)(G.dispatch);var Q=a(783);let _=()=>(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(Q.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading Sales Dashboard..."})]})});function z(e){let{children:t}=e;return(0,s.jsx)(o.Kq,{store:G,children:(0,s.jsx)(i.Q,{loading:(0,s.jsx)(_,{}),persistor:H,children:t})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[619,399,713,595,708,390,110,358],()=>t(8817)),_N_E=e.O()}]);