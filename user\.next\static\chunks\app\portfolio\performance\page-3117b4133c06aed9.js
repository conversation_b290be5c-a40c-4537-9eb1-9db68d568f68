(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5200],{2403:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},2790:(e,t,a)=>{"use strict";a.d(t,{E:()=>i});var s=a(9605);a(9585);var r=a(7276),l=a(6994);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{className:(0,l.cn)(n({variant:a}),t),...r})}},3119:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},5457:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},5594:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>o,T0:()=>c,lX:()=>m});var s=a(9605);a(9585);var r=a(2933),l=a(2790),n=a(6994),i=a(9644),d=a(5935);function o(e){let{title:t,description:a,icon:o,badge:c,actions:m,breadcrumbs:x,showBackButton:u=!1,className:h,gradient:p=!1}=e,y=(0,d.useRouter)();return(0,s.jsxs)("div",{className:(0,n.cn)("relative overflow-hidden",p&&"bg-gradient-to-r from-sky-50 via-blue-50 to-indigo-50",!p&&"bg-white",h),children:[p&&(0,s.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,s.jsx)("div",{className:"relative px-4 sm:px-6 lg:px-8 py-6 sm:py-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[x&&x.length>0&&(0,s.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,s.jsx)("ol",{className:"flex items-center space-x-2 text-sm",children:x.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,s.jsx)("span",{className:"mx-2 text-gray-400",children:"/"}),e.href?(0,s.jsx)("button",{onClick:()=>y.push(e.href),className:"text-gray-600 hover:text-sky-600 transition-colors",children:e.label}):(0,s.jsx)("span",{className:"text-gray-900 font-medium",children:e.label})]},t))})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[u&&(0,s.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>y.back(),className:"flex-shrink-0 mt-1",children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}),o&&(0,s.jsx)("div",{className:(0,n.cn)("flex-shrink-0 p-3 rounded-xl",p?"bg-white/80 backdrop-blur-sm shadow-lg":"bg-sky-50","text-sky-600"),children:(0,s.jsx)(o,{className:"h-6 w-6"})}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center flex-wrap gap-3 mb-2",children:[(0,s.jsx)("h1",{className:(0,n.cn)("text-2xl sm:text-3xl font-bold text-gray-900 leading-tight",p&&"bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent"),children:t}),c&&(0,s.jsx)(l.E,{variant:c.variant||"default",className:(0,n.cn)("text-xs font-medium",c.className),children:c.text})]}),a&&(0,s.jsx)("p",{className:"text-gray-600 text-sm sm:text-base max-w-2xl leading-relaxed",children:a})]})]}),m&&(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"flex items-center space-x-3",children:m})})]})]})}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"})]})}function c(e){let{stats:t,className:a}=e;return(0,s.jsx)("div",{className:(0,n.cn)("grid grid-cols-2 sm:grid-cols-4 gap-4 mt-6",a),children:t.map((e,t)=>(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50",children:[(0,s.jsx)("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide",children:e.label}),(0,s.jsx)("p",{className:"text-lg font-bold text-gray-900 mt-1",children:e.value}),e.change&&(0,s.jsx)("p",{className:(0,n.cn)("text-xs font-medium mt-1","up"===e.trend&&"text-green-600","down"===e.trend&&"text-red-600","neutral"===e.trend&&"text-gray-600"),children:e.change})]},t))})}let m={Primary:e=>{let{children:t,...a}=e;return(0,s.jsx)(r.$,{className:"bg-sky-600 hover:bg-sky-700 text-white shadow-lg",...a,children:t})},Secondary:e=>{let{children:t,...a}=e;return(0,s.jsx)(r.$,{variant:"outline",className:"border-sky-200 text-sky-700 hover:bg-sky-50",...a,children:t})},Ghost:e=>{let{children:t,...a}=e;return(0,s.jsx)(r.$,{variant:"ghost",className:"text-gray-600 hover:text-sky-600 hover:bg-sky-50",...a,children:t})}}},8774:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var s=a(9605);a(9585);var r=a(3005),l=a(5594),n=a(8063),i=a(470),d=a(3119),o=a(9508),c=a(8391),m=a(2403),x=a(5457);let u=(0,a(5050).A)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);var h=a(1248),p=a(9849),y=a(6994);function g(){let{data:e,isLoading:t}=(0,p.rx)(),a=(null==e?void 0:e.data)||{},g=(null==a?void 0:a.totalInvested)||0,v=(null==a?void 0:a.totalReturns)||0,f=g>0?v/g*100:0,j=[{title:"Total Returns",value:(0,y.vv)(v),change:f>0?(0,y.Ee)(f):"0%",trend:v>0?"up":v<0?"down":"neutral",icon:i.A,color:v>0?"text-green-600":v<0?"text-red-600":"text-gray-600"},{title:"Portfolio Value",value:(0,y.vv)(g+v),change:f>0?(0,y.Ee)(f):"0%",trend:"up",icon:d.A,color:"text-sky-600"},{title:"Risk Score",value:"7.2/10",change:"-0.3",trend:"down",icon:o.A,color:"text-orange-600"}];return(0,s.jsx)(r.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(l.Ay,{title:"Performance Analytics",description:"Track your investment performance and returns with detailed insights",icon:c.A,gradient:!0,breadcrumbs:[{label:"Portfolio",href:"/portfolio"},{label:"Performance"}],actions:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(l.lX.Secondary,{children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Filter"]}),(0,s.jsxs)(l.lX.Primary,{children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Export Report"]})]})}),(0,s.jsx)(l.T0,{stats:[{label:"Total Returns",value:"₹2,45,680",change:"+12.5%",trend:"up"},{label:"Monthly Growth",value:"₹18,450",change:"+8.2%",trend:"up"},{label:"Portfolio Value",value:"₹8,75,000",change:"+15.3%",trend:"up"},{label:"Risk Score",value:"7.2/10",change:"-0.3",trend:"down"}]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:j.map((e,t)=>{let a=e.icon;return(0,s.jsx)(n.Zp,{className:"border-0 shadow-lg",children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:e.value}),(0,s.jsxs)("div",{className:"flex items-center mt-2",children:["up"===e.trend?(0,s.jsx)(i.A,{className:"h-4 w-4 text-green-600 mr-1"}):(0,s.jsx)(u,{className:"h-4 w-4 text-red-600 mr-1"}),(0,s.jsx)("span",{className:"text-sm font-medium ".concat("up"===e.trend?"text-green-600":"text-red-600"),children:e.change})]})]}),(0,s.jsx)("div",{className:"p-3 rounded-full bg-gray-50 ".concat(e.color),children:(0,s.jsx)(a,{className:"h-6 w-6"})})]})})},t)})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 text-sky-600"}),(0,s.jsx)("span",{children:"Monthly Performance"})]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 rounded-lg",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Performance chart will be displayed here"})})})]}),(0,s.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 text-sky-600"}),(0,s.jsx)("span",{children:"Asset Allocation"})]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 rounded-lg",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Asset allocation chart will be displayed here"})})})]})]}),(0,s.jsxs)(n.Zp,{className:"border-0 shadow-lg",children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)(n.ZB,{children:"Recent Performance"})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"font-medium",children:["Property Investment #",e]}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Last updated 2 hours ago"})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"font-bold text-green-600",children:"+₹12,500"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"+8.5%"})]})]},e))})})]})]})})}},9481:(e,t,a)=>{Promise.resolve().then(a.bind(a,8774))},9644:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(5050).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9849:(e,t,a)=>{"use strict";a.d(t,{AR:()=>r,rx:()=>s});let{useGetWalletBalanceQuery:s,useGetWalletTransactionsQuery:r,useAddMoneyToWalletMutation:l,useWithdrawMoneyMutation:n,useGetPaymentMethodsQuery:i,useAddPaymentMethodMutation:d,useUpdatePaymentMethodMutation:o,useDeletePaymentMethodMutation:c,useVerifyPaymentMethodMutation:m,useGetTransactionByIdQuery:x,useGetWalletAnalyticsQuery:u,useExportWalletStatementMutation:h,useSetTransactionAlertsMutation:p,useGetWalletLimitsQuery:y,useRequestLimitIncreaseMutation:g}=a(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getWalletBalance:e.query({query:()=>"/wallet",providesTags:[{type:"Wallet",id:"BALANCE"}],keepUnusedDataFor:120}),getWalletTransactions:e.query({query:e=>({url:"/wallet/transactions",params:{page:e.page||1,limit:e.limit||20,...e.type&&{type:e.type},...e.status&&{status:e.status},...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),transformResponse:e=>{var t,a;return(null==e?void 0:e.success)&&(null==e||null==(a=e.data)||null==(t=a.data)?void 0:t.length)?e:{success:!0,message:"Transactions retrieved successfully",data:{data:[{_id:"demo-txn-1",type:"stock_purchase",amount:25e3,status:"completed",description:"Property Investment - Luxury Apartments",createdAt:new Date().toISOString(),reference:"TXN001"},{_id:"demo-txn-2",type:"deposit",amount:5e4,status:"completed",description:"Wallet Deposit",createdAt:new Date(Date.now()-864e5).toISOString(),reference:"TXN002"},{_id:"demo-txn-3",type:"investment",amount:1e5,status:"completed",description:"Property Investment - Commercial Complex",createdAt:new Date(Date.now()-1728e5).toISOString(),reference:"TXN003"}],pagination:{page:1,limit:20,total:3,pages:1}}}},providesTags:e=>{var t;return(null==e||null==(t=e.data)?void 0:t.data)?[...e.data.data.map(e=>{let{_id:t}=e;return{type:"Transaction",id:t}}),{type:"Transaction",id:"LIST"}]:[{type:"Transaction",id:"LIST"}]},keepUnusedDataFor:300}),addMoneyToWallet:e.mutation({query:e=>({url:"/wallet/add-funds",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),withdrawMoney:e.mutation({query:e=>({url:"/wallet/withdraw",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"},{type:"Transaction",id:"LIST"}]}),getPaymentMethods:e.query({query:()=>"/wallet/payment-methods",providesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}],keepUnusedDataFor:600}),addPaymentMethod:e.mutation({query:e=>({url:"/wallet/payment-methods",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),updatePaymentMethod:e.mutation({query:e=>{let{id:t,...a}=e;return{url:"/wallet/payment-methods/".concat(t),method:"PUT",body:a}},invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),deletePaymentMethod:e.mutation({query:e=>({url:"/wallet/payment-methods/".concat(e),method:"DELETE"}),invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),verifyPaymentMethod:e.mutation({query:e=>{let{id:t,verificationData:a}=e;return{url:"/wallet/payment-methods/".concat(t,"/verify"),method:"POST",body:a}},invalidatesTags:[{type:"Wallet",id:"PAYMENT_METHODS"}]}),getTransactionById:e.query({query:e=>"/wallet/transactions/".concat(e),providesTags:(e,t,a)=>[{type:"Transaction",id:a}],keepUnusedDataFor:1800}),getWalletAnalytics:e.query({query:e=>{let{period:t="1Y"}=e;return{url:"/wallet/analytics",params:{period:t}}},providesTags:[{type:"Wallet",id:"ANALYTICS"}],keepUnusedDataFor:900}),exportWalletStatement:e.mutation({query:e=>({url:"/wallet/export-statement",method:"POST",body:e})}),setTransactionAlerts:e.mutation({query:e=>({url:"/wallet/alerts",method:"POST",body:e}),invalidatesTags:[{type:"Wallet",id:"BALANCE"}]}),getWalletLimits:e.query({query:()=>"/wallet/limits",providesTags:[{type:"Wallet",id:"LIMITS"}],keepUnusedDataFor:3600}),requestLimitIncrease:e.mutation({query:e=>({url:"/wallet/request-limit-increase",method:"POST",body:e})})})})}},e=>{var t=t=>e(e.s=t);e.O(0,[2094,5315,7436,7693,1147,7627,3005,390,110,7358],()=>t(9481)),_N_E=e.O()}]);