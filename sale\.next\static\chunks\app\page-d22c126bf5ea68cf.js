(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3815:(e,a,r)=>{"use strict";r.d(a,{Ay:()=>k,H$:()=>m,Kc:()=>f,PK:()=>i,ZB:()=>C,_v:()=>l,mB:()=>y});var t=r(7895);let s={setCurrentUser:e=>{localStorage.setItem("salesUser",JSON.stringify(e))},clearCurrentUser:()=>{localStorage.removeItem("salesUser")}},l=(0,t.zD)("auth/login",async(e,a)=>{let{rejectWithValue:r}=a;try{console.log("\uD83D\uDD10 Sales backend login attempt:",e.email);let a=await fetch("".concat("http://localhost:5000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Client-Type":"sales-dashboard","X-Client-Version":"1.0.0"},credentials:"include",body:JSON.stringify(e)}),t=await a.json();if(!a.ok)throw Error(t.message||"Login failed");console.log("✅ Sales: Backend login response:",t);let{user:l,accessToken:n,refreshToken:o}=t.data||t;if(!l||!n)return r("Invalid response format");try{return s.setCurrentUser(l),console.log("✅ Sales login successful, backend set HttpOnly cookies:",{user:l.email,role:l.role,note:"Tokens stored in HttpOnly cookies (not accessible from JS)"}),{user:l,accessToken:n,refreshToken:o}}catch(e){return console.error("Sales user storage failed:",e),r("Failed to store user data")}}catch(e){return console.error("Sales login error:",e),r(e.message||"Login failed")}}),n=(0,t.zD)("auth/logout",async()=>{try{await fetch("".concat("http://localhost:5000/api","/auth/logout"),{method:"POST",headers:{"Content-Type":"application/json","X-Client-Type":"sales-dashboard"},credentials:"include"})}catch(e){console.error("Sales logout API error:",e)}finally{s.clearCurrentUser()}}),o=(0,t.zD)("auth/refreshUser",async(e,a)=>{let{rejectWithValue:r}=a;try{let e=await fetch("".concat("http://localhost:5000/api","/auth/profile"),{method:"GET",headers:{"Content-Type":"application/json","X-Client-Type":"sales-dashboard"},credentials:"include"}),a=await e.json();if(!e.ok)throw Error(a.message||"Failed to get profile");let r=a.data||a;return s.setCurrentUser(r),r}catch(e){return r(e.message||"Failed to refresh user")}}),i=(0,t.zD)("auth/checkAuth",async(e,a)=>{let{rejectWithValue:r}=a;try{console.log("\uD83D\uDD0D Sales: Checking auth with backend (HttpOnly cookies)...");let e=await fetch("".concat("http://localhost:5000/api","/auth/check"),{method:"GET",headers:{"Content-Type":"application/json","X-Client-Type":"sales-dashboard"},credentials:"include"}),a=await e.json();if(console.log("✅ Sales: Backend auth response:",a),e.ok&&a.success&&a.data)return s.setCurrentUser(a.data),a.data;return console.log("❌ Sales: Backend auth failed:",a.message),s.clearCurrentUser(),r(a.message||"Authentication failed")}catch(e){return console.error("❌ Sales: Auth check error:",e.message),s.clearCurrentUser(),r(e.message||"Authentication check failed")}}),d=(0,t.Z0)({name:"auth",initialState:{user:null,isAuthenticated:!1,loading:!1,error:null,sessionExpiry:null},reducers:{clearError:e=>{e.error=null},setUser:(e,a)=>{e.user=a.payload,e.isAuthenticated=!0,s.setCurrentUser(a.payload)},clearAuth:e=>{e.user=null,e.isAuthenticated=!1,e.error=null,e.sessionExpiry=null,s.clearCurrentUser()},setSessionExpiry:(e,a)=>{e.sessionExpiry=a.payload},updateUserProfile:(e,a)=>{e.user&&(e.user={...e.user,...a.payload},s.setCurrentUser(e.user))}},extraReducers:e=>{e.addCase(l.pending,e=>{e.loading=!0,e.error=null}).addCase(l.fulfilled,(e,a)=>{e.loading=!1,e.user=a.payload.user||a.payload,e.isAuthenticated=!0,e.error=null}).addCase(l.rejected,(e,a)=>{e.loading=!1,e.error=a.payload,e.isAuthenticated=!1,e.user=null}),e.addCase(n.pending,e=>{e.loading=!0}).addCase(n.fulfilled,e=>{e.loading=!1,e.user=null,e.isAuthenticated=!1,e.error=null,e.sessionExpiry=null}).addCase(n.rejected,e=>{e.loading=!1,e.user=null,e.isAuthenticated=!1,e.error=null,e.sessionExpiry=null}),e.addCase(o.pending,e=>{e.loading=!0}).addCase(o.fulfilled,(e,a)=>{e.loading=!1,e.user=a.payload.user||a.payload,e.error=null}).addCase(o.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}),e.addCase(i.pending,e=>{e.loading=!0}).addCase(i.fulfilled,(e,a)=>{e.loading=!1,e.user=a.payload.user||a.payload,e.isAuthenticated=!0,e.error=null}).addCase(i.rejected,(e,a)=>{e.loading=!1,e.error=a.payload,e.isAuthenticated=!1,e.user=null})}}),{clearError:c,setUser:u,clearAuth:h,setSessionExpiry:p,updateUserProfile:g}=d.actions,y=e=>e.auth.user,f=e=>e.auth.isAuthenticated,m=e=>e.auth.loading,C=e=>e.auth.error,k=d.reducer},4965:(e,a,r)=>{"use strict";r.d(a,{G:()=>l,j:()=>s});var t=r(9559);let s=()=>(0,t.wA)(),l=t.d4},8072:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>i});var t=r(9605),s=r(9585),l=r(5935),n=r(4965),o=r(3815);function i(){let e=(0,l.useRouter)(),a=(0,n.G)(o.Kc);return(0,s.useEffect)(()=>{a?e.replace("/dashboard"):e.replace("/login")},[a,e]),(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-sky-600 mx-auto mb-4"}),(0,t.jsx)("h1",{className:"text-xl font-semibold text-gray-900 mb-2",children:"SGM Sales Portal"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Redirecting..."})]})})}},8163:(e,a,r)=>{Promise.resolve().then(r.bind(r,8072))}},e=>{var a=a=>e(e.s=a);e.O(0,[399,390,110,358],()=>a(8163)),_N_E=e.O()}]);