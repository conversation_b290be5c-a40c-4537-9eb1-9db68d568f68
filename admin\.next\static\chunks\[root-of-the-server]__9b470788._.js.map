{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,6JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,6JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,6JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/utils/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport Cookies from 'js-cookie';\nimport { toast } from 'sonner';\n\n// API Configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance with cookies support\nconst api: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  withCredentials: true, // Enable cookies for backend communication\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  },\n});\n\n// Request interceptor\napi.interceptors.request.use(\n  (config) => {\n    // Add auth token from cookies or localStorage\n    const token = Cookies.get('accessToken') || localStorage.getItem('accessToken');\n    \n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n\n    // Add request timestamp\n    config.headers['X-Request-Time'] = new Date().toISOString();\n    \n    // Log request in development\n    if (process.env.NODE_ENV === 'development') {\n      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {\n        data: config.data,\n        params: config.params,\n      });\n    }\n\n    return config;\n  },\n  (error) => {\n    console.error('Request interceptor error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor\napi.interceptors.response.use(\n  (response: AxiosResponse) => {\n    // Log response in development\n    if (process.env.NODE_ENV === 'development') {\n      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {\n        status: response.status,\n        data: response.data,\n      });\n    }\n\n    return response;\n  },\n  async (error) => {\n    const originalRequest = error.config;\n\n    // Handle 401 errors (unauthorized)\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      try {\n        // Try to refresh token\n        const refreshToken = Cookies.get('refreshToken');\n        \n        if (refreshToken) {\n          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\n            refreshToken,\n          }, {\n            withCredentials: true,\n          });\n\n          if (response.data.success) {\n            const newToken = response.data.data.accessToken;\n            \n            // Update token in cookies and localStorage\n            Cookies.set('accessToken', newToken);\n            localStorage.setItem('accessToken', newToken);\n            \n            // Retry original request with new token\n            originalRequest.headers.Authorization = `Bearer ${newToken}`;\n            return api(originalRequest);\n          }\n        }\n      } catch (refreshError) {\n        console.error('Token refresh failed:', refreshError);\n      }\n\n      // If refresh fails, redirect to login\n      Cookies.remove('accessToken');\n      Cookies.remove('refreshToken');\n      localStorage.removeItem('accessToken');\n      \n      if (typeof window !== 'undefined') {\n        window.location.href = '/login';\n      }\n    }\n\n    // Handle other errors\n    const errorMessage = error.response?.data?.message || error.message || 'An error occurred';\n    \n    // Show error toast for non-401 errors\n    if (error.response?.status !== 401) {\n      toast.error(errorMessage);\n    }\n\n    // Log error in development\n    if (process.env.NODE_ENV === 'development') {\n      console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {\n        status: error.response?.status,\n        message: errorMessage,\n        data: error.response?.data,\n      });\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// API Helper Functions\nexport const apiClient = {\n  // GET request\n  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await api.get(url, config);\n    return response.data;\n  },\n\n  // POST request\n  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await api.post(url, data, config);\n    return response.data;\n  },\n\n  // PUT request\n  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await api.put(url, data, config);\n    return response.data;\n  },\n\n  // PATCH request\n  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await api.patch(url, data, config);\n    return response.data;\n  },\n\n  // DELETE request\n  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await api.delete(url, config);\n    return response.data;\n  },\n\n  // Upload file\n  upload: async <T = any>(url: string, formData: FormData, onProgress?: (progress: number) => void): Promise<T> => {\n    const response = await api.post(url, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      onUploadProgress: (progressEvent) => {\n        if (onProgress && progressEvent.total) {\n          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n          onProgress(progress);\n        }\n      },\n    });\n    return response.data;\n  },\n};\n\n// Removed mock JWT generator - using real backend tokens\n\n// Removed mock login - using real backend only\n\n// Auth API\nexport const authAPI = {\n  login: async (credentials: { email: string; password: string; rememberMe?: boolean }) => {\n    console.log('🔐 Attempting backend login:', credentials.email)\n\n    try {\n      // Call real backend API\n      const response = await apiClient.post('/auth/login', credentials)\n      console.log('✅ Backend login response:', response)\n\n      return response\n    } catch (backendError: any) {\n      console.error('❌ Backend login failed:', backendError.response?.data || backendError.message)\n\n      // Re-throw the error so the UI can handle it\n      throw new Error(\n        backendError.response?.data?.message ||\n        backendError.message ||\n        'Login failed - please check your credentials'\n      )\n    }\n  },\n\n  // Check if user is authenticated (reads HttpOnly cookies)\n  checkAuth: async () => {\n    console.log('🔍 Backend auth check started')\n\n    try {\n      // Backend will check HttpOnly cookies and return user data\n      const response = await apiClient.get('/auth/me')\n      console.log('✅ Backend auth check successful:', response)\n      return response\n    } catch (backendError: any) {\n      console.error('❌ Backend auth check failed:', backendError.response?.data || backendError.message)\n      throw new Error(\n        backendError.response?.data?.message ||\n        backendError.message ||\n        'Authentication check failed'\n      )\n    }\n  },\n  \n  logout: () =>\n    apiClient.post('/auth/logout'),\n  \n  refreshToken: () =>\n    apiClient.post('/auth/refresh'),\n  \n  getProfile: () =>\n    apiClient.get('/auth/me'),\n  \n  updateProfile: (data: any) =>\n    apiClient.put('/auth/profile', data),\n  \n  changePassword: (data: { currentPassword: string; newPassword: string }) =>\n    apiClient.post('/auth/change-password', data),\n};\n\n// Users API\nexport const usersAPI = {\n  getUsers: (params?: any) =>\n    apiClient.get('/users', { params }),\n  \n  getUser: (id: string) =>\n    apiClient.get(`/users/${id}`),\n  \n  createUser: (data: any) =>\n    apiClient.post('/users', data),\n  \n  updateUser: (id: string, data: any) =>\n    apiClient.put(`/users/${id}`, data),\n  \n  deleteUser: (id: string) =>\n    apiClient.delete(`/users/${id}`),\n  \n  getUserStats: () =>\n    apiClient.get('/users/stats'),\n};\n\n// Properties API\nexport const propertiesAPI = {\n  getProperties: (params?: any) =>\n    apiClient.get('/properties', { params }),\n  \n  getProperty: (id: string) =>\n    apiClient.get(`/properties/${id}`),\n  \n  createProperty: (data: any) =>\n    apiClient.post('/properties', data),\n  \n  updateProperty: (id: string, data: any) =>\n    apiClient.put(`/properties/${id}`, data),\n  \n  deleteProperty: (id: string) =>\n    apiClient.delete(`/properties/${id}`),\n  \n  getPropertyStats: () =>\n    apiClient.get('/properties/stats'),\n};\n\n// Leads API\nexport const leadsAPI = {\n  getLeads: (params?: any) =>\n    apiClient.get('/leads', { params }),\n  \n  getLead: (id: string) =>\n    apiClient.get(`/leads/${id}`),\n  \n  createLead: (data: any) =>\n    apiClient.post('/leads', data),\n  \n  updateLead: (id: string, data: any) =>\n    apiClient.put(`/leads/${id}`, data),\n  \n  deleteLead: (id: string) =>\n    apiClient.delete(`/leads/${id}`),\n  \n  assignLead: (id: string, data: { assignedTo: string }) =>\n    apiClient.post(`/leads/${id}/assign`, data),\n  \n  getLeadStats: () =>\n    apiClient.get('/leads/dashboard/stats'),\n};\n\n// Transactions API\nexport const transactionsAPI = {\n  getTransactions: (params?: any) =>\n    apiClient.get('/transactions', { params }),\n  \n  getTransaction: (id: string) =>\n    apiClient.get(`/transactions/${id}`),\n  \n  getTransactionStats: () =>\n    apiClient.get('/transactions/stats'),\n};\n\n// Dashboard API\nexport const dashboardAPI = {\n  getStats: () =>\n    apiClient.get('/dashboard/stats'),\n  \n  getChartData: (type: string, period: string) =>\n    apiClient.get(`/dashboard/charts/${type}`, { params: { period } }),\n  \n  getRecentActivity: () =>\n    apiClient.get('/dashboard/activity'),\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;AAKqB;AALrB;AACA;AACA;;;;AAEA,oBAAoB;AACpB,MAAM,eAAe,iEAAmC;AAExD,6CAA6C;AAC7C,MAAM,MAAqB,2LAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACtC,SAAS;IACT,SAAS;IACT,iBAAiB;IACjB,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,sBAAsB;AACtB,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,8CAA8C;IAC9C,MAAM,QAAQ,iNAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kBAAkB,aAAa,OAAO,CAAC;IAEjE,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IAEA,wBAAwB;IACxB,OAAO,OAAO,CAAC,iBAAiB,GAAG,IAAI,OAAO,WAAW;IAEzD,6BAA6B;IAC7B,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO,MAAM,EAAE,cAAc,CAAC,EAAE,OAAO,GAAG,EAAE,EAAE;YAC3E,MAAM,OAAO,IAAI;YACjB,QAAQ,OAAO,MAAM;QACvB;IACF;IAEA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,8BAA8B;IAC5C,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,uBAAuB;AACvB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC;IACC,8BAA8B;IAC9B,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,EAAE;YAC7F,QAAQ,SAAS,MAAM;YACvB,MAAM,SAAS,IAAI;QACrB;IACF;IAEA,OAAO;AACT,GACA,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAEpC,mCAAmC;IACnC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;QAC7D,gBAAgB,MAAM,GAAG;QAEzB,IAAI;YACF,uBAAuB;YACvB,MAAM,eAAe,iNAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAEjC,IAAI,cAAc;gBAChB,MAAM,WAAW,MAAM,2LAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,aAAa,aAAa,CAAC,EAAE;oBAChE;gBACF,GAAG;oBACD,iBAAiB;gBACnB;gBAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;oBACzB,MAAM,WAAW,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW;oBAE/C,2CAA2C;oBAC3C,iNAAA,CAAA,UAAO,CAAC,GAAG,CAAC,eAAe;oBAC3B,aAAa,OAAO,CAAC,eAAe;oBAEpC,wCAAwC;oBACxC,gBAAgB,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,UAAU;oBAC5D,OAAO,IAAI;gBACb;YACF;QACF,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,yBAAyB;QACzC;QAEA,sCAAsC;QACtC,iNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,iNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,aAAa,UAAU,CAAC;QAExB,wCAAmC;YACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,sBAAsB;IACtB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;IAEvE,sCAAsC;IACtC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IACd;IAEA,2BAA2B;IAC3B,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE,QAAQ,cAAc,CAAC,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE;YACxF,QAAQ,MAAM,QAAQ,EAAE;YACxB,SAAS;YACT,MAAM,MAAM,QAAQ,EAAE;QACxB;IACF;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,YAAY;IACvB,cAAc;IACd,KAAK,OAAgB,KAAa;QAChC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,KAAK;QACpC,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,MAAM,OAAgB,KAAa,MAAY;QAC7C,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,KAAK,MAAM;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,KAAK,OAAgB,KAAa,MAAY;QAC5C,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,KAAK,MAAM;QAC1C,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,OAAO,OAAgB,KAAa,MAAY;QAC9C,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,KAAK,MAAM;QAC5C,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,QAAQ,OAAgB,KAAa;QACnC,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,KAAK;QACvC,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,QAAQ,OAAgB,KAAa,UAAoB;QACvD,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,KAAK,UAAU;YAC7C,SAAS;gBACP,gBAAgB;YAClB;YACA,kBAAkB,CAAC;gBACjB,IAAI,cAAc,cAAc,KAAK,EAAE;oBACrC,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;oBAC9E,WAAW;gBACb;YACF;QACF;QACA,OAAO,SAAS,IAAI;IACtB;AACF;AAOO,MAAM,UAAU;IACrB,OAAO,OAAO;QACZ,QAAQ,GAAG,CAAC,gCAAgC,YAAY,KAAK;QAE7D,IAAI;YACF,wBAAwB;YACxB,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,eAAe;YACrD,QAAQ,GAAG,CAAC,6BAA6B;YAEzC,OAAO;QACT,EAAE,OAAO,cAAmB;YAC1B,QAAQ,KAAK,CAAC,2BAA2B,aAAa,QAAQ,EAAE,QAAQ,aAAa,OAAO;YAE5F,6CAA6C;YAC7C,MAAM,IAAI,MACR,aAAa,QAAQ,EAAE,MAAM,WAC7B,aAAa,OAAO,IACpB;QAEJ;IACF;IAEA,0DAA0D;IAC1D,WAAW;QACT,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,2DAA2D;YAC3D,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC;YACrC,QAAQ,GAAG,CAAC,oCAAoC;YAChD,OAAO;QACT,EAAE,OAAO,cAAmB;YAC1B,QAAQ,KAAK,CAAC,gCAAgC,aAAa,QAAQ,EAAE,QAAQ,aAAa,OAAO;YACjG,MAAM,IAAI,MACR,aAAa,QAAQ,EAAE,MAAM,WAC7B,aAAa,OAAO,IACpB;QAEJ;IACF;IAEA,QAAQ,IACN,UAAU,IAAI,CAAC;IAEjB,cAAc,IACZ,UAAU,IAAI,CAAC;IAEjB,YAAY,IACV,UAAU,GAAG,CAAC;IAEhB,eAAe,CAAC,OACd,UAAU,GAAG,CAAC,iBAAiB;IAEjC,gBAAgB,CAAC,OACf,UAAU,IAAI,CAAC,yBAAyB;AAC5C;AAGO,MAAM,WAAW;IACtB,UAAU,CAAC,SACT,UAAU,GAAG,CAAC,UAAU;YAAE;QAAO;IAEnC,SAAS,CAAC,KACR,UAAU,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI;IAE9B,YAAY,CAAC,OACX,UAAU,IAAI,CAAC,UAAU;IAE3B,YAAY,CAAC,IAAY,OACvB,UAAU,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;IAEhC,YAAY,CAAC,KACX,UAAU,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;IAEjC,cAAc,IACZ,UAAU,GAAG,CAAC;AAClB;AAGO,MAAM,gBAAgB;IAC3B,eAAe,CAAC,SACd,UAAU,GAAG,CAAC,eAAe;YAAE;QAAO;IAExC,aAAa,CAAC,KACZ,UAAU,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;IAEnC,gBAAgB,CAAC,OACf,UAAU,IAAI,CAAC,eAAe;IAEhC,gBAAgB,CAAC,IAAY,OAC3B,UAAU,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;IAErC,gBAAgB,CAAC,KACf,UAAU,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;IAEtC,kBAAkB,IAChB,UAAU,GAAG,CAAC;AAClB;AAGO,MAAM,WAAW;IACtB,UAAU,CAAC,SACT,UAAU,GAAG,CAAC,UAAU;YAAE;QAAO;IAEnC,SAAS,CAAC,KACR,UAAU,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI;IAE9B,YAAY,CAAC,OACX,UAAU,IAAI,CAAC,UAAU;IAE3B,YAAY,CAAC,IAAY,OACvB,UAAU,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;IAEhC,YAAY,CAAC,KACX,UAAU,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;IAEjC,YAAY,CAAC,IAAY,OACvB,UAAU,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,EAAE;IAExC,cAAc,IACZ,UAAU,GAAG,CAAC;AAClB;AAGO,MAAM,kBAAkB;IAC7B,iBAAiB,CAAC,SAChB,UAAU,GAAG,CAAC,iBAAiB;YAAE;QAAO;IAE1C,gBAAgB,CAAC,KACf,UAAU,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;IAErC,qBAAqB,IACnB,UAAU,GAAG,CAAC;AAClB;AAGO,MAAM,eAAe;IAC1B,UAAU,IACR,UAAU,GAAG,CAAC;IAEhB,cAAc,CAAC,MAAc,SAC3B,UAAU,GAAG,CAAC,CAAC,kBAAkB,EAAE,MAAM,EAAE;YAAE,QAAQ;gBAAE;YAAO;QAAE;IAElE,mBAAmB,IACjB,UAAU,GAAG,CAAC;AAClB;uCAEe", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/types/index.ts"], "sourcesContent": ["// User Types\nexport interface User {\n  id: string;\n  _id?: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  fullName?: string;\n  phone: string;\n  role: UserRole;\n  status: UserStatus;\n  kycStatus: KYCStatus;\n  kycSubmittedAt?: string;\n  kycVerifiedAt?: string;\n  kycRejectedAt?: string;\n  kycRejectionReason?: string;\n  createdAt: string;\n  updatedAt: string;\n  lastLogin?: string;\n  accessToken?: string;\n  permissions?: string[];\n  isEmailVerified: boolean;\n  emailVerified?: boolean;\n  isPhoneVerified: boolean;\n  phoneVerified?: boolean;\n  profileImage?: string;\n  avatar?: string;\n  dateOfBirth?: string;\n  gender?: string;\n  address?: {\n    street?: string;\n    city?: string;\n    state?: string;\n    country?: string;\n    pincode?: string;\n  };\n  referralCode?: string;\n  referredBy?: string;\n  documents?: KYCDocument[];\n  wallet?: {\n    balance: number;\n    totalInvested: number;\n    totalReturns: number;\n    totalDeposited: number;\n    totalWithdrawn: number;\n  };\n  // Additional properties used in UserDetailsModal\n  propertiesCount?: number;\n  activeInvestments?: number;\n  pendingWithdrawals?: number;\n  recentTransactions?: Array<{\n    type: 'investment' | 'return' | 'withdrawal';\n    description: string;\n    amount: number;\n    status: string;\n    createdAt: string;\n  }>;\n}\n\nexport interface KYCDocument {\n  id: string;\n  type: 'identity' | 'address' | 'income' | 'bank' | 'photo' | 'signature' | 'other';\n  subType: string;\n  documentNumber?: string;\n  issuedBy?: string;\n  issuedDate?: string;\n  expiryDate?: string;\n  status: 'pending' | 'verified' | 'rejected';\n  rejectionReason?: string;\n  fileUrl?: string;\n  uploadedAt: string;\n  verifiedAt?: string;\n}\n\nexport enum UserRole {\n  ADMIN = 'admin',\n  SUBADMIN = 'subadmin',\n  USER = 'user',\n  SALES = 'sales'\n}\n\nexport enum UserStatus {\n  ACTIVE = 'active',\n  INACTIVE = 'inactive',\n  SUSPENDED = 'suspended',\n  PENDING = 'pending'\n}\n\nexport enum KYCStatus {\n  NOT_STARTED = 'not_started',\n  PENDING = 'pending',\n  UNDER_REVIEW = 'under_review',\n  APPROVED = 'approved',\n  REJECTED = 'rejected'\n}\n\n// Auth Types\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n  rememberMe?: boolean;\n}\n\nexport interface AuthResponse {\n  success: boolean;\n  message: string;\n  data: {\n    user: User;\n    accessToken: string;\n    refreshToken: string;\n    clientType: string;\n    authMethods: {\n      primary: string;\n      cookies: {\n        set: boolean;\n        note: string;\n      };\n      header: {\n        format: string;\n        recommended: boolean;\n      };\n    };\n  };\n}\n\n// Property Types\nexport interface Property {\n  id: string;\n  title: string;\n  name?: string; // Alternative name field\n  description: string;\n  location: {\n    address: string;\n    city: string;\n    state: string;\n    country: string;\n    zipCode?: string;\n    coordinates?: {\n      lat: number;\n      lng: number;\n      latitude?: number;\n      longitude?: number;\n    };\n  };\n  propertyType?: string;\n  pricePerStock: number;\n  totalStocks: number;\n  availableStocks: number;\n  minimumInvestment?: number;\n  images: string[];\n  documents?: string[];\n  videos?: string[];\n  status: PropertyStatus;\n  createdAt: string;\n  updatedAt: string;\n  ownerId: string;\n  features: string[];\n  amenities?: string[];\n  specifications?: any;\n  expectedReturns: number;\n  maturityPeriodMonths?: number;\n  constructionStatus: ConstructionStatus;\n  constructionTimeline?: string;\n  featured?: boolean;\n}\n\nexport enum PropertyStatus {\n  ACTIVE = 'active',\n  INACTIVE = 'inactive',\n  SOLD_OUT = 'sold_out',\n  COMING_SOON = 'coming_soon'\n}\n\nexport enum ConstructionStatus {\n  PLANNING = 'planning',\n  FOUNDATION = 'foundation',\n  STRUCTURE = 'structure',\n  FINISHING = 'finishing',\n  COMPLETED = 'completed'\n}\n\n// Lead Types\nexport interface Lead {\n  id: string;\n  leadId: string;\n  fullName: string;\n  email: string;\n  phone: string;\n  city: string;\n  country: string;\n  status: LeadStatus;\n  source: LeadSource;\n  priority: LeadPriority;\n  assignedTo?: string;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n  notes?: string;\n  tags?: string[];\n  // Additional fields for compatibility\n  interestedProperty?: string;\n  budget?: string;\n  score?: number;\n  nextFollowUp?: string;\n  lastContactDate?: string;\n}\n\nexport enum LeadStatus {\n  NEW = 'new',\n  CONTACTED = 'contacted',\n  QUALIFIED = 'qualified',\n  PROPOSAL_SENT = 'proposal_sent',\n  NEGOTIATION = 'negotiation',\n  CONVERTED = 'converted',\n  LOST = 'lost',\n  ON_HOLD = 'on_hold'\n}\n\nexport enum LeadSource {\n  WEBSITE = 'website',\n  SOCIAL_MEDIA = 'social_media',\n  REFERRAL = 'referral',\n  COLD_CALL = 'cold_call',\n  EMAIL_CAMPAIGN = 'email_campaign',\n  ADVERTISEMENT = 'advertisement',\n  WALK_IN = 'walk_in',\n  OTHER = 'other'\n}\n\nexport enum LeadPriority {\n  LOW = 'low',\n  MEDIUM = 'medium',\n  HIGH = 'high',\n  URGENT = 'urgent'\n}\n\n// Transaction Types\nexport interface Transaction {\n  id: string;\n  transactionId: string;\n  userId: string;\n  propertyId: string;\n  type: TransactionType;\n  amount: number;\n  status: TransactionStatus;\n  createdAt: string;\n  updatedAt: string;\n  description?: string;\n  paymentMethod?: string;\n}\n\nexport enum TransactionType {\n  INVESTMENT = 'investment',\n  RETURN = 'return',\n  WITHDRAWAL = 'withdrawal',\n  REFUND = 'refund'\n}\n\nexport enum TransactionStatus {\n  PENDING = 'pending',\n  COMPLETED = 'completed',\n  FAILED = 'failed',\n  CANCELLED = 'cancelled'\n}\n\n// Dashboard Types\nexport interface DashboardStats {\n  // User Stats\n  totalUsers: number;\n  activeUsers?: number;\n  newUsersThisMonth?: number;\n  userGrowth: number;\n\n  // Property Stats\n  totalProperties: number;\n  activeProperties?: number;\n  soldOutProperties?: number;\n  featuredProperties?: number;\n  propertyGrowth?: number;\n\n  // Investment & Revenue Stats\n  totalInvestments: number;\n  totalInvestment?: number; // Alternative naming\n  totalRevenue: number;\n  revenueGrowth: number;\n  investmentGrowth?: number;\n  averageInvestment?: number;\n\n  // Lead Stats\n  activeLeads: number;\n  totalLeads?: number;\n  convertedLeads?: number;\n  leadGrowth?: number;\n\n  // Transaction Stats\n  pendingTransactions: number;\n  totalTransactions?: number;\n  completedTransactions?: number;\n  transactionGrowth?: number;\n\n  // Additional metrics\n  conversionRate?: number;\n  averageROI?: number;\n  totalStocks?: number;\n  soldStocks?: number;\n  availableStocks?: number;\n}\n\n// Activity Types\nexport interface RecentActivity {\n  id: string;\n  type: 'user_registration' | 'property_investment' | 'lead_conversion' | 'kyc_approval' | 'transaction' | 'property_added' | 'commission_paid' | 'returns_distributed';\n  title: string;\n  description: string;\n  message?: string;\n  timestamp: string;\n  time?: string;\n  status?: 'pending' | 'completed' | 'failed' | 'approved' | 'rejected';\n  userId?: string;\n  userName?: string;\n  userEmail?: string;\n  propertyId?: string;\n  propertyName?: string;\n  amount?: number;\n  totalInvestment?: number;\n  investmentAmount?: number;\n  transactionId?: string;\n  leadId?: string;\n  kycStatus?: string;\n  metadata?: {\n    [key: string]: any;\n  };\n  icon?: string;\n  color?: string;\n  bgColor?: string;\n  priority?: 'low' | 'medium' | 'high' | 'urgent';\n}\n\n// API Response Types\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  message: string;\n  data?: T;\n  error?: string;\n  meta?: {\n    pagination?: {\n      currentPage: number;\n      totalPages: number;\n      totalItems: number;\n      itemsPerPage: number;\n    };\n  };\n}\n\n// Form Types\nexport interface CreateUserForm {\n  firstName: string;\n  lastName: string;\n  email: string;\n  phone: string;\n  role: UserRole;\n  password: string;\n  confirmPassword: string;\n}\n\nexport interface CreatePropertyForm {\n  title: string;\n  description: string;\n  location: {\n    address: string;\n    city: string;\n    state: string;\n    country: string;\n  };\n  pricePerStock: number;\n  totalStocks: number;\n  expectedReturns: number;\n  features: string[];\n  images: File[];\n}\n\n// Table Types\nexport interface TableColumn<T = any> {\n  key: keyof T;\n  label: string;\n  sortable?: boolean;\n  render?: (value: any, row: T) => React.ReactNode;\n}\n\nexport interface TableProps<T = any> {\n  data: T[];\n  columns: TableColumn<T>[];\n  loading?: boolean;\n  pagination?: {\n    currentPage: number;\n    totalPages: number;\n    onPageChange: (page: number) => void;\n  };\n  onSort?: (key: keyof T, direction: 'asc' | 'desc') => void;\n}\n\n// Navigation Types\nexport interface NavItem {\n  label: string;\n  href: string;\n  icon: React.ComponentType<any>;\n  children?: NavItem[];\n  roles?: UserRole[];\n}\n\n// Modal Types\nexport interface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n}\n\n// Notification Types\nexport interface Notification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  title: string;\n  message: string;\n  timestamp: string;\n  read: boolean;\n}\n"], "names": [], "mappings": "AAAA,aAAa;;;;;;;;;;;;;AA0EN,IAAA,AAAK,kCAAA;;;;;WAAA;;AAOL,IAAA,AAAK,oCAAA;;;;;WAAA;;AAOL,IAAA,AAAK,mCAAA;;;;;;WAAA;;AA8EL,IAAA,AAAK,wCAAA;;;;;WAAA;;AAOL,IAAA,AAAK,4CAAA;;;;;;WAAA;;AAkCL,IAAA,AAAK,oCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,oCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,sCAAA;;;;;WAAA;;AAsBL,IAAA,AAAK,yCAAA;;;;;WAAA;;AAOL,IAAA,AAAK,2CAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/utils/auth.ts"], "sourcesContent": ["import Cookies from 'js-cookie';\nimport { User, UserRole } from '@/types';\n\n// Token management\nexport const tokenManager = {\n  // Get access token\n  getAccessToken: (): string | null => {\n    // For HttpOnly cookies, we cannot access them from JavaScript\n    // This function will return null for HttpOnly cookies (which is expected)\n\n    // Try to get from regular cookies (non-HttpOnly) - for development/testing\n    const cookieToken = Cookies.get('accessToken');\n    if (cookieToken) {\n      console.log('📝 Found non-HttpOnly cookie token')\n      return cookieToken;\n    }\n\n    // Try localStorage as fallback (for development/testing)\n    if (typeof window !== 'undefined') {\n      const localToken = localStorage.getItem('accessToken');\n      if (localToken) {\n        console.log('📝 Found localStorage token')\n        return localToken;\n      }\n    }\n\n    // For production with HttpOnly cookies, this will always return null\n    // Auth check should be done via backend API calls\n    console.log('📝 No accessible token found (HttpOnly cookies are not accessible)')\n    return null;\n  },\n\n  // Set access token\n  setAccessToken: (token: string, rememberMe: boolean = false): void => {\n    const isProduction = process.env.NODE_ENV === 'production'\n\n    if (rememberMe) {\n      // Set cookie for 30 days\n      Cookies.set('accessToken', token, {\n        expires: 30,\n        secure: isProduction, // Only secure in production\n        sameSite: 'lax', // Changed from strict to lax for better compatibility\n        path: '/' // Ensure cookie is available site-wide\n      });\n    } else {\n      // Session cookie\n      Cookies.set('accessToken', token, {\n        secure: isProduction, // Only secure in production\n        sameSite: 'lax', // Changed from strict to lax\n        path: '/' // Ensure cookie is available site-wide\n      });\n    }\n\n    // Only set localStorage on client side\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('accessToken', token);\n    }\n\n    // Debug: Verify cookie was set\n    if (process.env.NODE_ENV === 'development') {\n      setTimeout(() => {\n        const cookieCheck = Cookies.get('accessToken')\n        console.log('🍪 Cookie set verification:', {\n          tokenLength: token.length,\n          cookieExists: !!cookieCheck,\n          cookieValue: cookieCheck ? `${cookieCheck.substring(0, 20)}...` : 'none'\n        })\n      }, 100)\n    }\n  },\n\n  // Get refresh token\n  getRefreshToken: (): string | null => {\n    return Cookies.get('refreshToken') || null;\n  },\n\n  // Set refresh token\n  setRefreshToken: (token: string, rememberMe: boolean = false): void => {\n    if (rememberMe) {\n      Cookies.set('refreshToken', token, { expires: 30, secure: true, sameSite: 'strict' });\n    } else {\n      Cookies.set('refreshToken', token, { secure: true, sameSite: 'strict' });\n    }\n  },\n\n  // Verify token is properly stored and accessible\n  verifyTokenStorage: (): boolean => {\n    const cookieToken = Cookies.get('accessToken')\n    const localToken = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null\n\n    console.log('🔍 Token verification:', {\n      cookieToken: cookieToken ? `${cookieToken.substring(0, 20)}...` : 'none',\n      localToken: localToken ? `${localToken.substring(0, 20)}...` : 'none',\n      cookieExists: !!cookieToken,\n      localExists: !!localToken\n    })\n\n    return !!(cookieToken || localToken)\n  },\n\n  // Clear all tokens\n  clearTokens: (): void => {\n    Cookies.remove('accessToken');\n    Cookies.remove('refreshToken');\n\n    // Only clear localStorage on client side\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('user');\n    }\n  },\n\n  // Check if user is authenticated\n  isAuthenticated: (): boolean => {\n    const token = tokenManager.getAccessToken();\n    return !!token;\n  },\n};\n\n// User management\nexport const userManager = {\n  // Get current user\n  getCurrentUser: (): User | null => {\n    try {\n      const userStr = localStorage.getItem('user');\n      return userStr ? JSON.parse(userStr) : null;\n    } catch (error) {\n      console.error('Error parsing user data:', error);\n      return null;\n    }\n  },\n\n  // Set current user\n  setCurrentUser: (user: User): void => {\n    localStorage.setItem('user', JSON.stringify(user));\n  },\n\n  // Clear current user\n  clearCurrentUser: (): void => {\n    localStorage.removeItem('user');\n  },\n\n  // Check user role\n  hasRole: (requiredRole: UserRole): boolean => {\n    const user = userManager.getCurrentUser();\n    if (!user) return false;\n\n    const roleHierarchy = {\n      [UserRole.ADMIN]: 4,\n      [UserRole.SUBADMIN]: 3,\n      [UserRole.SALES]: 2,\n      [UserRole.USER]: 1,\n    };\n\n    const userLevel = roleHierarchy[user.role];\n    const requiredLevel = roleHierarchy[requiredRole];\n\n    return userLevel >= requiredLevel;\n  },\n\n  // Check if user has any of the required roles\n  hasAnyRole: (roles: UserRole[]): boolean => {\n    return roles.some(role => userManager.hasRole(role));\n  },\n\n  // Get user display name\n  getDisplayName: (): string => {\n    const user = userManager.getCurrentUser();\n    if (!user) return 'Guest';\n    return `${user.firstName} ${user.lastName}`.trim() || user.email;\n  },\n\n  // Get user initials\n  getInitials: (): string => {\n    const user = userManager.getCurrentUser();\n    if (!user) return 'G';\n    \n    const firstName = user.firstName || '';\n    const lastName = user.lastName || '';\n    \n    if (firstName && lastName) {\n      return `${firstName[0]}${lastName[0]}`.toUpperCase();\n    } else if (firstName) {\n      return firstName.substring(0, 2).toUpperCase();\n    } else if (user.email) {\n      return user.email.substring(0, 2).toUpperCase();\n    }\n    \n    return 'U';\n  },\n};\n\n// Auth utilities\nexport const authUtils = {\n  // Login user\n  login: async (user: User, tokens: { accessToken: string; refreshToken: string }, rememberMe: boolean = false): Promise<void> => {\n    tokenManager.setAccessToken(tokens.accessToken, rememberMe);\n    tokenManager.setRefreshToken(tokens.refreshToken, rememberMe);\n    userManager.setCurrentUser(user);\n  },\n\n  // Logout user\n  logout: (): void => {\n    tokenManager.clearTokens();\n    userManager.clearCurrentUser();\n    \n    // Redirect to login page\n    if (typeof window !== 'undefined') {\n      window.location.href = '/login';\n    }\n  },\n\n  // Check if user is admin\n  isAdmin: (): boolean => {\n    return userManager.hasRole(UserRole.ADMIN);\n  },\n\n  // Check if user is sub-admin\n  isSubAdmin: (): boolean => {\n    return userManager.hasRole(UserRole.SUBADMIN);\n  },\n\n  // Check if user is sales\n  isSales: (): boolean => {\n    return userManager.hasRole(UserRole.SALES);\n  },\n\n  // Check if user can manage users\n  canManageUsers: (): boolean => {\n    return userManager.hasAnyRole([UserRole.ADMIN, UserRole.SUBADMIN]);\n  },\n\n  // Check if user can manage properties\n  canManageProperties: (): boolean => {\n    return userManager.hasAnyRole([UserRole.ADMIN, UserRole.SUBADMIN]);\n  },\n\n  // Check if user can manage leads\n  canManageLeads: (): boolean => {\n    return userManager.hasAnyRole([UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES]);\n  },\n\n  // Check if user can view analytics\n  canViewAnalytics: (): boolean => {\n    return userManager.hasAnyRole([UserRole.ADMIN, UserRole.SUBADMIN]);\n  },\n\n  // Get allowed routes for user\n  getAllowedRoutes: (): string[] => {\n    const user = userManager.getCurrentUser();\n    if (!user) return ['/login'];\n\n    const baseRoutes = ['/dashboard', '/profile', '/settings'];\n\n    switch (user.role) {\n      case UserRole.ADMIN:\n        return [\n          ...baseRoutes,\n          '/users',\n          '/properties',\n          '/leads',\n          '/transactions',\n          '/analytics',\n          '/system',\n        ];\n      \n      case UserRole.SUBADMIN:\n        return [\n          ...baseRoutes,\n          '/users',\n          '/properties',\n          '/leads',\n          '/transactions',\n          '/analytics',\n        ];\n      \n      case UserRole.SALES:\n        return [\n          ...baseRoutes,\n          '/leads',\n          '/customers',\n        ];\n      \n      default:\n        return baseRoutes;\n    }\n  },\n\n  // Check if route is allowed for user\n  isRouteAllowed: (route: string): boolean => {\n    const allowedRoutes = authUtils.getAllowedRoutes();\n    return allowedRoutes.some(allowedRoute => route.startsWith(allowedRoute));\n  },\n};\n\n// JWT utilities\nexport const jwtUtils = {\n  // Decode JWT token (without verification)\n  decode: (token: string): any => {\n    try {\n      const base64Url = token.split('.')[1];\n      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n      const jsonPayload = decodeURIComponent(\n        atob(base64)\n          .split('')\n          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))\n          .join('')\n      );\n      return JSON.parse(jsonPayload);\n    } catch (error) {\n      console.error('Error decoding JWT:', error);\n      return null;\n    }\n  },\n\n  // Check if token is expired\n  isExpired: (token: string): boolean => {\n    try {\n      const decoded = jwtUtils.decode(token);\n      if (!decoded || !decoded.exp) return true;\n      \n      const currentTime = Date.now() / 1000;\n      return decoded.exp < currentTime;\n    } catch (error) {\n      return true;\n    }\n  },\n\n  // Get token expiration time\n  getExpirationTime: (token: string): Date | null => {\n    try {\n      const decoded = jwtUtils.decode(token);\n      if (!decoded || !decoded.exp) return null;\n      \n      return new Date(decoded.exp * 1000);\n    } catch (error) {\n      return null;\n    }\n  },\n\n  // Get time until token expires (in minutes)\n  getTimeUntilExpiry: (token: string): number => {\n    try {\n      const expirationTime = jwtUtils.getExpirationTime(token);\n      if (!expirationTime) return 0;\n      \n      const currentTime = new Date();\n      const timeDiff = expirationTime.getTime() - currentTime.getTime();\n      \n      return Math.max(0, Math.floor(timeDiff / (1000 * 60))); // Convert to minutes\n    } catch (error) {\n      return 0;\n    }\n  },\n};\n\n// Session management\nexport const sessionManager = {\n  // Check session validity\n  isSessionValid: (): boolean => {\n    const token = tokenManager.getAccessToken();\n    if (!token) return false;\n    \n    return !jwtUtils.isExpired(token);\n  },\n\n  // Get session info\n  getSessionInfo: () => {\n    const token = tokenManager.getAccessToken();\n    const user = userManager.getCurrentUser();\n    \n    if (!token || !user) {\n      return {\n        isValid: false,\n        user: null,\n        expiresAt: null,\n        timeUntilExpiry: 0,\n      };\n    }\n\n    const expiresAt = jwtUtils.getExpirationTime(token);\n    const timeUntilExpiry = jwtUtils.getTimeUntilExpiry(token);\n\n    return {\n      isValid: !jwtUtils.isExpired(token),\n      user,\n      expiresAt,\n      timeUntilExpiry,\n    };\n  },\n\n  // Extend session (refresh token)\n  extendSession: async (): Promise<boolean> => {\n    try {\n      const refreshToken = tokenManager.getRefreshToken();\n      if (!refreshToken) return false;\n\n      // This would typically call the refresh endpoint\n      // Implementation depends on your API\n      return true;\n    } catch (error) {\n      console.error('Error extending session:', error);\n      return false;\n    }\n  },\n};\n\nexport default {\n  tokenManager,\n  userManager,\n  authUtils,\n  jwtUtils,\n  sessionManager,\n};\n"], "names": [], "mappings": ";;;;;;;;AAkCyB;AAlCzB;AACA;;;AAGO,MAAM,eAAe;IAC1B,mBAAmB;IACnB,gBAAgB;QACd,8DAA8D;QAC9D,0EAA0E;QAE1E,2EAA2E;QAC3E,MAAM,cAAc,iNAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAChC,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,yDAAyD;QACzD,wCAAmC;YACjC,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,qEAAqE;QACrE,kDAAkD;QAClD,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,mBAAmB;IACnB,gBAAgB,CAAC,OAAe,aAAsB,KAAK;QACzD,MAAM,eAAe,oDAAyB;QAE9C,IAAI,YAAY;YACd,yBAAyB;YACzB,iNAAA,CAAA,UAAO,CAAC,GAAG,CAAC,eAAe,OAAO;gBAChC,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,MAAM,IAAI,uCAAuC;YACnD;QACF,OAAO;YACL,iBAAiB;YACjB,iNAAA,CAAA,UAAO,CAAC,GAAG,CAAC,eAAe,OAAO;gBAChC,QAAQ;gBACR,UAAU;gBACV,MAAM,IAAI,uCAAuC;YACnD;QACF;QAEA,uCAAuC;QACvC,wCAAmC;YACjC,aAAa,OAAO,CAAC,eAAe;QACtC;QAEA,+BAA+B;QAC/B,wCAA4C;YAC1C,WAAW;gBACT,MAAM,cAAc,iNAAA,CAAA,UAAO,CAAC,GAAG,CAAC;gBAChC,QAAQ,GAAG,CAAC,+BAA+B;oBACzC,aAAa,MAAM,MAAM;oBACzB,cAAc,CAAC,CAAC;oBAChB,aAAa,cAAc,GAAG,YAAY,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;gBACpE;YACF,GAAG;QACL;IACF;IAEA,oBAAoB;IACpB,iBAAiB;QACf,OAAO,iNAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC;IAEA,oBAAoB;IACpB,iBAAiB,CAAC,OAAe,aAAsB,KAAK;QAC1D,IAAI,YAAY;YACd,iNAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB,OAAO;gBAAE,SAAS;gBAAI,QAAQ;gBAAM,UAAU;YAAS;QACrF,OAAO;YACL,iNAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB,OAAO;gBAAE,QAAQ;gBAAM,UAAU;YAAS;QACxE;IACF;IAEA,iDAAiD;IACjD,oBAAoB;QAClB,MAAM,cAAc,iNAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAChC,MAAM,aAAa,uCAAgC,aAAa,OAAO,CAAC;QAExE,QAAQ,GAAG,CAAC,0BAA0B;YACpC,aAAa,cAAc,GAAG,YAAY,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;YAClE,YAAY,aAAa,GAAG,WAAW,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;YAC/D,cAAc,CAAC,CAAC;YAChB,aAAa,CAAC,CAAC;QACjB;QAEA,OAAO,CAAC,CAAC,CAAC,eAAe,UAAU;IACrC;IAEA,mBAAmB;IACnB,aAAa;QACX,iNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,iNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QAEf,yCAAyC;QACzC,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,iCAAiC;IACjC,iBAAiB;QACf,MAAM,QAAQ,aAAa,cAAc;QACzC,OAAO,CAAC,CAAC;IACX;AACF;AAGO,MAAM,cAAc;IACzB,mBAAmB;IACnB,gBAAgB;QACd,IAAI;YACF,MAAM,UAAU,aAAa,OAAO,CAAC;YACrC,OAAO,UAAU,KAAK,KAAK,CAAC,WAAW;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEA,mBAAmB;IACnB,gBAAgB,CAAC;QACf,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA,qBAAqB;IACrB,kBAAkB;QAChB,aAAa,UAAU,CAAC;IAC1B;IAEA,kBAAkB;IAClB,SAAS,CAAC;QACR,MAAM,OAAO,YAAY,cAAc;QACvC,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,gBAAgB;YACpB,CAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,EAAE;YAClB,CAAC,wHAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC,EAAE;YACrB,CAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,EAAE;YAClB,CAAC,wHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,EAAE;QACnB;QAEA,MAAM,YAAY,aAAa,CAAC,KAAK,IAAI,CAAC;QAC1C,MAAM,gBAAgB,aAAa,CAAC,aAAa;QAEjD,OAAO,aAAa;IACtB;IAEA,8CAA8C;IAC9C,YAAY,CAAC;QACX,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,YAAY,OAAO,CAAC;IAChD;IAEA,wBAAwB;IACxB,gBAAgB;QACd,MAAM,OAAO,YAAY,cAAc;QACvC,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,CAAC,IAAI,MAAM,KAAK,KAAK;IAClE;IAEA,oBAAoB;IACpB,aAAa;QACX,MAAM,OAAO,YAAY,cAAc;QACvC,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,YAAY,KAAK,SAAS,IAAI;QACpC,MAAM,WAAW,KAAK,QAAQ,IAAI;QAElC,IAAI,aAAa,UAAU;YACzB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW;QACpD,OAAO,IAAI,WAAW;YACpB,OAAO,UAAU,SAAS,CAAC,GAAG,GAAG,WAAW;QAC9C,OAAO,IAAI,KAAK,KAAK,EAAE;YACrB,OAAO,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,WAAW;QAC/C;QAEA,OAAO;IACT;AACF;AAGO,MAAM,YAAY;IACvB,aAAa;IACb,OAAO,OAAO,MAAY,QAAuD,aAAsB,KAAK;QAC1G,aAAa,cAAc,CAAC,OAAO,WAAW,EAAE;QAChD,aAAa,eAAe,CAAC,OAAO,YAAY,EAAE;QAClD,YAAY,cAAc,CAAC;IAC7B;IAEA,cAAc;IACd,QAAQ;QACN,aAAa,WAAW;QACxB,YAAY,gBAAgB;QAE5B,yBAAyB;QACzB,wCAAmC;YACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,yBAAyB;IACzB,SAAS;QACP,OAAO,YAAY,OAAO,CAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;IAC3C;IAEA,6BAA6B;IAC7B,YAAY;QACV,OAAO,YAAY,OAAO,CAAC,wHAAA,CAAA,WAAQ,CAAC,QAAQ;IAC9C;IAEA,yBAAyB;IACzB,SAAS;QACP,OAAO,YAAY,OAAO,CAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;IAC3C;IAEA,iCAAiC;IACjC,gBAAgB;QACd,OAAO,YAAY,UAAU,CAAC;YAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;SAAC;IACnE;IAEA,sCAAsC;IACtC,qBAAqB;QACnB,OAAO,YAAY,UAAU,CAAC;YAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;SAAC;IACnE;IAEA,iCAAiC;IACjC,gBAAgB;QACd,OAAO,YAAY,UAAU,CAAC;YAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;YAAE,wHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;IACnF;IAEA,mCAAmC;IACnC,kBAAkB;QAChB,OAAO,YAAY,UAAU,CAAC;YAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;SAAC;IACnE;IAEA,8BAA8B;IAC9B,kBAAkB;QAChB,MAAM,OAAO,YAAY,cAAc;QACvC,IAAI,CAAC,MAAM,OAAO;YAAC;SAAS;QAE5B,MAAM,aAAa;YAAC;YAAc;YAAY;SAAY;QAE1D,OAAQ,KAAK,IAAI;YACf,KAAK,wHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;uBACF;oBACH;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YAEH,KAAK,wHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;uBACF;oBACH;oBACA;oBACA;oBACA;oBACA;iBACD;YAEH,KAAK,wHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;uBACF;oBACH;oBACA;iBACD;YAEH;gBACE,OAAO;QACX;IACF;IAEA,qCAAqC;IACrC,gBAAgB,CAAC;QACf,MAAM,gBAAgB,UAAU,gBAAgB;QAChD,OAAO,cAAc,IAAI,CAAC,CAAA,eAAgB,MAAM,UAAU,CAAC;IAC7D;AACF;AAGO,MAAM,WAAW;IACtB,0CAA0C;IAC1C,QAAQ,CAAC;QACP,IAAI;YACF,MAAM,YAAY,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,SAAS,UAAU,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;YAC1D,MAAM,cAAc,mBAClB,KAAK,QACF,KAAK,CAAC,IACN,GAAG,CAAC,CAAA,IAAK,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,IAC5D,IAAI,CAAC;YAEV,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,WAAW,CAAC;QACV,IAAI;YACF,MAAM,UAAU,SAAS,MAAM,CAAC;YAChC,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,EAAE,OAAO;YAErC,MAAM,cAAc,KAAK,GAAG,KAAK;YACjC,OAAO,QAAQ,GAAG,GAAG;QACvB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,mBAAmB,CAAC;QAClB,IAAI;YACF,MAAM,UAAU,SAAS,MAAM,CAAC;YAChC,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,EAAE,OAAO;YAErC,OAAO,IAAI,KAAK,QAAQ,GAAG,GAAG;QAChC,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,4CAA4C;IAC5C,oBAAoB,CAAC;QACnB,IAAI;YACF,MAAM,iBAAiB,SAAS,iBAAiB,CAAC;YAClD,IAAI,CAAC,gBAAgB,OAAO;YAE5B,MAAM,cAAc,IAAI;YACxB,MAAM,WAAW,eAAe,OAAO,KAAK,YAAY,OAAO;YAE/D,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,qBAAqB;QAC/E,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,yBAAyB;IACzB,gBAAgB;QACd,MAAM,QAAQ,aAAa,cAAc;QACzC,IAAI,CAAC,OAAO,OAAO;QAEnB,OAAO,CAAC,SAAS,SAAS,CAAC;IAC7B;IAEA,mBAAmB;IACnB,gBAAgB;QACd,MAAM,QAAQ,aAAa,cAAc;QACzC,MAAM,OAAO,YAAY,cAAc;QAEvC,IAAI,CAAC,SAAS,CAAC,MAAM;YACnB,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,WAAW;gBACX,iBAAiB;YACnB;QACF;QAEA,MAAM,YAAY,SAAS,iBAAiB,CAAC;QAC7C,MAAM,kBAAkB,SAAS,kBAAkB,CAAC;QAEpD,OAAO;YACL,SAAS,CAAC,SAAS,SAAS,CAAC;YAC7B;YACA;YACA;QACF;IACF;IAEA,iCAAiC;IACjC,eAAe;QACb,IAAI;YACF,MAAM,eAAe,aAAa,eAAe;YACjD,IAAI,CAAC,cAAc,OAAO;YAE1B,iDAAiD;YACjD,qCAAqC;YACrC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;AACF;uCAEe;IACb;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/slices/authSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'\nimport { User, LoginCredentials, AuthResponse } from '@/types'\nimport { authAPI } from '@/utils/api'\nimport { tokenManager, userManager } from '@/utils/auth'\n\ninterface AuthState {\n  user: User | null\n  isAuthenticated: boolean\n  loading: boolean\n  error: string | null\n  sessionExpiry: string | null\n}\n\nconst initialState: AuthState = {\n  user: null,\n  isAuthenticated: false,\n  loading: false,\n  error: null,\n  sessionExpiry: null,\n}\n\n// Async thunks\nexport const loginAsync = createAsyncThunk(\n  'auth/login',\n  async (credentials: LoginCredentials, { rejectWithValue }) => {\n    try {\n      const response: AuthResponse = await authAPI.login(credentials)\n\n      // Handle both backend and mock responses\n      let responseData\n      if (response.success && response.data) {\n        responseData = response.data\n      } else if (response.data && response.data.user) {\n        // Handle direct response format\n        responseData = response.data\n      } else {\n        return rejectWithValue(response.message || 'Login failed')\n      }\n\n      const { user, accessToken, refreshToken } = responseData\n\n      if (!user || !accessToken) {\n        return rejectWithValue('Invalid response format')\n      }\n\n      // Backend sets HttpOnly cookies automatically\n      // We only store user data locally (tokens are in HttpOnly cookies)\n      try {\n        userManager.setCurrentUser(user)\n\n        console.log('✅ Login successful, backend set HttpOnly cookies:', {\n          user: user.email,\n          role: user.role,\n          note: 'Tokens stored in HttpOnly cookies (not accessible from JS)'\n        })\n\n        return { user, accessToken, refreshToken }\n      } catch (storageError) {\n        console.error('User storage failed:', storageError)\n        return rejectWithValue('Failed to store user data')\n      }\n\n    } catch (error: any) {\n      console.error('Login error:', error)\n      return rejectWithValue(error.response?.data?.message || error.message || 'Login failed')\n    }\n  }\n)\n\nexport const logoutAsync = createAsyncThunk(\n  'auth/logout',\n  async (_, { rejectWithValue }) => {\n    try {\n      await authAPI.logout()\n    } catch (error: any) {\n      console.error('Logout API error:', error)\n    } finally {\n      // Always clear local state\n      tokenManager.clearTokens()\n      userManager.clearCurrentUser()\n    }\n  }\n)\n\nexport const refreshUserAsync = createAsyncThunk(\n  'auth/refreshUser',\n  async (_, { rejectWithValue }) => {\n    try {\n      const response = await authAPI.getProfile()\n      \n      if (response.success) {\n        userManager.setCurrentUser(response.data)\n        return response.data\n      } else {\n        return rejectWithValue(response.message)\n      }\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to refresh user')\n    }\n  }\n)\n\nexport const checkAuthAsync = createAsyncThunk(\n  'auth/checkAuth',\n  async (_, { rejectWithValue }) => {\n    try {\n      console.log('🔍 Checking auth with backend (HttpOnly cookies)...')\n\n      // Direct backend call - backend will check HttpOnly cookies\n      // No need to check localStorage/cookies from frontend\n      const response = await authAPI.getProfile()\n\n      console.log('✅ Backend auth response:', response)\n\n      if (response.success && response.data) {\n        // Store user data (not tokens, as they're in HttpOnly cookies)\n        userManager.setCurrentUser(response.data)\n        return response.data\n      } else {\n        console.log('❌ Backend auth failed:', response.message)\n        // Clear any local user data (tokens are HttpOnly, can't clear from frontend)\n        userManager.clearCurrentUser()\n        return rejectWithValue(response.message || 'Authentication failed')\n      }\n    } catch (error: any) {\n      console.error('❌ Auth check error:', error.response?.data || error.message)\n\n      // Clear local user data on error\n      userManager.clearCurrentUser()\n\n      // Return appropriate error message\n      const errorMessage = error.response?.data?.message ||\n                          error.message ||\n                          'Authentication check failed'\n\n      return rejectWithValue(errorMessage)\n    }\n  }\n)\n\n// Auth slice\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null\n    },\n    setUser: (state, action: PayloadAction<User>) => {\n      state.user = action.payload\n      state.isAuthenticated = true\n      userManager.setCurrentUser(action.payload)\n    },\n    clearAuth: (state) => {\n      state.user = null\n      state.isAuthenticated = false\n      state.error = null\n      state.sessionExpiry = null\n      tokenManager.clearTokens()\n      userManager.clearCurrentUser()\n    },\n    setSessionExpiry: (state, action: PayloadAction<string>) => {\n      state.sessionExpiry = action.payload\n    },\n    updateUserProfile: (state, action: PayloadAction<Partial<User>>) => {\n      if (state.user) {\n        state.user = { ...state.user, ...action.payload }\n        userManager.setCurrentUser(state.user)\n      }\n    },\n  },\n  extraReducers: (builder) => {\n    // Login\n    builder\n      .addCase(loginAsync.pending, (state) => {\n        state.loading = true\n        state.error = null\n      })\n      .addCase(loginAsync.fulfilled, (state, action) => {\n        state.loading = false\n        // Handle nested user structure from backend\n        state.user = action.payload.user || action.payload\n        state.isAuthenticated = true\n        state.error = null\n      })\n      .addCase(loginAsync.rejected, (state, action) => {\n        state.loading = false\n        state.error = action.payload as string\n        state.isAuthenticated = false\n        state.user = null\n      })\n\n    // Logout\n    builder\n      .addCase(logoutAsync.pending, (state) => {\n        state.loading = true\n      })\n      .addCase(logoutAsync.fulfilled, (state) => {\n        state.loading = false\n        state.user = null\n        state.isAuthenticated = false\n        state.error = null\n        state.sessionExpiry = null\n      })\n      .addCase(logoutAsync.rejected, (state) => {\n        state.loading = false\n        state.user = null\n        state.isAuthenticated = false\n        state.error = null\n        state.sessionExpiry = null\n      })\n\n    // Refresh user\n    builder\n      .addCase(refreshUserAsync.pending, (state) => {\n        state.loading = true\n      })\n      .addCase(refreshUserAsync.fulfilled, (state, action) => {\n        state.loading = false\n        // Handle nested user structure from backend\n        state.user = action.payload.user || action.payload\n        state.error = null\n      })\n      .addCase(refreshUserAsync.rejected, (state, action) => {\n        state.loading = false\n        state.error = action.payload as string\n      })\n\n    // Check auth\n    builder\n      .addCase(checkAuthAsync.pending, (state) => {\n        state.loading = true\n      })\n      .addCase(checkAuthAsync.fulfilled, (state, action) => {\n        state.loading = false\n        // Handle nested user structure from backend\n        state.user = action.payload.user || action.payload\n        state.isAuthenticated = true\n        state.error = null\n      })\n      .addCase(checkAuthAsync.rejected, (state, action) => {\n        state.loading = false\n        state.error = action.payload as string\n        state.isAuthenticated = false\n        state.user = null\n      })\n  },\n})\n\n// Actions\nexport const {\n  clearError,\n  setUser,\n  clearAuth,\n  setSessionExpiry,\n  updateUserProfile\n} = authSlice.actions\n\n// Additional exports for compatibility\nexport const clearUser = clearAuth\nexport const setLoading = (loading: boolean) => (dispatch: any) => {\n  // This is handled by async thunks, but we can add a simple action if needed\n}\n\n// Selectors\nexport const selectAuth = (state: { auth: AuthState }) => state.auth\nexport const selectUser = (state: { auth: AuthState }) => state.auth.user\nexport const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated\nexport const selectAuthLoading = (state: { auth: AuthState }) => state.auth.loading\nexport const selectAuthError = (state: { auth: AuthState }) => state.auth.error\n\nexport default authSlice.reducer\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAEA;AACA;;;;AAUA,MAAM,eAA0B;IAC9B,MAAM;IACN,iBAAiB;IACjB,SAAS;IACT,OAAO;IACP,eAAe;AACjB;AAGO,MAAM,aAAa,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EACvC,cACA,OAAO,aAA+B,EAAE,eAAe,EAAE;IACvD,IAAI;QACF,MAAM,WAAyB,MAAM,sHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAEnD,yCAAyC;QACzC,IAAI;QACJ,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,eAAe,SAAS,IAAI;QAC9B,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;YAC9C,gCAAgC;YAChC,eAAe,SAAS,IAAI;QAC9B,OAAO;YACL,OAAO,gBAAgB,SAAS,OAAO,IAAI;QAC7C;QAEA,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG;QAE5C,IAAI,CAAC,QAAQ,CAAC,aAAa;YACzB,OAAO,gBAAgB;QACzB;QAEA,8CAA8C;QAC9C,mEAAmE;QACnE,IAAI;YACF,uHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;YAE3B,QAAQ,GAAG,CAAC,qDAAqD;gBAC/D,MAAM,KAAK,KAAK;gBAChB,MAAM,KAAK,IAAI;gBACf,MAAM;YACR;YAEA,OAAO;gBAAE;gBAAM;gBAAa;YAAa;QAC3C,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gBAAgB;QACzB;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;IAC3E;AACF;AAGK,MAAM,cAAc,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EACxC,eACA,OAAO,GAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,MAAM,sHAAA,CAAA,UAAO,CAAC,MAAM;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,qBAAqB;IACrC,SAAU;QACR,2BAA2B;QAC3B,uHAAA,CAAA,eAAY,CAAC,WAAW;QACxB,uHAAA,CAAA,cAAW,CAAC,gBAAgB;IAC9B;AACF;AAGK,MAAM,mBAAmB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAC7C,oBACA,OAAO,GAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,UAAO,CAAC,UAAU;QAEzC,IAAI,SAAS,OAAO,EAAE;YACpB,uHAAA,CAAA,cAAW,CAAC,cAAc,CAAC,SAAS,IAAI;YACxC,OAAO,SAAS,IAAI;QACtB,OAAO;YACL,OAAO,gBAAgB,SAAS,OAAO;QACzC;IACF,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGK,MAAM,iBAAiB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAC3C,kBACA,OAAO,GAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,4DAA4D;QAC5D,sDAAsD;QACtD,MAAM,WAAW,MAAM,sHAAA,CAAA,UAAO,CAAC,UAAU;QAEzC,QAAQ,GAAG,CAAC,4BAA4B;QAExC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,+DAA+D;YAC/D,uHAAA,CAAA,cAAW,CAAC,cAAc,CAAC,SAAS,IAAI;YACxC,OAAO,SAAS,IAAI;QACtB,OAAO;YACL,QAAQ,GAAG,CAAC,0BAA0B,SAAS,OAAO;YACtD,6EAA6E;YAC7E,uHAAA,CAAA,cAAW,CAAC,gBAAgB;YAC5B,OAAO,gBAAgB,SAAS,OAAO,IAAI;QAC7C;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,uBAAuB,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;QAE1E,iCAAiC;QACjC,uHAAA,CAAA,cAAW,CAAC,gBAAgB;QAE5B,mCAAmC;QACnC,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACvB,MAAM,OAAO,IACb;QAEpB,OAAO,gBAAgB;IACzB;AACF;AAGF,aAAa;AACb,MAAM,YAAY,CAAA,GAAA,qSAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QACA,SAAS,CAAC,OAAO;YACf,MAAM,IAAI,GAAG,OAAO,OAAO;YAC3B,MAAM,eAAe,GAAG;YACxB,uHAAA,CAAA,cAAW,CAAC,cAAc,CAAC,OAAO,OAAO;QAC3C;QACA,WAAW,CAAC;YACV,MAAM,IAAI,GAAG;YACb,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;YACd,MAAM,aAAa,GAAG;YACtB,uHAAA,CAAA,eAAY,CAAC,WAAW;YACxB,uHAAA,CAAA,cAAW,CAAC,gBAAgB;QAC9B;QACA,kBAAkB,CAAC,OAAO;YACxB,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC;QACA,mBAAmB,CAAC,OAAO;YACzB,IAAI,MAAM,IAAI,EAAE;gBACd,MAAM,IAAI,GAAG;oBAAE,GAAG,MAAM,IAAI;oBAAE,GAAG,OAAO,OAAO;gBAAC;gBAChD,uHAAA,CAAA,cAAW,CAAC,cAAc,CAAC,MAAM,IAAI;YACvC;QACF;IACF;IACA,eAAe,CAAC;QACd,QAAQ;QACR,QACG,OAAO,CAAC,WAAW,OAAO,EAAE,CAAC;YAC5B,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,WAAW,SAAS,EAAE,CAAC,OAAO;YACrC,MAAM,OAAO,GAAG;YAChB,4CAA4C;YAC5C,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI,IAAI,OAAO,OAAO;YAClD,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,WAAW,QAAQ,EAAE,CAAC,OAAO;YACpC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,eAAe,GAAG;YACxB,MAAM,IAAI,GAAG;QACf;QAEF,SAAS;QACT,QACG,OAAO,CAAC,YAAY,OAAO,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG;QAClB,GACC,OAAO,CAAC,YAAY,SAAS,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG;YAChB,MAAM,IAAI,GAAG;YACb,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;YACd,MAAM,aAAa,GAAG;QACxB,GACC,OAAO,CAAC,YAAY,QAAQ,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAG;YAChB,MAAM,IAAI,GAAG;YACb,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;YACd,MAAM,aAAa,GAAG;QACxB;QAEF,eAAe;QACf,QACG,OAAO,CAAC,iBAAiB,OAAO,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG;QAClB,GACC,OAAO,CAAC,iBAAiB,SAAS,EAAE,CAAC,OAAO;YAC3C,MAAM,OAAO,GAAG;YAChB,4CAA4C;YAC5C,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI,IAAI,OAAO,OAAO;YAClD,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,iBAAiB,QAAQ,EAAE,CAAC,OAAO;YAC1C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEF,aAAa;QACb,QACG,OAAO,CAAC,eAAe,OAAO,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG;QAClB,GACC,OAAO,CAAC,eAAe,SAAS,EAAE,CAAC,OAAO;YACzC,MAAM,OAAO,GAAG;YAChB,4CAA4C;YAC5C,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI,IAAI,OAAO,OAAO;YAClD,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,eAAe,QAAQ,EAAE,CAAC,OAAO;YACxC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,eAAe,GAAG;YACxB,MAAM,IAAI,GAAG;QACf;IACJ;AACF;AAGO,MAAM,EACX,UAAU,EACV,OAAO,EACP,SAAS,EACT,gBAAgB,EAChB,iBAAiB,EAClB,GAAG,UAAU,OAAO;AAGd,MAAM,YAAY;AAClB,MAAM,aAAa,CAAC,UAAqB,CAAC;IAC/C,4EAA4E;IAC9E;AAGO,MAAM,aAAa,CAAC,QAA+B,MAAM,IAAI;AAC7D,MAAM,aAAa,CAAC,QAA+B,MAAM,IAAI,CAAC,IAAI;AAClE,MAAM,wBAAwB,CAAC,QAA+B,MAAM,IAAI,CAAC,eAAe;AACxF,MAAM,oBAAoB,CAAC,QAA+B,MAAM,IAAI,CAAC,OAAO;AAC5E,MAAM,kBAAkB,CAAC,QAA+B,MAAM,IAAI,CAAC,KAAK;uCAEhE,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/slices/uiSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit'\n\ninterface UIState {\n  sidebarOpen: boolean\n  sidebarCollapsed: boolean\n  theme: 'light' | 'dark' | 'system'\n  notifications: Notification[]\n  loading: {\n    global: boolean\n    [key: string]: boolean\n  }\n  modals: {\n    [key: string]: boolean\n  }\n  breadcrumbs: BreadcrumbItem[]\n  pageTitle: string\n}\n\ninterface Notification {\n  id: string\n  type: 'success' | 'error' | 'warning' | 'info'\n  title: string\n  message: string\n  timestamp: string\n  read: boolean\n  action?: {\n    label: string\n    onClick: () => void\n  }\n}\n\ninterface BreadcrumbItem {\n  label: string\n  href?: string\n  current?: boolean\n}\n\nconst initialState: UIState = {\n  sidebarOpen: true,\n  sidebarCollapsed: false,\n  theme: 'light',\n  notifications: [],\n  loading: {\n    global: false,\n  },\n  modals: {},\n  breadcrumbs: [],\n  pageTitle: 'Dashboard',\n}\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    // Sidebar\n    toggleSidebar: (state) => {\n      state.sidebarOpen = !state.sidebarOpen\n    },\n    setSidebarOpen: (state, action: PayloadAction<boolean>) => {\n      state.sidebarOpen = action.payload\n    },\n    toggleSidebarCollapsed: (state) => {\n      state.sidebarCollapsed = !state.sidebarCollapsed\n    },\n    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {\n      state.sidebarCollapsed = action.payload\n    },\n\n    // Theme\n    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {\n      state.theme = action.payload\n    },\n\n    // Notifications\n    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp' | 'read'>>) => {\n      const notification: Notification = {\n        ...action.payload,\n        id: Date.now().toString(),\n        timestamp: new Date().toISOString(),\n        read: false,\n      }\n      state.notifications.unshift(notification)\n      \n      // Keep only last 50 notifications\n      if (state.notifications.length > 50) {\n        state.notifications = state.notifications.slice(0, 50)\n      }\n    },\n    markNotificationRead: (state, action: PayloadAction<string>) => {\n      const notification = state.notifications.find(n => n.id === action.payload)\n      if (notification) {\n        notification.read = true\n      }\n    },\n    markAllNotificationsRead: (state) => {\n      state.notifications.forEach(notification => {\n        notification.read = true\n      })\n    },\n    removeNotification: (state, action: PayloadAction<string>) => {\n      state.notifications = state.notifications.filter(n => n.id !== action.payload)\n    },\n    clearNotifications: (state) => {\n      state.notifications = []\n    },\n\n    // Loading\n    setGlobalLoading: (state, action: PayloadAction<boolean>) => {\n      state.loading.global = action.payload\n    },\n    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {\n      state.loading[action.payload.key] = action.payload.loading\n    },\n    clearLoading: (state, action: PayloadAction<string>) => {\n      delete state.loading[action.payload]\n    },\n\n    // Modals\n    openModal: (state, action: PayloadAction<string>) => {\n      state.modals[action.payload] = true\n    },\n    closeModal: (state, action: PayloadAction<string>) => {\n      state.modals[action.payload] = false\n    },\n    toggleModal: (state, action: PayloadAction<string>) => {\n      state.modals[action.payload] = !state.modals[action.payload]\n    },\n    closeAllModals: (state) => {\n      Object.keys(state.modals).forEach(key => {\n        state.modals[key] = false\n      })\n    },\n\n    // Breadcrumbs\n    setBreadcrumbs: (state, action: PayloadAction<BreadcrumbItem[]>) => {\n      state.breadcrumbs = action.payload\n    },\n    addBreadcrumb: (state, action: PayloadAction<BreadcrumbItem>) => {\n      // Mark previous breadcrumbs as not current\n      state.breadcrumbs.forEach(item => {\n        item.current = false\n      })\n      \n      // Add new breadcrumb\n      state.breadcrumbs.push({ ...action.payload, current: true })\n    },\n    clearBreadcrumbs: (state) => {\n      state.breadcrumbs = []\n    },\n\n    // Page title\n    setPageTitle: (state, action: PayloadAction<string>) => {\n      state.pageTitle = action.payload\n    },\n\n    // Reset UI state\n    resetUI: (state) => {\n      return {\n        ...initialState,\n        theme: state.theme, // Preserve theme\n        sidebarCollapsed: state.sidebarCollapsed, // Preserve sidebar state\n      }\n    },\n  },\n})\n\n// Actions\nexport const {\n  toggleSidebar,\n  setSidebarOpen,\n  toggleSidebarCollapsed,\n  setSidebarCollapsed,\n  setTheme,\n  addNotification,\n  markNotificationRead,\n  markAllNotificationsRead,\n  removeNotification,\n  clearNotifications,\n  setGlobalLoading,\n  setLoading,\n  clearLoading,\n  openModal,\n  closeModal,\n  toggleModal,\n  closeAllModals,\n  setBreadcrumbs,\n  addBreadcrumb,\n  clearBreadcrumbs,\n  setPageTitle,\n  resetUI,\n} = uiSlice.actions\n\n// Selectors\nexport const selectUI = (state: { ui: UIState }) => state.ui\nexport const selectSidebarOpen = (state: { ui: UIState }) => state.ui.sidebarOpen\nexport const selectSidebarCollapsed = (state: { ui: UIState }) => state.ui.sidebarCollapsed\nexport const selectTheme = (state: { ui: UIState }) => state.ui.theme\nexport const selectNotifications = (state: { ui: UIState }) => state.ui.notifications\nexport const selectUnreadNotifications = (state: { ui: UIState }) => \n  state.ui.notifications.filter(n => !n.read)\nexport const selectGlobalLoading = (state: { ui: UIState }) => state.ui.loading.global\nexport const selectLoading = (key: string) => (state: { ui: UIState }) => state.ui.loading[key] || false\nexport const selectModal = (key: string) => (state: { ui: UIState }) => state.ui.modals[key] || false\nexport const selectBreadcrumbs = (state: { ui: UIState }) => state.ui.breadcrumbs\nexport const selectPageTitle = (state: { ui: UIState }) => state.ui.pageTitle\n\nexport default uiSlice.reducer\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAqCA,MAAM,eAAwB;IAC5B,aAAa;IACb,kBAAkB;IAClB,OAAO;IACP,eAAe,EAAE;IACjB,SAAS;QACP,QAAQ;IACV;IACA,QAAQ,CAAC;IACT,aAAa,EAAE;IACf,WAAW;AACb;AAEA,MAAM,UAAU,CAAA,GAAA,qSAAA,CAAA,cAAW,AAAD,EAAE;IAC1B,MAAM;IACN;IACA,UAAU;QACR,UAAU;QACV,eAAe,CAAC;YACd,MAAM,WAAW,GAAG,CAAC,MAAM,WAAW;QACxC;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,wBAAwB,CAAC;YACvB,MAAM,gBAAgB,GAAG,CAAC,MAAM,gBAAgB;QAClD;QACA,qBAAqB,CAAC,OAAO;YAC3B,MAAM,gBAAgB,GAAG,OAAO,OAAO;QACzC;QAEA,QAAQ;QACR,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEA,gBAAgB;QAChB,iBAAiB,CAAC,OAAO;YACvB,MAAM,eAA6B;gBACjC,GAAG,OAAO,OAAO;gBACjB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,WAAW,IAAI,OAAO,WAAW;gBACjC,MAAM;YACR;YACA,MAAM,aAAa,CAAC,OAAO,CAAC;YAE5B,kCAAkC;YAClC,IAAI,MAAM,aAAa,CAAC,MAAM,GAAG,IAAI;gBACnC,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,KAAK,CAAC,GAAG;YACrD;QACF;QACA,sBAAsB,CAAC,OAAO;YAC5B,MAAM,eAAe,MAAM,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,OAAO;YAC1E,IAAI,cAAc;gBAChB,aAAa,IAAI,GAAG;YACtB;QACF;QACA,0BAA0B,CAAC;YACzB,MAAM,aAAa,CAAC,OAAO,CAAC,CAAA;gBAC1B,aAAa,IAAI,GAAG;YACtB;QACF;QACA,oBAAoB,CAAC,OAAO;YAC1B,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,OAAO;QAC/E;QACA,oBAAoB,CAAC;YACnB,MAAM,aAAa,GAAG,EAAE;QAC1B;QAEA,UAAU;QACV,kBAAkB,CAAC,OAAO;YACxB,MAAM,OAAO,CAAC,MAAM,GAAG,OAAO,OAAO;QACvC;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,OAAO,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,OAAO,OAAO,CAAC,OAAO;QAC5D;QACA,cAAc,CAAC,OAAO;YACpB,OAAO,MAAM,OAAO,CAAC,OAAO,OAAO,CAAC;QACtC;QAEA,SAAS;QACT,WAAW,CAAC,OAAO;YACjB,MAAM,MAAM,CAAC,OAAO,OAAO,CAAC,GAAG;QACjC;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,MAAM,CAAC,OAAO,OAAO,CAAC,GAAG;QACjC;QACA,aAAa,CAAC,OAAO;YACnB,MAAM,MAAM,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,OAAO,OAAO,CAAC;QAC9D;QACA,gBAAgB,CAAC;YACf,OAAO,IAAI,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAA;gBAChC,MAAM,MAAM,CAAC,IAAI,GAAG;YACtB;QACF;QAEA,cAAc;QACd,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,eAAe,CAAC,OAAO;YACrB,2CAA2C;YAC3C,MAAM,WAAW,CAAC,OAAO,CAAC,CAAA;gBACxB,KAAK,OAAO,GAAG;YACjB;YAEA,qBAAqB;YACrB,MAAM,WAAW,CAAC,IAAI,CAAC;gBAAE,GAAG,OAAO,OAAO;gBAAE,SAAS;YAAK;QAC5D;QACA,kBAAkB,CAAC;YACjB,MAAM,WAAW,GAAG,EAAE;QACxB;QAEA,aAAa;QACb,cAAc,CAAC,OAAO;YACpB,MAAM,SAAS,GAAG,OAAO,OAAO;QAClC;QAEA,iBAAiB;QACjB,SAAS,CAAC;YACR,OAAO;gBACL,GAAG,YAAY;gBACf,OAAO,MAAM,KAAK;gBAClB,kBAAkB,MAAM,gBAAgB;YAC1C;QACF;IACF;AACF;AAGO,MAAM,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,EACtB,mBAAmB,EACnB,QAAQ,EACR,eAAe,EACf,oBAAoB,EACpB,wBAAwB,EACxB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,UAAU,EACV,YAAY,EACZ,SAAS,EACT,UAAU,EACV,WAAW,EACX,cAAc,EACd,cAAc,EACd,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACR,GAAG,QAAQ,OAAO;AAGZ,MAAM,WAAW,CAAC,QAA2B,MAAM,EAAE;AACrD,MAAM,oBAAoB,CAAC,QAA2B,MAAM,EAAE,CAAC,WAAW;AAC1E,MAAM,yBAAyB,CAAC,QAA2B,MAAM,EAAE,CAAC,gBAAgB;AACpF,MAAM,cAAc,CAAC,QAA2B,MAAM,EAAE,CAAC,KAAK;AAC9D,MAAM,sBAAsB,CAAC,QAA2B,MAAM,EAAE,CAAC,aAAa;AAC9E,MAAM,4BAA4B,CAAC,QACxC,MAAM,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI;AACrC,MAAM,sBAAsB,CAAC,QAA2B,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM;AAC/E,MAAM,gBAAgB,CAAC,MAAgB,CAAC,QAA2B,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,IAAI;AAC5F,MAAM,cAAc,CAAC,MAAgB,CAAC,QAA2B,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI,IAAI;AACzF,MAAM,oBAAoB,CAAC,QAA2B,MAAM,EAAE,CAAC,WAAW;AAC1E,MAAM,kBAAkB,CAAC,QAA2B,MAAM,EAAE,CAAC,SAAS;uCAE9D,QAAQ,OAAO", "debugId": null}}, {"offset": {"line": 1220, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/slices/usersSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'\nimport { User, ApiResponse } from '@/types'\nimport { usersAPI } from '@/utils/api'\n\ninterface UsersState {\n  users: User[]\n  currentUser: User | null\n  loading: boolean\n  error: string | null\n  pagination: {\n    currentPage: number\n    totalPages: number\n    totalItems: number\n    itemsPerPage: number\n  }\n}\n\nconst initialState: UsersState = {\n  users: [],\n  currentUser: null,\n  loading: false,\n  error: null,\n  pagination: {\n    currentPage: 1,\n    totalPages: 1,\n    totalItems: 0,\n    itemsPerPage: 10,\n  },\n}\n\nexport const fetchUsersAsync = createAsyncThunk(\n  'users/fetchUsers',\n  async (params: any, { rejectWithValue }) => {\n    try {\n      const response: ApiResponse = await usersAPI.getUsers(params)\n      return response\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch users')\n    }\n  }\n)\n\nexport const createUserAsync = createAsyncThunk(\n  'users/createUser',\n  async (userData: any, { rejectWithValue }) => {\n    try {\n      const response: ApiResponse = await usersAPI.createUser(userData)\n      return response.data\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to create user')\n    }\n  }\n)\n\nexport const updateUserAsync = createAsyncThunk(\n  'users/updateUser',\n  async ({ id, userData }: { id: string; userData: any }, { rejectWithValue }) => {\n    try {\n      const response: ApiResponse = await usersAPI.updateUser(id, userData)\n      return response.data\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to update user')\n    }\n  }\n)\n\nexport const deleteUserAsync = createAsyncThunk(\n  'users/deleteUser',\n  async (id: string, { rejectWithValue }) => {\n    try {\n      await usersAPI.deleteUser(id)\n      return id\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to delete user')\n    }\n  }\n)\n\nconst usersSlice = createSlice({\n  name: 'users',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null\n    },\n    setCurrentUser: (state, action) => {\n      state.currentUser = action.payload\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchUsersAsync.pending, (state) => {\n        state.loading = true\n        state.error = null\n      })\n      .addCase(fetchUsersAsync.fulfilled, (state, action) => {\n        state.loading = false\n        state.users = action.payload.data\n        if (action.payload.meta?.pagination) {\n          state.pagination = action.payload.meta.pagination\n        }\n      })\n      .addCase(fetchUsersAsync.rejected, (state, action) => {\n        state.loading = false\n        state.error = action.payload as string\n      })\n      .addCase(createUserAsync.fulfilled, (state, action) => {\n        state.users.unshift(action.payload)\n      })\n      .addCase(updateUserAsync.fulfilled, (state, action) => {\n        const index = state.users.findIndex(user => user.id === action.payload.id)\n        if (index !== -1) {\n          state.users[index] = action.payload\n        }\n      })\n      .addCase(deleteUserAsync.fulfilled, (state, action) => {\n        state.users = state.users.filter(user => user.id !== action.payload)\n      })\n  },\n})\n\nexport const { clearError, setCurrentUser } = usersSlice.actions\nexport default usersSlice.reducer\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;AAeA,MAAM,eAA2B;IAC/B,OAAO,EAAE;IACT,aAAa;IACb,SAAS;IACT,OAAO;IACP,YAAY;QACV,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,cAAc;IAChB;AACF;AAEO,MAAM,kBAAkB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAC5C,oBACA,OAAO,QAAa,EAAE,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAwB,MAAM,sHAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC;QACtD,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGK,MAAM,kBAAkB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAC5C,oBACA,OAAO,UAAe,EAAE,eAAe,EAAE;IACvC,IAAI;QACF,MAAM,WAAwB,MAAM,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;QACxD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGK,MAAM,kBAAkB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAC5C,oBACA,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAiC,EAAE,EAAE,eAAe,EAAE;IACzE,IAAI;QACF,MAAM,WAAwB,MAAM,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,IAAI;QAC5D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGK,MAAM,kBAAkB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAC5C,oBACA,OAAO,IAAY,EAAE,eAAe,EAAE;IACpC,IAAI;QACF,MAAM,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;QAC1B,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGF,MAAM,aAAa,CAAA,GAAA,qSAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM;IACN;IACA,UAAU;QACR,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;IACF;IACA,eAAe,CAAC;QACd,QACG,OAAO,CAAC,gBAAgB,OAAO,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,gBAAgB,SAAS,EAAE,CAAC,OAAO;YAC1C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO,CAAC,IAAI;YACjC,IAAI,OAAO,OAAO,CAAC,IAAI,EAAE,YAAY;gBACnC,MAAM,UAAU,GAAG,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU;YACnD;QACF,GACC,OAAO,CAAC,gBAAgB,QAAQ,EAAE,CAAC,OAAO;YACzC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,GACC,OAAO,CAAC,gBAAgB,SAAS,EAAE,CAAC,OAAO;YAC1C,MAAM,KAAK,CAAC,OAAO,CAAC,OAAO,OAAO;QACpC,GACC,OAAO,CAAC,gBAAgB,SAAS,EAAE,CAAC,OAAO;YAC1C,MAAM,QAAQ,MAAM,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE;YACzE,IAAI,UAAU,CAAC,GAAG;gBAChB,MAAM,KAAK,CAAC,MAAM,GAAG,OAAO,OAAO;YACrC;QACF,GACC,OAAO,CAAC,gBAAgB,SAAS,EAAE,CAAC,OAAO;YAC1C,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO;QACrE;IACJ;AACF;AAEO,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,WAAW,OAAO;uCACjD,WAAW,OAAO", "debugId": null}}, {"offset": {"line": 1324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/slices/propertiesSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'\nimport { Property, ApiResponse } from '@/types'\nimport { propertiesAPI } from '@/utils/api'\n\ninterface PropertiesState {\n  properties: Property[]\n  currentProperty: Property | null\n  loading: boolean\n  error: string | null\n  pagination: {\n    currentPage: number\n    totalPages: number\n    totalItems: number\n    itemsPerPage: number\n  }\n}\n\nconst initialState: PropertiesState = {\n  properties: [],\n  currentProperty: null,\n  loading: false,\n  error: null,\n  pagination: {\n    currentPage: 1,\n    totalPages: 1,\n    totalItems: 0,\n    itemsPerPage: 10,\n  },\n}\n\nexport const fetchPropertiesAsync = createAsyncThunk(\n  'properties/fetchProperties',\n  async (params: any, { rejectWithValue }) => {\n    try {\n      const response: ApiResponse = await propertiesAPI.getProperties(params)\n      return response\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch properties')\n    }\n  }\n)\n\nexport const createPropertyAsync = createAsyncThunk(\n  'properties/createProperty',\n  async (propertyData: any, { rejectWithValue }) => {\n    try {\n      const response: ApiResponse = await propertiesAPI.createProperty(propertyData)\n      return response.data\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to create property')\n    }\n  }\n)\n\nexport const updatePropertyAsync = createAsyncThunk(\n  'properties/updateProperty',\n  async ({ id, propertyData }: { id: string; propertyData: any }, { rejectWithValue }) => {\n    try {\n      const response: ApiResponse = await propertiesAPI.updateProperty(id, propertyData)\n      return response.data\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to update property')\n    }\n  }\n)\n\nexport const deletePropertyAsync = createAsyncThunk(\n  'properties/deleteProperty',\n  async (id: string, { rejectWithValue }) => {\n    try {\n      await propertiesAPI.deleteProperty(id)\n      return id\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to delete property')\n    }\n  }\n)\n\nconst propertiesSlice = createSlice({\n  name: 'properties',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null\n    },\n    setCurrentProperty: (state, action) => {\n      state.currentProperty = action.payload\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchPropertiesAsync.pending, (state) => {\n        state.loading = true\n        state.error = null\n      })\n      .addCase(fetchPropertiesAsync.fulfilled, (state, action) => {\n        state.loading = false\n        state.properties = action.payload.data\n        if (action.payload.meta?.pagination) {\n          state.pagination = action.payload.meta.pagination\n        }\n      })\n      .addCase(fetchPropertiesAsync.rejected, (state, action) => {\n        state.loading = false\n        state.error = action.payload as string\n      })\n      .addCase(createPropertyAsync.fulfilled, (state, action) => {\n        state.properties.unshift(action.payload)\n      })\n      .addCase(updatePropertyAsync.fulfilled, (state, action) => {\n        const index = state.properties.findIndex(property => property.id === action.payload.id)\n        if (index !== -1) {\n          state.properties[index] = action.payload\n        }\n      })\n      .addCase(deletePropertyAsync.fulfilled, (state, action) => {\n        state.properties = state.properties.filter(property => property.id !== action.payload)\n      })\n  },\n})\n\nexport const { clearError, setCurrentProperty } = propertiesSlice.actions\nexport default propertiesSlice.reducer\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;AAeA,MAAM,eAAgC;IACpC,YAAY,EAAE;IACd,iBAAiB;IACjB,SAAS;IACT,OAAO;IACP,YAAY;QACV,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,cAAc;IAChB;AACF;AAEO,MAAM,uBAAuB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EACjD,8BACA,OAAO,QAAa,EAAE,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAwB,MAAM,sHAAA,CAAA,gBAAa,CAAC,aAAa,CAAC;QAChE,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGK,MAAM,sBAAsB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAChD,6BACA,OAAO,cAAmB,EAAE,eAAe,EAAE;IAC3C,IAAI;QACF,MAAM,WAAwB,MAAM,sHAAA,CAAA,gBAAa,CAAC,cAAc,CAAC;QACjE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGK,MAAM,sBAAsB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAChD,6BACA,OAAO,EAAE,EAAE,EAAE,YAAY,EAAqC,EAAE,EAAE,eAAe,EAAE;IACjF,IAAI;QACF,MAAM,WAAwB,MAAM,sHAAA,CAAA,gBAAa,CAAC,cAAc,CAAC,IAAI;QACrE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGK,MAAM,sBAAsB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAChD,6BACA,OAAO,IAAY,EAAE,eAAe,EAAE;IACpC,IAAI;QACF,MAAM,sHAAA,CAAA,gBAAa,CAAC,cAAc,CAAC;QACnC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGF,MAAM,kBAAkB,CAAA,GAAA,qSAAA,CAAA,cAAW,AAAD,EAAE;IAClC,MAAM;IACN;IACA,UAAU;QACR,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QACA,oBAAoB,CAAC,OAAO;YAC1B,MAAM,eAAe,GAAG,OAAO,OAAO;QACxC;IACF;IACA,eAAe,CAAC;QACd,QACG,OAAO,CAAC,qBAAqB,OAAO,EAAE,CAAC;YACtC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,qBAAqB,SAAS,EAAE,CAAC,OAAO;YAC/C,MAAM,OAAO,GAAG;YAChB,MAAM,UAAU,GAAG,OAAO,OAAO,CAAC,IAAI;YACtC,IAAI,OAAO,OAAO,CAAC,IAAI,EAAE,YAAY;gBACnC,MAAM,UAAU,GAAG,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU;YACnD;QACF,GACC,OAAO,CAAC,qBAAqB,QAAQ,EAAE,CAAC,OAAO;YAC9C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,GACC,OAAO,CAAC,oBAAoB,SAAS,EAAE,CAAC,OAAO;YAC9C,MAAM,UAAU,CAAC,OAAO,CAAC,OAAO,OAAO;QACzC,GACC,OAAO,CAAC,oBAAoB,SAAS,EAAE,CAAC,OAAO;YAC9C,MAAM,QAAQ,MAAM,UAAU,CAAC,SAAS,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE;YACtF,IAAI,UAAU,CAAC,GAAG;gBAChB,MAAM,UAAU,CAAC,MAAM,GAAG,OAAO,OAAO;YAC1C;QACF,GACC,OAAO,CAAC,oBAAoB,SAAS,EAAE,CAAC,OAAO;YAC9C,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK,OAAO,OAAO;QACvF;IACJ;AACF;AAEO,MAAM,EAAE,UAAU,EAAE,kBAAkB,EAAE,GAAG,gBAAgB,OAAO;uCAC1D,gBAAgB,OAAO", "debugId": null}}, {"offset": {"line": 1428, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/slices/leadsSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'\nimport { Lead, ApiResponse } from '@/types'\nimport { leadsAPI } from '@/utils/api'\n\ninterface LeadsState {\n  leads: Lead[]\n  currentLead: Lead | null\n  loading: boolean\n  error: string | null\n  pagination: {\n    currentPage: number\n    totalPages: number\n    totalItems: number\n    itemsPerPage: number\n  }\n}\n\nconst initialState: LeadsState = {\n  leads: [],\n  currentLead: null,\n  loading: false,\n  error: null,\n  pagination: {\n    currentPage: 1,\n    totalPages: 1,\n    totalItems: 0,\n    itemsPerPage: 10,\n  },\n}\n\nexport const fetchLeadsAsync = createAsyncThunk(\n  'leads/fetchLeads',\n  async (params: any, { rejectWithValue }) => {\n    try {\n      const response: ApiResponse = await leadsAPI.getLeads(params)\n      return response\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch leads')\n    }\n  }\n)\n\nexport const createLeadAsync = createAsyncThunk(\n  'leads/createLead',\n  async (leadData: any, { rejectWithValue }) => {\n    try {\n      const response: ApiResponse = await leadsAPI.createLead(leadData)\n      return response.data\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to create lead')\n    }\n  }\n)\n\nexport const updateLeadAsync = createAsyncThunk(\n  'leads/updateLead',\n  async ({ id, leadData }: { id: string; leadData: any }, { rejectWithValue }) => {\n    try {\n      const response: ApiResponse = await leadsAPI.updateLead(id, leadData)\n      return response.data\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to update lead')\n    }\n  }\n)\n\nexport const deleteLeadAsync = createAsyncThunk(\n  'leads/deleteLead',\n  async (id: string, { rejectWithValue }) => {\n    try {\n      await leadsAPI.deleteLead(id)\n      return id\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to delete lead')\n    }\n  }\n)\n\nconst leadsSlice = createSlice({\n  name: 'leads',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null\n    },\n    setCurrentLead: (state, action) => {\n      state.currentLead = action.payload\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchLeadsAsync.pending, (state) => {\n        state.loading = true\n        state.error = null\n      })\n      .addCase(fetchLeadsAsync.fulfilled, (state, action) => {\n        state.loading = false\n        state.leads = action.payload.data\n        if (action.payload.meta?.pagination) {\n          state.pagination = action.payload.meta.pagination\n        }\n      })\n      .addCase(fetchLeadsAsync.rejected, (state, action) => {\n        state.loading = false\n        state.error = action.payload as string\n      })\n      .addCase(createLeadAsync.fulfilled, (state, action) => {\n        state.leads.unshift(action.payload)\n      })\n      .addCase(updateLeadAsync.fulfilled, (state, action) => {\n        const index = state.leads.findIndex(lead => lead.id === action.payload.id)\n        if (index !== -1) {\n          state.leads[index] = action.payload\n        }\n      })\n      .addCase(deleteLeadAsync.fulfilled, (state, action) => {\n        state.leads = state.leads.filter(lead => lead.id !== action.payload)\n      })\n  },\n})\n\nexport const { clearError, setCurrentLead } = leadsSlice.actions\nexport default leadsSlice.reducer\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;AAeA,MAAM,eAA2B;IAC/B,OAAO,EAAE;IACT,aAAa;IACb,SAAS;IACT,OAAO;IACP,YAAY;QACV,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,cAAc;IAChB;AACF;AAEO,MAAM,kBAAkB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAC5C,oBACA,OAAO,QAAa,EAAE,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAwB,MAAM,sHAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC;QACtD,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGK,MAAM,kBAAkB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAC5C,oBACA,OAAO,UAAe,EAAE,eAAe,EAAE;IACvC,IAAI;QACF,MAAM,WAAwB,MAAM,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;QACxD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGK,MAAM,kBAAkB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAC5C,oBACA,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAiC,EAAE,EAAE,eAAe,EAAE;IACzE,IAAI;QACF,MAAM,WAAwB,MAAM,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,IAAI;QAC5D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGK,MAAM,kBAAkB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAC5C,oBACA,OAAO,IAAY,EAAE,eAAe,EAAE;IACpC,IAAI;QACF,MAAM,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;QAC1B,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGF,MAAM,aAAa,CAAA,GAAA,qSAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM;IACN;IACA,UAAU;QACR,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;IACF;IACA,eAAe,CAAC;QACd,QACG,OAAO,CAAC,gBAAgB,OAAO,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,gBAAgB,SAAS,EAAE,CAAC,OAAO;YAC1C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO,CAAC,IAAI;YACjC,IAAI,OAAO,OAAO,CAAC,IAAI,EAAE,YAAY;gBACnC,MAAM,UAAU,GAAG,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU;YACnD;QACF,GACC,OAAO,CAAC,gBAAgB,QAAQ,EAAE,CAAC,OAAO;YACzC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,GACC,OAAO,CAAC,gBAAgB,SAAS,EAAE,CAAC,OAAO;YAC1C,MAAM,KAAK,CAAC,OAAO,CAAC,OAAO,OAAO;QACpC,GACC,OAAO,CAAC,gBAAgB,SAAS,EAAE,CAAC,OAAO;YAC1C,MAAM,QAAQ,MAAM,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE;YACzE,IAAI,UAAU,CAAC,GAAG;gBAChB,MAAM,KAAK,CAAC,MAAM,GAAG,OAAO,OAAO;YACrC;QACF,GACC,OAAO,CAAC,gBAAgB,SAAS,EAAE,CAAC,OAAO;YAC1C,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO;QACrE;IACJ;AACF;AAEO,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,WAAW,OAAO;uCACjD,WAAW,OAAO", "debugId": null}}, {"offset": {"line": 1532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/slices/dashboardSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'\nimport { DashboardStats, ApiResponse } from '@/types'\nimport { dashboardAPI } from '@/utils/api'\n\ninterface DashboardState {\n  stats: DashboardStats | null\n  chartData: any\n  recentActivity: any[]\n  loading: boolean\n  error: string | null\n}\n\nconst initialState: DashboardState = {\n  stats: null,\n  chartData: {},\n  recentActivity: [],\n  loading: false,\n  error: null,\n}\n\nexport const fetchDashboardStatsAsync = createAsyncThunk(\n  'dashboard/fetchStats',\n  async (_, { rejectWithValue }) => {\n    try {\n      const response: ApiResponse = await dashboardAPI.getStats()\n      return response.data\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard stats')\n    }\n  }\n)\n\nexport const fetchChartDataAsync = createAsyncThunk(\n  'dashboard/fetchChartData',\n  async ({ type, period }: { type: string; period: string }, { rejectWithValue }) => {\n    try {\n      const response: ApiResponse = await dashboardAPI.getChartData(type, period)\n      return { type, data: response.data }\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch chart data')\n    }\n  }\n)\n\nconst dashboardSlice = createSlice({\n  name: 'dashboard',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchDashboardStatsAsync.pending, (state) => {\n        state.loading = true\n        state.error = null\n      })\n      .addCase(fetchDashboardStatsAsync.fulfilled, (state, action) => {\n        state.loading = false\n        state.stats = action.payload\n      })\n      .addCase(fetchDashboardStatsAsync.rejected, (state, action) => {\n        state.loading = false\n        state.error = action.payload as string\n      })\n      .addCase(fetchChartDataAsync.fulfilled, (state, action) => {\n        state.chartData[action.payload.type] = action.payload.data\n      })\n  },\n})\n\nexport const { clearError } = dashboardSlice.actions\nexport default dashboardSlice.reducer\n"], "names": [], "mappings": ";;;;;;AAAA;AAEA;;;AAUA,MAAM,eAA+B;IACnC,OAAO;IACP,WAAW,CAAC;IACZ,gBAAgB,EAAE;IAClB,SAAS;IACT,OAAO;AACT;AAEO,MAAM,2BAA2B,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EACrD,wBACA,OAAO,GAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,MAAM,WAAwB,MAAM,sHAAA,CAAA,eAAY,CAAC,QAAQ;QACzD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGK,MAAM,sBAAsB,CAAA,GAAA,qSAAA,CAAA,mBAAgB,AAAD,EAChD,4BACA,OAAO,EAAE,IAAI,EAAE,MAAM,EAAoC,EAAE,EAAE,eAAe,EAAE;IAC5E,IAAI;QACF,MAAM,WAAwB,MAAM,sHAAA,CAAA,eAAY,CAAC,YAAY,CAAC,MAAM;QACpE,OAAO;YAAE;YAAM,MAAM,SAAS,IAAI;QAAC;IACrC,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,QAAQ,EAAE,MAAM,WAAW;IAC1D;AACF;AAGF,MAAM,iBAAiB,CAAA,GAAA,qSAAA,CAAA,cAAW,AAAD,EAAE;IACjC,MAAM;IACN;IACA,UAAU;QACR,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;IACF;IACA,eAAe,CAAC;QACd,QACG,OAAO,CAAC,yBAAyB,OAAO,EAAE,CAAC;YAC1C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,yBAAyB,SAAS,EAAE,CAAC,OAAO;YACnD,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,GACC,OAAO,CAAC,yBAAyB,QAAQ,EAAE,CAAC,OAAO;YAClD,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,GACC,OAAO,CAAC,oBAAoB,SAAS,EAAE,CAAC,OAAO;YAC9C,MAAM,SAAS,CAAC,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,OAAO,CAAC,IAAI;QAC5D;IACJ;AACF;AAEO,MAAM,EAAE,UAAU,EAAE,GAAG,eAAe,OAAO;uCACrC,eAAe,OAAO", "debugId": null}}, {"offset": {"line": 1602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/utils/cookies.ts"], "sourcesContent": ["/**\n * Cookie utility functions for client-side cookie management\n */\n\nexport interface CookieOptions {\n  expires?: Date | string\n  maxAge?: number\n  path?: string\n  domain?: string\n  secure?: boolean\n  sameSite?: 'strict' | 'lax' | 'none'\n  httpOnly?: boolean\n}\n\nexport class CookieUtils {\n  /**\n   * Get a cookie value by name\n   */\n  static getCookie(name: string): string | null {\n    if (typeof document === 'undefined') return null\n    \n    const value = `; ${document.cookie}`\n    const parts = value.split(`; ${name}=`)\n    \n    if (parts.length === 2) {\n      const cookieValue = parts.pop()?.split(';').shift()\n      return cookieValue ? decodeURIComponent(cookieValue) : null\n    }\n    \n    return null\n  }\n\n  /**\n   * Set a cookie\n   */\n  static setCookie(name: string, value: string, options: CookieOptions = {}): void {\n    if (typeof document === 'undefined') return\n\n    let cookieString = `${name}=${encodeURIComponent(value)}`\n\n    if (options.expires) {\n      if (typeof options.expires === 'string') {\n        cookieString += `; expires=${options.expires}`\n      } else {\n        cookieString += `; expires=${options.expires.toUTCString()}`\n      }\n    }\n\n    if (options.maxAge) {\n      cookieString += `; max-age=${options.maxAge}`\n    }\n\n    if (options.path) {\n      cookieString += `; path=${options.path}`\n    } else {\n      cookieString += `; path=/`\n    }\n\n    if (options.domain) {\n      cookieString += `; domain=${options.domain}`\n    }\n\n    if (options.secure) {\n      cookieString += `; secure`\n    }\n\n    if (options.sameSite) {\n      cookieString += `; samesite=${options.sameSite}`\n    }\n\n    document.cookie = cookieString\n  }\n\n  /**\n   * Delete a cookie\n   */\n  static deleteCookie(name: string, options: Partial<CookieOptions> = {}): void {\n    this.setCookie(name, '', {\n      ...options,\n      expires: new Date(0)\n    })\n  }\n\n  /**\n   * Check if a cookie exists\n   */\n  static hasCookie(name: string): boolean {\n    return this.getCookie(name) !== null\n  }\n\n  /**\n   * Get all cookies as an object\n   */\n  static getAllCookies(): Record<string, string> {\n    if (typeof document === 'undefined') return {}\n\n    const cookies: Record<string, string> = {}\n    \n    document.cookie.split(';').forEach(cookie => {\n      const [name, value] = cookie.trim().split('=')\n      if (name && value) {\n        cookies[name] = decodeURIComponent(value)\n      }\n    })\n\n    return cookies\n  }\n\n  /**\n   * Get authentication token from cookies\n   */\n  static getAuthToken(): string | null {\n    // Try accessToken first (new format), then token (legacy)\n    return this.getCookie('accessToken') || this.getCookie('token')\n  }\n\n  /**\n   * Get refresh token from cookies\n   */\n  static getRefreshToken(): string | null {\n    return this.getCookie('refreshToken')\n  }\n\n  /**\n   * Check if user is authenticated based on cookies\n   */\n  static isAuthenticated(): boolean {\n    return this.hasCookie('accessToken') || this.hasCookie('token')\n  }\n\n  /**\n   * Clear all authentication cookies\n   */\n  static clearAuthCookies(): void {\n    this.deleteCookie('accessToken')\n    this.deleteCookie('refreshToken')\n    this.deleteCookie('token') // Legacy cleanup\n  }\n\n  /**\n   * Get redirect URL from cookies\n   */\n  static getRedirectUrl(): string | null {\n    return this.getCookie('redirectTo')\n  }\n\n  /**\n   * Set redirect URL in cookies\n   */\n  static setRedirectUrl(url: string): void {\n    this.setCookie('redirectTo', url, {\n      maxAge: 300, // 5 minutes\n      sameSite: 'lax'\n    })\n  }\n\n  /**\n   * Clear redirect URL from cookies\n   */\n  static clearRedirectUrl(): void {\n    this.deleteCookie('redirectTo')\n  }\n\n  /**\n   * Parse cookie string (useful for server-side)\n   */\n  static parseCookieString(cookieString: string): Record<string, string> {\n    const cookies: Record<string, string> = {}\n    \n    cookieString.split(';').forEach(cookie => {\n      const [name, value] = cookie.trim().split('=')\n      if (name && value) {\n        cookies[name] = decodeURIComponent(value)\n      }\n    })\n\n    return cookies\n  }\n\n  /**\n   * Set session cookie (expires when browser closes)\n   */\n  static setSessionCookie(name: string, value: string, options: Partial<CookieOptions> = {}): void {\n    this.setCookie(name, value, {\n      ...options,\n      // Don't set expires or maxAge for session cookies\n      expires: undefined,\n      maxAge: undefined\n    })\n  }\n\n  /**\n   * Set persistent cookie (with expiration)\n   */\n  static setPersistentCookie(name: string, value: string, days: number = 7, options: Partial<CookieOptions> = {}): void {\n    const expires = new Date()\n    expires.setDate(expires.getDate() + days)\n    \n    this.setCookie(name, value, {\n      ...options,\n      expires\n    })\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAYM,MAAM;IACX;;GAEC,GACD,OAAO,UAAU,IAAY,EAAiB;QAC5C,IAAI,OAAO,aAAa,aAAa,OAAO;QAE5C,MAAM,QAAQ,CAAC,EAAE,EAAE,SAAS,MAAM,EAAE;QACpC,MAAM,QAAQ,MAAM,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAEtC,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,MAAM,cAAc,MAAM,GAAG,IAAI,MAAM,KAAK;YAC5C,OAAO,cAAc,mBAAmB,eAAe;QACzD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,UAAU,IAAY,EAAE,KAAa,EAAE,UAAyB,CAAC,CAAC,EAAQ;QAC/E,IAAI,OAAO,aAAa,aAAa;QAErC,IAAI,eAAe,GAAG,KAAK,CAAC,EAAE,mBAAmB,QAAQ;QAEzD,IAAI,QAAQ,OAAO,EAAE;YACnB,IAAI,OAAO,QAAQ,OAAO,KAAK,UAAU;gBACvC,gBAAgB,CAAC,UAAU,EAAE,QAAQ,OAAO,EAAE;YAChD,OAAO;gBACL,gBAAgB,CAAC,UAAU,EAAE,QAAQ,OAAO,CAAC,WAAW,IAAI;YAC9D;QACF;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,gBAAgB,CAAC,UAAU,EAAE,QAAQ,MAAM,EAAE;QAC/C;QAEA,IAAI,QAAQ,IAAI,EAAE;YAChB,gBAAgB,CAAC,OAAO,EAAE,QAAQ,IAAI,EAAE;QAC1C,OAAO;YACL,gBAAgB,CAAC,QAAQ,CAAC;QAC5B;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,gBAAgB,CAAC,SAAS,EAAE,QAAQ,MAAM,EAAE;QAC9C;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,gBAAgB,CAAC,QAAQ,CAAC;QAC5B;QAEA,IAAI,QAAQ,QAAQ,EAAE;YACpB,gBAAgB,CAAC,WAAW,EAAE,QAAQ,QAAQ,EAAE;QAClD;QAEA,SAAS,MAAM,GAAG;IACpB;IAEA;;GAEC,GACD,OAAO,aAAa,IAAY,EAAE,UAAkC,CAAC,CAAC,EAAQ;QAC5E,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI;YACvB,GAAG,OAAO;YACV,SAAS,IAAI,KAAK;QACpB;IACF;IAEA;;GAEC,GACD,OAAO,UAAU,IAAY,EAAW;QACtC,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU;IAClC;IAEA;;GAEC,GACD,OAAO,gBAAwC;QAC7C,IAAI,OAAO,aAAa,aAAa,OAAO,CAAC;QAE7C,MAAM,UAAkC,CAAC;QAEzC,SAAS,MAAM,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;YACjC,MAAM,CAAC,MAAM,MAAM,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;YAC1C,IAAI,QAAQ,OAAO;gBACjB,OAAO,CAAC,KAAK,GAAG,mBAAmB;YACrC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,eAA8B;QACnC,0DAA0D;QAC1D,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,IAAI,CAAC,SAAS,CAAC;IACzD;IAEA;;GAEC,GACD,OAAO,kBAAiC;QACtC,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB;IAEA;;GAEC,GACD,OAAO,kBAA2B;QAChC,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,IAAI,CAAC,SAAS,CAAC;IACzD;IAEA;;GAEC,GACD,OAAO,mBAAyB;QAC9B,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,YAAY,CAAC,SAAS,iBAAiB;;IAC9C;IAEA;;GAEC,GACD,OAAO,iBAAgC;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB;IAEA;;GAEC,GACD,OAAO,eAAe,GAAW,EAAQ;QACvC,IAAI,CAAC,SAAS,CAAC,cAAc,KAAK;YAChC,QAAQ;YACR,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,OAAO,mBAAyB;QAC9B,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA;;GAEC,GACD,OAAO,kBAAkB,YAAoB,EAA0B;QACrE,MAAM,UAAkC,CAAC;QAEzC,aAAa,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;YAC9B,MAAM,CAAC,MAAM,MAAM,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;YAC1C,IAAI,QAAQ,OAAO;gBACjB,OAAO,CAAC,KAAK,GAAG,mBAAmB;YACrC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,iBAAiB,IAAY,EAAE,KAAa,EAAE,UAAkC,CAAC,CAAC,EAAQ;QAC/F,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO;YAC1B,GAAG,OAAO;YACV,kDAAkD;YAClD,SAAS;YACT,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,OAAO,oBAAoB,IAAY,EAAE,KAAa,EAAE,OAAe,CAAC,EAAE,UAAkC,CAAC,CAAC,EAAQ;QACpH,MAAM,UAAU,IAAI;QACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;QAEpC,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO;YAC1B,GAAG,OAAO;YACV;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/baseApi.ts"], "sourcesContent": ["import { createA<PERSON>, fetchB<PERSON><PERSON><PERSON><PERSON>, BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query/react'\nimport { RootState } from '@/store'\nimport { CookieUtils } from '@/utils/cookies'\n\n// Define common API response interface\nexport interface ApiResponse<T = any> {\n  success: boolean\n  message: string\n  data?: T\n  error?: string\n  meta?: {\n    pagination?: {\n      currentPage: number\n      totalPages: number\n      totalItems: number\n      itemsPerPage: number\n    }\n    timestamp?: string\n    version?: string\n  }\n}\n\n// Base query configuration with authentication and error handling\nconst baseQuery = fetchBaseQuery({\n  baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api',\n  credentials: 'include',\n  prepareHeaders: (headers, { getState }) => {\n    // Get token from cookies first, then localStorage, then Redux state\n    let token = null\n    if (typeof window !== 'undefined') {\n      token = CookieUtils.getAuthToken() || localStorage.getItem('accessToken')\n    }\n\n    if (!token) {\n      token = (getState() as RootState)?.auth?.user?.accessToken\n    }\n\n    if (token) {\n      headers.set('authorization', `Bearer ${token}`)\n    }\n\n    // Set common headers\n    headers.set('content-type', 'application/json')\n    headers.set('accept', 'application/json')\n    \n    // Add client type for backend identification\n    headers.set('x-client-type', 'admin-dashboard')\n    headers.set('x-client-version', '1.0.0')\n\n    return headers\n  },\n})\n\n// Enhanced base query with automatic token refresh and error handling\nconst baseQueryWithReauth: BaseQueryFn<\n  string | FetchArgs,\n  unknown,\n  FetchBaseQueryError\n> = async (args, api, extraOptions) => {\n  let result = await baseQuery(args, api, extraOptions)\n\n  // Log API errors for debugging\n  if (result.error) {\n    console.error('API Error:', result.error)\n  }\n\n  // Handle network errors or API server not available\n  if (result.error && (result.error.status === 'FETCH_ERROR' || result.error.status === 'PARSING_ERROR')) {\n    console.error('API server not available. Please ensure the backend server is running.')\n    // Don't return mock data - let the error propagate to the UI\n  }\n\n  // Handle 401 Unauthorized - attempt token refresh\n  if (result.error && result.error.status === 401) {\n    console.log('Token expired, attempting refresh...')\n    \n    // Try to refresh token\n    const refreshResult = await baseQuery(\n      {\n        url: '/auth/refresh',\n        method: 'POST',\n        body: {\n          refreshToken: typeof window !== 'undefined' ? localStorage.getItem('refreshToken') : null\n        }\n      },\n      api,\n      extraOptions\n    )\n\n    if (refreshResult.data) {\n      // Store new token\n      const newTokenData = refreshResult.data as any\n      if (newTokenData.success && newTokenData.data?.accessToken) {\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('accessToken', newTokenData.data.accessToken)\n        }\n\n        // Retry original request with new token\n        result = await baseQuery(args, api, extraOptions)\n      }\n    } else {\n      // Refresh failed - redirect to login\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem('accessToken')\n        localStorage.removeItem('refreshToken')\n      }\n      \n      // Dispatch logout action if available\n      if (typeof window !== 'undefined') {\n        window.location.href = '/login'\n      }\n    }\n  }\n\n  // Handle other common errors\n  if (result.error) {\n    console.error('API Error:', result.error)\n    \n    // Handle network errors\n    if (result.error.status === 'FETCH_ERROR') {\n      console.error('Network error - check if backend is running')\n    }\n    \n    // Handle server errors\n    if (result.error.status === 500) {\n      console.error('Server error - check backend logs')\n    }\n  }\n\n  return result\n}\n\n// Create base API slice that other APIs will extend\nexport const baseApi = createApi({\n  reducerPath: 'baseApi',\n  baseQuery: baseQueryWithReauth,\n  tagTypes: [\n    'User', 'UserStats',\n    'Property', 'PropertyStats',\n    'Lead', 'LeadStats',\n    'Transaction', 'TransactionStats',\n    'Dashboard', 'DashboardStats',\n    'PropertyOwner', 'PropertyOwnerStats',\n    'Support', 'SupportStats',\n    'Finance', 'FinanceStats',\n    'Stock', 'StockStats',\n    'Settings', 'SystemSettings',\n    'Notification', 'Activity',\n    'KYC', 'Analytics',\n    'System', 'AuditLog', 'Backup',\n    'Communication', 'Campaign', 'NotificationLog',\n    'PaymentMethod', 'Dispute',\n    'Sales', 'SalesStats',\n    'SalesTeam', 'SalesTask', 'SalesTarget',\n    'Commission', 'Calendar',\n    'Investment', 'Portfolio', 'Wallet'\n  ],\n  endpoints: () => ({}), // Base API has no endpoints, extended APIs will add them\n})\n\n// Common query parameters interface\nexport interface BaseQueryParams {\n  page?: number\n  limit?: number\n  search?: string\n  sortBy?: string\n  sortOrder?: 'asc' | 'desc'\n  dateRange?: {\n    start?: string\n    end?: string\n  }\n}\n\n// Common mutation response interface\nexport interface MutationResponse<T = any> extends ApiResponse<T> {\n  timestamp: string\n}\n\n// Utility function to create standardized query parameters\nexport const createQueryParams = (params: BaseQueryParams = {}) => ({\n  page: params.page || 1,\n  limit: params.limit || 10,\n  search: params.search,\n  sortBy: params.sortBy,\n  sortOrder: params.sortOrder,\n  startDate: params.dateRange?.start,\n  endDate: params.dateRange?.end,\n})\n\n// Utility function to handle file uploads\nexport const createFormDataQuery = (data: Record<string, any>) => {\n  const formData = new FormData()\n  \n  Object.entries(data).forEach(([key, value]) => {\n    if (value instanceof File || value instanceof Blob) {\n      formData.append(key, value)\n    } else if (Array.isArray(value)) {\n      value.forEach((item, index) => {\n        if (item instanceof File || item instanceof Blob) {\n          formData.append(`${key}[${index}]`, item)\n        } else {\n          formData.append(`${key}[${index}]`, JSON.stringify(item))\n        }\n      })\n    } else if (typeof value === 'object' && value !== null) {\n      formData.append(key, JSON.stringify(value))\n    } else {\n      formData.append(key, String(value))\n    }\n  })\n  \n  return formData\n}\n\n// Export common types and utilities\n// Types exported in individual API files\n"], "names": [], "mappings": ";;;;;AAwBW;AAxBX;AAAA;AAEA;;;AAoBA,kEAAkE;AAClE,MAAM,YAAY,CAAA,GAAA,0RAAA,CAAA,iBAAc,AAAD,EAAE;IAC/B,SAAS,iEAAmC;IAC5C,aAAa;IACb,gBAAgB,CAAC,SAAS,EAAE,QAAQ,EAAE;QACpC,oEAAoE;QACpE,IAAI,QAAQ;QACZ,wCAAmC;YACjC,QAAQ,0HAAA,CAAA,cAAW,CAAC,YAAY,MAAM,aAAa,OAAO,CAAC;QAC7D;QAEA,IAAI,CAAC,OAAO;YACV,QAAS,YAA0B,MAAM,MAAM;QACjD;QAEA,IAAI,OAAO;YACT,QAAQ,GAAG,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO;QAChD;QAEA,qBAAqB;QACrB,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,QAAQ,GAAG,CAAC,UAAU;QAEtB,6CAA6C;QAC7C,QAAQ,GAAG,CAAC,iBAAiB;QAC7B,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,OAAO;IACT;AACF;AAEA,sEAAsE;AACtE,MAAM,sBAIF,OAAO,MAAM,KAAK;IACpB,IAAI,SAAS,MAAM,UAAU,MAAM,KAAK;IAExC,+BAA+B;IAC/B,IAAI,OAAO,KAAK,EAAE;QAChB,QAAQ,KAAK,CAAC,cAAc,OAAO,KAAK;IAC1C;IAEA,oDAAoD;IACpD,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,KAAK,CAAC,MAAM,KAAK,iBAAiB,OAAO,KAAK,CAAC,MAAM,KAAK,eAAe,GAAG;QACtG,QAAQ,KAAK,CAAC;IACd,6DAA6D;IAC/D;IAEA,kDAAkD;IAClD,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,KAAK;QAC/C,QAAQ,GAAG,CAAC;QAEZ,uBAAuB;QACvB,MAAM,gBAAgB,MAAM,UAC1B;YACE,KAAK;YACL,QAAQ;YACR,MAAM;gBACJ,cAAc,uCAAgC,aAAa,OAAO,CAAC;YACrE;QACF,GACA,KACA;QAGF,IAAI,cAAc,IAAI,EAAE;YACtB,kBAAkB;YAClB,MAAM,eAAe,cAAc,IAAI;YACvC,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,EAAE,aAAa;gBAC1D,wCAAmC;oBACjC,aAAa,OAAO,CAAC,eAAe,aAAa,IAAI,CAAC,WAAW;gBACnE;gBAEA,wCAAwC;gBACxC,SAAS,MAAM,UAAU,MAAM,KAAK;YACtC;QACF,OAAO;YACL,qCAAqC;YACrC,wCAAmC;gBACjC,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;YAEA,sCAAsC;YACtC,wCAAmC;gBACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;IACF;IAEA,6BAA6B;IAC7B,IAAI,OAAO,KAAK,EAAE;QAChB,QAAQ,KAAK,CAAC,cAAc,OAAO,KAAK;QAExC,wBAAwB;QACxB,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,eAAe;YACzC,QAAQ,KAAK,CAAC;QAChB;QAEA,uBAAuB;QACvB,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,KAAK;YAC/B,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,UAAU,CAAA,GAAA,4TAAA,CAAA,YAAS,AAAD,EAAE;IAC/B,aAAa;IACb,WAAW;IACX,UAAU;QACR;QAAQ;QACR;QAAY;QACZ;QAAQ;QACR;QAAe;QACf;QAAa;QACb;QAAiB;QACjB;QAAW;QACX;QAAW;QACX;QAAS;QACT;QAAY;QACZ;QAAgB;QAChB;QAAO;QACP;QAAU;QAAY;QACtB;QAAiB;QAAY;QAC7B;QAAiB;QACjB;QAAS;QACT;QAAa;QAAa;QAC1B;QAAc;QACd;QAAc;QAAa;KAC5B;IACD,WAAW,IAAM,CAAC,CAAC,CAAC;AACtB;AAqBO,MAAM,oBAAoB,CAAC,SAA0B,CAAC,CAAC,GAAK,CAAC;QAClE,MAAM,OAAO,IAAI,IAAI;QACrB,OAAO,OAAO,KAAK,IAAI;QACvB,QAAQ,OAAO,MAAM;QACrB,QAAQ,OAAO,MAAM;QACrB,WAAW,OAAO,SAAS;QAC3B,WAAW,OAAO,SAAS,EAAE;QAC7B,SAAS,OAAO,SAAS,EAAE;IAC7B,CAAC;AAGM,MAAM,sBAAsB,CAAC;IAClC,MAAM,WAAW,IAAI;IAErB,OAAO,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QACxC,IAAI,iBAAiB,QAAQ,iBAAiB,MAAM;YAClD,SAAS,MAAM,CAAC,KAAK;QACvB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;YAC/B,MAAM,OAAO,CAAC,CAAC,MAAM;gBACnB,IAAI,gBAAgB,QAAQ,gBAAgB,MAAM;oBAChD,SAAS,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE;gBACtC,OAAO;oBACL,SAAS,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC;gBACrD;YACF;QACF,OAAO,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;YACtD,SAAS,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC;QACtC,OAAO;YACL,SAAS,MAAM,CAAC,KAAK,OAAO;QAC9B;IACF;IAEA,OAAO;AACT,EAEA,oCAAoC;CACpC,yCAAyC", "debugId": null}}, {"offset": {"line": 1946, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/usersApi.ts"], "sourcesContent": ["import { User } from '@/types'\nimport { baseApi, ApiResponse, BaseQueryParams, createQueryParams } from './baseApi'\n\n// Define users-specific query parameters\nexport interface UsersQueryParams extends BaseQueryParams {\n  role?: string\n  status?: string\n  kycStatus?: string\n  isEmailVerified?: boolean\n  isPhoneVerified?: boolean\n  dateFrom?: string\n  dateTo?: string\n  sort?: string\n  order?: 'asc' | 'desc'\n}\n\n// Define the API slice extending base API\nexport const usersApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    // Get users with pagination and filters\n    getUsers: builder.query<ApiResponse<User[]>, UsersQueryParams>({\n      query: (params = {}) => {\n        const queryParams: any = {\n          page: params.page || 1,\n          limit: params.limit || 10,\n          search: params.search,\n          sort: params.sort || params.sortBy || 'createdAt',\n          order: params.order || params.sortOrder || 'desc',\n        }\n\n        // Add optional filters\n        if (params.role && params.role !== 'all') queryParams.role = params.role\n        if (params.status && params.status !== 'all') queryParams.status = params.status\n        if (params.kycStatus && params.kycStatus !== 'all') queryParams.kycStatus = params.kycStatus\n        if (params.isEmailVerified !== undefined) queryParams.isEmailVerified = params.isEmailVerified\n        if (params.isPhoneVerified !== undefined) queryParams.isPhoneVerified = params.isPhoneVerified\n        if (params.dateFrom) queryParams.dateFrom = params.dateFrom\n        if (params.dateTo) queryParams.dateTo = params.dateTo\n\n        return {\n          url: '/users',\n          params: queryParams,\n        }\n      },\n      providesTags: ['User'],\n    }),\n\n    // Get user by ID\n    getUserById: builder.query<ApiResponse<User>, string>({\n      query: (id) => `/users/${id}`,\n      providesTags: (_, __, id) => [{ type: 'User', id }],\n    }),\n\n    // Get users statistics\n    getUsersStats: builder.query<ApiResponse<{\n      totalUsers: number\n      activeUsers: number\n      pendingKyc: number\n      newThisMonth: number\n    }>, void>({\n      query: () => '/users/stats',\n      providesTags: ['UserStats'],\n    }),\n\n    // Create user\n    createUser: builder.mutation<ApiResponse<User>, Partial<User>>({\n      query: (userData) => ({\n        url: '/users',\n        method: 'POST',\n        body: userData,\n      }),\n      invalidatesTags: ['User', 'UserStats'],\n    }),\n\n    // Update user\n    updateUser: builder.mutation<ApiResponse<User>, { id: string; userData: Partial<User> }>({\n      query: ({ id, userData }) => ({\n        url: `/users/${id}`,\n        method: 'PUT',\n        body: userData,\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'User', id },\n        'User',\n        'UserStats',\n      ],\n    }),\n\n    // Delete user\n    deleteUser: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/users/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['User', 'UserStats'],\n    }),\n\n    // Bulk operations\n    bulkUpdateUsers: builder.mutation<ApiResponse<User[]>, { userIds: string[]; updates: Partial<User> }>({\n      query: ({ userIds, updates }) => ({\n        url: '/users/bulk-update',\n        method: 'PUT',\n        body: { userIds, updates },\n      }),\n      invalidatesTags: ['User', 'UserStats'],\n    }),\n\n    // Export users\n    exportUsers: builder.mutation<Blob, any>({\n      query: (filters) => ({\n        url: '/users/export',\n        method: 'POST',\n        body: filters,\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Get presigned URL for user document upload\n    getUserPresignedUrl: builder.mutation<ApiResponse<{\n      presignedUrl: string\n      fileKey: string\n      uploadUrl: string\n    }>, {\n      fileName: string\n      fileType: string\n      fileSize: number\n      uploadType: 'user-document' | 'user-avatar'\n      userId?: string\n    }>({\n      query: (uploadData) => ({\n        url: '/s3/presigned-url',\n        method: 'POST',\n        body: uploadData,\n      }),\n    }),\n\n    // Confirm user document upload\n    confirmUserFileUpload: builder.mutation<ApiResponse<{\n      fileUrl: string\n      fileKey: string\n    }>, {\n      userId?: string\n      fileKey: string\n      fileName: string\n      fileType: string\n      uploadType: 'user-document' | 'user-avatar'\n      documentType?: 'id_proof' | 'address_proof' | 'income_proof' | 'other'\n    }>({\n      query: (confirmData) => ({\n        url: '/s3/confirm-upload',\n        method: 'POST',\n        body: confirmData,\n      }),\n      invalidatesTags: (_, __, { userId }) => userId ? [{ type: 'User', id: userId }] : [],\n    }),\n\n    // Delete user document\n    deleteUserDocument: builder.mutation<ApiResponse<void>, {\n      userId: string\n      fileKey: string\n    }>({\n      query: ({ userId, fileKey }) => ({\n        url: `/users/${userId}/documents/${encodeURIComponent(fileKey)}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (_, __, { userId }) => [{ type: 'User', id: userId }],\n    }),\n\n    // Role Management\n    updateUserRole: builder.mutation<ApiResponse<User>, {\n      userId: string\n      role: string\n      permissions?: string[]\n    }>({\n      query: ({ userId, ...data }) => ({\n        url: `/users/${userId}/role`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (_, __, { userId }) => [\n        { type: 'User', id: userId },\n        'User',\n        'UserStats'\n      ],\n    }),\n\n    // Property Linking\n    linkUserToProperty: builder.mutation<ApiResponse<void>, {\n      userId: string\n      propertyId: string\n      linkType: 'owner' | 'investor' | 'manager'\n      sharePercentage?: number\n    }>({\n      query: ({ userId, ...data }) => ({\n        url: `/users/${userId}/properties/link`,\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: (_, __, { userId }) => [\n        { type: 'User', id: userId },\n        'User',\n        'Property'\n      ],\n    }),\n\n    unlinkUserFromProperty: builder.mutation<ApiResponse<void>, {\n      userId: string\n      propertyId: string\n    }>({\n      query: ({ userId, propertyId }) => ({\n        url: `/users/${userId}/properties/${propertyId}/unlink`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (_, __, { userId }) => [\n        { type: 'User', id: userId },\n        'User',\n        'Property'\n      ],\n    }),\n\n    // Get user's properties\n    getUserProperties: builder.query<ApiResponse<{\n      properties: Array<{\n        id: string\n        title: string\n        type: string\n        linkType: 'owner' | 'investor' | 'manager'\n        sharePercentage?: number\n        linkedAt: string\n      }>\n    }>, string>({\n      query: (userId) => `/users/${userId}/properties`,\n      providesTags: (_, __, userId) => [{ type: 'User', id: userId }],\n    }),\n\n    // Stock Management for Users\n    getUserStockHoldings: builder.query<ApiResponse<{\n      stocks: Array<{\n        id: string\n        propertyId: string\n        propertyTitle: string\n        stocksOwned: number\n        totalStocks: number\n        sharePercentage: number\n        currentValue: number\n        purchaseValue: number\n        profitLoss: number\n        purchaseDate: string\n      }>\n    }>, string>({\n      query: (userId) => `/users/${userId}/stocks`,\n      providesTags: (_, __, userId) => [{ type: 'User', id: userId }],\n    }),\n\n    // User Verification Actions\n    verifyUserEmail: builder.mutation<ApiResponse<void>, string>({\n      query: (userId) => ({\n        url: `/users/${userId}/verify-email`,\n        method: 'POST',\n      }),\n      invalidatesTags: (_, __, userId) => [\n        { type: 'User', id: userId },\n        'User'\n      ],\n    }),\n\n    verifyUserPhone: builder.mutation<ApiResponse<void>, string>({\n      query: (userId) => ({\n        url: `/users/${userId}/verify-phone`,\n        method: 'POST',\n      }),\n      invalidatesTags: (_, __, userId) => [\n        { type: 'User', id: userId },\n        'User'\n      ],\n    }),\n  }),\n  overrideExisting: true,\n})\n\n// Export hooks for usage in functional components\nexport const {\n  useGetUsersQuery,\n  useGetUserByIdQuery,\n  useLazyGetUserByIdQuery,\n  useGetUsersStatsQuery,\n  useCreateUserMutation,\n  useUpdateUserMutation,\n  useDeleteUserMutation,\n  useBulkUpdateUsersMutation,\n  useExportUsersMutation,\n  useGetUserPresignedUrlMutation,\n  useConfirmUserFileUploadMutation,\n  useDeleteUserDocumentMutation,\n  // New hooks for enhanced functionality\n  useUpdateUserRoleMutation,\n  useLinkUserToPropertyMutation,\n  useUnlinkUserFromPropertyMutation,\n  useGetUserPropertiesQuery,\n  useGetUserStockHoldingsQuery,\n  useVerifyUserEmailMutation,\n  useVerifyUserPhoneMutation,\n} = usersApi\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA;;AAgBO,MAAM,WAAW,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAC9C,WAAW,CAAC,UAAY,CAAC;YACvB,wCAAwC;YACxC,UAAU,QAAQ,KAAK,CAAwC;gBAC7D,OAAO,CAAC,SAAS,CAAC,CAAC;oBACjB,MAAM,cAAmB;wBACvB,MAAM,OAAO,IAAI,IAAI;wBACrB,OAAO,OAAO,KAAK,IAAI;wBACvB,QAAQ,OAAO,MAAM;wBACrB,MAAM,OAAO,IAAI,IAAI,OAAO,MAAM,IAAI;wBACtC,OAAO,OAAO,KAAK,IAAI,OAAO,SAAS,IAAI;oBAC7C;oBAEA,uBAAuB;oBACvB,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,OAAO,YAAY,IAAI,GAAG,OAAO,IAAI;oBACxE,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,OAAO,YAAY,MAAM,GAAG,OAAO,MAAM;oBAChF,IAAI,OAAO,SAAS,IAAI,OAAO,SAAS,KAAK,OAAO,YAAY,SAAS,GAAG,OAAO,SAAS;oBAC5F,IAAI,OAAO,eAAe,KAAK,WAAW,YAAY,eAAe,GAAG,OAAO,eAAe;oBAC9F,IAAI,OAAO,eAAe,KAAK,WAAW,YAAY,eAAe,GAAG,OAAO,eAAe;oBAC9F,IAAI,OAAO,QAAQ,EAAE,YAAY,QAAQ,GAAG,OAAO,QAAQ;oBAC3D,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,GAAG,OAAO,MAAM;oBAErD,OAAO;wBACL,KAAK;wBACL,QAAQ;oBACV;gBACF;gBACA,cAAc;oBAAC;iBAAO;YACxB;YAEA,iBAAiB;YACjB,aAAa,QAAQ,KAAK,CAA4B;gBACpD,OAAO,CAAC,KAAO,CAAC,OAAO,EAAE,IAAI;gBAC7B,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAQ;wBAAG;qBAAE;YACrD;YAEA,uBAAuB;YACvB,eAAe,QAAQ,KAAK,CAKlB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAY;YAC7B;YAEA,cAAc;YACd,YAAY,QAAQ,QAAQ,CAAmC;gBAC7D,OAAO,CAAC,WAAa,CAAC;wBACpB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAQ;iBAAY;YACxC;YAEA,cAAc;YACd,YAAY,QAAQ,QAAQ,CAA6D;gBACvF,OAAO,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAK,CAAC;wBAC5B,KAAK,CAAC,OAAO,EAAE,IAAI;wBACnB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAQ;wBAAG;wBACnB;wBACA;qBACD;YACH;YAEA,cAAc;YACd,YAAY,QAAQ,QAAQ,CAA4B;gBACtD,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,OAAO,EAAE,IAAI;wBACnB,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;oBAAQ;iBAAY;YACxC;YAEA,kBAAkB;YAClB,iBAAiB,QAAQ,QAAQ,CAAqE;gBACpG,OAAO,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,GAAK,CAAC;wBAChC,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAS;wBAAQ;oBAC3B,CAAC;gBACD,iBAAiB;oBAAC;oBAAQ;iBAAY;YACxC;YAEA,eAAe;YACf,aAAa,QAAQ,QAAQ,CAAY;gBACvC,OAAO,CAAC,UAAY,CAAC;wBACnB,KAAK;wBACL,QAAQ;wBACR,MAAM;wBACN,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,6CAA6C;YAC7C,qBAAqB,QAAQ,QAAQ,CAUlC;gBACD,OAAO,CAAC,aAAe,CAAC;wBACtB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;YACH;YAEA,+BAA+B;YAC/B,uBAAuB,QAAQ,QAAQ,CAUpC;gBACD,OAAO,CAAC,cAAgB,CAAC;wBACvB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAK,SAAS;wBAAC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;qBAAE,GAAG,EAAE;YACtF;YAEA,uBAAuB;YACvB,oBAAoB,QAAQ,QAAQ,CAGjC;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE,mBAAmB,UAAU;wBAChE,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;qBAAE;YACxE;YAEA,kBAAkB;YAClB,gBAAgB,QAAQ,QAAQ,CAI7B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,MAAM,GAAK,CAAC;wBAC/B,KAAK,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;wBAC5B,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAK;wBACtC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;wBACA;qBACD;YACH;YAEA,mBAAmB;YACnB,oBAAoB,QAAQ,QAAQ,CAKjC;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,MAAM,GAAK,CAAC;wBAC/B,KAAK,CAAC,OAAO,EAAE,OAAO,gBAAgB,CAAC;wBACvC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAK;wBACtC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;wBACA;qBACD;YACH;YAEA,wBAAwB,QAAQ,QAAQ,CAGrC;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,GAAK,CAAC;wBAClC,KAAK,CAAC,OAAO,EAAE,OAAO,YAAY,EAAE,WAAW,OAAO,CAAC;wBACvD,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAK;wBACtC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;wBACA;qBACD;YACH;YAEA,wBAAwB;YACxB,mBAAmB,QAAQ,KAAK,CASpB;gBACV,OAAO,CAAC,SAAW,CAAC,OAAO,EAAE,OAAO,WAAW,CAAC;gBAChD,cAAc,CAAC,GAAG,IAAI,SAAW;wBAAC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;qBAAE;YACjE;YAEA,6BAA6B;YAC7B,sBAAsB,QAAQ,KAAK,CAavB;gBACV,OAAO,CAAC,SAAW,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC;gBAC5C,cAAc,CAAC,GAAG,IAAI,SAAW;wBAAC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;qBAAE;YACjE;YAEA,4BAA4B;YAC5B,iBAAiB,QAAQ,QAAQ,CAA4B;gBAC3D,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK,CAAC,OAAO,EAAE,OAAO,aAAa,CAAC;wBACpC,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,SAAW;wBAClC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,iBAAiB,QAAQ,QAAQ,CAA4B;gBAC3D,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK,CAAC,OAAO,EAAE,OAAO,aAAa,CAAC;wBACpC,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,SAAW;wBAClC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;QACF,CAAC;IACD,kBAAkB;AACpB;AAGO,MAAM,EACX,gBAAgB,EAChB,mBAAmB,EACnB,uBAAuB,EACvB,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACrB,0BAA0B,EAC1B,sBAAsB,EACtB,8BAA8B,EAC9B,gCAAgC,EAChC,6BAA6B,EAC7B,uCAAuC;AACvC,yBAAyB,EACzB,6BAA6B,EAC7B,iCAAiC,EACjC,yBAAyB,EACzB,4BAA4B,EAC5B,0BAA0B,EAC1B,0BAA0B,EAC3B,GAAG", "debugId": null}}, {"offset": {"line": 2221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/propertiesApi.ts"], "sourcesContent": ["import { Property } from '@/types'\nimport { baseApi, ApiResponse, BaseQueryParams, createQueryParams } from './baseApi'\n\n// Define properties-specific query parameters\nexport interface PropertiesQueryParams extends BaseQueryParams {\n  status?: string\n  location?: string\n  priceRange?: {\n    min?: number\n    max?: number\n  }\n  constructionStatus?: string\n  featured?: boolean\n}\n\n// Define the API slice extending base API\nexport const propertiesApi = baseApi.injectEndpoints({\n   overrideExisting: true,\n  endpoints: (builder) => ({\n    // Get properties with pagination and filters\n    getProperties: builder.query<ApiResponse<Property[]>, PropertiesQueryParams>({\n      query: (params = {}) => ({\n        url: '/properties',\n        params: {\n          ...createQueryParams(params),\n          status: params.status,\n          location: params.location,\n          minPrice: params.priceRange?.min,\n          maxPrice: params.priceRange?.max,\n          constructionStatus: params.constructionStatus,\n          featured: params.featured,\n        },\n      }),\n      providesTags: ['Property'],\n    }),\n\n    // Get property by ID\n    getPropertyById: builder.query<ApiResponse<Property>, string>({\n      query: (id) => `/properties/${id}`,\n      providesTags: (_, __, id) => [{ type: 'Property', id }],\n    }),\n\n    // Get properties statistics\n    getPropertiesStats: builder.query<ApiResponse<{\n      totalProperties: number\n      activeProperties: number\n      soldOut: number\n      totalValue: number\n      averagePrice: number\n      featuredProperties: number\n    }>, void>({\n      query: () => '/properties/stats',\n      providesTags: ['PropertyStats'],\n    }),\n\n    // Create property\n    createProperty: builder.mutation<ApiResponse<Property>, Partial<Property>>({\n      query: (propertyData) => ({\n        url: '/properties',\n        method: 'POST',\n        body: propertyData,\n      }),\n      invalidatesTags: ['Property', 'PropertyStats'],\n    }),\n\n    // Update property\n    updateProperty: builder.mutation<ApiResponse<Property>, { id: string; propertyData: Partial<Property> }>({\n      query: ({ id, propertyData }) => ({\n        url: `/properties/${id}`,\n        method: 'PUT',\n        body: propertyData,\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'Property', id },\n        'Property',\n        'PropertyStats',\n      ],\n    }),\n\n    // Delete property\n    deleteProperty: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/properties/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Property', 'PropertyStats'],\n    }),\n\n    // Feature property\n    featureProperty: builder.mutation<ApiResponse<Property>, { id: string; featured: boolean }>({\n      query: ({ id, featured }) => ({\n        url: `/properties/${id}/feature`,\n        method: 'PUT',\n        body: { featured },\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'Property', id },\n        'Property',\n        'PropertyStats',\n      ],\n    }),\n\n    // Bulk operations\n    bulkUpdateProperties: builder.mutation<ApiResponse<Property[]>, { ids: string[]; updates: Partial<Property> }>({\n      query: ({ ids, updates }) => ({\n        url: '/properties/bulk-update',\n        method: 'PUT',\n        body: { ids, updates },\n      }),\n      invalidatesTags: ['Property', 'PropertyStats'],\n    }),\n\n    // Export properties\n    exportProperties: builder.mutation<Blob, PropertiesQueryParams>({\n      query: (filters) => ({\n        url: '/properties/export',\n        method: 'POST',\n        body: filters,\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Get presigned URL for file upload\n    getPresignedUrl: builder.mutation<ApiResponse<{\n      presignedUrl: string\n      fileKey: string\n      uploadUrl: string\n    }>, {\n      fileName: string\n      fileType: string\n      fileSize: number\n      uploadType: 'property-image' | 'property-document' | 'property-video'\n      propertyId?: string\n    }>({\n      query: (uploadData) => ({\n        url: '/s3/presigned-url',\n        method: 'POST',\n        body: uploadData,\n      }),\n    }),\n\n    // Confirm file upload after S3 upload\n    confirmFileUpload: builder.mutation<ApiResponse<{\n      fileUrl: string\n      fileKey: string\n    }>, {\n      propertyId?: string\n      fileKey: string\n      fileName: string\n      fileType: string\n      uploadType: 'property-image' | 'property-document' | 'property-video'\n    }>({\n      query: (confirmData) => ({\n        url: '/s3/confirm-upload',\n        method: 'POST',\n        body: confirmData,\n      }),\n      invalidatesTags: (_, __, { propertyId }) => propertyId ? [{ type: 'Property', id: propertyId }] : [],\n    }),\n\n    // Delete uploaded file\n    deletePropertyFile: builder.mutation<ApiResponse<void>, {\n      propertyId: string\n      fileKey: string\n    }>({\n      query: ({ propertyId, fileKey }) => ({\n        url: `/properties/${propertyId}/files/${encodeURIComponent(fileKey)}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (_, __, { propertyId }) => [{ type: 'Property', id: propertyId }],\n    }),\n\n    // Owner Management\n    addPropertyOwner: builder.mutation<ApiResponse<void>, {\n      propertyId: string\n      userId: string\n      ownershipType: 'full' | 'partial'\n      ownershipPercentage?: number\n    }>({\n      query: ({ propertyId, ...data }) => ({\n        url: `/properties/${propertyId}/owners`,\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: (_, __, { propertyId }) => [\n        { type: 'Property', id: propertyId },\n        'Property',\n        'User'\n      ],\n    }),\n\n    removePropertyOwner: builder.mutation<ApiResponse<void>, {\n      propertyId: string\n      userId: string\n    }>({\n      query: ({ propertyId, userId }) => ({\n        url: `/properties/${propertyId}/owners/${userId}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (_, __, { propertyId }) => [\n        { type: 'Property', id: propertyId },\n        'Property',\n        'User'\n      ],\n    }),\n\n    // Get property owners\n    getPropertyOwners: builder.query<ApiResponse<{\n      owners: Array<{\n        id: string\n        userId: string\n        user: {\n          id: string\n          firstName: string\n          lastName: string\n          email: string\n        }\n        ownershipType: 'full' | 'partial'\n        ownershipPercentage: number\n        startDate: string\n        isActive: boolean\n      }>\n    }>, string>({\n      query: (propertyId) => `/properties/${propertyId}/owners`,\n      providesTags: (_, __, propertyId) => [{ type: 'Property', id: propertyId }],\n    }),\n\n    // Stock Management\n    createPropertyStocks: builder.mutation<ApiResponse<void>, {\n      propertyId: string\n      totalStocks: number\n      pricePerStock: number\n      minimumInvestment: number\n    }>({\n      query: ({ propertyId, ...data }) => ({\n        url: `/properties/${propertyId}/stocks`,\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: (_, __, { propertyId }) => [\n        { type: 'Property', id: propertyId },\n        'Property',\n        'Stock'\n      ],\n    }),\n\n    getPropertyStocks: builder.query<ApiResponse<{\n      stocks: {\n        id: string\n        totalStocks: number\n        availableStocks: number\n        soldStocks: number\n        pricePerStock: number\n        minimumInvestment: number\n        totalValue: number\n        investors: Array<{\n          userId: string\n          userName: string\n          stocksOwned: number\n          investmentAmount: number\n          purchaseDate: string\n        }>\n      }\n    }>, string>({\n      query: (propertyId) => `/properties/${propertyId}/stocks`,\n      providesTags: (_, __, propertyId) => [{ type: 'Property', id: propertyId }],\n    }),\n  }),\n})\n\n// Export hooks for usage in functional components\nexport const {\n  useGetPropertiesQuery,\n  useGetPropertyByIdQuery,\n  useLazyGetPropertyByIdQuery,\n  useGetPropertiesStatsQuery,\n  useCreatePropertyMutation,\n  useUpdatePropertyMutation,\n  useDeletePropertyMutation,\n  useFeaturePropertyMutation,\n  useBulkUpdatePropertiesMutation,\n  useExportPropertiesMutation,\n  useGetPresignedUrlMutation,\n  useConfirmFileUploadMutation,\n  useDeletePropertyFileMutation,\n  // New hooks for enhanced functionality\n  useAddPropertyOwnerMutation,\n  useRemovePropertyOwnerMutation,\n  useGetPropertyOwnersQuery,\n  useCreatePropertyStocksMutation,\n  useGetPropertyStocksQuery,\n} = propertiesApi\n\n// Export the Property type for convenience\nexport type { Property }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA;;AAeO,MAAM,gBAAgB,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAClD,kBAAkB;IACnB,WAAW,CAAC,UAAY,CAAC;YACvB,6CAA6C;YAC7C,eAAe,QAAQ,KAAK,CAAiD;gBAC3E,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;4BAC5B,QAAQ,OAAO,MAAM;4BACrB,UAAU,OAAO,QAAQ;4BACzB,UAAU,OAAO,UAAU,EAAE;4BAC7B,UAAU,OAAO,UAAU,EAAE;4BAC7B,oBAAoB,OAAO,kBAAkB;4BAC7C,UAAU,OAAO,QAAQ;wBAC3B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAW;YAC5B;YAEA,qBAAqB;YACrB,iBAAiB,QAAQ,KAAK,CAAgC;gBAC5D,OAAO,CAAC,KAAO,CAAC,YAAY,EAAE,IAAI;gBAClC,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAY;wBAAG;qBAAE;YACzD;YAEA,4BAA4B;YAC5B,oBAAoB,QAAQ,KAAK,CAOvB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAgB;YACjC;YAEA,kBAAkB;YAClB,gBAAgB,QAAQ,QAAQ,CAA2C;gBACzE,OAAO,CAAC,eAAiB,CAAC;wBACxB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAY;iBAAgB;YAChD;YAEA,kBAAkB;YAClB,gBAAgB,QAAQ,QAAQ,CAAyE;gBACvG,OAAO,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,GAAK,CAAC;wBAChC,KAAK,CAAC,YAAY,EAAE,IAAI;wBACxB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAY;wBAAG;wBACvB;wBACA;qBACD;YACH;YAEA,kBAAkB;YAClB,gBAAgB,QAAQ,QAAQ,CAA4B;gBAC1D,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,YAAY,EAAE,IAAI;wBACxB,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;oBAAY;iBAAgB;YAChD;YAEA,mBAAmB;YACnB,iBAAiB,QAAQ,QAAQ,CAA2D;gBAC1F,OAAO,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAK,CAAC;wBAC5B,KAAK,CAAC,YAAY,EAAE,GAAG,QAAQ,CAAC;wBAChC,QAAQ;wBACR,MAAM;4BAAE;wBAAS;oBACnB,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAY;wBAAG;wBACvB;wBACA;qBACD;YACH;YAEA,kBAAkB;YAClB,sBAAsB,QAAQ,QAAQ,CAAyE;gBAC7G,OAAO,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,GAAK,CAAC;wBAC5B,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAK;wBAAQ;oBACvB,CAAC;gBACD,iBAAiB;oBAAC;oBAAY;iBAAgB;YAChD;YAEA,oBAAoB;YACpB,kBAAkB,QAAQ,QAAQ,CAA8B;gBAC9D,OAAO,CAAC,UAAY,CAAC;wBACnB,KAAK;wBACL,QAAQ;wBACR,MAAM;wBACN,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,oCAAoC;YACpC,iBAAiB,QAAQ,QAAQ,CAU9B;gBACD,OAAO,CAAC,aAAe,CAAC;wBACtB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;YACH;YAEA,sCAAsC;YACtC,mBAAmB,QAAQ,QAAQ,CAShC;gBACD,OAAO,CAAC,cAAgB,CAAC;wBACvB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,UAAU,EAAE,GAAK,aAAa;wBAAC;4BAAE,MAAM;4BAAY,IAAI;wBAAW;qBAAE,GAAG,EAAE;YACtG;YAEA,uBAAuB;YACvB,oBAAoB,QAAQ,QAAQ,CAGjC;gBACD,OAAO,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,GAAK,CAAC;wBACnC,KAAK,CAAC,YAAY,EAAE,WAAW,OAAO,EAAE,mBAAmB,UAAU;wBACrE,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,UAAU,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAY,IAAI;wBAAW;qBAAE;YACpF;YAEA,mBAAmB;YACnB,kBAAkB,QAAQ,QAAQ,CAK/B;gBACD,OAAO,CAAC,EAAE,UAAU,EAAE,GAAG,MAAM,GAAK,CAAC;wBACnC,KAAK,CAAC,YAAY,EAAE,WAAW,OAAO,CAAC;wBACvC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,UAAU,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAY,IAAI;wBAAW;wBACnC;wBACA;qBACD;YACH;YAEA,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,CAAC;wBAClC,KAAK,CAAC,YAAY,EAAE,WAAW,QAAQ,EAAE,QAAQ;wBACjD,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,UAAU,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAY,IAAI;wBAAW;wBACnC;wBACA;qBACD;YACH;YAEA,sBAAsB;YACtB,mBAAmB,QAAQ,KAAK,CAepB;gBACV,OAAO,CAAC,aAAe,CAAC,YAAY,EAAE,WAAW,OAAO,CAAC;gBACzD,cAAc,CAAC,GAAG,IAAI,aAAe;wBAAC;4BAAE,MAAM;4BAAY,IAAI;wBAAW;qBAAE;YAC7E;YAEA,mBAAmB;YACnB,sBAAsB,QAAQ,QAAQ,CAKnC;gBACD,OAAO,CAAC,EAAE,UAAU,EAAE,GAAG,MAAM,GAAK,CAAC;wBACnC,KAAK,CAAC,YAAY,EAAE,WAAW,OAAO,CAAC;wBACvC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,UAAU,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAY,IAAI;wBAAW;wBACnC;wBACA;qBACD;YACH;YAEA,mBAAmB,QAAQ,KAAK,CAiBpB;gBACV,OAAO,CAAC,aAAe,CAAC,YAAY,EAAE,WAAW,OAAO,CAAC;gBACzD,cAAc,CAAC,GAAG,IAAI,aAAe;wBAAC;4BAAE,MAAM;4BAAY,IAAI;wBAAW;qBAAE;YAC7E;QACF,CAAC;AACH;AAGO,MAAM,EACX,qBAAqB,EACrB,uBAAuB,EACvB,2BAA2B,EAC3B,0BAA0B,EAC1B,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,EAC1B,+BAA+B,EAC/B,2BAA2B,EAC3B,0BAA0B,EAC1B,4BAA4B,EAC5B,6BAA6B,EAC7B,uCAAuC;AACvC,2BAA2B,EAC3B,8BAA8B,EAC9B,yBAAyB,EACzB,+BAA+B,EAC/B,yBAAyB,EAC1B,GAAG", "debugId": null}}, {"offset": {"line": 2476, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/leadsApi.ts"], "sourcesContent": ["import { Lead } from '@/types'\nimport { baseApi, ApiResponse, BaseQueryParams, createQueryParams } from './baseApi'\n\n// Define leads-specific query parameters\nexport interface LeadsQueryParams extends BaseQueryParams {\n  status?: string\n  priority?: string\n  assignedTo?: string\n  source?: string\n  city?: string\n  country?: string\n}\n\n// Define the API slice extending base API\nexport const leadsApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    // Get leads with pagination and filters\n    getLeads: builder.query<ApiResponse<Lead[]>, LeadsQueryParams>({\n      query: (params = {}) => ({\n        url: '/leads',\n        params: {\n          ...createQueryParams(params),\n          status: params.status,\n          priority: params.priority,\n          assignedTo: params.assignedTo,\n          source: params.source,\n          city: params.city,\n          country: params.country,\n        },\n      }),\n      providesTags: ['Lead'],\n    }),\n\n    // Get lead by ID\n    getLeadById: builder.query<ApiResponse<Lead>, string>({\n      query: (id) => `/leads/${id}`,\n      providesTags: (_, __, id) => [{ type: 'Lead', id }],\n    }),\n\n    // Get leads statistics\n    getLeadsStats: builder.query<ApiResponse<{\n      totalLeads: number\n      newLeads: number\n      qualifiedLeads: number\n      convertedLeads: number\n      conversionRate: number\n      averageResponseTime: number\n    }>, void>({\n      query: () => '/leads/stats',\n      providesTags: ['LeadStats'],\n    }),\n\n    // Create lead\n    createLead: builder.mutation<ApiResponse<Lead>, Partial<Lead>>({\n      query: (leadData) => ({\n        url: '/leads',\n        method: 'POST',\n        body: leadData,\n      }),\n      invalidatesTags: ['Lead', 'LeadStats'],\n    }),\n\n    // Update lead\n    updateLead: builder.mutation<ApiResponse<Lead>, { id: string; leadData: Partial<Lead> }>({\n      query: ({ id, leadData }) => ({\n        url: `/leads/${id}`,\n        method: 'PUT',\n        body: leadData,\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'Lead', id },\n        'Lead',\n        'LeadStats',\n      ],\n    }),\n\n    // Delete lead\n    deleteLead: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/leads/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Lead', 'LeadStats'],\n    }),\n\n    // Assign lead\n    assignLead: builder.mutation<ApiResponse<Lead>, { id: string; assignedTo: string }>({\n      query: ({ id, assignedTo }) => ({\n        url: `/leads/${id}/assign`,\n        method: 'PUT',\n        body: { assignedTo },\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'Lead', id },\n        'Lead',\n        'LeadStats',\n      ],\n    }),\n\n    // Bulk operations\n    bulkUpdateLeads: builder.mutation<ApiResponse<Lead[]>, { ids: string[]; updates: Partial<Lead> }>({\n      query: ({ ids, updates }) => ({\n        url: '/leads/bulk-update',\n        method: 'PUT',\n        body: { ids, updates },\n      }),\n      invalidatesTags: ['Lead', 'LeadStats'],\n    }),\n\n    // Export leads\n    exportLeads: builder.mutation<Blob, LeadsQueryParams>({\n      query: (filters) => ({\n        url: '/leads/export',\n        method: 'POST',\n        body: filters,\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Get lead activity\n    getLeadActivity: builder.query<ApiResponse<any[]>, string>({\n      query: (id) => `/leads/${id}/activity`,\n      providesTags: (_, __, id) => [{ type: 'Lead', id }],\n    }),\n\n    // Add lead note\n    addLeadNote: builder.mutation<ApiResponse<any>, { id: string; note: string }>({\n      query: ({ id, note }) => ({\n        url: `/leads/${id}/notes`,\n        method: 'POST',\n        body: { note },\n      }),\n      invalidatesTags: (_, __, { id }) => [{ type: 'Lead', id }],\n    }),\n  }),\n  overrideExisting: true,\n})\n\n// Export hooks for usage in functional components\nexport const {\n  useGetLeadsQuery,\n  useGetLeadByIdQuery,\n  useLazyGetLeadByIdQuery,\n  useGetLeadsStatsQuery,\n  useCreateLeadMutation,\n  useUpdateLeadMutation,\n  useDeleteLeadMutation,\n  useAssignLeadMutation,\n  useBulkUpdateLeadsMutation,\n  useExportLeadsMutation,\n  useGetLeadActivityQuery,\n  useAddLeadNoteMutation,\n} = leadsApi\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA;;AAaO,MAAM,WAAW,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAC9C,WAAW,CAAC,UAAY,CAAC;YACvB,wCAAwC;YACxC,UAAU,QAAQ,KAAK,CAAwC;gBAC7D,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;4BAC5B,QAAQ,OAAO,MAAM;4BACrB,UAAU,OAAO,QAAQ;4BACzB,YAAY,OAAO,UAAU;4BAC7B,QAAQ,OAAO,MAAM;4BACrB,MAAM,OAAO,IAAI;4BACjB,SAAS,OAAO,OAAO;wBACzB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAO;YACxB;YAEA,iBAAiB;YACjB,aAAa,QAAQ,KAAK,CAA4B;gBACpD,OAAO,CAAC,KAAO,CAAC,OAAO,EAAE,IAAI;gBAC7B,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAQ;wBAAG;qBAAE;YACrD;YAEA,uBAAuB;YACvB,eAAe,QAAQ,KAAK,CAOlB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAY;YAC7B;YAEA,cAAc;YACd,YAAY,QAAQ,QAAQ,CAAmC;gBAC7D,OAAO,CAAC,WAAa,CAAC;wBACpB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAQ;iBAAY;YACxC;YAEA,cAAc;YACd,YAAY,QAAQ,QAAQ,CAA6D;gBACvF,OAAO,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAK,CAAC;wBAC5B,KAAK,CAAC,OAAO,EAAE,IAAI;wBACnB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAQ;wBAAG;wBACnB;wBACA;qBACD;YACH;YAEA,cAAc;YACd,YAAY,QAAQ,QAAQ,CAA4B;gBACtD,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,OAAO,EAAE,IAAI;wBACnB,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;oBAAQ;iBAAY;YACxC;YAEA,cAAc;YACd,YAAY,QAAQ,QAAQ,CAAwD;gBAClF,OAAO,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,GAAK,CAAC;wBAC9B,KAAK,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC;wBAC1B,QAAQ;wBACR,MAAM;4BAAE;wBAAW;oBACrB,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAQ;wBAAG;wBACnB;wBACA;qBACD;YACH;YAEA,kBAAkB;YAClB,iBAAiB,QAAQ,QAAQ,CAAiE;gBAChG,OAAO,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,GAAK,CAAC;wBAC5B,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAK;wBAAQ;oBACvB,CAAC;gBACD,iBAAiB;oBAAC;oBAAQ;iBAAY;YACxC;YAEA,eAAe;YACf,aAAa,QAAQ,QAAQ,CAAyB;gBACpD,OAAO,CAAC,UAAY,CAAC;wBACnB,KAAK;wBACL,QAAQ;wBACR,MAAM;wBACN,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,oBAAoB;YACpB,iBAAiB,QAAQ,KAAK,CAA6B;gBACzD,OAAO,CAAC,KAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;gBACtC,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAQ;wBAAG;qBAAE;YACrD;YAEA,gBAAgB;YAChB,aAAa,QAAQ,QAAQ,CAAiD;gBAC5E,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC;wBACzB,QAAQ;wBACR,MAAM;4BAAE;wBAAK;oBACf,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAQ;wBAAG;qBAAE;YAC5D;QACF,CAAC;IACD,kBAAkB;AACpB;AAGO,MAAM,EACX,gBAAgB,EAChB,mBAAmB,EACnB,uBAAuB,EACvB,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACrB,0BAA0B,EAC1B,sBAAsB,EACtB,uBAAuB,EACvB,sBAAsB,EACvB,GAAG", "debugId": null}}, {"offset": {"line": 2650, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/dashboardApi.ts"], "sourcesContent": ["import { baseApi, ApiResponse } from './baseApi'\n\n// Define dashboard-specific interfaces\nexport interface DashboardStats {\n  // User Stats\n  totalUsers: number\n  activeUsers?: number\n  newUsersThisMonth?: number\n  userGrowth: number\n\n  // Property Stats\n  totalProperties: number\n  activeProperties?: number\n  soldOutProperties?: number\n  featuredProperties?: number\n  propertyGrowth: number\n\n  // Investment & Revenue Stats\n  totalInvestments: number\n  totalInvestment?: number // Alternative naming for compatibility\n  totalRevenue: number\n  monthlyRevenue?: number\n  revenueGrowth?: number\n  investmentGrowth?: number\n  averageInvestment?: number\n  activeInvestments?: number\n  maturedInvestments?: number\n\n  // Lead Stats\n  activeLeads: number\n  totalLeads?: number\n  convertedLeads?: number\n  leadGrowth: number\n\n  // Transaction Stats\n  pendingTransactions: number\n  totalTransactions?: number\n  completedTransactions?: number\n  transactionGrowth?: number\n}\n\nexport interface RecentActivity {\n  id: string\n  type: 'user_registration' | 'property_investment' | 'lead_conversion' | 'transaction' | 'kyc_approval' | 'property_added' | 'commission_paid' | 'returns_distributed'\n  title?: string\n  description?: string\n  message?: string\n  timestamp?: string\n  time?: string\n  userId?: string\n  userName?: string\n  userEmail?: string\n  propertyName?: string\n  amount?: number\n  totalInvestment?: number\n  investmentAmount?: number\n  status?: 'completed' | 'pending' | 'failed'\n  priority?: 'urgent' | 'high' | 'normal' | 'low'\n}\n\nexport interface ChartData {\n  labels: string[]\n  datasets: Array<{\n    label: string\n    data: number[]\n    backgroundColor?: string\n    borderColor?: string\n    fill?: boolean\n  }>\n}\n\nexport interface TopProperty {\n  id: string\n  name: string\n  totalValue: number\n  soldStocks: number\n  totalStocks: number\n  trend?: 'up' | 'down'\n  growth?: number\n}\n\nexport interface RecentTransaction {\n  id: string\n  type: 'credit' | 'debit'\n  description?: string\n  amount: number\n  status: 'completed' | 'pending' | 'failed'\n  createdAt: string\n}\n\nexport interface SystemHealth {\n  serverUptime: string\n  databasePerformance: string\n  apiResponseTime: string\n}\n\nexport interface Notification {\n  id: string\n  type: 'error' | 'warning' | 'success' | 'info'\n  message: string\n  time: string\n}\n\nexport interface QuickActions {\n  pendingApprovals: number\n  newLeads: number\n  expiringSoon: number\n}\n\n// Define the API slice extending base API\nexport const dashboardApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    // Get dashboard statistics\n    getDashboardStats: builder.query<ApiResponse<DashboardStats>, { period?: string }>({\n      query: (params = {}) => ({\n        url: '/dashboard/stats',\n        params,\n      }),\n      providesTags: ['DashboardStats'],\n    }),\n\n    // Get recent activities\n    getRecentActivities: builder.query<ApiResponse<RecentActivity[]>, { limit?: number }>({\n      query: (params = {}) => ({\n        url: '/dashboard/activities',\n        params: {\n          limit: params.limit || 10,\n        },\n      }),\n      providesTags: ['Activity'],\n    }),\n\n    // Get revenue chart data\n    getRevenueChart: builder.query<ApiResponse<ChartData>, { period?: string }>({\n      query: (params = {}) => ({\n        url: '/dashboard/charts',\n        params: {\n          type: 'revenue',\n          period: params.period || '7d',\n        },\n      }),\n      providesTags: ['DashboardStats'],\n    }),\n\n    // Get user growth chart data\n    getUserGrowthChart: builder.query<ApiResponse<ChartData>, { period?: string }>({\n      query: (params = {}) => ({\n        url: '/dashboard/charts',\n        params: {\n          type: 'userGrowth',\n          period: params.period || '7d',\n        },\n      }),\n      providesTags: ['DashboardStats'],\n    }),\n\n    // Get property performance chart data\n    getPropertyPerformanceChart: builder.query<ApiResponse<ChartData>, { period?: string }>({\n      query: (params = {}) => ({\n        url: '/dashboard/charts',\n        params: {\n          type: 'propertyPerformance',\n          period: params.period || '7d',\n        },\n      }),\n      providesTags: ['DashboardStats'],\n    }),\n\n    // Get lead conversion chart data\n    getLeadConversionChart: builder.query<ApiResponse<ChartData>, { period?: string }>({\n      query: (params = {}) => ({\n        url: '/dashboard/charts',\n        params: {\n          type: 'leadConversion',\n          period: params.period || '7d',\n        },\n      }),\n      providesTags: ['DashboardStats'],\n    }),\n\n    // Get top performing properties\n    getTopProperties: builder.query<ApiResponse<TopProperty[]>, { limit?: number }>({\n      query: (params = {}) => ({\n        url: '/dashboard/top-properties',\n        params: {\n          limit: params.limit || 5,\n        },\n      }),\n      providesTags: ['DashboardStats'],\n    }),\n\n    // Get recent transactions\n    getRecentTransactions: builder.query<ApiResponse<RecentTransaction[]>, { limit?: number }>({\n      query: (params = {}) => ({\n        url: '/dashboard/recent-transactions',\n        params: {\n          limit: params.limit || 10,\n        },\n      }),\n      providesTags: ['DashboardStats'],\n    }),\n\n    // Get system health\n    getSystemHealth: builder.query<ApiResponse<{\n      status: 'healthy' | 'warning' | 'critical'\n      uptime: number\n      memoryUsage: number\n      cpuUsage: number\n      diskUsage: number\n      activeConnections: number\n      lastBackup: string\n    }>, void>({\n      query: () => '/dashboard/system-health',\n      providesTags: ['DashboardStats'],\n    }),\n\n    // Get notifications\n    getNotifications: builder.query<ApiResponse<any[]>, { \n      limit?: number\n      unreadOnly?: boolean \n    }>({\n      query: (params = {}) => ({\n        url: '/dashboard/notifications',\n        params: {\n          limit: params.limit || 10,\n          unreadOnly: params.unreadOnly || false,\n        },\n      }),\n      providesTags: ['Notification'],\n    }),\n\n    // Mark notification as read\n    markNotificationRead: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/dashboard/notifications/${id}/read`,\n        method: 'PUT',\n      }),\n      invalidatesTags: ['Notification'],\n    }),\n\n    // Mark all notifications as read\n    markAllNotificationsRead: builder.mutation<ApiResponse<void>, void>({\n      query: () => ({\n        url: '/dashboard/notifications/mark-all-read',\n        method: 'PUT',\n      }),\n      invalidatesTags: ['Notification'],\n    }),\n\n    // Get quick actions data\n    getQuickActions: builder.query<ApiResponse<{\n      pendingApprovals: number\n      newLeads: number\n      expiringSoon: number\n      lowStock: number\n    }>, void>({\n      query: () => '/dashboard/quick-actions',\n      providesTags: ['DashboardStats'],\n    }),\n\n    // Export dashboard report\n    exportDashboardReport: builder.mutation<Blob, { \n      format: 'pdf' | 'excel'\n      period: string \n    }>({\n      query: ({ format, period }) => ({\n        url: '/dashboard/export',\n        method: 'POST',\n        body: { format, period },\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n  }),\n  overrideExisting: true,\n})\n\n// Export hooks for usage in functional components\nexport const {\n  useGetDashboardStatsQuery,\n  useGetRecentActivitiesQuery,\n  useGetRevenueChartQuery,\n  useGetUserGrowthChartQuery,\n  useGetPropertyPerformanceChartQuery,\n  useGetLeadConversionChartQuery,\n  useGetTopPropertiesQuery,\n  useGetRecentTransactionsQuery,\n  useGetSystemHealthQuery,\n  useGetNotificationsQuery,\n  useMarkNotificationReadMutation,\n  useMarkAllNotificationsReadMutation,\n  useGetQuickActionsQuery,\n  useExportDashboardReportMutation,\n} = dashboardApi\n\n// Export types\n// Types exported in types/index.ts to avoid conflicts\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;AA8GO,MAAM,eAAe,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAClD,WAAW,CAAC,UAAY,CAAC;YACvB,2BAA2B;YAC3B,mBAAmB,QAAQ,KAAK,CAAmD;gBACjF,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAiB;YAClC;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,KAAK,CAAoD;gBACpF,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,OAAO,OAAO,KAAK,IAAI;wBACzB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAW;YAC5B;YAEA,yBAAyB;YACzB,iBAAiB,QAAQ,KAAK,CAA8C;gBAC1E,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,MAAM;4BACN,QAAQ,OAAO,MAAM,IAAI;wBAC3B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAiB;YAClC;YAEA,6BAA6B;YAC7B,oBAAoB,QAAQ,KAAK,CAA8C;gBAC7E,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,MAAM;4BACN,QAAQ,OAAO,MAAM,IAAI;wBAC3B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAiB;YAClC;YAEA,sCAAsC;YACtC,6BAA6B,QAAQ,KAAK,CAA8C;gBACtF,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,MAAM;4BACN,QAAQ,OAAO,MAAM,IAAI;wBAC3B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAiB;YAClC;YAEA,iCAAiC;YACjC,wBAAwB,QAAQ,KAAK,CAA8C;gBACjF,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,MAAM;4BACN,QAAQ,OAAO,MAAM,IAAI;wBAC3B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAiB;YAClC;YAEA,gCAAgC;YAChC,kBAAkB,QAAQ,KAAK,CAAiD;gBAC9E,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,OAAO,OAAO,KAAK,IAAI;wBACzB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAiB;YAClC;YAEA,0BAA0B;YAC1B,uBAAuB,QAAQ,KAAK,CAAuD;gBACzF,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,OAAO,OAAO,KAAK,IAAI;wBACzB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAiB;YAClC;YAEA,oBAAoB;YACpB,iBAAiB,QAAQ,KAAK,CAQpB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAiB;YAClC;YAEA,oBAAoB;YACpB,kBAAkB,QAAQ,KAAK,CAG5B;gBACD,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,OAAO,OAAO,KAAK,IAAI;4BACvB,YAAY,OAAO,UAAU,IAAI;wBACnC;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAe;YAChC;YAEA,4BAA4B;YAC5B,sBAAsB,QAAQ,QAAQ,CAA4B;gBAChE,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,yBAAyB,EAAE,GAAG,KAAK,CAAC;wBAC1C,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAe;YACnC;YAEA,iCAAiC;YACjC,0BAA0B,QAAQ,QAAQ,CAA0B;gBAClE,OAAO,IAAM,CAAC;wBACZ,KAAK;wBACL,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAe;YACnC;YAEA,yBAAyB;YACzB,iBAAiB,QAAQ,KAAK,CAKpB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAiB;YAClC;YAEA,0BAA0B;YAC1B,uBAAuB,QAAQ,QAAQ,CAGpC;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,GAAK,CAAC;wBAC9B,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAQ;wBAAO;wBACvB,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;QACF,CAAC;IACD,kBAAkB;AACpB;AAGO,MAAM,EACX,yBAAyB,EACzB,2BAA2B,EAC3B,uBAAuB,EACvB,0BAA0B,EAC1B,mCAAmC,EACnC,8BAA8B,EAC9B,wBAAwB,EACxB,6BAA6B,EAC7B,uBAAuB,EACvB,wBAAwB,EACxB,+BAA+B,EAC/B,mCAAmC,EACnC,uBAAuB,EACvB,gCAAgC,EACjC,GAAG,aAEJ,eAAe;CACf,sDAAsD", "debugId": null}}, {"offset": {"line": 2843, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/financeApi.ts"], "sourcesContent": ["import { base<PERSON><PERSON>, ApiRespo<PERSON>, BaseQueryParams, createQueryParams } from './baseApi'\n\n// Define the Transaction type that matches the API response\nexport interface Transaction {\n  id: string\n  userId: string\n  user: {\n    id: string\n    firstName: string\n    lastName: string\n    email: string\n  }\n  propertyId?: string\n  property?: {\n    id: string\n    title: string\n    type: string\n  }\n  type: 'investment' | 'withdrawal' | 'dividend' | 'fee' | 'refund' | 'commission'\n  amount: number\n  currency: string\n  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded'\n  paymentMethod: 'bank_transfer' | 'credit_card' | 'debit_card' | 'wallet' | 'crypto' | 'upi'\n  paymentGateway?: string\n  transactionReference: string\n  externalReference?: string\n  description: string\n  metadata?: Record<string, any>\n  fees: {\n    platformFee: number\n    paymentFee: number\n    totalFees: number\n  }\n  createdAt: string\n  updatedAt: string\n  completedAt?: string\n  failureReason?: string\n  // Additional fields for compatibility with the UI\n  transactionId?: string\n  date?: string\n  time?: string\n  stocks?: number\n  pricePerStock?: number\n  netAmount?: number\n  tax?: number\n}\n\n// Define finance-specific query parameters\nexport interface FinanceQueryParams extends BaseQueryParams {\n  type?: string\n  status?: string\n  userId?: string\n  propertyId?: string\n  amountRange?: {\n    min?: number\n    max?: number\n  }\n  paymentMethod?: string\n}\n\n// Define finance-specific interfaces\nexport interface FinanceStats {\n  totalRevenue: number\n  totalInvestments: number\n  totalWithdrawals: number\n  totalRefunds: number\n  totalTransactions: number\n  pendingTransactions: number\n  completedTransactions: number\n  failedTransactions: number\n  averageTransactionAmount: number\n  revenueGrowth: number\n  transactionGrowth: number\n}\n\nexport interface PaymentMethod {\n  id: string\n  name: string\n  type: 'bank_transfer' | 'credit_card' | 'debit_card' | 'wallet' | 'crypto'\n  isActive: boolean\n  processingFee: number\n  minAmount: number\n  maxAmount: number\n}\n\n// Define the API slice extending base API\nexport const financeApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // Get transactions with pagination and filters\n    getTransactions: builder.query<ApiResponse<Transaction[]>, FinanceQueryParams>({\n      query: (params = {}) => ({\n        url: '/finance/transactions',\n        params: {\n          ...createQueryParams(params),\n          type: params.type,\n          status: params.status,\n          userId: params.userId,\n          propertyId: params.propertyId,\n          minAmount: params.amountRange?.min,\n          maxAmount: params.amountRange?.max,\n          paymentMethod: params.paymentMethod,\n        },\n      }),\n      providesTags: ['Transaction'],\n    }),\n\n    // Get transaction by ID\n    getTransactionById: builder.query<ApiResponse<Transaction>, string>({\n      query: (id) => `/finance/transactions/${id}`,\n      providesTags: (_, __, id) => [{ type: 'Transaction', id }],\n    }),\n\n    // Get finance statistics\n    getFinanceStats: builder.query<ApiResponse<FinanceStats>, void>({\n      query: () => '/finance/stats',\n      providesTags: ['FinanceStats'],\n    }),\n\n    // Create transaction\n    createTransaction: builder.mutation<ApiResponse<Transaction>, Partial<Transaction>>({\n      query: (transactionData) => ({\n        url: '/finance/transactions',\n        method: 'POST',\n        body: transactionData,\n      }),\n      invalidatesTags: ['Transaction', 'FinanceStats'],\n    }),\n\n    // Update transaction\n    updateTransaction: builder.mutation<ApiResponse<Transaction>, { \n      id: string\n      transactionData: Partial<Transaction> \n    }>({\n      query: ({ id, transactionData }) => ({\n        url: `/finance/transactions/${id}`,\n        method: 'PUT',\n        body: transactionData,\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'Transaction', id },\n        'Transaction',\n        'FinanceStats',\n      ],\n    }),\n\n    // Process transaction\n    processTransaction: builder.mutation<ApiResponse<Transaction>, { \n      id: string\n      action: 'approve' | 'reject' | 'refund'\n      reason?: string \n    }>({\n      query: ({ id, action, reason }) => ({\n        url: `/finance/transactions/${id}/process`,\n        method: 'POST',\n        body: { action, reason },\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'Transaction', id },\n        'Transaction',\n        'FinanceStats',\n      ],\n    }),\n\n    // Get payment methods\n    getPaymentMethods: builder.query<ApiResponse<PaymentMethod[]>, void>({\n      query: () => '/finance/payment-methods',\n      providesTags: ['FinanceStats'],\n    }),\n\n    // Update payment method\n    updatePaymentMethod: builder.mutation<ApiResponse<PaymentMethod>, {\n      id: string\n      methodData: Partial<PaymentMethod>\n    }>({\n      query: ({ id, methodData }) => ({\n        url: `/finance/payment-methods/${id}`,\n        method: 'PUT',\n        body: methodData,\n      }),\n      invalidatesTags: ['FinanceStats'],\n    }),\n\n    // Get revenue analytics\n    getRevenueAnalytics: builder.query<ApiResponse<{\n      daily: Array<{ date: string; amount: number }>\n      monthly: Array<{ month: string; amount: number }>\n      yearly: Array<{ year: string; amount: number }>\n      byProperty: Array<{ propertyId: string; propertyName: string; amount: number }>\n      byPaymentMethod: Array<{ method: string; amount: number; count: number }>\n    }>, { period?: string }>({\n      query: (params = {}) => ({\n        url: '/finance/analytics/revenue',\n        params: {\n          period: params.period || '30d',\n        },\n      }),\n      providesTags: ['FinanceStats'],\n    }),\n\n    // Get transaction analytics\n    getTransactionAnalytics: builder.query<ApiResponse<{\n      statusDistribution: Array<{ status: string; count: number; percentage: number }>\n      typeDistribution: Array<{ type: string; count: number; amount: number }>\n      hourlyDistribution: Array<{ hour: number; count: number }>\n      averageProcessingTime: number\n      successRate: number\n    }>, { period?: string }>({\n      query: (params = {}) => ({\n        url: '/finance/analytics/transactions',\n        params: {\n          period: params.period || '30d',\n        },\n      }),\n      providesTags: ['FinanceStats'],\n    }),\n\n    // Export transactions\n    exportTransactions: builder.mutation<Blob, FinanceQueryParams & { \n      format: 'csv' | 'excel' | 'pdf' \n    }>({\n      query: ({ format, ...filters }) => ({\n        url: '/finance/transactions/export',\n        method: 'POST',\n        body: { format, filters },\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Generate financial report\n    generateFinancialReport: builder.mutation<Blob, {\n      type: 'monthly' | 'quarterly' | 'yearly'\n      period: string\n      format: 'pdf' | 'excel'\n    }>({\n      query: ({ type, period, format }) => ({\n        url: '/finance/reports/generate',\n        method: 'POST',\n        body: { type, period, format },\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Bulk process transactions\n    bulkProcessTransactions: builder.mutation<ApiResponse<Transaction[]>, {\n      ids: string[]\n      action: 'approve' | 'reject'\n      reason?: string\n    }>({\n      query: ({ ids, action, reason }) => ({\n        url: '/finance/transactions/bulk-process',\n        method: 'POST',\n        body: { ids, action, reason },\n      }),\n      invalidatesTags: ['Transaction', 'FinanceStats'],\n    }),\n\n    // Get pending approvals\n    getPendingApprovals: builder.query<ApiResponse<Transaction[]>, void>({\n      query: () => '/finance/pending-approvals',\n      providesTags: ['Transaction'],\n    }),\n\n    // Get commission data\n    getCommissionData: builder.query<ApiResponse<{\n      totalCommissions: number\n      paidCommissions: number\n      pendingCommissions: number\n      commissionsByUser: Array<{ userId: string; userName: string; amount: number }>\n      commissionsByProperty: Array<{ propertyId: string; propertyName: string; amount: number }>\n    }>, { period?: string }>({\n      query: (params = {}) => ({\n        url: '/finance/commissions',\n        params: {\n          period: params.period || '30d',\n        },\n      }),\n      providesTags: ['FinanceStats'],\n    }),\n  }),\n})\n\n// Export hooks for usage in functional components\nexport const {\n  useGetTransactionsQuery,\n  useGetTransactionByIdQuery,\n  useGetFinanceStatsQuery,\n  useCreateTransactionMutation,\n  useUpdateTransactionMutation,\n  useProcessTransactionMutation,\n  useGetPaymentMethodsQuery,\n  useUpdatePaymentMethodMutation,\n  useGetRevenueAnalyticsQuery,\n  useGetTransactionAnalyticsQuery,\n  useExportTransactionsMutation,\n  useGenerateFinancialReportMutation,\n  useBulkProcessTransactionsMutation,\n  useGetPendingApprovalsQuery,\n  useGetCommissionDataQuery,\n} = financeApi\n\n// Export types\n// Types exported in types/index.ts to avoid conflicts\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;AAsFO,MAAM,aAAa,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAChD,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,+CAA+C;YAC/C,iBAAiB,QAAQ,KAAK,CAAiD;gBAC7E,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;4BAC5B,MAAM,OAAO,IAAI;4BACjB,QAAQ,OAAO,MAAM;4BACrB,QAAQ,OAAO,MAAM;4BACrB,YAAY,OAAO,UAAU;4BAC7B,WAAW,OAAO,WAAW,EAAE;4BAC/B,WAAW,OAAO,WAAW,EAAE;4BAC/B,eAAe,OAAO,aAAa;wBACrC;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAc;YAC/B;YAEA,wBAAwB;YACxB,oBAAoB,QAAQ,KAAK,CAAmC;gBAClE,OAAO,CAAC,KAAO,CAAC,sBAAsB,EAAE,IAAI;gBAC5C,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAe;wBAAG;qBAAE;YAC5D;YAEA,yBAAyB;YACzB,iBAAiB,QAAQ,KAAK,CAAkC;gBAC9D,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAe;YAChC;YAEA,qBAAqB;YACrB,mBAAmB,QAAQ,QAAQ,CAAiD;gBAClF,OAAO,CAAC,kBAAoB,CAAC;wBAC3B,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAe;iBAAe;YAClD;YAEA,qBAAqB;YACrB,mBAAmB,QAAQ,QAAQ,CAGhC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,eAAe,EAAE,GAAK,CAAC;wBACnC,KAAK,CAAC,sBAAsB,EAAE,IAAI;wBAClC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAe;wBAAG;wBAC1B;wBACA;qBACD;YACH;YAEA,sBAAsB;YACtB,oBAAoB,QAAQ,QAAQ,CAIjC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,GAAK,CAAC;wBAClC,KAAK,CAAC,sBAAsB,EAAE,GAAG,QAAQ,CAAC;wBAC1C,QAAQ;wBACR,MAAM;4BAAE;4BAAQ;wBAAO;oBACzB,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAe;wBAAG;wBAC1B;wBACA;qBACD;YACH;YAEA,sBAAsB;YACtB,mBAAmB,QAAQ,KAAK,CAAqC;gBACnE,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAe;YAChC;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,GAAK,CAAC;wBAC9B,KAAK,CAAC,yBAAyB,EAAE,IAAI;wBACrC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAe;YACnC;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,KAAK,CAMT;gBACvB,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,QAAQ,OAAO,MAAM,IAAI;wBAC3B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAe;YAChC;YAEA,4BAA4B;YAC5B,yBAAyB,QAAQ,KAAK,CAMb;gBACvB,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,QAAQ,OAAO,MAAM,IAAI;wBAC3B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAe;YAChC;YAEA,sBAAsB;YACtB,oBAAoB,QAAQ,QAAQ,CAEjC;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,GAAK,CAAC;wBAClC,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAQ;wBAAQ;wBACxB,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,4BAA4B;YAC5B,yBAAyB,QAAQ,QAAQ,CAItC;gBACD,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,GAAK,CAAC;wBACpC,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAM;4BAAQ;wBAAO;wBAC7B,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,4BAA4B;YAC5B,yBAAyB,QAAQ,QAAQ,CAItC;gBACD,OAAO,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,GAAK,CAAC;wBACnC,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAK;4BAAQ;wBAAO;oBAC9B,CAAC;gBACD,iBAAiB;oBAAC;oBAAe;iBAAe;YAClD;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,KAAK,CAAmC;gBACnE,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAc;YAC/B;YAEA,sBAAsB;YACtB,mBAAmB,QAAQ,KAAK,CAMP;gBACvB,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,QAAQ,OAAO,MAAM,IAAI;wBAC3B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAe;YAChC;QACF,CAAC;AACH;AAGO,MAAM,EACX,uBAAuB,EACvB,0BAA0B,EAC1B,uBAAuB,EACvB,4BAA4B,EAC5B,4BAA4B,EAC5B,6BAA6B,EAC7B,yBAAyB,EACzB,8BAA8B,EAC9B,2BAA2B,EAC3B,+BAA+B,EAC/B,6BAA6B,EAC7B,kCAAkC,EAClC,kCAAkC,EAClC,2BAA2B,EAC3B,yBAAyB,EAC1B,GAAG,WAEJ,eAAe;CACf,sDAAsD", "debugId": null}}, {"offset": {"line": 3065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/stocksApi.ts"], "sourcesContent": ["import { base<PERSON><PERSON>, <PERSON>piRespo<PERSON>, BaseQueryParams, createQueryParams } from './baseApi'\n\n// Define stock-specific interfaces\nexport interface Stock {\n  id: string\n  propertyId: string\n  propertyName: string\n  userId: string\n  userName: string\n  quantity: number\n  pricePerStock: number\n  totalAmount: number\n  purchaseDate: string\n  status: 'active' | 'sold' | 'transferred'\n  certificateNumber: string\n  returns: number\n  dividends: number\n}\n\nexport interface StockTransaction {\n  id: string\n  stockId: string\n  type: 'purchase' | 'sale' | 'transfer' | 'dividend'\n  quantity: number\n  pricePerStock: number\n  totalAmount: number\n  fromUserId?: string\n  toUserId?: string\n  transactionDate: string\n  status: 'pending' | 'completed' | 'failed'\n  description?: string\n}\n\nexport interface StockStats {\n  totalStocks: number\n  activeStocks: number\n  soldStocks: number\n  totalValue: number\n  totalReturns: number\n  totalDividends: number\n  averageReturn: number\n  topPerformingProperties: Array<{\n    propertyId: string\n    propertyName: string\n    totalStocks: number\n    averageReturn: number\n  }>\n}\n\n// Define stocks-specific query parameters\nexport interface StocksQueryParams extends BaseQueryParams {\n  propertyId?: string\n  userId?: string\n  status?: string\n  priceRange?: {\n    min?: number\n    max?: number\n  }\n  returnRange?: {\n    min?: number\n    max?: number\n  }\n}\n\n// Define the API slice extending base API\nexport const stocksApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // Get stocks with pagination and filters\n    getStocks: builder.query<ApiResponse<Stock[]>, StocksQueryParams>({\n      query: (params = {}) => ({\n        url: '/stocks',\n        params: {\n          ...createQueryParams(params),\n          propertyId: params.propertyId,\n          userId: params.userId,\n          status: params.status,\n          minPrice: params.priceRange?.min,\n          maxPrice: params.priceRange?.max,\n          minReturn: params.returnRange?.min,\n          maxReturn: params.returnRange?.max,\n        },\n      }),\n      providesTags: ['Stock'],\n    }),\n\n    // Get stock by ID\n    getStockById: builder.query<ApiResponse<Stock>, string>({\n      query: (id) => `/stocks/${id}`,\n      providesTags: (_, __, id) => [{ type: 'Stock', id }],\n    }),\n\n    // Get stocks statistics\n    getStocksStats: builder.query<ApiResponse<StockStats>, void>({\n      query: () => '/stocks/stats',\n      providesTags: ['StockStats'],\n    }),\n\n    // Get stock transactions\n    getStockTransactions: builder.query<ApiResponse<StockTransaction[]>, {\n      stockId?: string\n      userId?: string\n      type?: string\n      status?: string\n      page?: number\n      limit?: number\n    }>({\n      query: (params = {}) => ({\n        url: '/stocks/transactions',\n        params: {\n          stockId: params.stockId,\n          userId: params.userId,\n          type: params.type,\n          status: params.status,\n          page: params.page || 1,\n          limit: params.limit || 10,\n        },\n      }),\n      providesTags: ['Stock'],\n    }),\n\n    // Create stock purchase\n    createStockPurchase: builder.mutation<ApiResponse<Stock>, {\n      propertyId: string\n      userId: string\n      quantity: number\n      pricePerStock: number\n    }>({\n      query: (purchaseData) => ({\n        url: '/stocks/purchase',\n        method: 'POST',\n        body: purchaseData,\n      }),\n      invalidatesTags: ['Stock', 'StockStats'],\n    }),\n\n    // Transfer stock\n    transferStock: builder.mutation<ApiResponse<StockTransaction>, {\n      stockId: string\n      fromUserId: string\n      toUserId: string\n      quantity: number\n      reason?: string\n    }>({\n      query: (transferData) => ({\n        url: '/stocks/transfer',\n        method: 'POST',\n        body: transferData,\n      }),\n      invalidatesTags: ['Stock', 'StockStats'],\n    }),\n\n    // Sell stock\n    sellStock: builder.mutation<ApiResponse<StockTransaction>, {\n      stockId: string\n      quantity: number\n      pricePerStock: number\n    }>({\n      query: (saleData) => ({\n        url: '/stocks/sell',\n        method: 'POST',\n        body: saleData,\n      }),\n      invalidatesTags: ['Stock', 'StockStats'],\n    }),\n\n    // Update stock returns\n    updateStockReturns: builder.mutation<ApiResponse<Stock>, {\n      stockId: string\n      returns: number\n      dividends?: number\n    }>({\n      query: ({ stockId, returns, dividends }) => ({\n        url: `/stocks/${stockId}/returns`,\n        method: 'PUT',\n        body: { returns, dividends },\n      }),\n      invalidatesTags: (_, __, { stockId }) => [\n        { type: 'Stock', id: stockId },\n        'Stock',\n        'StockStats',\n      ],\n    }),\n\n    // Get user stocks\n    getUserStocks: builder.query<ApiResponse<Stock[]>, {\n      userId: string\n      status?: string\n      page?: number\n      limit?: number\n    }>({\n      query: ({ userId, status, page = 1, limit = 10 }) => ({\n        url: `/stocks/user/${userId}`,\n        params: { status, page, limit },\n      }),\n      providesTags: ['Stock'],\n    }),\n\n    // Get property stocks\n    getPropertyStocks: builder.query<ApiResponse<Stock[]>, {\n      \n      propertyId: string\n      status?: string\n      page?: number\n      limit?: number\n    }>({\n      query: ({ propertyId, status, page = 1, limit = 10 }) => ({\n        url: `/stocks/property/${propertyId}`,\n        params: { status, page, limit },\n      }),\n      providesTags: ['Stock'],\n    }),\n\n    // Generate stock certificate\n    generateStockCertificate: builder.mutation<Blob, {\n      stockId: string\n      format: 'pdf' | 'png'\n    }>({\n      query: ({ stockId, format }) => ({\n        url: `/stocks/${stockId}/certificate`,\n        method: 'POST',\n        body: { format },\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Get stock performance analytics\n    getStockPerformance: builder.query<ApiResponse<{\n      performanceByProperty: Array<{\n        propertyId: string\n        propertyName: string\n        totalStocks: number\n        averageReturn: number\n        totalValue: number\n        growth: number\n      }>\n      performanceByUser: Array<{\n        userId: string\n        userName: string\n        totalStocks: number\n        totalValue: number\n        totalReturns: number\n        portfolioGrowth: number\n      }>\n      monthlyPerformance: Array<{\n        month: string\n        totalValue: number\n        returns: number\n        dividends: number\n      }>\n    }>, { period?: string }>({\n      query: (params = {}) => ({\n        url: '/stocks/analytics/performance',\n        params: {\n          period: params.period || '12m',\n        },\n      }),\n      providesTags: ['StockStats'],\n    }),\n\n    // Bulk update stock returns\n    bulkUpdateReturns: builder.mutation<ApiResponse<Stock[]>, {\n      updates: Array<{\n        stockId: string\n        returns: number\n        dividends?: number\n      }>\n    }>({\n      query: ({ updates }) => ({\n        url: '/stocks/bulk-update-returns',\n        method: 'PUT',\n        body: { updates },\n      }),\n      invalidatesTags: ['Stock', 'StockStats'],\n    }),\n\n    // Export stocks data\n    exportStocks: builder.mutation<Blob, StocksQueryParams & {\n      format: 'csv' | 'excel' | 'pdf'\n    }>({\n      query: ({ format, ...filters }) => ({\n        url: '/stocks/export',\n        method: 'POST',\n        body: { format, filters },\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Get dividend history\n    getDividendHistory: builder.query<ApiResponse<Array<{\n      id: string\n      propertyId: string\n      propertyName: string\n      amount: number\n      perStock: number\n      paymentDate: string\n      status: 'pending' | 'paid'\n      totalRecipients: number\n    }>>, {\n      propertyId?: string\n      status?: string\n      page?: number\n      limit?: number\n    }>({\n      query: (params = {}) => ({\n        url: '/stocks/dividends',\n        params: {\n          propertyId: params.propertyId,\n          status: params.status,\n          page: params.page || 1,\n          limit: params.limit || 10,\n        },\n      }),\n      providesTags: ['Stock'],\n    }),\n\n    // Process dividend payment\n    processDividendPayment: builder.mutation<ApiResponse<void>, {\n      propertyId: string\n      amountPerStock: number\n      paymentDate: string\n    }>({\n      query: (dividendData) => ({\n        url: '/stocks/dividends/process',\n        method: 'POST',\n        body: dividendData,\n      }),\n      invalidatesTags: ['Stock', 'StockStats'],\n    }),\n  }),\n})\n\n// Export hooks for usage in functional components\nexport const {\n  useGetStocksQuery,\n  useGetStockByIdQuery,\n  useGetStocksStatsQuery,\n  useGetStockTransactionsQuery,\n  useCreateStockPurchaseMutation,\n  useTransferStockMutation,\n  useSellStockMutation,\n  useUpdateStockReturnsMutation,\n  useGetUserStocksQuery,\n  useGetPropertyStocksQuery,\n  useGenerateStockCertificateMutation,\n  useGetStockPerformanceQuery,\n  useBulkUpdateReturnsMutation,\n  useExportStocksMutation,\n  useGetDividendHistoryQuery,\n  useProcessDividendPaymentMutation,\n} = stocksApi\n\n// Export types\n// Types exported in types/index.ts to avoid conflicts\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;;AAiEO,MAAM,YAAY,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAC/C,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,yCAAyC;YACzC,WAAW,QAAQ,KAAK,CAA0C;gBAChE,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;4BAC5B,YAAY,OAAO,UAAU;4BAC7B,QAAQ,OAAO,MAAM;4BACrB,QAAQ,OAAO,MAAM;4BACrB,UAAU,OAAO,UAAU,EAAE;4BAC7B,UAAU,OAAO,UAAU,EAAE;4BAC7B,WAAW,OAAO,WAAW,EAAE;4BAC/B,WAAW,OAAO,WAAW,EAAE;wBACjC;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAQ;YACzB;YAEA,kBAAkB;YAClB,cAAc,QAAQ,KAAK,CAA6B;gBACtD,OAAO,CAAC,KAAO,CAAC,QAAQ,EAAE,IAAI;gBAC9B,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAS;wBAAG;qBAAE;YACtD;YAEA,wBAAwB;YACxB,gBAAgB,QAAQ,KAAK,CAAgC;gBAC3D,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAa;YAC9B;YAEA,yBAAyB;YACzB,sBAAsB,QAAQ,KAAK,CAOhC;gBACD,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,SAAS,OAAO,OAAO;4BACvB,QAAQ,OAAO,MAAM;4BACrB,MAAM,OAAO,IAAI;4BACjB,QAAQ,OAAO,MAAM;4BACrB,MAAM,OAAO,IAAI,IAAI;4BACrB,OAAO,OAAO,KAAK,IAAI;wBACzB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAQ;YACzB;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAKlC;gBACD,OAAO,CAAC,eAAiB,CAAC;wBACxB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAS;iBAAa;YAC1C;YAEA,iBAAiB;YACjB,eAAe,QAAQ,QAAQ,CAM5B;gBACD,OAAO,CAAC,eAAiB,CAAC;wBACxB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAS;iBAAa;YAC1C;YAEA,aAAa;YACb,WAAW,QAAQ,QAAQ,CAIxB;gBACD,OAAO,CAAC,WAAa,CAAC;wBACpB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAS;iBAAa;YAC1C;YAEA,uBAAuB;YACvB,oBAAoB,QAAQ,QAAQ,CAIjC;gBACD,OAAO,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,GAAK,CAAC;wBAC3C,KAAK,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC;wBACjC,QAAQ;wBACR,MAAM;4BAAE;4BAAS;wBAAU;oBAC7B,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,OAAO,EAAE,GAAK;wBACvC;4BAAE,MAAM;4BAAS,IAAI;wBAAQ;wBAC7B;wBACA;qBACD;YACH;YAEA,kBAAkB;YAClB,eAAe,QAAQ,KAAK,CAKzB;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAK,CAAC;wBACpD,KAAK,CAAC,aAAa,EAAE,QAAQ;wBAC7B,QAAQ;4BAAE;4BAAQ;4BAAM;wBAAM;oBAChC,CAAC;gBACD,cAAc;oBAAC;iBAAQ;YACzB;YAEA,sBAAsB;YACtB,mBAAmB,QAAQ,KAAK,CAM7B;gBACD,OAAO,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAK,CAAC;wBACxD,KAAK,CAAC,iBAAiB,EAAE,YAAY;wBACrC,QAAQ;4BAAE;4BAAQ;4BAAM;wBAAM;oBAChC,CAAC;gBACD,cAAc;oBAAC;iBAAQ;YACzB;YAEA,6BAA6B;YAC7B,0BAA0B,QAAQ,QAAQ,CAGvC;gBACD,OAAO,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,QAAQ,EAAE,QAAQ,YAAY,CAAC;wBACrC,QAAQ;wBACR,MAAM;4BAAE;wBAAO;wBACf,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,kCAAkC;YAClC,qBAAqB,QAAQ,KAAK,CAuBT;gBACvB,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,QAAQ,OAAO,MAAM,IAAI;wBAC3B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAa;YAC9B;YAEA,4BAA4B;YAC5B,mBAAmB,QAAQ,QAAQ,CAMhC;gBACD,OAAO,CAAC,EAAE,OAAO,EAAE,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;wBAAQ;oBAClB,CAAC;gBACD,iBAAiB;oBAAC;oBAAS;iBAAa;YAC1C;YAEA,qBAAqB;YACrB,cAAc,QAAQ,QAAQ,CAE3B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,GAAK,CAAC;wBAClC,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAQ;wBAAQ;wBACxB,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,uBAAuB;YACvB,oBAAoB,QAAQ,KAAK,CAc9B;gBACD,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,YAAY,OAAO,UAAU;4BAC7B,QAAQ,OAAO,MAAM;4BACrB,MAAM,OAAO,IAAI,IAAI;4BACrB,OAAO,OAAO,KAAK,IAAI;wBACzB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAQ;YACzB;YAEA,2BAA2B;YAC3B,wBAAwB,QAAQ,QAAQ,CAIrC;gBACD,OAAO,CAAC,eAAiB,CAAC;wBACxB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAS;iBAAa;YAC1C;QACF,CAAC;AACH;AAGO,MAAM,EACX,iBAAiB,EACjB,oBAAoB,EACpB,sBAAsB,EACtB,4BAA4B,EAC5B,8BAA8B,EAC9B,wBAAwB,EACxB,oBAAoB,EACpB,6BAA6B,EAC7B,qBAAqB,EACrB,yBAAyB,EACzB,mCAAmC,EACnC,2BAA2B,EAC3B,4BAA4B,EAC5B,uBAAuB,EACvB,0BAA0B,EAC1B,iCAAiC,EAClC,GAAG,UAEJ,eAAe;CACf,sDAAsD", "debugId": null}}, {"offset": {"line": 3315, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/propertyStocksApi.ts"], "sourcesContent": ["import { base<PERSON><PERSON>, ApiResponse, BaseQueryParams, createQueryParams } from './baseApi'\n\nexport interface StockTier {\n  name: string\n  pricePerStock: number\n  stockCount: number\n  minInvestment: number\n  maxInvestment?: number\n  description?: string\n  benefits?: string[]\n}\n\nexport interface CreateStockRequest {\n  propertyId: string\n  totalStocks: number\n  basePrice: number\n  expectedROI: number\n  minimumInvestment: number\n  stockTiers?: StockTier[]\n  riskLevel: 'low' | 'medium' | 'high'\n  investmentPeriod: number\n  dividendFrequency: 'monthly' | 'quarterly' | 'half-yearly' | 'yearly'\n  description?: string\n}\n\nexport interface PropertyStock {\n  _id: string\n  propertyId: {\n    _id: string\n    name: string\n    description?: string\n    location: {\n      address: string\n      city: string\n      state: string\n      pincode: string\n    }\n    propertyType: string\n    constructionStatus: string\n    status: string\n  }\n  totalStocks: number\n  stockPrice: number\n  minimumPurchase: number\n  maximumPurchase?: number\n  stocksSold: number\n  stocksReserved: number\n  totalRevenue: number\n  averageSalePrice: number\n  referralCommissionRate: number\n  salesCommissionRate: number\n  referralCommissionPerStock: number\n  salesCommissionPerStock: number\n  commissionType: string\n  expectedROI?: number\n  riskLevel?: 'low' | 'medium' | 'high'\n  investmentPeriod?: number\n  dividendFrequency?: 'monthly' | 'quarterly' | 'half-yearly' | 'yearly'\n  investmentDescription?: string\n  stockTiers?: StockTier[]\n  availableStocks: number\n  totalValue: number\n  soldPercentage: number\n  isAvailable: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface StocksResponse {\n  stocks: PropertyStock[]\n  pagination: {\n    currentPage: number\n    totalPages: number\n    totalItems: number\n    itemsPerPage: number\n  }\n}\n\nexport interface StockQueryParams extends BaseQueryParams {\n  status?: string\n  riskLevel?: string\n}\n\nexport interface StockTransaction {\n  _id: string\n  stockId: string\n  userId: {\n    _id: string\n    fullName: string\n    email: string\n  }\n  transactionType: 'purchase' | 'sale' | 'transfer'\n  quantity: number\n  pricePerStock: number\n  totalAmount: number\n  transactionDate: string\n  status: 'pending' | 'completed' | 'failed'\n  paymentMethod: string\n  certificateNumber?: string\n}\n\nexport interface StockInvestor {\n  userId: {\n    _id: string\n    fullName: string\n    email: string\n  }\n  totalStocks: number\n  totalInvestment: number\n  averagePrice: number\n  purchaseDate: string\n  status: string\n  returns: number\n  returnsPercentage: number\n}\n\nexport const propertyStocksApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // Create a new stock\n    createStock: builder.mutation<ApiResponse<any>, CreateStockRequest>({\n      query: (stockData) => ({\n        url: '/stocks',\n        method: 'POST',\n        body: stockData,\n      }),\n      invalidatesTags: ['Stock'],\n    }),\n\n    // Get all stocks with pagination and filtering\n    getPropertyStocks: builder.query<ApiResponse<StocksResponse>, StockQueryParams>({\n      query: (params = {}) => ({\n        url: '/stocks',\n        params: {\n          ...createQueryParams(params),\n          status: params.status,\n          riskLevel: params.riskLevel,\n        },\n      }),\n      providesTags: ['Stock'],\n    }),\n\n    // Get stock by ID\n    getPropertyStockById: builder.query<ApiResponse<PropertyStock>, string>({\n      query: (id) => `/stocks/${id}`,\n      providesTags: (_, __, id) => [{ type: 'Stock', id }],\n    }),\n\n    // Update stock\n    updatePropertyStock: builder.mutation<ApiResponse<PropertyStock>, { id: string; updates: Partial<PropertyStock> }>({\n      query: ({ id, updates }) => ({\n        url: `/stocks/${id}`,\n        method: 'PUT',\n        body: updates,\n      }),\n      invalidatesTags: (_, __, { id }) => [{ type: 'Stock', id }, 'Stock'],\n    }),\n\n    // Delete stock\n    deletePropertyStock: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/stocks/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Stock'],\n    }),\n\n    // Get stock transactions\n    getStockTransactions: builder.query<ApiResponse<{ transactions: StockTransaction[] }>, string>({\n      query: (stockId) => `/stocks/${stockId}/transactions`,\n      providesTags: (_, __, stockId) => [{ type: 'Stock', id: stockId }],\n    }),\n\n    // Get stock investors\n    getStockInvestors: builder.query<ApiResponse<{ investors: StockInvestor[] }>, string>({\n      query: (stockId) => `/stocks/${stockId}/investors`,\n      providesTags: (_, __, stockId) => [{ type: 'Stock', id: stockId }],\n    }),\n  }),\n})\n\nexport const {\n  useCreateStockMutation,\n  useGetPropertyStocksQuery,\n  useGetPropertyStockByIdQuery,\n  useUpdatePropertyStockMutation,\n  useDeletePropertyStockMutation,\n  useGetStockTransactionsQuery,\n  useGetStockInvestorsQuery,\n} = propertyStocksApi\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAoHO,MAAM,oBAAoB,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACvD,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,qBAAqB;YACrB,aAAa,QAAQ,QAAQ,CAAuC;gBAClE,OAAO,CAAC,YAAc,CAAC;wBACrB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAQ;YAC5B;YAEA,+CAA+C;YAC/C,mBAAmB,QAAQ,KAAK,CAAgD;gBAC9E,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;4BAC5B,QAAQ,OAAO,MAAM;4BACrB,WAAW,OAAO,SAAS;wBAC7B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAQ;YACzB;YAEA,kBAAkB;YAClB,sBAAsB,QAAQ,KAAK,CAAqC;gBACtE,OAAO,CAAC,KAAO,CAAC,QAAQ,EAAE,IAAI;gBAC9B,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAS;wBAAG;qBAAE;YACtD;YAEA,eAAe;YACf,qBAAqB,QAAQ,QAAQ,CAA8E;gBACjH,OAAO,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,GAAK,CAAC;wBAC3B,KAAK,CAAC,QAAQ,EAAE,IAAI;wBACpB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAS;wBAAG;wBAAG;qBAAQ;YACtE;YAEA,eAAe;YACf,qBAAqB,QAAQ,QAAQ,CAA4B;gBAC/D,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,QAAQ,EAAE,IAAI;wBACpB,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAQ;YAC5B;YAEA,yBAAyB;YACzB,sBAAsB,QAAQ,KAAK,CAA4D;gBAC7F,OAAO,CAAC,UAAY,CAAC,QAAQ,EAAE,QAAQ,aAAa,CAAC;gBACrD,cAAc,CAAC,GAAG,IAAI,UAAY;wBAAC;4BAAE,MAAM;4BAAS,IAAI;wBAAQ;qBAAE;YACpE;YAEA,sBAAsB;YACtB,mBAAmB,QAAQ,KAAK,CAAsD;gBACpF,OAAO,CAAC,UAAY,CAAC,QAAQ,EAAE,QAAQ,UAAU,CAAC;gBAClD,cAAc,CAAC,GAAG,IAAI,UAAY;wBAAC;4BAAE,MAAM;4BAAS,IAAI;wBAAQ;qBAAE;YACpE;QACF,CAAC;AACH;AAEO,MAAM,EACX,sBAAsB,EACtB,yBAAyB,EACzB,4BAA4B,EAC5B,8BAA8B,EAC9B,8BAA8B,EAC9B,4BAA4B,EAC5B,yBAAyB,EAC1B,GAAG", "debugId": null}}, {"offset": {"line": 3422, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/propertyOwnersApi.ts"], "sourcesContent": ["import { base<PERSON><PERSON>, <PERSON>piR<PERSON>ponse, BaseQueryParams, createQueryParams } from './baseApi'\n\n// Define property owner interface\nexport interface PropertyOwner {\n  _id: string\n  id?: string\n  firstName: string\n  lastName: string\n  fullName: string\n  email: string\n  phone: string\n  company?: string\n  address: {\n    street: string\n    city: string\n    state: string\n    country: string\n    zipCode?: string\n    pincode?: string\n  }\n  documents: {\n    panCard?: string\n    aadharCard?: string\n    bankStatement?: string\n    ownershipProof?: string\n    otherDocuments?: string[]\n  }\n  bankDetails: {\n    accountNumber: string\n    ifscCode: string\n    bankName: string\n    accountHolderName: string\n  }\n  properties?: Array<{\n    _id: string\n    name: string\n    location: {\n      address: string\n      city: string\n      state: string\n      country: string\n    }\n    status: string\n    featured: boolean\n    totalValue: number\n    expectedReturns: number\n    propertyType: string\n    createdAt: string\n  }>\n  totalProperties: number\n  totalInvestment: number\n  totalValue?: number\n  status: 'active' | 'inactive' | 'pending' | 'verified'\n  verificationStatus: 'pending' | 'verified' | 'rejected'\n  isVerified: boolean\n  verifiedBy?: string\n  verifiedAt?: string\n  createdAt: string\n  updatedAt: string\n  lastLogin?: string\n  notes?: string\n}\n\nexport interface PropertyOwnerStats {\n  totalOwners: number\n  activeOwners: number\n  pendingVerification: number\n  totalProperties: number\n  totalValue: number\n  averagePropertiesPerOwner: number\n  verificationRate: number\n  ownerGrowth: number\n}\n\n// Define property owners-specific query parameters\nexport interface PropertyOwnersQueryParams extends BaseQueryParams {\n  status?: string\n  verificationStatus?: string\n  city?: string\n  state?: string\n  country?: string\n  hasProperties?: boolean\n  company?: string\n}\n\n// Define the API slice extending base API\nexport const propertyOwnersApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    // Get property owners with pagination and filters\n    getPropertyOwners: builder.query<ApiResponse<{\n      data: PropertyOwner[]\n      meta: {\n        pagination: {\n          currentPage: number\n          totalPages: number\n          totalItems: number\n          itemsPerPage: number\n        }\n      }\n    }>, PropertyOwnersQueryParams>({\n      query: (params = {}) => ({\n        url: '/property-owners',\n        params: {\n          ...createQueryParams(params),\n          status: params.status,\n          verificationStatus: params.verificationStatus,\n          city: params.city,\n          state: params.state,\n          country: params.country,\n          hasProperties: params.hasProperties,\n          company: params.company,\n          includeProperties: 'true',\n        },\n      }),\n      providesTags: ['PropertyOwner'],\n    }),\n\n    // Get property owner by ID\n    getPropertyOwnerById: builder.query<ApiResponse<PropertyOwner>, string>({\n      query: (id) => `/property-owners/${id}`,\n      providesTags: (_, __, id) => [{ type: 'PropertyOwner', id }],\n    }),\n\n    // Get property owners statistics\n    getPropertyOwnersStats: builder.query<ApiResponse<PropertyOwnerStats>, void>({\n      query: () => '/property-owners/stats',\n      providesTags: ['PropertyOwnerStats'],\n    }),\n\n    // Create property owner\n    createPropertyOwner: builder.mutation<ApiResponse<PropertyOwner>, Partial<PropertyOwner>>({\n      query: (ownerData) => ({\n        url: '/property-owners',\n        method: 'POST',\n        body: ownerData,\n      }),\n      invalidatesTags: ['PropertyOwner', 'PropertyOwnerStats'],\n    }),\n\n    // Update property owner\n    updatePropertyOwner: builder.mutation<ApiResponse<PropertyOwner>, {\n      id: string\n      ownerData: Partial<PropertyOwner>\n    }>({\n      query: ({ id, ownerData }) => ({\n        url: `/property-owners/${id}`,\n        method: 'PUT',\n        body: ownerData,\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'PropertyOwner', id },\n        'PropertyOwner',\n        'PropertyOwnerStats',\n      ],\n    }),\n\n    // Delete property owner\n    deletePropertyOwner: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/property-owners/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['PropertyOwner', 'PropertyOwnerStats'],\n    }),\n\n    // Verify property owner\n    verifyPropertyOwner: builder.mutation<ApiResponse<PropertyOwner>, {\n      id: string\n      status: 'verified' | 'rejected'\n      reason?: string\n    }>({\n      query: ({ id, status, reason }) => ({\n        url: `/property-owners/${id}/verify`,\n        method: 'PUT',\n        body: { status, reason },\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'PropertyOwner', id },\n        'PropertyOwner',\n        'PropertyOwnerStats',\n      ],\n    }),\n\n    // Upload property owner document\n    uploadOwnerDocument: builder.mutation<ApiResponse<PropertyOwner>, {\n      id: string\n      documentType: string\n      file: File\n    }>({\n      query: ({ id, documentType, file }) => {\n        const formData = new FormData()\n        formData.append('document', file)\n        formData.append('type', documentType)\n        \n        return {\n          url: `/property-owners/${id}/documents`,\n          method: 'POST',\n          body: formData,\n        }\n      },\n      invalidatesTags: (_, __, { id }) => [{ type: 'PropertyOwner', id }],\n    }),\n\n    // Get property owner properties\n    getOwnerProperties: builder.query<ApiResponse<any[]>, {\n      ownerId: string\n      status?: string\n      page?: number\n      limit?: number\n    }>({\n      query: ({ ownerId, status, page = 1, limit = 10 }) => ({\n        url: `/property-owners/${ownerId}/properties`,\n        params: { status, page, limit },\n      }),\n      providesTags: ['PropertyOwner'],\n    }),\n\n    // Get property owner transactions\n    getOwnerTransactions: builder.query<ApiResponse<any[]>, {\n      ownerId: string\n      type?: string\n      status?: string\n      page?: number\n      limit?: number\n    }>({\n      query: ({ ownerId, type, status, page = 1, limit = 10 }) => ({\n        url: `/property-owners/${ownerId}/transactions`,\n        params: { type, status, page, limit },\n      }),\n      providesTags: ['PropertyOwner'],\n    }),\n\n    // Search property owners\n    searchPropertyOwners: builder.query<ApiResponse<{\n      data: PropertyOwner[]\n      total: number\n      query: string\n      type: string\n      filters: any\n      searchType?: string\n    }>, {\n      query: string\n      type?: string\n      country?: string\n      verified?: boolean\n      limit?: number\n    }>({\n      query: ({ query, type = 'all', country, verified, limit = 20 }) => ({\n        url: '/property-owners/search',\n        params: { q: query, type, country, verified, limit },\n      }),\n      providesTags: ['PropertyOwner'],\n    }),\n\n    // Link property to owner\n    linkPropertyToOwner: builder.mutation<ApiResponse<any>, {\n      ownerId: string\n      propertyId: string\n    }>({\n      query: ({ ownerId, propertyId }) => ({\n        url: `/property-owners/${ownerId}/properties/${propertyId}`,\n        method: 'POST',\n      }),\n      invalidatesTags: ['PropertyOwner', 'Property'],\n    }),\n\n    // Unlink property from owner\n    unlinkPropertyFromOwner: builder.mutation<ApiResponse<any>, {\n      ownerId: string\n      propertyId: string\n    }>({\n      query: ({ ownerId, propertyId }) => ({\n        url: `/property-owners/${ownerId}/properties/${propertyId}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['PropertyOwner', 'Property'],\n    }),\n\n    // Get property owner analytics\n    getOwnerAnalytics: builder.query<ApiResponse<{\n      totalRevenue: number\n      totalProperties: number\n      averagePropertyValue: number\n      occupancyRate: number\n      monthlyRevenue: Array<{ month: string; revenue: number }>\n      propertyPerformance: Array<{\n        propertyId: string\n        propertyName: string\n        revenue: number\n        occupancy: number\n        roi: number\n      }>\n    }>, string>({\n      query: (ownerId) => `/property-owners/${ownerId}/analytics`,\n      providesTags: ['PropertyOwner'],\n    }),\n\n    // Bulk update property owners\n    bulkUpdatePropertyOwners: builder.mutation<ApiResponse<PropertyOwner[]>, {\n      ids: string[]\n      updates: Partial<PropertyOwner>\n    }>({\n      query: ({ ids, updates }) => ({\n        url: '/property-owners/bulk-update',\n        method: 'PUT',\n        body: { ids, updates },\n      }),\n      invalidatesTags: ['PropertyOwner', 'PropertyOwnerStats'],\n    }),\n\n    // Export property owners\n    exportPropertyOwners: builder.mutation<Blob, PropertyOwnersQueryParams & {\n      format: 'csv' | 'excel' | 'pdf'\n    }>({\n      query: ({ format, ...filters }) => ({\n        url: '/property-owners/export',\n        method: 'POST',\n        body: { format, filters },\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Get pending verifications\n    getPendingVerifications: builder.query<ApiResponse<PropertyOwner[]>, void>({\n      query: () => '/property-owners/pending-verifications',\n      providesTags: ['PropertyOwner'],\n    }),\n\n    // Send notification to property owner\n    sendOwnerNotification: builder.mutation<ApiResponse<void>, {\n      ownerId: string\n      type: 'email' | 'sms' | 'push'\n      subject: string\n      message: string\n    }>({\n      query: ({ ownerId, type, subject, message }) => ({\n        url: `/property-owners/${ownerId}/notify`,\n        method: 'POST',\n        body: { type, subject, message },\n      }),\n    }),\n\n    // Get property owner performance\n    getOwnerPerformance: builder.query<ApiResponse<{\n      topPerformers: Array<{\n        ownerId: string\n        ownerName: string\n        totalProperties: number\n        totalRevenue: number\n        averageRoi: number\n        rating: number\n      }>\n      performanceMetrics: {\n        averageRevenue: number\n        averageProperties: number\n        averageRoi: number\n        satisfactionScore: number\n      }\n      growthTrends: Array<{\n        month: string\n        newOwners: number\n        totalRevenue: number\n        averagePropertyValue: number\n      }>\n    }>, { period?: string }>({\n      query: (params = {}) => ({\n        url: '/property-owners/performance',\n        params: {\n          period: params.period || '12m',\n        },\n      }),\n      providesTags: ['PropertyOwnerStats'],\n    }),\n\n    // Update owner bank details\n    updateOwnerBankDetails: builder.mutation<ApiResponse<PropertyOwner>, {\n      id: string\n      bankDetails: PropertyOwner['bankDetails']\n    }>({\n      query: ({ id, bankDetails }) => ({\n        url: `/property-owners/${id}/bank-details`,\n        method: 'PUT',\n        body: bankDetails,\n      }),\n      invalidatesTags: (_, __, { id }) => [{ type: 'PropertyOwner', id }],\n    }),\n  }),\n  overrideExisting: true,\n})\n\n// Export hooks for usage in functional components\nexport const {\n  useGetPropertyOwnersQuery,\n  useGetPropertyOwnerByIdQuery,\n  useGetPropertyOwnersStatsQuery,\n  useCreatePropertyOwnerMutation,\n  useUpdatePropertyOwnerMutation,\n  useDeletePropertyOwnerMutation,\n  useVerifyPropertyOwnerMutation,\n  useUploadOwnerDocumentMutation,\n  useGetOwnerPropertiesQuery,\n  useGetOwnerTransactionsQuery,\n  useGetOwnerAnalyticsQuery,\n  useBulkUpdatePropertyOwnersMutation,\n  useExportPropertyOwnersMutation,\n  useGetPendingVerificationsQuery,\n  useSendOwnerNotificationMutation,\n  useGetOwnerPerformanceQuery,\n  useUpdateOwnerBankDetailsMutation,\n  useSearchPropertyOwnersQuery,\n  useLinkPropertyToOwnerMutation,\n  useUnlinkPropertyFromOwnerMutation,\n} = propertyOwnersApi\n\n// Export types\n// Types exported in types/index.ts to avoid conflicts\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAsFO,MAAM,oBAAoB,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACvD,WAAW,CAAC,UAAY,CAAC;YACvB,kDAAkD;YAClD,mBAAmB,QAAQ,KAAK,CAUD;gBAC7B,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;4BAC5B,QAAQ,OAAO,MAAM;4BACrB,oBAAoB,OAAO,kBAAkB;4BAC7C,MAAM,OAAO,IAAI;4BACjB,OAAO,OAAO,KAAK;4BACnB,SAAS,OAAO,OAAO;4BACvB,eAAe,OAAO,aAAa;4BACnC,SAAS,OAAO,OAAO;4BACvB,mBAAmB;wBACrB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAgB;YACjC;YAEA,2BAA2B;YAC3B,sBAAsB,QAAQ,KAAK,CAAqC;gBACtE,OAAO,CAAC,KAAO,CAAC,iBAAiB,EAAE,IAAI;gBACvC,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAiB;wBAAG;qBAAE;YAC9D;YAEA,iCAAiC;YACjC,wBAAwB,QAAQ,KAAK,CAAwC;gBAC3E,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAqB;YACtC;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAAqD;gBACxF,OAAO,CAAC,YAAc,CAAC;wBACrB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAiB;iBAAqB;YAC1D;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,GAAK,CAAC;wBAC7B,KAAK,CAAC,iBAAiB,EAAE,IAAI;wBAC7B,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAiB;wBAAG;wBAC5B;wBACA;qBACD;YACH;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAA4B;gBAC/D,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,iBAAiB,EAAE,IAAI;wBAC7B,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;oBAAiB;iBAAqB;YAC1D;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAIlC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,GAAK,CAAC;wBAClC,KAAK,CAAC,iBAAiB,EAAE,GAAG,OAAO,CAAC;wBACpC,QAAQ;wBACR,MAAM;4BAAE;4BAAQ;wBAAO;oBACzB,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAiB;wBAAG;wBAC5B;wBACA;qBACD;YACH;YAEA,iCAAiC;YACjC,qBAAqB,QAAQ,QAAQ,CAIlC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE;oBAChC,MAAM,WAAW,IAAI;oBACrB,SAAS,MAAM,CAAC,YAAY;oBAC5B,SAAS,MAAM,CAAC,QAAQ;oBAExB,OAAO;wBACL,KAAK,CAAC,iBAAiB,EAAE,GAAG,UAAU,CAAC;wBACvC,QAAQ;wBACR,MAAM;oBACR;gBACF;gBACA,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAiB;wBAAG;qBAAE;YACrE;YAEA,gCAAgC;YAChC,oBAAoB,QAAQ,KAAK,CAK9B;gBACD,OAAO,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAK,CAAC;wBACrD,KAAK,CAAC,iBAAiB,EAAE,QAAQ,WAAW,CAAC;wBAC7C,QAAQ;4BAAE;4BAAQ;4BAAM;wBAAM;oBAChC,CAAC;gBACD,cAAc;oBAAC;iBAAgB;YACjC;YAEA,kCAAkC;YAClC,sBAAsB,QAAQ,KAAK,CAMhC;gBACD,OAAO,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAK,CAAC;wBAC3D,KAAK,CAAC,iBAAiB,EAAE,QAAQ,aAAa,CAAC;wBAC/C,QAAQ;4BAAE;4BAAM;4BAAQ;4BAAM;wBAAM;oBACtC,CAAC;gBACD,cAAc;oBAAC;iBAAgB;YACjC;YAEA,yBAAyB;YACzB,sBAAsB,QAAQ,KAAK,CAahC;gBACD,OAAO,CAAC,EAAE,KAAK,EAAE,OAAO,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,GAAK,CAAC;wBAClE,KAAK;wBACL,QAAQ;4BAAE,GAAG;4BAAO;4BAAM;4BAAS;4BAAU;wBAAM;oBACrD,CAAC;gBACD,cAAc;oBAAC;iBAAgB;YACjC;YAEA,yBAAyB;YACzB,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,GAAK,CAAC;wBACnC,KAAK,CAAC,iBAAiB,EAAE,QAAQ,YAAY,EAAE,YAAY;wBAC3D,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;oBAAiB;iBAAW;YAChD;YAEA,6BAA6B;YAC7B,yBAAyB,QAAQ,QAAQ,CAGtC;gBACD,OAAO,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,GAAK,CAAC;wBACnC,KAAK,CAAC,iBAAiB,EAAE,QAAQ,YAAY,EAAE,YAAY;wBAC3D,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;oBAAiB;iBAAW;YAChD;YAEA,+BAA+B;YAC/B,mBAAmB,QAAQ,KAAK,CAapB;gBACV,OAAO,CAAC,UAAY,CAAC,iBAAiB,EAAE,QAAQ,UAAU,CAAC;gBAC3D,cAAc;oBAAC;iBAAgB;YACjC;YAEA,8BAA8B;YAC9B,0BAA0B,QAAQ,QAAQ,CAGvC;gBACD,OAAO,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,GAAK,CAAC;wBAC5B,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAK;wBAAQ;oBACvB,CAAC;gBACD,iBAAiB;oBAAC;oBAAiB;iBAAqB;YAC1D;YAEA,yBAAyB;YACzB,sBAAsB,QAAQ,QAAQ,CAEnC;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,GAAK,CAAC;wBAClC,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAQ;wBAAQ;wBACxB,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,4BAA4B;YAC5B,yBAAyB,QAAQ,KAAK,CAAqC;gBACzE,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAgB;YACjC;YAEA,sCAAsC;YACtC,uBAAuB,QAAQ,QAAQ,CAKpC;gBACD,OAAO,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAK,CAAC;wBAC/C,KAAK,CAAC,iBAAiB,EAAE,QAAQ,OAAO,CAAC;wBACzC,QAAQ;wBACR,MAAM;4BAAE;4BAAM;4BAAS;wBAAQ;oBACjC,CAAC;YACH;YAEA,iCAAiC;YACjC,qBAAqB,QAAQ,KAAK,CAqBT;gBACvB,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,QAAQ,OAAO,MAAM,IAAI;wBAC3B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAqB;YACtC;YAEA,4BAA4B;YAC5B,wBAAwB,QAAQ,QAAQ,CAGrC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,WAAW,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,iBAAiB,EAAE,GAAG,aAAa,CAAC;wBAC1C,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAiB;wBAAG;qBAAE;YACrE;QACF,CAAC;IACD,kBAAkB;AACpB;AAGO,MAAM,EACX,yBAAyB,EACzB,4BAA4B,EAC5B,8BAA8B,EAC9B,8BAA8B,EAC9B,8BAA8B,EAC9B,8BAA8B,EAC9B,8BAA8B,EAC9B,8BAA8B,EAC9B,0BAA0B,EAC1B,4BAA4B,EAC5B,yBAAyB,EACzB,mCAAmC,EACnC,+BAA+B,EAC/B,+BAA+B,EAC/B,gCAAgC,EAChC,2BAA2B,EAC3B,iCAAiC,EACjC,4BAA4B,EAC5B,8BAA8B,EAC9B,kCAAkC,EACnC,GAAG,kBAEJ,eAAe;CACf,sDAAsD", "debugId": null}}, {"offset": {"line": 3724, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/supportApi.ts"], "sourcesContent": ["import { base<PERSON><PERSON>, ApiResponse, BaseQueryParams, createQueryParams } from './baseApi'\n\n// Define support-specific interfaces\nexport interface SupportTicket {\n  id: string\n  ticketNumber: string\n  userId: string\n  userName: string\n  userEmail: string\n  subject: string\n  description: string\n  category: 'technical' | 'billing' | 'property' | 'account' | 'general'\n  priority: 'low' | 'medium' | 'high' | 'urgent'\n  status: 'open' | 'in_progress' | 'resolved' | 'closed' | 'pending_user'\n  assignedTo?: string\n  assignedToName?: string\n  tags: string[]\n  attachments: Array<{\n    id: string\n    filename: string\n    url: string\n    size: number\n    uploadDate: string\n  }>\n  messages: SupportMessage[]\n  createdAt: string\n  updatedAt: string\n  resolvedAt?: string\n  firstResponseTime?: number\n  resolutionTime?: number\n  satisfactionRating?: number\n  satisfactionFeedback?: string\n}\n\nexport interface SupportMessage {\n  id: string\n  ticketId: string\n  senderId: string\n  senderName: string\n  senderType: 'user' | 'agent' | 'system'\n  message: string\n  attachments: Array<{\n    filename: string\n    url: string\n    size: number\n  }>\n  isInternal: boolean\n  createdAt: string\n}\n\nexport interface SupportStats {\n  totalTickets: number\n  openTickets: number\n  inProgressTickets: number\n  resolvedTickets: number\n  closedTickets: number\n  averageResponseTime: number\n  averageResolutionTime: number\n  satisfactionScore: number\n  ticketsByCategory: Array<{ category: string; count: number }>\n  ticketsByPriority: Array<{ priority: string; count: number }>\n  agentPerformance: Array<{\n    agentId: string\n    agentName: string\n    assignedTickets: number\n    resolvedTickets: number\n    averageResolutionTime: number\n    satisfactionScore: number\n  }>\n}\n\nexport interface KnowledgeBaseArticle {\n  id: string\n  title: string\n  content: string\n  category: string\n  tags: string[]\n  isPublished: boolean\n  views: number\n  helpful: number\n  notHelpful: number\n  createdBy: string\n  createdAt: string\n  updatedAt: string\n}\n\n// Define support-specific query parameters\nexport interface SupportQueryParams extends BaseQueryParams {\n  status?: string\n  priority?: string\n  category?: string\n  assignedTo?: string\n  userId?: string\n}\n\n// Define the API slice extending base API\nexport const supportApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // Get support tickets with pagination and filters\n    getSupportTickets: builder.query<ApiResponse<SupportTicket[]>, SupportQueryParams>({\n      query: (params = {}) => ({\n        url: '/support/tickets',\n        params: {\n          ...createQueryParams(params),\n          status: params.status,\n          priority: params.priority,\n          category: params.category,\n          assignedTo: params.assignedTo,\n          userId: params.userId,\n        },\n      }),\n      providesTags: ['Support'],\n    }),\n\n    // Get support ticket by ID\n    getSupportTicketById: builder.query<ApiResponse<SupportTicket>, string>({\n      query: (id) => `/support/tickets/${id}`,\n      providesTags: (_, __, id) => [{ type: 'Support', id }],\n    }),\n\n    // Get support statistics\n    getSupportStats: builder.query<ApiResponse<SupportStats>, void>({\n      query: () => '/support/stats',\n      providesTags: ['SupportStats'],\n    }),\n\n    // Create support ticket\n    createSupportTicket: builder.mutation<ApiResponse<SupportTicket>, {\n      subject: string\n      description: string\n      category: string\n      priority: string\n      attachments?: string[]\n    }>({\n      query: (ticketData) => ({\n        url: '/support/tickets',\n        method: 'POST',\n        body: ticketData,\n      }),\n      invalidatesTags: ['Support', 'SupportStats'],\n    }),\n\n    // Get presigned URL for support attachment upload\n    getSupportPresignedUrl: builder.mutation<ApiResponse<{\n      presignedUrl: string\n      fileKey: string\n      uploadUrl: string\n    }>, {\n      fileName: string\n      fileType: string\n      fileSize: number\n      uploadType: 'support-attachment'\n      ticketId?: string\n    }>({\n      query: (uploadData) => ({\n        url: '/s3/presigned-url',\n        method: 'POST',\n        body: uploadData,\n      }),\n    }),\n\n    // Confirm support attachment upload\n    confirmSupportFileUpload: builder.mutation<ApiResponse<{\n      fileUrl: string\n      fileKey: string\n    }>, {\n      ticketId?: string\n      fileKey: string\n      fileName: string\n      fileType: string\n      uploadType: 'support-attachment'\n    }>({\n      query: (confirmData) => ({\n        url: '/support/upload/confirm',\n        method: 'POST',\n        body: confirmData,\n      }),\n      invalidatesTags: (_, __, { ticketId }) =>\n        ticketId ? [{ type: 'Support', id: ticketId }] : ['Support'],\n    }),\n\n    // Update support ticket\n    updateSupportTicket: builder.mutation<ApiResponse<SupportTicket>, {\n      id: string\n      ticketData: Partial<SupportTicket>\n    }>({\n      query: ({ id, ticketData }) => ({\n        url: `/support/tickets/${id}`,\n        method: 'PUT',\n        body: ticketData,\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'Support', id },\n        'Support',\n        'SupportStats',\n      ],\n    }),\n\n    // Assign ticket\n    assignTicket: builder.mutation<ApiResponse<SupportTicket>, {\n      ticketId: string\n      assignedTo: string\n    }>({\n      query: ({ ticketId, assignedTo }) => ({\n        url: `/support/tickets/${ticketId}/assign`,\n        method: 'POST',\n        body: { assignedTo },\n      }),\n      invalidatesTags: (_, __, { ticketId }) => [\n        { type: 'Support', id: ticketId },\n        'Support',\n        'SupportStats',\n      ],\n    }),\n\n    // Add message to ticket\n    addTicketMessage: builder.mutation<ApiResponse<SupportMessage>, {\n      ticketId: string\n      content: string\n      isInternal?: boolean\n      attachments?: string[]\n    }>({\n      query: ({ ticketId, ...messageData }) => ({\n        url: `/support/tickets/${ticketId}/messages`,\n        method: 'POST',\n        body: messageData,\n      }),\n      invalidatesTags: (_, __, { ticketId }) => [\n        { type: 'Support', id: ticketId },\n        'Support',\n        'SupportStats'\n      ],\n    }),\n\n    // Resolve ticket\n    resolveTicket: builder.mutation<ApiResponse<SupportTicket>, {\n      ticketId: string\n      resolutionNotes?: string\n    }>({\n      query: ({ ticketId, resolutionNotes }) => ({\n        url: `/support/tickets/${ticketId}/resolve`,\n        method: 'POST',\n        body: { resolutionNotes },\n      }),\n      invalidatesTags: (_, __, { ticketId }) => [\n        { type: 'Support', id: ticketId },\n        'Support',\n        'SupportStats',\n      ],\n    }),\n\n    // Close ticket\n    closeTicket: builder.mutation<ApiResponse<SupportTicket>, {\n      id: string\n      resolution: string\n    }>({\n      query: ({ id, resolution }) => ({\n        url: `/support/tickets/${id}/close`,\n        method: 'PUT',\n        body: { resolution },\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'Support', id },\n        'Support',\n        'SupportStats',\n      ],\n    }),\n\n    // Reopen ticket\n    reopenTicket: builder.mutation<ApiResponse<SupportTicket>, {\n      id: string\n      reason: string\n    }>({\n      query: ({ id, reason }) => ({\n        url: `/support/tickets/${id}/reopen`,\n        method: 'PUT',\n        body: { reason },\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'Support', id },\n        'Support',\n        'SupportStats',\n      ],\n    }),\n\n    // Get knowledge base articles\n    getKnowledgeBaseArticles: builder.query<ApiResponse<KnowledgeBaseArticle[]>, {\n      category?: string\n      search?: string\n      published?: boolean\n      page?: number\n      limit?: number\n    }>({\n      query: (params = {}) => ({\n        url: '/support/knowledge-base',\n        params: {\n          category: params.category,\n          search: params.search,\n          published: params.published,\n          page: params.page || 1,\n          limit: params.limit || 10,\n        },\n      }),\n      providesTags: ['Support'],\n    }),\n\n    // Create knowledge base article\n    createKnowledgeBaseArticle: builder.mutation<ApiResponse<KnowledgeBaseArticle>, Partial<KnowledgeBaseArticle>>({\n      query: (articleData) => ({\n        url: '/support/knowledge-base',\n        method: 'POST',\n        body: articleData,\n      }),\n      invalidatesTags: ['Support'],\n    }),\n\n    // Update knowledge base article\n    updateKnowledgeBaseArticle: builder.mutation<ApiResponse<KnowledgeBaseArticle>, {\n      id: string\n      articleData: Partial<KnowledgeBaseArticle>\n    }>({\n      query: ({ id, articleData }) => ({\n        url: `/support/knowledge-base/${id}`,\n        method: 'PUT',\n        body: articleData,\n      }),\n      invalidatesTags: ['Support'],\n    }),\n\n    // Delete knowledge base article\n    deleteKnowledgeBaseArticle: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/support/knowledge-base/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Support'],\n    }),\n\n    // Get support agents\n    getSupportAgents: builder.query<ApiResponse<Array<{\n      id: string\n      name: string\n      email: string\n      isOnline: boolean\n      assignedTickets: number\n      resolvedTickets: number\n      averageRating: number\n    }>>, void>({\n      query: () => '/support/agents',\n      providesTags: ['Support'],\n    }),\n\n    // Export support tickets\n    exportSupportTickets: builder.mutation<Blob, SupportQueryParams & {\n      format: 'csv' | 'excel' | 'pdf'\n    }>({\n      query: ({ format, ...filters }) => ({\n        url: '/support/tickets/export',\n        method: 'POST',\n        body: { format, filters },\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Submit satisfaction rating\n    submitSatisfactionRating: builder.mutation<ApiResponse<void>, {\n      ticketId: string\n      rating: number\n      feedback?: string\n    }>({\n      query: ({ ticketId, rating, feedback }) => ({\n        url: `/support/tickets/${ticketId}/satisfaction`,\n        method: 'POST',\n        body: { rating, feedback },\n      }),\n      invalidatesTags: (_, __, { ticketId }) => [\n        { type: 'Support', id: ticketId },\n        'SupportStats',\n      ],\n    }),\n\n    // Get ticket analytics\n    getTicketAnalytics: builder.query<ApiResponse<{\n      resolutionTrends: Array<{ date: string; resolved: number; created: number }>\n      categoryDistribution: Array<{ category: string; count: number; percentage: number }>\n      priorityDistribution: Array<{ priority: string; count: number; avgResolutionTime: number }>\n      agentWorkload: Array<{ agentId: string; agentName: string; assigned: number; resolved: number }>\n      satisfactionTrends: Array<{ month: string; averageRating: number; responseCount: number }>\n    }>, { period?: string }>({\n      query: (params = {}) => ({\n        url: '/support/analytics',\n        params: {\n          period: params.period || '30d',\n        },\n      }),\n      providesTags: ['SupportStats'],\n    }),\n  }),\n})\n\n// Export hooks for usage in functional components\nexport const {\n  useGetSupportTicketsQuery,\n  useGetSupportTicketByIdQuery,\n  useGetSupportStatsQuery,\n  useCreateSupportTicketMutation,\n  useUpdateSupportTicketMutation,\n  useAssignTicketMutation,\n  useAddTicketMessageMutation,\n  useResolveTicketMutation,\n  useCloseTicketMutation,\n  useReopenTicketMutation,\n  useGetKnowledgeBaseArticlesQuery,\n  useCreateKnowledgeBaseArticleMutation,\n  useUpdateKnowledgeBaseArticleMutation,\n  useDeleteKnowledgeBaseArticleMutation,\n  useGetSupportAgentsQuery,\n  useExportSupportTicketsMutation,\n  useSubmitSatisfactionRatingMutation,\n  useGetTicketAnalyticsQuery,\n  useGetSupportPresignedUrlMutation,\n  useConfirmSupportFileUploadMutation,\n} = supportApi\n\n// Export types\n// Types exported in types/index.ts to avoid conflicts\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAgGO,MAAM,aAAa,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAChD,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,kDAAkD;YAClD,mBAAmB,QAAQ,KAAK,CAAmD;gBACjF,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;4BAC5B,QAAQ,OAAO,MAAM;4BACrB,UAAU,OAAO,QAAQ;4BACzB,UAAU,OAAO,QAAQ;4BACzB,YAAY,OAAO,UAAU;4BAC7B,QAAQ,OAAO,MAAM;wBACvB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAU;YAC3B;YAEA,2BAA2B;YAC3B,sBAAsB,QAAQ,KAAK,CAAqC;gBACtE,OAAO,CAAC,KAAO,CAAC,iBAAiB,EAAE,IAAI;gBACvC,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAW;wBAAG;qBAAE;YACxD;YAEA,yBAAyB;YACzB,iBAAiB,QAAQ,KAAK,CAAkC;gBAC9D,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAe;YAChC;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAMlC;gBACD,OAAO,CAAC,aAAe,CAAC;wBACtB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAW;iBAAe;YAC9C;YAEA,kDAAkD;YAClD,wBAAwB,QAAQ,QAAQ,CAUrC;gBACD,OAAO,CAAC,aAAe,CAAC;wBACtB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;YACH;YAEA,oCAAoC;YACpC,0BAA0B,QAAQ,QAAQ,CASvC;gBACD,OAAO,CAAC,cAAgB,CAAC;wBACvB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,QAAQ,EAAE,GACnC,WAAW;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAS;qBAAE,GAAG;wBAAC;qBAAU;YAChE;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,GAAK,CAAC;wBAC9B,KAAK,CAAC,iBAAiB,EAAE,IAAI;wBAC7B,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAW;wBAAG;wBACtB;wBACA;qBACD;YACH;YAEA,gBAAgB;YAChB,cAAc,QAAQ,QAAQ,CAG3B;gBACD,OAAO,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAK,CAAC;wBACpC,KAAK,CAAC,iBAAiB,EAAE,SAAS,OAAO,CAAC;wBAC1C,QAAQ;wBACR,MAAM;4BAAE;wBAAW;oBACrB,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAK;wBACxC;4BAAE,MAAM;4BAAW,IAAI;wBAAS;wBAChC;wBACA;qBACD;YACH;YAEA,wBAAwB;YACxB,kBAAkB,QAAQ,QAAQ,CAK/B;gBACD,OAAO,CAAC,EAAE,QAAQ,EAAE,GAAG,aAAa,GAAK,CAAC;wBACxC,KAAK,CAAC,iBAAiB,EAAE,SAAS,SAAS,CAAC;wBAC5C,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAK;wBACxC;4BAAE,MAAM;4BAAW,IAAI;wBAAS;wBAChC;wBACA;qBACD;YACH;YAEA,iBAAiB;YACjB,eAAe,QAAQ,QAAQ,CAG5B;gBACD,OAAO,CAAC,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAK,CAAC;wBACzC,KAAK,CAAC,iBAAiB,EAAE,SAAS,QAAQ,CAAC;wBAC3C,QAAQ;wBACR,MAAM;4BAAE;wBAAgB;oBAC1B,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAK;wBACxC;4BAAE,MAAM;4BAAW,IAAI;wBAAS;wBAChC;wBACA;qBACD;YACH;YAEA,eAAe;YACf,aAAa,QAAQ,QAAQ,CAG1B;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,GAAK,CAAC;wBAC9B,KAAK,CAAC,iBAAiB,EAAE,GAAG,MAAM,CAAC;wBACnC,QAAQ;wBACR,MAAM;4BAAE;wBAAW;oBACrB,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAW;wBAAG;wBACtB;wBACA;qBACD;YACH;YAEA,gBAAgB;YAChB,cAAc,QAAQ,QAAQ,CAG3B;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAK,CAAC;wBAC1B,KAAK,CAAC,iBAAiB,EAAE,GAAG,OAAO,CAAC;wBACpC,QAAQ;wBACR,MAAM;4BAAE;wBAAO;oBACjB,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAW;wBAAG;wBACtB;wBACA;qBACD;YACH;YAEA,8BAA8B;YAC9B,0BAA0B,QAAQ,KAAK,CAMpC;gBACD,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,UAAU,OAAO,QAAQ;4BACzB,QAAQ,OAAO,MAAM;4BACrB,WAAW,OAAO,SAAS;4BAC3B,MAAM,OAAO,IAAI,IAAI;4BACrB,OAAO,OAAO,KAAK,IAAI;wBACzB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAU;YAC3B;YAEA,gCAAgC;YAChC,4BAA4B,QAAQ,QAAQ,CAAmE;gBAC7G,OAAO,CAAC,cAAgB,CAAC;wBACvB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAU;YAC9B;YAEA,gCAAgC;YAChC,4BAA4B,QAAQ,QAAQ,CAGzC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,WAAW,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,wBAAwB,EAAE,IAAI;wBACpC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAU;YAC9B;YAEA,gCAAgC;YAChC,4BAA4B,QAAQ,QAAQ,CAA4B;gBACtE,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,wBAAwB,EAAE,IAAI;wBACpC,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAU;YAC9B;YAEA,qBAAqB;YACrB,kBAAkB,QAAQ,KAAK,CAQpB;gBACT,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAU;YAC3B;YAEA,yBAAyB;YACzB,sBAAsB,QAAQ,QAAQ,CAEnC;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,GAAK,CAAC;wBAClC,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAQ;wBAAQ;wBACxB,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,6BAA6B;YAC7B,0BAA0B,QAAQ,QAAQ,CAIvC;gBACD,OAAO,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAK,CAAC;wBAC1C,KAAK,CAAC,iBAAiB,EAAE,SAAS,aAAa,CAAC;wBAChD,QAAQ;wBACR,MAAM;4BAAE;4BAAQ;wBAAS;oBAC3B,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAK;wBACxC;4BAAE,MAAM;4BAAW,IAAI;wBAAS;wBAChC;qBACD;YACH;YAEA,uBAAuB;YACvB,oBAAoB,QAAQ,KAAK,CAMR;gBACvB,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,QAAQ,OAAO,MAAM,IAAI;wBAC3B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAe;YAChC;QACF,CAAC;AACH;AAGO,MAAM,EACX,yBAAyB,EACzB,4BAA4B,EAC5B,uBAAuB,EACvB,8BAA8B,EAC9B,8BAA8B,EAC9B,uBAAuB,EACvB,2BAA2B,EAC3B,wBAAwB,EACxB,sBAAsB,EACtB,uBAAuB,EACvB,gCAAgC,EAChC,qCAAqC,EACrC,qCAAqC,EACrC,qCAAqC,EACrC,wBAAwB,EACxB,+BAA+B,EAC/B,mCAAmC,EACnC,0BAA0B,EAC1B,iCAAiC,EACjC,mCAAmC,EACpC,GAAG,WAEJ,eAAe;CACf,sDAAsD", "debugId": null}}, {"offset": {"line": 4037, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/settingsApi.ts"], "sourcesContent": ["import { baseApi, ApiResponse } from './baseApi'\n\n// Define settings-specific interfaces\nexport interface SystemSettings {\n  id: string\n  category: 'general' | 'security' | 'email' | 'payment' | 'notification' | 'api'\n  key: string\n  value: any\n  type: 'string' | 'number' | 'boolean' | 'json' | 'array'\n  description: string\n  isPublic: boolean\n  isEditable: boolean\n  validationRules?: {\n    required?: boolean\n    min?: number\n    max?: number\n    pattern?: string\n    options?: string[]\n  }\n  updatedBy: string\n  updatedAt: string\n}\n\n// Admin settings interface (matches current page structure)\nexport interface AdminSetting {\n  id: string\n  _id?: string\n  key: string\n  name: string\n  description: string\n  category: 'authentication' | 'notification' | 'payment' | 'security' | 'system'\n  type: 'boolean' | 'string' | 'number' | 'json'\n  value: any\n  defaultValue: any\n  isActive: boolean\n  validationRules?: any\n  metadata?: any\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface EmailTemplate {\n  id: string\n  name: string\n  subject: string\n  body: string\n  type: 'welcome' | 'verification' | 'password_reset' | 'notification' | 'marketing'\n  variables: string[]\n  isActive: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface NotificationSettings {\n  id: string\n  userId?: string // null for system-wide settings\n  email: {\n    enabled: boolean\n    frequency: 'immediate' | 'daily' | 'weekly'\n    types: string[]\n  }\n  sms: {\n    enabled: boolean\n    types: string[]\n  }\n  push: {\n    enabled: boolean\n    types: string[]\n  }\n  inApp: {\n    enabled: boolean\n    types: string[]\n  }\n}\n\nexport interface SecuritySettings {\n  passwordPolicy: {\n    minLength: number\n    requireUppercase: boolean\n    requireLowercase: boolean\n    requireNumbers: boolean\n    requireSpecialChars: boolean\n    maxAge: number\n  }\n  sessionSettings: {\n    timeout: number\n    maxConcurrentSessions: number\n    requireReauth: boolean\n  }\n  twoFactorAuth: {\n    enabled: boolean\n    required: boolean\n    methods: string[]\n  }\n  ipWhitelist: string[]\n  loginAttempts: {\n    maxAttempts: number\n    lockoutDuration: number\n  }\n}\n\nexport interface PaymentSettings {\n  providers: Array<{\n    name: string\n    enabled: boolean\n    config: Record<string, any>\n  }>\n  currencies: Array<{\n    code: string\n    symbol: string\n    enabled: boolean\n    exchangeRate: number\n  }>\n  fees: {\n    transactionFee: number\n    withdrawalFee: number\n    refundFee: number\n  }\n  limits: {\n    minTransaction: number\n    maxTransaction: number\n    dailyLimit: number\n    monthlyLimit: number\n  }\n}\n\n// Define the API slice extending base API\nexport const settingsApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // Get all system settings\n    getSystemSettings: builder.query<ApiResponse<SystemSettings[]>, {\n      category?: string\n      publicOnly?: boolean\n    }>({\n      query: (params = {}) => ({\n        url: '/settings/system',\n        params: {\n          category: params.category,\n          publicOnly: params.publicOnly,\n        },\n      }),\n      providesTags: ['SystemSettings'],\n    }),\n\n    // Get setting by key\n    getSettingByKey: builder.query<ApiResponse<SystemSettings>, string>({\n      query: (key) => `/settings/system/${key}`,\n      providesTags: (_, __, key) => [{ type: 'SystemSettings', id: key }],\n    }),\n\n    // Update system setting\n    updateSystemSetting: builder.mutation<ApiResponse<SystemSettings>, {\n      key: string\n      value: any\n    }>({\n      query: ({ key, value }) => ({\n        url: `/settings/system/${key}`,\n        method: 'PUT',\n        body: { value },\n      }),\n      invalidatesTags: (_, __, { key }) => [\n        { type: 'SystemSettings', id: key },\n        'SystemSettings',\n      ],\n    }),\n\n    // Bulk update system settings\n    bulkUpdateSystemSettings: builder.mutation<ApiResponse<SystemSettings[]>, {\n      settings: Array<{ key: string; value: any }>\n    }>({\n      query: ({ settings }) => ({\n        url: '/settings/system/bulk-update',\n        method: 'PUT',\n        body: { settings },\n      }),\n      invalidatesTags: ['SystemSettings'],\n    }),\n\n    // Get email templates\n    getEmailTemplates: builder.query<ApiResponse<EmailTemplate[]>, {\n      type?: string\n      active?: boolean\n    }>({\n      query: (params = {}) => ({\n        url: '/settings/email-templates',\n        params: {\n          type: params.type,\n          active: params.active,\n        },\n      }),\n      providesTags: ['SystemSettings'],\n    }),\n\n    // Get email template by ID\n    getEmailTemplateById: builder.query<ApiResponse<EmailTemplate>, string>({\n      query: (id) => `/settings/email-templates/${id}`,\n      providesTags: (_, __, id) => [{ type: 'SystemSettings', id }],\n    }),\n\n    // Create email template\n    createEmailTemplate: builder.mutation<ApiResponse<EmailTemplate>, Partial<EmailTemplate>>({\n      query: (templateData) => ({\n        url: '/settings/email-templates',\n        method: 'POST',\n        body: templateData,\n      }),\n      invalidatesTags: ['SystemSettings'],\n    }),\n\n    // Update email template\n    updateEmailTemplate: builder.mutation<ApiResponse<EmailTemplate>, {\n      id: string\n      templateData: Partial<EmailTemplate>\n    }>({\n      query: ({ id, templateData }) => ({\n        url: `/settings/email-templates/${id}`,\n        method: 'PUT',\n        body: templateData,\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'SystemSettings', id },\n        'SystemSettings',\n      ],\n    }),\n\n    // Delete email template\n    deleteEmailTemplate: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/settings/email-templates/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['SystemSettings'],\n    }),\n\n    // Get notification settings\n    getNotificationSettings: builder.query<ApiResponse<NotificationSettings>, {\n      userId?: string\n    }>({\n      query: (params = {}) => ({\n        url: '/settings/notifications',\n        params: {\n          userId: params.userId,\n        },\n      }),\n      providesTags: ['SystemSettings'],\n    }),\n\n    // Update notification settings\n    updateNotificationSettings: builder.mutation<ApiResponse<NotificationSettings>, {\n      userId?: string\n      settings: Partial<NotificationSettings>\n    }>({\n      query: ({ userId, settings }) => ({\n        url: '/settings/notifications',\n        method: 'PUT',\n        body: { userId, ...settings },\n      }),\n      invalidatesTags: ['SystemSettings'],\n    }),\n\n    // Get security settings\n    getSecuritySettings: builder.query<ApiResponse<SecuritySettings>, void>({\n      query: () => '/settings/security',\n      providesTags: ['SystemSettings'],\n    }),\n\n    // Update security settings\n    updateSecuritySettings: builder.mutation<ApiResponse<SecuritySettings>, Partial<SecuritySettings>>({\n      query: (securityData) => ({\n        url: '/settings/security',\n        method: 'PUT',\n        body: securityData,\n      }),\n      invalidatesTags: ['SystemSettings'],\n    }),\n\n    // Get payment settings\n    getPaymentSettings: builder.query<ApiResponse<PaymentSettings>, void>({\n      query: () => '/settings/payment',\n      providesTags: ['SystemSettings'],\n    }),\n\n    // Update payment settings\n    updatePaymentSettings: builder.mutation<ApiResponse<PaymentSettings>, Partial<PaymentSettings>>({\n      query: (paymentData) => ({\n        url: '/settings/payment',\n        method: 'PUT',\n        body: paymentData,\n      }),\n      invalidatesTags: ['SystemSettings'],\n    }),\n\n    // Test email configuration\n    testEmailConfiguration: builder.mutation<ApiResponse<void>, {\n      to: string\n      subject: string\n      body: string\n    }>({\n      query: (emailData) => ({\n        url: '/settings/test-email',\n        method: 'POST',\n        body: emailData,\n      }),\n    }),\n\n    // Backup settings\n    backupSettings: builder.mutation<Blob, void>({\n      query: () => ({\n        url: '/settings/backup',\n        method: 'POST',\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Restore settings\n    restoreSettings: builder.mutation<ApiResponse<void>, {\n      file: File\n      overwrite?: boolean\n    }>({\n      query: ({ file, overwrite = false }) => {\n        const formData = new FormData()\n        formData.append('backup', file)\n        formData.append('overwrite', String(overwrite))\n        \n        return {\n          url: '/settings/restore',\n          method: 'POST',\n          body: formData,\n        }\n      },\n      invalidatesTags: ['SystemSettings'],\n    }),\n\n    // Get system logs\n    getSystemLogs: builder.query<ApiResponse<Array<{\n      id: string\n      level: 'info' | 'warning' | 'error' | 'debug'\n      message: string\n      module: string\n      userId?: string\n      ip?: string\n      userAgent?: string\n      timestamp: string\n    }>>, {\n      level?: string\n      module?: string\n      startDate?: string\n      endDate?: string\n      page?: number\n      limit?: number\n    }>({\n      query: (params = {}) => ({\n        url: '/settings/logs',\n        params: {\n          level: params.level,\n          module: params.module,\n          startDate: params.startDate,\n          endDate: params.endDate,\n          page: params.page || 1,\n          limit: params.limit || 50,\n        },\n      }),\n      providesTags: ['SystemSettings'],\n    }),\n\n    // Clear system logs\n    clearSystemLogs: builder.mutation<ApiResponse<void>, {\n      olderThan?: string\n      level?: string\n    }>({\n      query: (params) => ({\n        url: '/settings/logs/clear',\n        method: 'DELETE',\n        body: params,\n      }),\n      invalidatesTags: ['SystemSettings'],\n    }),\n\n    // Get API keys\n    getApiKeys: builder.query<ApiResponse<Array<{\n      id: string\n      name: string\n      key: string\n      permissions: string[]\n      isActive: boolean\n      lastUsed?: string\n      createdAt: string\n      expiresAt?: string\n    }>>, void>({\n      query: () => '/settings/api-keys',\n      providesTags: ['SystemSettings'],\n    }),\n\n    // Create API key\n    createApiKey: builder.mutation<ApiResponse<{\n      id: string\n      name: string\n      key: string\n      permissions: string[]\n    }>, {\n      name: string\n      permissions: string[]\n      expiresAt?: string\n    }>({\n      query: (keyData) => ({\n        url: '/settings/api-keys',\n        method: 'POST',\n        body: keyData,\n      }),\n      invalidatesTags: ['SystemSettings'],\n    }),\n\n    // Revoke API key\n    revokeApiKey: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/settings/api-keys/${id}/revoke`,\n        method: 'PUT',\n      }),\n      invalidatesTags: ['SystemSettings'],\n    }),\n\n    // Admin Settings Endpoints (matching current page structure)\n    // Get all admin settings\n    getAdminSettings: builder.query<ApiResponse<{\n      settings: AdminSetting[]\n      total?: number\n      page?: number\n      limit?: number\n    }>, {\n      category?: string\n      search?: string\n      page?: number\n      limit?: number\n    }>({\n      query: (params = {}) => ({\n        url: '/admin/settings',\n        params,\n      }),\n      providesTags: ['Settings'],\n    }),\n\n    // Get admin setting by ID\n    getAdminSettingById: builder.query<ApiResponse<AdminSetting>, string>({\n      query: (id) => `/admin/settings/${id}`,\n      providesTags: (_, __, id) => [{ type: 'Settings', id }],\n    }),\n\n    // Create admin setting\n    createAdminSetting: builder.mutation<ApiResponse<{\n      setting: AdminSetting\n    }>, Partial<AdminSetting>>({\n      query: (settingData) => ({\n        url: '/admin/settings',\n        method: 'POST',\n        body: settingData,\n      }),\n      invalidatesTags: ['Settings'],\n    }),\n\n    // Update admin setting\n    updateAdminSetting: builder.mutation<ApiResponse<AdminSetting>, {\n      id: string\n      settingData?: Partial<AdminSetting>\n      value?: any\n    }>({\n      query: ({ id, settingData, value }) => ({\n        url: `/admin/settings/${id}`,\n        method: 'PUT',\n        body: settingData || { value },\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'Settings', id },\n        'Settings',\n      ],\n    }),\n\n    // Toggle admin setting status\n    toggleAdminSettingStatus: builder.mutation<ApiResponse<AdminSetting>, {\n      id: string\n      isActive: boolean\n    }>({\n      query: ({ id, isActive }) => ({\n        url: `/admin/settings/${id}/toggle`,\n        method: 'PUT',\n        body: { isActive },\n      }),\n      invalidatesTags: (_, __, { id }) => [\n        { type: 'Settings', id },\n        'Settings',\n      ],\n    }),\n\n    // Delete admin setting\n    deleteAdminSetting: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/admin/settings/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (_, __, id) => [\n        { type: 'Settings', id },\n        'Settings',\n      ],\n    }),\n\n    // Get admin settings by category\n    getAdminSettingsByCategory: builder.query<ApiResponse<{\n      settings: AdminSetting[]\n      total?: number\n    }>, string>({\n      query: (category) => `/admin/settings/category/${category}`,\n      providesTags: (_, __, category) => [\n        { type: 'Settings', id: `category-${category}` },\n        'Settings',\n      ],\n    }),\n\n    // Reset all settings to defaults\n    resetAdminSettingsToDefaults: builder.mutation<ApiResponse<{\n      message: string\n      resetCount: number\n    }>, void>({\n      query: () => ({\n        url: '/admin/settings/reset',\n        method: 'POST',\n      }),\n      invalidatesTags: ['Settings'],\n    }),\n\n    // Export settings configuration\n    exportAdminSettings: builder.mutation<Blob, void>({\n      query: () => ({\n        url: '/admin/settings/export',\n        method: 'GET',\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Import settings configuration\n    importAdminSettings: builder.mutation<ApiResponse<{\n      message: string\n      importedCount: number\n    }>, {\n      settings: Partial<AdminSetting>[]\n      overwrite?: boolean\n    }>({\n      query: ({ settings, overwrite = false }) => ({\n        url: '/admin/settings/import',\n        method: 'POST',\n        body: { settings, overwrite },\n      }),\n      invalidatesTags: ['Settings'],\n    }),\n\n    // Get available configuration presets\n    getAdminSettingsPresets: builder.query<ApiResponse<{\n      presets: Array<{\n        name: string\n        description: string\n        settings: Record<string, any>\n      }>\n    }>, void>({\n      query: () => '/admin/settings/presets',\n      providesTags: ['Settings'],\n    }),\n\n    // Apply configuration preset\n    applyAdminSettingsPreset: builder.mutation<ApiResponse<{\n      message: string\n      appliedCount: number\n    }>, string>({\n      query: (presetName) => ({\n        url: `/admin/settings/presets/${presetName}`,\n        method: 'POST',\n      }),\n      invalidatesTags: ['Settings'],\n    }),\n\n    // Validate current configuration\n    validateAdminSettingsConfiguration: builder.query<ApiResponse<{\n      isValid: boolean\n      errors: Array<{\n        setting: string\n        error: string\n        severity: 'error' | 'warning'\n      }>\n      recommendations: Array<{\n        setting: string\n        recommendation: string\n        impact: 'high' | 'medium' | 'low'\n      }>\n    }>, void>({\n      query: () => '/admin/settings/validate',\n      providesTags: ['Settings'],\n    }),\n\n    // Get configuration recommendations\n    getAdminSettingsRecommendations: builder.query<ApiResponse<{\n      recommendations: Array<{\n        setting: string\n        currentValue: any\n        recommendedValue: any\n        reason: string\n        impact: 'high' | 'medium' | 'low'\n      }>\n    }>, void>({\n      query: () => '/admin/settings/recommendations',\n      providesTags: ['Settings'],\n    }),\n  }),\n})\n\n// Export hooks for usage in functional components\nexport const {\n  useGetSystemSettingsQuery,\n  useGetSettingByKeyQuery,\n  useUpdateSystemSettingMutation,\n  useBulkUpdateSystemSettingsMutation,\n  useGetEmailTemplatesQuery,\n  useGetEmailTemplateByIdQuery,\n  useCreateEmailTemplateMutation,\n  useUpdateEmailTemplateMutation,\n  useDeleteEmailTemplateMutation,\n  useGetNotificationSettingsQuery,\n  useUpdateNotificationSettingsMutation,\n  useGetSecuritySettingsQuery,\n  useUpdateSecuritySettingsMutation,\n  useGetPaymentSettingsQuery,\n  useUpdatePaymentSettingsMutation,\n  useTestEmailConfigurationMutation,\n  useBackupSettingsMutation,\n  useRestoreSettingsMutation,\n  useGetSystemLogsQuery,\n  useClearSystemLogsMutation,\n  useGetApiKeysQuery,\n  useCreateApiKeyMutation,\n  useRevokeApiKeyMutation,\n  // Admin Settings hooks\n  useGetAdminSettingsQuery,\n  useGetAdminSettingByIdQuery,\n  useCreateAdminSettingMutation,\n  useUpdateAdminSettingMutation,\n  useToggleAdminSettingStatusMutation,\n  useDeleteAdminSettingMutation,\n  useGetAdminSettingsByCategoryQuery,\n  useResetAdminSettingsToDefaultsMutation,\n  useExportAdminSettingsMutation,\n  useImportAdminSettingsMutation,\n  useGetAdminSettingsPresetsQuery,\n  useApplyAdminSettingsPresetMutation,\n  useValidateAdminSettingsConfigurationQuery,\n  useGetAdminSettingsRecommendationsQuery,\n} = settingsApi\n\n// Export types\n// Types exported in types/index.ts to avoid conflicts\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AA+HO,MAAM,cAAc,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACjD,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,0BAA0B;YAC1B,mBAAmB,QAAQ,KAAK,CAG7B;gBACD,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,UAAU,OAAO,QAAQ;4BACzB,YAAY,OAAO,UAAU;wBAC/B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAiB;YAClC;YAEA,qBAAqB;YACrB,iBAAiB,QAAQ,KAAK,CAAsC;gBAClE,OAAO,CAAC,MAAQ,CAAC,iBAAiB,EAAE,KAAK;gBACzC,cAAc,CAAC,GAAG,IAAI,MAAQ;wBAAC;4BAAE,MAAM;4BAAkB,IAAI;wBAAI;qBAAE;YACrE;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAK,CAAC;wBAC1B,KAAK,CAAC,iBAAiB,EAAE,KAAK;wBAC9B,QAAQ;wBACR,MAAM;4BAAE;wBAAM;oBAChB,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,GAAK;wBACnC;4BAAE,MAAM;4BAAkB,IAAI;wBAAI;wBAClC;qBACD;YACH;YAEA,8BAA8B;YAC9B,0BAA0B,QAAQ,QAAQ,CAEvC;gBACD,OAAO,CAAC,EAAE,QAAQ,EAAE,GAAK,CAAC;wBACxB,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;wBAAS;oBACnB,CAAC;gBACD,iBAAiB;oBAAC;iBAAiB;YACrC;YAEA,sBAAsB;YACtB,mBAAmB,QAAQ,KAAK,CAG7B;gBACD,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,MAAM,OAAO,IAAI;4BACjB,QAAQ,OAAO,MAAM;wBACvB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAiB;YAClC;YAEA,2BAA2B;YAC3B,sBAAsB,QAAQ,KAAK,CAAqC;gBACtE,OAAO,CAAC,KAAO,CAAC,0BAA0B,EAAE,IAAI;gBAChD,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAkB;wBAAG;qBAAE;YAC/D;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAAqD;gBACxF,OAAO,CAAC,eAAiB,CAAC;wBACxB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAiB;YACrC;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,GAAK,CAAC;wBAChC,KAAK,CAAC,0BAA0B,EAAE,IAAI;wBACtC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAkB;wBAAG;wBAC7B;qBACD;YACH;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAA4B;gBAC/D,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,0BAA0B,EAAE,IAAI;wBACtC,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAiB;YACrC;YAEA,4BAA4B;YAC5B,yBAAyB,QAAQ,KAAK,CAEnC;gBACD,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,QAAQ,OAAO,MAAM;wBACvB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAiB;YAClC;YAEA,+BAA+B;YAC/B,4BAA4B,QAAQ,QAAQ,CAGzC;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAK,CAAC;wBAChC,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAQ,GAAG,QAAQ;wBAAC;oBAC9B,CAAC;gBACD,iBAAiB;oBAAC;iBAAiB;YACrC;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,KAAK,CAAsC;gBACtE,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAiB;YAClC;YAEA,2BAA2B;YAC3B,wBAAwB,QAAQ,QAAQ,CAA2D;gBACjG,OAAO,CAAC,eAAiB,CAAC;wBACxB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAiB;YACrC;YAEA,uBAAuB;YACvB,oBAAoB,QAAQ,KAAK,CAAqC;gBACpE,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAiB;YAClC;YAEA,0BAA0B;YAC1B,uBAAuB,QAAQ,QAAQ,CAAyD;gBAC9F,OAAO,CAAC,cAAgB,CAAC;wBACvB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAiB;YACrC;YAEA,2BAA2B;YAC3B,wBAAwB,QAAQ,QAAQ,CAIrC;gBACD,OAAO,CAAC,YAAc,CAAC;wBACrB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;YACH;YAEA,kBAAkB;YAClB,gBAAgB,QAAQ,QAAQ,CAAa;gBAC3C,OAAO,IAAM,CAAC;wBACZ,KAAK;wBACL,QAAQ;wBACR,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,mBAAmB;YACnB,iBAAiB,QAAQ,QAAQ,CAG9B;gBACD,OAAO,CAAC,EAAE,IAAI,EAAE,YAAY,KAAK,EAAE;oBACjC,MAAM,WAAW,IAAI;oBACrB,SAAS,MAAM,CAAC,UAAU;oBAC1B,SAAS,MAAM,CAAC,aAAa,OAAO;oBAEpC,OAAO;wBACL,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR;gBACF;gBACA,iBAAiB;oBAAC;iBAAiB;YACrC;YAEA,kBAAkB;YAClB,eAAe,QAAQ,KAAK,CAgBzB;gBACD,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,OAAO,OAAO,KAAK;4BACnB,QAAQ,OAAO,MAAM;4BACrB,WAAW,OAAO,SAAS;4BAC3B,SAAS,OAAO,OAAO;4BACvB,MAAM,OAAO,IAAI,IAAI;4BACrB,OAAO,OAAO,KAAK,IAAI;wBACzB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAiB;YAClC;YAEA,oBAAoB;YACpB,iBAAiB,QAAQ,QAAQ,CAG9B;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAiB;YACrC;YAEA,eAAe;YACf,YAAY,QAAQ,KAAK,CASd;gBACT,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAiB;YAClC;YAEA,iBAAiB;YACjB,cAAc,QAAQ,QAAQ,CAS3B;gBACD,OAAO,CAAC,UAAY,CAAC;wBACnB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAiB;YACrC;YAEA,iBAAiB;YACjB,cAAc,QAAQ,QAAQ,CAA4B;gBACxD,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,mBAAmB,EAAE,GAAG,OAAO,CAAC;wBACtC,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAiB;YACrC;YAEA,6DAA6D;YAC7D,yBAAyB;YACzB,kBAAkB,QAAQ,KAAK,CAU5B;gBACD,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAW;YAC5B;YAEA,0BAA0B;YAC1B,qBAAqB,QAAQ,KAAK,CAAoC;gBACpE,OAAO,CAAC,KAAO,CAAC,gBAAgB,EAAE,IAAI;gBACtC,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAY;wBAAG;qBAAE;YACzD;YAEA,uBAAuB;YACvB,oBAAoB,QAAQ,QAAQ,CAET;gBACzB,OAAO,CAAC,cAAgB,CAAC;wBACvB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAW;YAC/B;YAEA,uBAAuB;YACvB,oBAAoB,QAAQ,QAAQ,CAIjC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,GAAK,CAAC;wBACtC,KAAK,CAAC,gBAAgB,EAAE,IAAI;wBAC5B,QAAQ;wBACR,MAAM,eAAe;4BAAE;wBAAM;oBAC/B,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAY;wBAAG;wBACvB;qBACD;YACH;YAEA,8BAA8B;YAC9B,0BAA0B,QAAQ,QAAQ,CAGvC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAK,CAAC;wBAC5B,KAAK,CAAC,gBAAgB,EAAE,GAAG,OAAO,CAAC;wBACnC,QAAQ;wBACR,MAAM;4BAAE;wBAAS;oBACnB,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAClC;4BAAE,MAAM;4BAAY;wBAAG;wBACvB;qBACD;YACH;YAEA,uBAAuB;YACvB,oBAAoB,QAAQ,QAAQ,CAA4B;gBAC9D,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,gBAAgB,EAAE,IAAI;wBAC5B,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,KAAO;wBAC9B;4BAAE,MAAM;4BAAY;wBAAG;wBACvB;qBACD;YACH;YAEA,iCAAiC;YACjC,4BAA4B,QAAQ,KAAK,CAG7B;gBACV,OAAO,CAAC,WAAa,CAAC,yBAAyB,EAAE,UAAU;gBAC3D,cAAc,CAAC,GAAG,IAAI,WAAa;wBACjC;4BAAE,MAAM;4BAAY,IAAI,CAAC,SAAS,EAAE,UAAU;wBAAC;wBAC/C;qBACD;YACH;YAEA,iCAAiC;YACjC,8BAA8B,QAAQ,QAAQ,CAGpC;gBACR,OAAO,IAAM,CAAC;wBACZ,KAAK;wBACL,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAW;YAC/B;YAEA,gCAAgC;YAChC,qBAAqB,QAAQ,QAAQ,CAAa;gBAChD,OAAO,IAAM,CAAC;wBACZ,KAAK;wBACL,QAAQ;wBACR,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,gCAAgC;YAChC,qBAAqB,QAAQ,QAAQ,CAMlC;gBACD,OAAO,CAAC,EAAE,QAAQ,EAAE,YAAY,KAAK,EAAE,GAAK,CAAC;wBAC3C,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAU;wBAAU;oBAC9B,CAAC;gBACD,iBAAiB;oBAAC;iBAAW;YAC/B;YAEA,sCAAsC;YACtC,yBAAyB,QAAQ,KAAK,CAM5B;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAW;YAC5B;YAEA,6BAA6B;YAC7B,0BAA0B,QAAQ,QAAQ,CAG9B;gBACV,OAAO,CAAC,aAAe,CAAC;wBACtB,KAAK,CAAC,wBAAwB,EAAE,YAAY;wBAC5C,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAW;YAC/B;YAEA,iCAAiC;YACjC,oCAAoC,QAAQ,KAAK,CAYvC;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAW;YAC5B;YAEA,oCAAoC;YACpC,iCAAiC,QAAQ,KAAK,CAQpC;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAW;YAC5B;QACF,CAAC;AACH;AAGO,MAAM,EACX,yBAAyB,EACzB,uBAAuB,EACvB,8BAA8B,EAC9B,mCAAmC,EACnC,yBAAyB,EACzB,4BAA4B,EAC5B,8BAA8B,EAC9B,8BAA8B,EAC9B,8BAA8B,EAC9B,+BAA+B,EAC/B,qCAAqC,EACrC,2BAA2B,EAC3B,iCAAiC,EACjC,0BAA0B,EAC1B,gCAAgC,EAChC,iCAAiC,EACjC,yBAAyB,EACzB,0BAA0B,EAC1B,qBAAqB,EACrB,0BAA0B,EAC1B,kBAAkB,EAClB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB;AACvB,wBAAwB,EACxB,2BAA2B,EAC3B,6BAA6B,EAC7B,6BAA6B,EAC7B,mCAAmC,EACnC,6BAA6B,EAC7B,kCAAkC,EAClC,uCAAuC,EACvC,8BAA8B,EAC9B,8BAA8B,EAC9B,+BAA+B,EAC/B,mCAAmC,EACnC,0CAA0C,EAC1C,uCAAuC,EACxC,GAAG,YAEJ,eAAe;CACf,sDAAsD", "debugId": null}}, {"offset": {"line": 4513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/adminPaymentApi.ts"], "sourcesContent": ["import { baseApi } from './baseApi'\n\n// Types\nexport interface User {\n  _id: string\n  firstName: string\n  lastName: string\n  email: string\n  phone: string\n  walletBalance: number\n  createdAt: string\n}\n\nexport interface Transaction {\n  _id: string\n  transactionId: string\n  userId: {\n    _id: string\n    firstName: string\n    lastName: string\n    email: string\n    phone: string\n  }\n  transactionType: 'credit' | 'debit'\n  amount: number\n  description: string\n  referenceType: string\n  paymentMethod?: string\n  paymentReference?: string\n  balanceBefore: number\n  balanceAfter: number\n  status: 'pending' | 'completed' | 'failed' | 'cancelled'\n  metadata?: any\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface TransactionStats {\n  totalAmount: number\n  totalCredit: number\n  totalDebit: number\n  totalTransactions: number\n}\n\nexport interface PaginationInfo {\n  page: number\n  limit: number\n  total: number\n  pages: number\n}\n\nexport interface AddFundsRequest {\n  userId: string\n  amount: number\n  description?: string\n  reference?: string\n}\n\nexport interface DeductFundsRequest {\n  userId: string\n  amount: number\n  description?: string\n  reference?: string\n}\n\nexport interface WithdrawalApprovalRequest {\n  transactionId: string\n  adminNotes?: string\n}\n\nexport interface WithdrawalRejectionRequest {\n  transactionId: string\n  reason?: string\n}\n\nexport interface TransactionFilters {\n  page?: number\n  limit?: number\n  userId?: string\n  status?: string\n  type?: string\n  startDate?: string\n  endDate?: string\n}\n\nexport const adminPaymentApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    // Add funds to user\n    addFundsToUser: builder.mutation<any, AddFundsRequest>({\n      query: (data) => ({\n        url: '/admin/payments/add-funds',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Transaction', 'User'],\n    }),\n\n    // Deduct funds from user\n    deductFundsFromUser: builder.mutation<any, DeductFundsRequest>({\n      query: (data) => ({\n        url: '/admin/payments/deduct-funds',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Transaction', 'User'],\n    }),\n\n    // Get all transactions\n    getAllTransactions: builder.query<{\n      transactions: Transaction[]\n      pagination: PaginationInfo\n      statistics: TransactionStats\n    }, TransactionFilters>({\n      query: (filters = {}) => {\n        const params = new URLSearchParams()\n        Object.entries(filters).forEach(([key, value]) => {\n          if (value !== undefined && value !== '') {\n            params.append(key, value.toString())\n          }\n        })\n        return `/admin/payments/transactions?${params.toString()}`\n      },\n      transformResponse: (response: any) => {\n        // Handle the API response structure\n        if (response.success && response.data) {\n          return response.data\n        }\n        return { transactions: [], pagination: { page: 1, limit: 50, total: 0, pages: 0 }, statistics: { totalAmount: 0, totalCredit: 0, totalDebit: 0, totalTransactions: 0 } }\n      },\n      providesTags: ['Transaction'],\n    }),\n\n    // Get pending withdrawals\n    getPendingWithdrawals: builder.query<{\n      withdrawals: Transaction[]\n      pagination: PaginationInfo\n    }, { page?: number; limit?: number }>({\n      query: ({ page = 1, limit = 20 } = {}) =>\n        `/admin/payments/withdrawals/pending?page=${page}&limit=${limit}`,\n      transformResponse: (response: any) => {\n        // Handle the API response structure\n        if (response.success && response.data) {\n          return response.data\n        }\n        return { withdrawals: [], pagination: { page: 1, limit: 20, total: 0, pages: 0 } }\n      },\n      providesTags: ['Transaction'],\n    }),\n\n    // Approve withdrawal\n    approveWithdrawal: builder.mutation<any, WithdrawalApprovalRequest>({\n      query: (data) => ({\n        url: '/admin/payments/withdrawals/approve',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Transaction'],\n    }),\n\n    // Reject withdrawal\n    rejectWithdrawal: builder.mutation<any, WithdrawalRejectionRequest>({\n      query: (data) => ({\n        url: '/admin/payments/withdrawals/reject',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Transaction'],\n    }),\n\n    // Search users\n    searchUsers: builder.query<{\n      users: User[]\n      total: number\n    }, { query: string; limit?: number }>({\n      query: ({ query, limit = 10 }) =>\n        `/admin/payments/users/search?query=${encodeURIComponent(query)}&limit=${limit}`,\n      transformResponse: (response: any) => {\n        // Debug logging\n        console.log('Search Users API Response:', response)\n\n        // Handle the API response structure\n        if (response.success && response.data) {\n          return response.data\n        }\n        return { users: [], total: 0 }\n      },\n      providesTags: ['User'],\n    }),\n  }),\n  overrideExisting: false,\n})\n\nexport const {\n  useAddFundsToUserMutation,\n  useDeductFundsFromUserMutation,\n  useGetAllTransactionsQuery,\n  useGetPendingWithdrawalsQuery,\n  useApproveWithdrawalMutation,\n  useRejectWithdrawalMutation,\n  useSearchUsersQuery,\n} = adminPaymentApi\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAqFO,MAAM,kBAAkB,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACrD,WAAW,CAAC,UAAY,CAAC;YACvB,oBAAoB;YACpB,gBAAgB,QAAQ,QAAQ,CAAuB;gBACrD,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAe;iBAAO;YAC1C;YAEA,yBAAyB;YACzB,qBAAqB,QAAQ,QAAQ,CAA0B;gBAC7D,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAe;iBAAO;YAC1C;YAEA,uBAAuB;YACvB,oBAAoB,QAAQ,KAAK,CAIV;gBACrB,OAAO,CAAC,UAAU,CAAC,CAAC;oBAClB,MAAM,SAAS,IAAI;oBACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;wBAC3C,IAAI,UAAU,aAAa,UAAU,IAAI;4BACvC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;wBACnC;oBACF;oBACA,OAAO,CAAC,6BAA6B,EAAE,OAAO,QAAQ,IAAI;gBAC5D;gBACA,mBAAmB,CAAC;oBAClB,oCAAoC;oBACpC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,OAAO,SAAS,IAAI;oBACtB;oBACA,OAAO;wBAAE,cAAc,EAAE;wBAAE,YAAY;4BAAE,MAAM;4BAAG,OAAO;4BAAI,OAAO;4BAAG,OAAO;wBAAE;wBAAG,YAAY;4BAAE,aAAa;4BAAG,aAAa;4BAAG,YAAY;4BAAG,mBAAmB;wBAAE;oBAAE;gBACzK;gBACA,cAAc;oBAAC;iBAAc;YAC/B;YAEA,0BAA0B;YAC1B,uBAAuB,QAAQ,KAAK,CAGE;gBACpC,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC,GACnC,CAAC,yCAAyC,EAAE,KAAK,OAAO,EAAE,OAAO;gBACnE,mBAAmB,CAAC;oBAClB,oCAAoC;oBACpC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,OAAO,SAAS,IAAI;oBACtB;oBACA,OAAO;wBAAE,aAAa,EAAE;wBAAE,YAAY;4BAAE,MAAM;4BAAG,OAAO;4BAAI,OAAO;4BAAG,OAAO;wBAAE;oBAAE;gBACnF;gBACA,cAAc;oBAAC;iBAAc;YAC/B;YAEA,qBAAqB;YACrB,mBAAmB,QAAQ,QAAQ,CAAiC;gBAClE,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAc;YAClC;YAEA,oBAAoB;YACpB,kBAAkB,QAAQ,QAAQ,CAAkC;gBAClE,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAc;YAClC;YAEA,eAAe;YACf,aAAa,QAAQ,KAAK,CAGY;gBACpC,OAAO,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,GAC3B,CAAC,mCAAmC,EAAE,mBAAmB,OAAO,OAAO,EAAE,OAAO;gBAClF,mBAAmB,CAAC;oBAClB,gBAAgB;oBAChB,QAAQ,GAAG,CAAC,8BAA8B;oBAE1C,oCAAoC;oBACpC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,OAAO,SAAS,IAAI;oBACtB;oBACA,OAAO;wBAAE,OAAO,EAAE;wBAAE,OAAO;oBAAE;gBAC/B;gBACA,cAAc;oBAAC;iBAAO;YACxB;QACF,CAAC;IACD,kBAAkB;AACpB;AAEO,MAAM,EACX,yBAAyB,EACzB,8BAA8B,EAC9B,0BAA0B,EAC1B,6BAA6B,EAC7B,4BAA4B,EAC5B,2BAA2B,EAC3B,mBAAmB,EACpB,GAAG", "debugId": null}}, {"offset": {"line": 4663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/adminUserApi.ts"], "sourcesContent": ["import { baseApi } from './baseApi'\n\n// Types\nexport interface AdminUser {\n  _id: string\n  firstName: string\n  lastName: string\n  email: string\n  phone: string\n  role: string\n  status: 'active' | 'inactive' | 'blocked' | 'suspended'\n  createdAt: string\n  updatedAt: string\n  kyc: {\n    status: 'not_submitted' | 'pending' | 'approved' | 'rejected' | 'under_review'\n    verificationLevel: 'none' | 'basic' | 'intermediate' | 'advanced'\n    documents?: any[]\n  }\n  wallet: {\n    balance: number\n  }\n  walletBalance: number // For backward compatibility\n}\n\nexport interface UserDetails {\n  user: AdminUser & {\n    emailVerified: boolean\n    phoneVerified: boolean\n    kycStatus: string\n    referredBy?: string\n    avatar?: {\n      uploadedAt: string\n    }\n    referralCode: string\n    lastLogin?: string\n  }\n  kyc: {\n    _id: string\n    userId: string\n    country: string\n    documents: Array<{\n      _id?: string\n      type: string\n      category: string\n      documentNumber?: string\n      documentUrl: string\n      uploadedAt: string\n      status: string\n      subType?: string\n      rejectionReason?: string\n    }>\n    address: {\n      street: string\n      city: string\n      state: string\n      postalCode: string\n      country: string\n      addressType: string\n      residenceSince: string\n    }\n    status: string\n    riskLevel: string\n    complianceFlags: any[]\n    completedSteps: string[]\n    createdAt: string\n    updatedAt: string\n    personalInfo: {\n      nationality: string\n      placeOfBirth: string\n      gender: string\n      maritalStatus: string\n    }\n    submittedAt?: string\n    level?: string\n    reviewedAt?: string\n    reviewedBy?: {\n      firstName: string\n      lastName: string\n    }\n    rejectionReason?: string\n    identityInfo?: {\n      aadharNumber?: string\n      panNumber?: string\n      passportNumber?: string\n      drivingLicenseNumber?: string\n    }\n    bankInfo?: {\n      accountNumber?: string\n      ifscCode?: string\n      bankName?: string\n      accountType?: string\n      accountHolderName?: string\n    }\n  }\n  wallet: {\n    _id: string\n    userId: string\n    balance: number\n    totalInvested: number\n    totalReturns: number\n    totalReferralEarnings: number\n    totalDeposited: number\n    totalWithdrawn: number\n    pendingWithdrawals: number\n    lifetimeEarnings: number\n    currency: string\n    isActive: boolean\n    createdAt: string\n    updatedAt: string\n    lastTransactionAt?: string\n  }\n  recentTransactions: any[]\n  transactionStats: {\n    totalTransactions: number\n    totalCredit: number\n    totalDebit: number\n  }\n}\n\nexport interface UserKYC {\n  user: {\n    _id: string\n    firstName: string\n    lastName: string\n    email: string\n    phone: string\n  }\n  kyc: {\n    status: string\n    verificationLevel: string\n    documents: any[]\n    personalInfo: any\n    addressInfo: any\n    employmentInfo: any\n    createdAt?: string\n    updatedAt?: string\n  }\n}\n\nexport interface UserStats {\n  totalUsers: number\n  activeUsers: number\n  inactiveUsers: number\n  blockedUsers: number\n}\n\nexport interface PaginationInfo {\n  page: number\n  limit: number\n  total: number\n  pages: number\n}\n\nexport interface UserFilters {\n  page?: number\n  limit?: number\n  search?: string\n  status?: string\n  kycStatus?: string\n  role?: string\n  sortBy?: string\n  sortOrder?: 'asc' | 'desc'\n}\n\nexport interface UpdateUserStatusRequest {\n  userId: string\n  status: 'active' | 'inactive' | 'blocked' | 'suspended'\n  reason?: string\n}\n\nexport const adminUserApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    // Get all users\n    getAllUsers: builder.query<{\n      users: AdminUser[]\n      pagination: PaginationInfo\n      statistics: UserStats\n    }, UserFilters>({\n      query: (filters = {}) => {\n        const params = new URLSearchParams()\n        Object.entries(filters).forEach(([key, value]) => {\n          if (value !== undefined && value !== '') {\n            params.append(key, value.toString())\n          }\n        })\n        return `/admin/users?${params.toString()}`\n      },\n      transformResponse: (response: any) => {\n        // Debug logging\n        console.log('Get All Users API Response:', response)\n        \n        // Handle the API response structure\n        if (response.success && response.data) {\n          return response.data\n        }\n        return { \n          users: [], \n          pagination: { page: 1, limit: 20, total: 0, pages: 0 }, \n          statistics: { totalUsers: 0, activeUsers: 0, inactiveUsers: 0, blockedUsers: 0 } \n        }\n      },\n      providesTags: ['User'],\n    }),\n\n    // Get user details\n    getUserDetails: builder.query<UserDetails, string>({\n      query: (userId) => `/admin/users/${userId}`,\n      transformResponse: (response: any) => {\n        // Debug logging\n        console.log('Get User Details API Response:', response)\n        \n        // Handle the API response structure\n        if (response.success && response.data) {\n          return response.data\n        }\n        return null\n      },\n      providesTags: (result, error, userId) => [{ type: 'User', id: userId }],\n    }),\n\n    // Get user KYC details\n    getUserKYC: builder.query<UserKYC, string>({\n      query: (userId) => `/admin/users/${userId}/kyc`,\n      transformResponse: (response: any) => {\n        // Debug logging\n        console.log('Get User KYC API Response:', response)\n        \n        // Handle the API response structure\n        if (response.success && response.data) {\n          return response.data\n        }\n        return null\n      },\n      providesTags: (result, error, userId) => [{ type: 'User', id: userId }],\n    }),\n\n    // Update user status\n    updateUserStatus: builder.mutation<any, UpdateUserStatusRequest>({\n      query: ({ userId, ...data }) => ({\n        url: `/admin/users/${userId}/status`,\n        method: 'PUT',\n        body: data,\n      }),\n      transformResponse: (response: any) => {\n        // Debug logging\n        console.log('Update User Status API Response:', response)\n        \n        // Handle the API response structure\n        if (response.success && response.data) {\n          return response.data\n        }\n        return response\n      },\n      invalidatesTags: (result, error, { userId }) => [\n        'User',\n        { type: 'User', id: userId }\n      ],\n    }),\n\n    // Search users (for payment management)\n    searchUsersForPayments: builder.query<{\n      users: AdminUser[]\n      total: number\n    }, { query: string; limit?: number }>({\n      query: ({ query, limit = 10 }) => \n        `/admin/payments/users/search?query=${encodeURIComponent(query)}&limit=${limit}`,\n      transformResponse: (response: any) => {\n        // Debug logging\n        console.log('Search Users for Payments API Response:', response)\n        \n        // Handle the API response structure\n        if (response.success && response.data) {\n          return response.data\n        }\n        return { users: [], total: 0 }\n      },\n      providesTags: ['User'],\n    }),\n\n    // Admin Password Reset for User\n    adminResetUserPassword: builder.mutation<\n      { success: boolean; message: string },\n      { userId: string; sendEmail?: boolean }\n    >({\n      query: ({ userId, sendEmail = true }) => ({\n        url: `/admin/users/${userId}/reset-password`,\n        method: 'POST',\n        body: { sendEmail },\n      }),\n      invalidatesTags: ['User'],\n    }),\n  }),\n  overrideExisting: false,\n})\n\nexport const {\n  useGetAllUsersQuery,\n  useGetUserDetailsQuery,\n  useGetUserKYCQuery,\n  useUpdateUserStatusMutation,\n  useSearchUsersForPaymentsQuery,\n  useAdminResetUserPasswordMutation,\n} = adminUserApi\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AA0KO,MAAM,eAAe,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAClD,WAAW,CAAC,UAAY,CAAC;YACvB,gBAAgB;YAChB,aAAa,QAAQ,KAAK,CAIV;gBACd,OAAO,CAAC,UAAU,CAAC,CAAC;oBAClB,MAAM,SAAS,IAAI;oBACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;wBAC3C,IAAI,UAAU,aAAa,UAAU,IAAI;4BACvC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;wBACnC;oBACF;oBACA,OAAO,CAAC,aAAa,EAAE,OAAO,QAAQ,IAAI;gBAC5C;gBACA,mBAAmB,CAAC;oBAClB,gBAAgB;oBAChB,QAAQ,GAAG,CAAC,+BAA+B;oBAE3C,oCAAoC;oBACpC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,OAAO,SAAS,IAAI;oBACtB;oBACA,OAAO;wBACL,OAAO,EAAE;wBACT,YAAY;4BAAE,MAAM;4BAAG,OAAO;4BAAI,OAAO;4BAAG,OAAO;wBAAE;wBACrD,YAAY;4BAAE,YAAY;4BAAG,aAAa;4BAAG,eAAe;4BAAG,cAAc;wBAAE;oBACjF;gBACF;gBACA,cAAc;oBAAC;iBAAO;YACxB;YAEA,mBAAmB;YACnB,gBAAgB,QAAQ,KAAK,CAAsB;gBACjD,OAAO,CAAC,SAAW,CAAC,aAAa,EAAE,QAAQ;gBAC3C,mBAAmB,CAAC;oBAClB,gBAAgB;oBAChB,QAAQ,GAAG,CAAC,kCAAkC;oBAE9C,oCAAoC;oBACpC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,OAAO,SAAS,IAAI;oBACtB;oBACA,OAAO;gBACT;gBACA,cAAc,CAAC,QAAQ,OAAO,SAAW;wBAAC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;qBAAE;YACzE;YAEA,uBAAuB;YACvB,YAAY,QAAQ,KAAK,CAAkB;gBACzC,OAAO,CAAC,SAAW,CAAC,aAAa,EAAE,OAAO,IAAI,CAAC;gBAC/C,mBAAmB,CAAC;oBAClB,gBAAgB;oBAChB,QAAQ,GAAG,CAAC,8BAA8B;oBAE1C,oCAAoC;oBACpC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,OAAO,SAAS,IAAI;oBACtB;oBACA,OAAO;gBACT;gBACA,cAAc,CAAC,QAAQ,OAAO,SAAW;wBAAC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;qBAAE;YACzE;YAEA,qBAAqB;YACrB,kBAAkB,QAAQ,QAAQ,CAA+B;gBAC/D,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,MAAM,GAAK,CAAC;wBAC/B,KAAK,CAAC,aAAa,EAAE,OAAO,OAAO,CAAC;wBACpC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,mBAAmB,CAAC;oBAClB,gBAAgB;oBAChB,QAAQ,GAAG,CAAC,oCAAoC;oBAEhD,oCAAoC;oBACpC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,OAAO,SAAS,IAAI;oBACtB;oBACA,OAAO;gBACT;gBACA,iBAAiB,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC9C;wBACA;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;qBAC5B;YACH;YAEA,wCAAwC;YACxC,wBAAwB,QAAQ,KAAK,CAGC;gBACpC,OAAO,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,GAC3B,CAAC,mCAAmC,EAAE,mBAAmB,OAAO,OAAO,EAAE,OAAO;gBAClF,mBAAmB,CAAC;oBAClB,gBAAgB;oBAChB,QAAQ,GAAG,CAAC,2CAA2C;oBAEvD,oCAAoC;oBACpC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,OAAO,SAAS,IAAI;oBACtB;oBACA,OAAO;wBAAE,OAAO,EAAE;wBAAE,OAAO;oBAAE;gBAC/B;gBACA,cAAc;oBAAC;iBAAO;YACxB;YAEA,gCAAgC;YAChC,wBAAwB,QAAQ,QAAQ,CAGtC;gBACA,OAAO,CAAC,EAAE,MAAM,EAAE,YAAY,IAAI,EAAE,GAAK,CAAC;wBACxC,KAAK,CAAC,aAAa,EAAE,OAAO,eAAe,CAAC;wBAC5C,QAAQ;wBACR,MAAM;4BAAE;wBAAU;oBACpB,CAAC;gBACD,iBAAiB;oBAAC;iBAAO;YAC3B;QACF,CAAC;IACD,kBAAkB;AACpB;AAEO,MAAM,EACX,mBAAmB,EACnB,sBAAsB,EACtB,kBAAkB,EAClB,2BAA2B,EAC3B,8BAA8B,EAC9B,iCAAiC,EAClC,GAAG", "debugId": null}}, {"offset": {"line": 4821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/analyticsApi.ts"], "sourcesContent": ["import { baseApi, ApiResponse } from './baseApi'\n\nexport interface SystemAnalytics {\n  totalUsers: number\n  activeUsers: number\n  totalProperties: number\n  totalInvestments: number\n  totalRevenue: number\n  monthlyGrowth: number\n  userGrowthRate: number\n  propertyGrowthRate: number\n  revenueGrowthRate: number\n  topPerformingProperties: {\n    id: string\n    title: string\n    totalInvestment: number\n    roi: number\n  }[]\n  userActivityTrends: {\n    date: string\n    activeUsers: number\n    newRegistrations: number\n    totalTransactions: number\n  }[]\n  revenueBreakdown: {\n    category: string\n    amount: number\n    percentage: number\n  }[]\n}\n\nexport interface UserAnalytics {\n  totalUsers: number\n  verifiedUsers: number\n  kycApprovedUsers: number\n  activeInvestors: number\n  usersByRegion: {\n    region: string\n    count: number\n    percentage: number\n  }[]\n  usersByAge: {\n    ageGroup: string\n    count: number\n    percentage: number\n  }[]\n  userRegistrationTrends: {\n    month: string\n    registrations: number\n    verifications: number\n  }[]\n  topInvestors: {\n    id: string\n    name: string\n    email: string\n    totalInvestment: number\n    propertiesOwned: number\n  }[]\n}\n\nexport interface PropertyAnalytics {\n  totalProperties: number\n  availableProperties: number\n  soldProperties: number\n  averagePropertyValue: number\n  totalPropertyValue: number\n  propertiesByType: {\n    type: string\n    count: number\n    totalValue: number\n  }[]\n  propertiesByLocation: {\n    city: string\n    state: string\n    count: number\n    averagePrice: number\n  }[]\n  propertyPerformance: {\n    id: string\n    title: string\n    totalInvestment: number\n    roi: number\n    occupancyRate: number\n  }[]\n  investmentTrends: {\n    month: string\n    totalInvestment: number\n    propertiesSold: number\n    averageInvestment: number\n  }[]\n}\n\nexport interface FinancialAnalytics {\n  totalRevenue: number\n  totalCommissions: number\n  totalProfits: number\n  monthlyRevenue: {\n    month: string\n    revenue: number\n    commissions: number\n    profits: number\n  }[]\n  revenueBySource: {\n    source: string\n    amount: number\n    percentage: number\n  }[]\n  expenseBreakdown: {\n    category: string\n    amount: number\n    percentage: number\n  }[]\n  profitMargins: {\n    month: string\n    grossMargin: number\n    netMargin: number\n  }[]\n}\n\nexport const analyticsApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // System Analytics\n    getSystemAnalytics: builder.query<ApiResponse<SystemAnalytics>, {\n      startDate?: string\n      endDate?: string\n      period?: 'daily' | 'weekly' | 'monthly' | 'yearly'\n    }>({\n      query: (params) => ({\n        url: '/admin/analytics/system',\n        params,\n      }),\n      providesTags: ['Analytics', 'Dashboard'],\n    }),\n\n    // User Analytics\n    getUserAnalytics: builder.query<ApiResponse<UserAnalytics>, {\n      startDate?: string\n      endDate?: string\n      region?: string\n    }>({\n      query: (params) => ({\n        url: '/admin/analytics/users',\n        params,\n      }),\n      providesTags: ['Analytics', 'User'],\n    }),\n\n    // Property Analytics\n    getPropertyAnalytics: builder.query<ApiResponse<PropertyAnalytics>, {\n      startDate?: string\n      endDate?: string\n      propertyType?: string\n      location?: string\n    }>({\n      query: (params) => ({\n        url: '/admin/analytics/properties',\n        params,\n      }),\n      providesTags: ['Analytics', 'Property'],\n    }),\n\n    // Financial Analytics\n    getFinancialAnalytics: builder.query<ApiResponse<FinancialAnalytics>, {\n      startDate?: string\n      endDate?: string\n      period?: 'monthly' | 'quarterly' | 'yearly'\n    }>({\n      query: (params) => ({\n        url: '/admin/analytics/financial',\n        params,\n      }),\n      providesTags: ['Analytics', 'Finance'],\n    }),\n\n    // Real-time Dashboard Metrics\n    getDashboardMetrics: builder.query<ApiResponse<{\n      todayStats: {\n        newUsers: number\n        newProperties: number\n        totalTransactions: number\n        totalRevenue: number\n      }\n      weeklyTrends: {\n        users: number[]\n        properties: number[]\n        revenue: number[]\n      }\n      alerts: {\n        id: string\n        type: 'warning' | 'error' | 'info'\n        message: string\n        timestamp: string\n      }[]\n      systemHealth: {\n        status: 'healthy' | 'warning' | 'critical'\n        uptime: number\n        responseTime: number\n        errorRate: number\n      }\n    }>, void>({\n      query: () => '/admin/analytics/dashboard-metrics',\n      providesTags: ['Analytics', 'Dashboard'],\n    }),\n\n    // Custom Reports\n    generateCustomReport: builder.mutation<ApiResponse<{\n      reportId: string\n      downloadUrl: string\n    }>, {\n      reportType: 'users' | 'properties' | 'financial' | 'system'\n      startDate: string\n      endDate: string\n      filters?: Record<string, any>\n      format: 'pdf' | 'excel' | 'csv'\n      includeCharts: boolean\n    }>({\n      query: (data) => ({\n        url: '/admin/analytics/generate-report',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Analytics'],\n    }),\n\n    // Export Analytics Data\n    exportAnalyticsData: builder.mutation<Blob, {\n      dataType: 'system' | 'users' | 'properties' | 'financial'\n      startDate: string\n      endDate: string\n      format: 'json' | 'csv' | 'excel'\n    }>({\n      query: (params) => ({\n        url: '/admin/analytics/export',\n        method: 'POST',\n        body: params,\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Comparative Analytics\n    getComparativeAnalytics: builder.query<ApiResponse<{\n      currentPeriod: SystemAnalytics\n      previousPeriod: SystemAnalytics\n      growth: {\n        users: number\n        properties: number\n        revenue: number\n        investments: number\n      }\n      trends: {\n        metric: string\n        trend: 'up' | 'down' | 'stable'\n        percentage: number\n      }[]\n    }>, {\n      period: 'month' | 'quarter' | 'year'\n      compareWith: 'previous' | 'lastYear'\n    }>({\n      query: (params) => ({\n        url: '/admin/analytics/comparative',\n        params,\n      }),\n      providesTags: ['Analytics'],\n    }),\n\n    // Predictive Analytics\n    getPredictiveAnalytics: builder.query<ApiResponse<{\n      userGrowthPrediction: {\n        month: string\n        predictedUsers: number\n        confidence: number\n      }[]\n      revenueForecast: {\n        month: string\n        predictedRevenue: number\n        confidence: number\n      }[]\n      marketTrends: {\n        trend: string\n        impact: 'positive' | 'negative' | 'neutral'\n        confidence: number\n        description: string\n      }[]\n    }>, {\n      months: number\n      model?: 'linear' | 'exponential' | 'seasonal'\n    }>({\n      query: (params) => ({\n        url: '/admin/analytics/predictive',\n        params,\n      }),\n      providesTags: ['Analytics'],\n    }),\n  }),\n})\n\nexport const {\n  useGetSystemAnalyticsQuery,\n  useGetUserAnalyticsQuery,\n  useGetPropertyAnalyticsQuery,\n  useGetFinancialAnalyticsQuery,\n  useGetDashboardMetricsQuery,\n  useGenerateCustomReportMutation,\n  useExportAnalyticsDataMutation,\n  useGetComparativeAnalyticsQuery,\n  useGetPredictiveAnalyticsQuery,\n} = analyticsApi\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAuHO,MAAM,eAAe,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAClD,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,mBAAmB;YACnB,oBAAoB,QAAQ,KAAK,CAI9B;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;oBAAa;iBAAY;YAC1C;YAEA,iBAAiB;YACjB,kBAAkB,QAAQ,KAAK,CAI5B;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;oBAAa;iBAAO;YACrC;YAEA,qBAAqB;YACrB,sBAAsB,QAAQ,KAAK,CAKhC;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;oBAAa;iBAAW;YACzC;YAEA,sBAAsB;YACtB,uBAAuB,QAAQ,KAAK,CAIjC;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;oBAAa;iBAAU;YACxC;YAEA,8BAA8B;YAC9B,qBAAqB,QAAQ,KAAK,CAwBxB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;oBAAa;iBAAY;YAC1C;YAEA,iBAAiB;YACjB,sBAAsB,QAAQ,QAAQ,CAUnC;gBACD,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAY;YAChC;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,QAAQ,CAKlC;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL,QAAQ;wBACR,MAAM;wBACN,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,wBAAwB;YACxB,yBAAyB,QAAQ,KAAK,CAiBnC;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAY;YAC7B;YAEA,uBAAuB;YACvB,wBAAwB,QAAQ,KAAK,CAoBlC;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAY;YAC7B;QACF,CAAC;AACH;AAEO,MAAM,EACX,0BAA0B,EAC1B,wBAAwB,EACxB,4BAA4B,EAC5B,6BAA6B,EAC7B,2BAA2B,EAC3B,+BAA+B,EAC/B,8BAA8B,EAC9B,+BAA+B,EAC/B,8BAA8B,EAC/B,GAAG", "debugId": null}}, {"offset": {"line": 4942, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/index.ts"], "sourcesContent": ["import { configureStore, combineReducers } from '@reduxjs/toolkit'\nimport { persistStore, persistReducer } from 'redux-persist'\nimport storage from 'redux-persist/lib/storage'\nimport createWebStorage from 'redux-persist/lib/storage/createWebStorage'\nimport { setupListeners } from '@reduxjs/toolkit/query'\n\n// Import slices\nimport authSlice from './slices/authSlice'\nimport uiSlice from './slices/uiSlice'\nimport usersSlice from './slices/usersSlice'\nimport propertiesSlice from './slices/propertiesSlice'\nimport leadsSlice from './slices/leadsSlice'\nimport dashboardSlice from './slices/dashboardSlice'\n\n// Import RTK Query API service\nimport { baseApi } from './api/baseApi'\n\n// Import all API slices to ensure they are injected into baseApi\nimport './api/usersApi'\nimport './api/propertiesApi'\nimport './api/leadsApi'\nimport './api/dashboardApi'\nimport './api/financeApi'\nimport './api/stocksApi'\nimport './api/propertyStocksApi'\nimport './api/propertyOwnersApi'\nimport './api/supportApi'\nimport './api/settingsApi'\nimport './api/adminPaymentApi'\nimport './api/adminUserApi'\nimport './api/analyticsApi'\n\n// Create noop storage for server-side rendering\nconst createNoopStorage = () => {\n  return {\n    getItem(_key: string) {\n      return Promise.resolve(null)\n    },\n    setItem(_key: string, value: any) {\n      return Promise.resolve(value)\n    },\n    removeItem(_key: string) {\n      return Promise.resolve()\n    },\n  }\n}\n\n// Use proper storage based on environment\nconst storageEngine = typeof window !== 'undefined' ? storage : createNoopStorage()\n\n// Persist configuration\nconst persistConfig = {\n  key: 'sgm-admin',\n  storage: storageEngine,\n  whitelist: ['auth', 'ui'], // Only persist auth and ui state\n  blacklist: ['users', 'properties', 'leads', 'dashboard', 'baseApi'], // Don't persist data that should be fresh\n}\n\n// Root reducer\nconst rootReducer = combineReducers({\n  auth: authSlice,\n  ui: uiSlice,\n  users: usersSlice,\n  properties: propertiesSlice,\n  leads: leadsSlice,\n  dashboard: dashboardSlice,\n  [baseApi.reducerPath]: baseApi.reducer,\n})\n\n// Persisted reducer\nconst persistedReducer = persistReducer(persistConfig, rootReducer)\n\n// Configure store\nexport const store = configureStore({\n  reducer: persistedReducer,\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],\n        ignoredPaths: ['register'],\n      },\n    }).concat(baseApi.middleware),\n  devTools: process.env.NODE_ENV !== 'production',\n})\n\n// Setup listeners for RTK Query\nsetupListeners(store.dispatch)\n\n// Create persistor\nexport const persistor = persistStore(store)\n\n// Types\nexport type RootState = ReturnType<typeof store.getState>\nexport type AppDispatch = typeof store.dispatch\n\n// Typed hooks\nimport { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux'\n\nexport const useAppDispatch = () => useDispatch<AppDispatch>()\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector\n\nexport default store\n"], "names": [], "mappings": ";;;;;;;AAkFY;AAlFZ;AAAA;AACA;AAAA;AAAA;AACA;AAEA;AAEA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AAEA,+BAA+B;AAC/B;AAEA,iEAAiE;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAiEA,cAAc;AACd;;;;;;;;;;;;;;;;;;;;;;;;;;AAhEA,gDAAgD;AAChD,MAAM,oBAAoB;IACxB,OAAO;QACL,SAAQ,IAAY;YAClB,OAAO,QAAQ,OAAO,CAAC;QACzB;QACA,SAAQ,IAAY,EAAE,KAAU;YAC9B,OAAO,QAAQ,OAAO,CAAC;QACzB;QACA,YAAW,IAAY;YACrB,OAAO,QAAQ,OAAO;QACxB;IACF;AACF;AAEA,0CAA0C;AAC1C,MAAM,gBAAgB,uCAAgC,sQAAA,CAAA,UAAO;AAE7D,wBAAwB;AACxB,MAAM,gBAAgB;IACpB,KAAK;IACL,SAAS;IACT,WAAW;QAAC;QAAQ;KAAK;IACzB,WAAW;QAAC;QAAS;QAAc;QAAS;QAAa;KAAU;AACrE;AAEA,eAAe;AACf,MAAM,cAAc,CAAA,GAAA,4LAAA,CAAA,kBAAe,AAAD,EAAE;IAClC,MAAM,sIAAA,CAAA,UAAS;IACf,IAAI,oIAAA,CAAA,UAAO;IACX,OAAO,uIAAA,CAAA,UAAU;IACjB,YAAY,4IAAA,CAAA,UAAe;IAC3B,OAAO,uIAAA,CAAA,UAAU;IACjB,WAAW,2IAAA,CAAA,UAAc;IACzB,CAAC,iIAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,iIAAA,CAAA,UAAO,CAAC,OAAO;AACxC;AAEA,oBAAoB;AACpB,MAAM,mBAAmB,CAAA,GAAA,gTAAA,CAAA,iBAAc,AAAD,EAAE,eAAe;AAGhD,MAAM,QAAQ,CAAA,GAAA,qSAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;IACT,YAAY,CAAC,uBACX,qBAAqB;YACnB,mBAAmB;gBACjB,gBAAgB;oBAAC;oBAAmB;iBAAoB;gBACxD,cAAc;oBAAC;iBAAW;YAC5B;QACF,GAAG,MAAM,CAAC,iIAAA,CAAA,UAAO,CAAC,UAAU;IAC9B,UAAU,oDAAyB;AACrC;AAEA,gCAAgC;AAChC,CAAA,GAAA,0RAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ;AAGtB,MAAM,YAAY,CAAA,GAAA,4SAAA,CAAA,eAAY,AAAD,EAAE;;AAS/B,MAAM,iBAAiB;;IAAM,OAAA,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD;AAAe;GAAhD;;QAAuB,wQAAA,CAAA,cAAW;;;AACxC,MAAM,iBAAkD,wQAAA,CAAA,cAAW;uCAE3D", "debugId": null}}, {"offset": {"line": 5090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, ReactNode } from 'react'\nimport { useAppDispatch, useAppSelector } from '@/store'\nimport { \n  checkAuthAsync, \n  selectIsAuthenticated, \n  selectUser, \n  selectAuthLoading \n} from '@/store/slices/authSlice'\nimport { User } from '@/types'\n\ninterface AuthContextType {\n  user: User | null\n  isAuthenticated: boolean\n  loading: boolean\n  checkAuth: () => void\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const dispatch = useAppDispatch()\n  const user = useAppSelector(selectUser)\n  const isAuthenticated = useAppSelector(selectIsAuthenticated)\n  const loading = useAppSelector(selectAuthLoading)\n\n  const checkAuth = () => {\n    dispatch(checkAuthAsync())\n  }\n\n  useEffect(() => {\n    // Only check auth if we don't already have user data and we have a token\n    const hasToken = typeof window !== 'undefined' && (\n      document.cookie.includes('accessToken=') ||\n      document.cookie.includes('token=') ||\n      localStorage.getItem('token')\n    )\n\n    // Only check auth if we have a token but no user data\n    if (hasToken && !user && !loading) {\n      console.log('🔍 AuthContext: Checking auth on mount')\n      checkAuth()\n    } else {\n      console.log('🔍 AuthContext: Skipping auth check - hasToken:', hasToken, 'user:', !!user, 'loading:', loading)\n    }\n  }, []) // Empty dependency array - only run once on mount\n\n  const value: AuthContextType = {\n    user,\n    isAuthenticated,\n    loading,\n    checkAuth\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuthContext() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuthContext must be used within an AuthProvider')\n  }\n  return context\n}\n\nexport default AuthContext\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAmBA,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,gBAAa,AAAD,EAA+B;AAMxD,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAC1D,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,OAAO,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,aAAU;IACtC,MAAM,kBAAkB,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,wBAAqB;IAC5D,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,oBAAiB;IAEhD,MAAM,YAAY;QAChB,SAAS,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD;IACxB;IAEA,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;kCAAE;YACR,yEAAyE;YACzE,MAAM,WAAW,aAAkB,eAAe,CAChD,SAAS,MAAM,CAAC,QAAQ,CAAC,mBACzB,SAAS,MAAM,CAAC,QAAQ,CAAC,aACzB,aAAa,OAAO,CAAC,QACvB;YAEA,sDAAsD;YACtD,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS;gBACjC,QAAQ,GAAG,CAAC;gBACZ;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,mDAAmD,UAAU,SAAS,CAAC,CAAC,MAAM,YAAY;YACxG;QACF;iCAAG,EAAE,EAAE,kDAAkD;;IAEzD,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;IACF;IAEA,qBACE,4TAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAvCgB;;QACG,wHAAA,CAAA,iBAAc;QAClB,wHAAA,CAAA,iBAAc;QACH,wHAAA,CAAA,iBAAc;QACtB,wHAAA,CAAA,iBAAc;;;KAJhB;AAyCT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;uCAQD", "debugId": null}}, {"offset": {"line": 5174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/app/layout.tsx"], "sourcesContent": ["'use client'\n\nimport { G<PERSON><PERSON>, <PERSON>eist_Mono } from \"next/font/google\";\nimport { Provider } from 'react-redux';\nimport { PersistGate } from 'redux-persist/integration/react';\nimport { Toaster } from 'sonner';\nimport { store, persistor } from '@/store';\nimport { AuthProvider } from '@/contexts/AuthContext';\nimport \"./globals.css\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <head>\n        <title>SGM Admin Dashboard</title>\n        <meta name=\"description\" content=\"SGM Real Estate Investment Platform Admin Dashboard\" />\n      </head>\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n      >\n        <Provider store={store}>\n          <PersistGate loading={<div>Loading...</div>} persistor={persistor}>\n            <AuthProvider>\n              {children}\n              <Toaster position=\"top-right\" richColors />\n            </AuthProvider>\n          </PersistGate>\n        </Provider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;;AAoBe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,4TAAC;QAAK,MAAK;;0BACT,4TAAC;;kCACC,4TAAC;kCAAM;;;;;;kCACP,4TAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;;;;;;0BAEnC,4TAAC;gBACC,WAAW,GAAG,4IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;0BAEpE,cAAA,4TAAC,wQAAA,CAAA,WAAQ;oBAAC,OAAO,wHAAA,CAAA,QAAK;8BACpB,cAAA,4TAAC,yQAAA,CAAA,cAAW;wBAAC,uBAAS,4TAAC;sCAAI;;;;;;wBAAkB,WAAW,wHAAA,CAAA,YAAS;kCAC/D,cAAA,4TAAC,kIAAA,CAAA,eAAY;;gCACV;8CACD,4TAAC,2QAAA,CAAA,UAAO;oCAAC,UAAS;oCAAY,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtD;KAzBwB", "debugId": null}}]}