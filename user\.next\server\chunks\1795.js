"use strict";exports.id=1795,exports.ids=[1795],exports.modules={20924:(e,t,n)=>{n.d(t,{UC:()=>nd,YJ:()=>nh,In:()=>nc,q7:()=>ng,VF:()=>nw,p4:()=>nv,JU:()=>nm,ZL:()=>nf,bL:()=>na,wn:()=>nx,PP:()=>ny,wv:()=>nb,l9:()=>ns,WT:()=>nu,LM:()=>np});var r=n(73356),o=n(40813);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var l=n(60952),a=n(60308),s=n(24629),u=n(64680),c=n(7957),f=n(21431),d=n(43424),p=n(25028),h=n(59705);let m=["top","right","bottom","left"],g=Math.min,v=Math.max,w=Math.round,y=Math.floor,x=e=>({x:e,y:e}),b={left:"right",right:"left",bottom:"top",top:"bottom"},S={start:"end",end:"start"};function C(e,t){return"function"==typeof e?e(t):e}function R(e){return e.split("-")[0]}function T(e){return e.split("-")[1]}function A(e){return"x"===e?"y":"x"}function P(e){return"y"===e?"height":"width"}let j=new Set(["top","bottom"]);function E(e){return j.has(R(e))?"y":"x"}function L(e){return e.replace(/start|end/g,e=>S[e])}let k=["left","right"],N=["right","left"],D=["top","bottom"],M=["bottom","top"];function H(e){return e.replace(/left|right|bottom|top/g,e=>b[e])}function O(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function I(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function B(e,t,n){let r,{reference:o,floating:i}=e,l=E(t),a=A(E(t)),s=P(a),u=R(t),c="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(u){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(T(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let F=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=B(u,r,s),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:g,y:v,data:w,reset:y}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=v?v:f,p={...p,[i]:{...p[i],...w}},y&&h<=50&&(h++,"object"==typeof y&&(y.placement&&(d=y.placement),y.rects&&(u=!0===y.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:c,y:f}=B(u,d,s)),n=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:p}};async function V(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=C(t,e),h=O(p),m=a[d?"floating"===f?"reference":"floating":f],g=I(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),v="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),y=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},x=I(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:w,strategy:s}):v);return{top:(g.top-x.top+h.top)/y.y,bottom:(x.bottom-g.bottom+h.bottom)/y.y,left:(g.left-x.left+h.left)/y.x,right:(x.right-g.right+h.right)/y.x}}function W(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function z(e){return m.some(t=>e[t]>=0)}let _=new Set(["left","top"]);async function G(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=R(n),a=T(n),s="y"===E(n),u=_.has(l)?-1:1,c=i&&s?-1:1,f=C(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),s?{x:p*c,y:d*u}:{x:d*u,y:p*c}}function $(){return"undefined"!=typeof window}function K(e){return X(e)?(e.nodeName||"").toLowerCase():"#document"}function U(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function q(e){var t;return null==(t=(X(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function X(e){return!!$()&&(e instanceof Node||e instanceof U(e).Node)}function Y(e){return!!$()&&(e instanceof Element||e instanceof U(e).Element)}function Z(e){return!!$()&&(e instanceof HTMLElement||e instanceof U(e).HTMLElement)}function J(e){return!!$()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof U(e).ShadowRoot)}let Q=new Set(["inline","contents"]);function ee(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ef(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Q.has(o)}let et=new Set(["table","td","th"]),en=[":popover-open",":modal"];function er(e){return en.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eo=["transform","translate","scale","rotate","perspective"],ei=["transform","translate","scale","rotate","perspective","filter"],el=["paint","layout","strict","content"];function ea(e){let t=es(),n=Y(e)?ef(e):e;return eo.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||ei.some(e=>(n.willChange||"").includes(e))||el.some(e=>(n.contain||"").includes(e))}function es(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eu=new Set(["html","body","#document"]);function ec(e){return eu.has(K(e))}function ef(e){return U(e).getComputedStyle(e)}function ed(e){return Y(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ep(e){if("html"===K(e))return e;let t=e.assignedSlot||e.parentNode||J(e)&&e.host||q(e);return J(t)?t.host:t}function eh(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ep(t);return ec(n)?t.ownerDocument?t.ownerDocument.body:t.body:Z(n)&&ee(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=U(o);if(i){let e=em(l);return t.concat(l,l.visualViewport||[],ee(o)?o:[],e&&n?eh(e):[])}return t.concat(o,eh(o,[],n))}function em(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eg(e){let t=ef(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=Z(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=w(n)!==i||w(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function ev(e){return Y(e)?e:e.contextElement}function ew(e){let t=ev(e);if(!Z(t))return x(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eg(t),l=(i?w(n.width):n.width)/r,a=(i?w(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let ey=x(0);function ex(e){let t=U(e);return es()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ey}function eb(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=ev(e),a=x(1);t&&(r?Y(r)&&(a=ew(r)):a=ew(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===U(l))&&o)?ex(l):x(0),u=(i.left+s.x)/a.x,c=(i.top+s.y)/a.y,f=i.width/a.x,d=i.height/a.y;if(l){let e=U(l),t=r&&Y(r)?U(r):r,n=e,o=em(n);for(;o&&r&&t!==n;){let e=ew(o),t=o.getBoundingClientRect(),r=ef(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,f*=e.x,d*=e.y,u+=i,c+=l,o=em(n=U(o))}}return I({width:f,height:d,x:u,y:c})}function eS(e,t){let n=ed(e).scrollLeft;return t?t.left+n:eb(q(e)).left+n}function eC(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eS(e,r)),y:r.top+t.scrollTop}}let eR=new Set(["absolute","fixed"]);function eT(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=U(e),r=q(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;let e=es();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=q(e),n=ed(e),r=e.ownerDocument.body,o=v(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=v(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eS(e),a=-n.scrollTop;return"rtl"===ef(r).direction&&(l+=v(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(q(e));else if(Y(t))r=function(e,t){let n=eb(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Z(e)?ew(e):x(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=ex(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return I(r)}function eA(e){return"static"===ef(e).position}function eP(e,t){if(!Z(e)||"fixed"===ef(e).position)return null;if(t)return t(e);let n=e.offsetParent;return q(e)===n&&(n=n.ownerDocument.body),n}function ej(e,t){var n;let r=U(e);if(er(e))return r;if(!Z(e)){let t=ep(e);for(;t&&!ec(t);){if(Y(t)&&!eA(t))return t;t=ep(t)}return r}let o=eP(e,t);for(;o&&(n=o,et.has(K(n)))&&eA(o);)o=eP(o,t);return o&&ec(o)&&eA(o)&&!ea(o)?r:o||function(e){let t=ep(e);for(;Z(t)&&!ec(t);){if(ea(t))return t;if(er(t))break;t=ep(t)}return null}(e)||r}let eE=async function(e){let t=this.getOffsetParent||ej,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=Z(t),o=q(t),i="fixed"===n,l=eb(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=x(0);if(r||!r&&!i)if(("body"!==K(t)||ee(o))&&(a=ed(t)),r){let e=eb(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eS(o));i&&!r&&o&&(s.x=eS(o));let u=!o||r||i?x(0):eC(o,a);return{x:l.left+a.scrollLeft-s.x-u.x,y:l.top+a.scrollTop-s.y-u.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eL={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=q(r),a=!!t&&er(t.floating);if(r===l||a&&i)return n;let s={scrollLeft:0,scrollTop:0},u=x(1),c=x(0),f=Z(r);if((f||!f&&!i)&&(("body"!==K(r)||ee(l))&&(s=ed(r)),Z(r))){let e=eb(r);u=ew(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let d=!l||f||i?x(0):eC(l,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+d.x,y:n.y*u.y-s.scrollTop*u.y+c.y+d.y}},getDocumentElement:q,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?er(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eh(e,[],!1).filter(e=>Y(e)&&"body"!==K(e)),o=null,i="fixed"===ef(e).position,l=i?ep(e):e;for(;Y(l)&&!ec(l);){let t=ef(l),n=ea(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&eR.has(o.position)||ee(l)&&!n&&function e(t,n){let r=ep(t);return!(r===n||!Y(r)||ec(r))&&("fixed"===ef(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=ep(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eT(t,n,o);return e.top=v(r.top,e.top),e.right=g(r.right,e.right),e.bottom=g(r.bottom,e.bottom),e.left=v(r.left,e.left),e},eT(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:ej,getElementRects:eE,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eg(e);return{width:t,height:n}},getScale:ew,isElement:Y,isRTL:function(e){return"rtl"===ef(e).direction}};function ek(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eN=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:s}=t,{element:u,padding:c=0}=C(e,t)||{};if(null==u)return{};let f=O(c),d={x:n,y:r},p=A(E(o)),h=P(p),m=await l.getDimensions(u),w="y"===p,y=w?"clientHeight":"clientWidth",x=i.reference[h]+i.reference[p]-d[p]-i.floating[h],b=d[p]-i.reference[p],S=await (null==l.getOffsetParent?void 0:l.getOffsetParent(u)),R=S?S[y]:0;R&&await (null==l.isElement?void 0:l.isElement(S))||(R=a.floating[y]||i.floating[h]);let j=R/2-m[h]/2-1,L=g(f[w?"top":"left"],j),k=g(f[w?"bottom":"right"],j),N=R-m[h]-k,D=R/2-m[h]/2+(x/2-b/2),M=v(L,g(D,N)),H=!s.arrow&&null!=T(o)&&D!==M&&i.reference[h]/2-(D<L?L:k)-m[h]/2<0,I=H?D<L?D-L:D-N:0;return{[p]:d[p]+I,data:{[p]:M,centerOffset:D-M-I,...H&&{alignmentOffset:I}},reset:H}}}),eD=(e,t,n)=>{let r=new Map,o={platform:eL,...n},i={...o.platform,_c:r};return F(e,t,{...o,platform:i})};var eM="undefined"!=typeof document?r.useLayoutEffect:function(){};function eH(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eH(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eH(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eO(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eI(e,t){let n=eO(e);return Math.round(t*n)/n}function eB(e){let t=r.useRef(e);return eM(()=>{t.current=e}),t}let eF=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eN({element:n.current,padding:r}).fn(t):{}:n?eN({element:n,padding:r}).fn(t):{}}}),eV=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,s=await G(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),eW=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=C(e,t),u={x:n,y:r},c=await V(t,s),f=E(R(o)),d=A(f),p=u[d],h=u[f];if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=p+c[e],r=p-c[t];p=v(n,g(p,r))}if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=h+c[e],r=h-c[t];h=v(n,g(h,r))}let m=a.fn({...t,[d]:p,[f]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[d]:i,[f]:l}}}}}}(e),options:[e,t]}),ez=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=C(e,t),c={x:n,y:r},f=E(o),d=A(f),p=c[d],h=c[f],m=C(a,t),g="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+g.mainAxis,n=i.reference[d]+i.reference[e]-g.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var v,w;let e="y"===d?"width":"height",t=_.has(R(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(v=l.offset)?void 0:v[f])||0)+(t?0:g.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(w=l.offset)?void 0:w[f])||0)-(t?g.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}}(e),options:[e,t]}),e_=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:f,elements:d}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:w=!0,...y}=C(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let x=R(a),b=E(c),S=R(c)===c,j=await (null==f.isRTL?void 0:f.isRTL(d.floating)),O=m||(S||!w?[H(c)]:function(e){let t=H(e);return[L(e),t,L(t)]}(c)),I="none"!==v;!m&&I&&O.push(...function(e,t,n,r){let o=T(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?N:k;return t?k:N;case"left":case"right":return t?D:M;default:return[]}}(R(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(L)))),i}(c,w,v,j));let B=[c,...O],F=await V(t,y),W=[],z=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&W.push(F[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=T(e),o=A(E(e)),i=P(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=H(l)),[l,H(l)]}(a,u,j);W.push(F[e[0]],F[e[1]])}if(z=[...z,{placement:a,overflows:W}],!W.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=B[e];if(t&&("alignment"!==h||b===E(t)||z.every(e=>e.overflows[0]>0&&E(e.placement)===b)))return{data:{index:e,overflows:z},reset:{placement:t}};let n=null==(i=z.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(g){case"bestFit":{let e=null==(l=z.filter(e=>{if(I){let t=E(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eG=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:l,rects:a,platform:s,elements:u}=t,{apply:c=()=>{},...f}=C(e,t),d=await V(t,f),p=R(l),h=T(l),m="y"===E(l),{width:w,height:y}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let x=y-d.top-d.bottom,b=w-d.left-d.right,S=g(y-d[o],x),A=g(w-d[i],b),P=!t.middlewareData.shift,j=S,L=A;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(L=b),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(j=x),P&&!h){let e=v(d.left,0),t=v(d.right,0),n=v(d.top,0),r=v(d.bottom,0);m?L=w-2*(0!==e||0!==t?e+t:v(d.left,d.right)):j=y-2*(0!==n||0!==r?n+r:v(d.top,d.bottom))}await c({...t,availableWidth:L,availableHeight:j});let k=await s.getDimensions(u.floating);return w!==k.width||y!==k.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),e$=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=C(e,t);switch(r){case"referenceHidden":{let e=W(await V(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:z(e)}}}case"escaped":{let e=W(await V(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:z(e)}}}default:return{}}}}}(e),options:[e,t]}),eK=(e,t)=>({...eF(e),options:[e,t]});var eU=n(81861),eq=n(40969),eX=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eq.jsx)(eU.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eq.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eX.displayName="Arrow";var eY=n(71060),eZ=n(7409),eJ=n(39827),eQ="Popper",[e0,e1]=(0,u.A)(eQ),[e2,e5]=e0(eQ),e9=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eq.jsx)(e2,{scope:t,anchor:o,onAnchorChange:i,children:n})};e9.displayName=eQ;var e4="PopperAnchor",e3=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=e5(e4,n),a=r.useRef(null),u=(0,s.s)(t,a);return r.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eq.jsx)(eU.sG.div,{...i,ref:u})});e3.displayName=e4;var e6="PopperContent",[e7,e8]=e0(e6),te=r.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:l=0,align:a="center",alignOffset:u=0,arrowPadding:c=0,avoidCollisions:f=!0,collisionBoundary:d=[],collisionPadding:p=0,sticky:h="partial",hideWhenDetached:m=!1,updatePositionStrategy:w="optimized",onPlaced:x,...b}=e,S=e5(e6,n),[C,R]=r.useState(null),T=(0,s.s)(t,e=>R(e)),[A,P]=r.useState(null),j=(0,eJ.X)(A),E=j?.width??0,L=j?.height??0,k="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},N=Array.isArray(d)?d:[d],D=N.length>0,M={padding:k,boundary:N.filter(to),altBoundary:D},{refs:H,floatingStyles:O,placement:I,isPositioned:B,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:s}={},transform:u=!0,whileElementsMounted:c,open:f}=e,[d,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=r.useState(i);eH(h,i)||m(i);let[g,v]=r.useState(null),[w,y]=r.useState(null),x=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=r.useCallback(e=>{e!==T.current&&(T.current=e,y(e))},[]),S=a||g,C=s||w,R=r.useRef(null),T=r.useRef(null),A=r.useRef(d),P=null!=c,j=eB(c),E=eB(l),L=eB(f),k=r.useCallback(()=>{if(!R.current||!T.current)return;let e={placement:t,strategy:n,middleware:h};E.current&&(e.platform=E.current),eD(R.current,T.current,e).then(e=>{let t={...e,isPositioned:!1!==L.current};N.current&&!eH(A.current,t)&&(A.current=t,o.flushSync(()=>{p(t)}))})},[h,t,n,E,L]);eM(()=>{!1===f&&A.current.isPositioned&&(A.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[f]);let N=r.useRef(!1);eM(()=>(N.current=!0,()=>{N.current=!1}),[]),eM(()=>{if(S&&(R.current=S),C&&(T.current=C),S&&C){if(j.current)return j.current(S,C,k);k()}},[S,C,k,j,P]);let D=r.useMemo(()=>({reference:R,floating:T,setReference:x,setFloating:b}),[x,b]),M=r.useMemo(()=>({reference:S,floating:C}),[S,C]),H=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=eI(M.floating,d.x),r=eI(M.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eO(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,M.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:k,refs:D,elements:M,floatingStyles:H}),[d,k,D,M,H])}({strategy:"fixed",placement:i+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=ev(e),f=i||l?[...c?eh(c):[],...eh(t)]:[];f.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let d=c&&s?function(e,t){let n,r=null,o=q(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),i();let u=e.getBoundingClientRect(),{left:c,top:f,width:d,height:p}=u;if(a||t(),!d||!p)return;let h=y(f),m=y(o.clientWidth-(c+d)),w={rootMargin:-h+"px "+-m+"px "+-y(o.clientHeight-(f+p))+"px "+-y(c)+"px",threshold:v(0,g(1,s))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==s){if(!x)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||ek(u,e.getBoundingClientRect())||l(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),i}(c,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!u&&h.observe(c),h.observe(t));let m=u?eb(e):null;return u&&function t(){let r=eb(e);m&&!ek(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=h)||e.disconnect(),h=null,u&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===w}),elements:{reference:S.anchor},middleware:[eV({mainAxis:l+L,alignmentAxis:u}),f&&eW({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?ez():void 0,...M}),f&&e_({...M}),eG({...M,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),A&&eK({element:A,padding:c}),ti({arrowWidth:E,arrowHeight:L}),m&&e$({strategy:"referenceHidden",...M})]}),[V,W]=tl(I),z=(0,eY.c)(x);(0,eZ.N)(()=>{B&&z?.()},[B,z]);let _=F.arrow?.x,G=F.arrow?.y,$=F.arrow?.centerOffset!==0,[K,U]=r.useState();return(0,eZ.N)(()=>{C&&U(window.getComputedStyle(C).zIndex)},[C]),(0,eq.jsx)("div",{ref:H.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:B?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eq.jsx)(e7,{scope:n,placedSide:V,onArrowChange:P,arrowX:_,arrowY:G,shouldHideArrow:$,children:(0,eq.jsx)(eU.sG.div,{"data-side":V,"data-align":W,...b,ref:T,style:{...b.style,animation:B?void 0:"none"}})})})});te.displayName=e6;var tt="PopperArrow",tn={top:"bottom",right:"left",bottom:"top",left:"right"},tr=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e8(tt,n),i=tn[o.placedSide];return(0,eq.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eq.jsx)(eX,{...r,ref:t,style:{...r.style,display:"block"}})})});function to(e){return null!==e}tr.displayName=tt;var ti=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[s,u]=tl(n),c={start:"0%",center:"50%",end:"100%"}[u],f=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===s?(p=i?c:`${f}px`,h=`${-a}px`):"top"===s?(p=i?c:`${f}px`,h=`${r.floating.height+a}px`):"right"===s?(p=`${-a}px`,h=i?c:`${d}px`):"left"===s&&(p=`${r.floating.width+a}px`,h=i?c:`${d}px`),{data:{x:p,y:h}}}});function tl(e){let[t,n="center"]=e.split("-");return[t,n]}var ta=n(36146),ts=n(19334),tu=n(26314),tc=n(42291),tf=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,eq.jsx)(eU.sG.span,{...e,ref:t,style:{...tf,...e.style}})).displayName="VisuallyHidden";var td=n(87465),tp=n(7111),th=[" ","Enter","ArrowUp","ArrowDown"],tm=[" ","Enter"],tg="Select",[tv,tw,ty]=(0,a.N)(tg),[tx,tb]=(0,u.A)(tg,[ty,e1]),tS=e1(),[tC,tR]=tx(tg),[tT,tA]=tx(tg),tP=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:s,onValueChange:u,dir:f,name:d,autoComplete:p,disabled:m,required:g,form:v}=e,w=tS(t),[y,x]=r.useState(null),[b,S]=r.useState(null),[C,R]=r.useState(!1),T=(0,c.jH)(f),[A,P]=(0,tu.i)({prop:o,defaultProp:i??!1,onChange:l,caller:tg}),[j,E]=(0,tu.i)({prop:a,defaultProp:s,onChange:u,caller:tg}),L=r.useRef(null),k=!y||v||!!y.closest("form"),[N,D]=r.useState(new Set),M=Array.from(N).map(e=>e.props.value).join(";");return(0,eq.jsx)(e9,{...w,children:(0,eq.jsxs)(tC,{required:g,scope:t,trigger:y,onTriggerChange:x,valueNode:b,onValueNodeChange:S,valueNodeHasChildren:C,onValueNodeHasChildrenChange:R,contentId:(0,h.B)(),value:j,onValueChange:E,open:A,onOpenChange:P,dir:T,triggerPointerDownPosRef:L,disabled:m,children:[(0,eq.jsx)(tv.Provider,{scope:t,children:(0,eq.jsx)(tT,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{D(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),k?(0,eq.jsxs)(nr,{"aria-hidden":!0,required:g,tabIndex:-1,name:d,autoComplete:p,value:j,onChange:e=>E(e.target.value),disabled:m,form:v,children:[void 0===j?(0,eq.jsx)("option",{value:""}):null,Array.from(N)]},M):null]})})};tP.displayName=tg;var tj="SelectTrigger",tE=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=tS(n),u=tR(tj,n),c=u.disabled||o,f=(0,s.s)(t,u.onTriggerChange),d=tw(n),p=r.useRef("touch"),[h,m,g]=ni(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=nl(t,e,n);void 0!==r&&u.onValueChange(r.value)}),v=e=>{c||(u.onOpenChange(!0),g()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,eq.jsx)(e3,{asChild:!0,...a,children:(0,eq.jsx)(eU.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":no(u.value)?"":void 0,...i,ref:f,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&v(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(v(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&th.includes(e.key)&&(v(),e.preventDefault())})})})});tE.displayName=tj;var tL="SelectValue",tk=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=tR(tL,n),{onValueNodeHasChildrenChange:c}=u,f=void 0!==i,d=(0,s.s)(t,u.onValueNodeChange);return(0,eZ.N)(()=>{c(f)},[c,f]),(0,eq.jsx)(eU.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:no(u.value)?(0,eq.jsx)(eq.Fragment,{children:l}):i})});tk.displayName=tL;var tN=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,eq.jsx)(eU.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tN.displayName="SelectIcon";var tD=e=>(0,eq.jsx)(ta.Z,{asChild:!0,...e});tD.displayName="SelectPortal";var tM="SelectContent",tH=r.forwardRef((e,t)=>{let n=tR(tM,e.__scopeSelect),[i,l]=r.useState();return((0,eZ.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,eq.jsx)(tF,{...e,ref:t}):i?o.createPortal((0,eq.jsx)(tO,{scope:e.__scopeSelect,children:(0,eq.jsx)(tv.Slot,{scope:e.__scopeSelect,children:(0,eq.jsx)("div",{children:e.children})})}),i):null});tH.displayName=tM;var[tO,tI]=tx(tM),tB=(0,ts.TL)("SelectContent.RemoveScroll"),tF=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:u,side:c,sideOffset:h,align:m,alignOffset:g,arrowPadding:v,collisionBoundary:w,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S,...C}=e,R=tR(tM,n),[T,A]=r.useState(null),[P,j]=r.useState(null),E=(0,s.s)(t,e=>A(e)),[L,k]=r.useState(null),[N,D]=r.useState(null),M=tw(n),[H,O]=r.useState(!1),I=r.useRef(!1);r.useEffect(()=>{if(T)return(0,td.Eq)(T)},[T]),(0,d.Oh)();let B=r.useCallback(e=>{let[t,...n]=M().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&P&&(P.scrollTop=0),n===r&&P&&(P.scrollTop=P.scrollHeight),n?.focus(),document.activeElement!==o))return},[M,P]),F=r.useCallback(()=>B([L,T]),[B,L,T]);r.useEffect(()=>{H&&F()},[H,F]);let{onOpenChange:V,triggerPointerDownPosRef:W}=R;r.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(W.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(W.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():T.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),W.current=null};return null!==W.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[T,V,W]),r.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[z,_]=ni(e=>{let t=M().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=nl(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),G=r.useCallback((e,t,n)=>{let r=!I.current&&!n;(void 0!==R.value&&R.value===t||r)&&(k(e),r&&(I.current=!0))},[R.value]),$=r.useCallback(()=>T?.focus(),[T]),K=r.useCallback((e,t,n)=>{let r=!I.current&&!n;(void 0!==R.value&&R.value===t||r)&&D(e)},[R.value]),U="popper"===o?tW:tV,q=U===tW?{side:c,sideOffset:h,align:m,alignOffset:g,arrowPadding:v,collisionBoundary:w,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,eq.jsx)(tO,{scope:n,content:T,viewport:P,onViewportChange:j,itemRefCallback:G,selectedItem:L,onItemLeave:$,itemTextRefCallback:K,focusSelectedItem:F,selectedItemText:N,position:o,isPositioned:H,searchRef:z,children:(0,eq.jsx)(tp.A,{as:tB,allowPinchZoom:!0,children:(0,eq.jsx)(p.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,eq.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,eq.jsx)(U,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...C,...q,onPlaced:()=>O(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,l.m)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||_(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=M().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});tF.displayName="SelectContentImpl";var tV=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=tR(tM,n),u=tI(tM,n),[c,f]=r.useState(null),[d,p]=r.useState(null),h=(0,s.s)(t,e=>p(e)),m=tw(n),g=r.useRef(!1),v=r.useRef(!0),{viewport:w,selectedItem:y,selectedItemText:x,focusSelectedItem:b}=u,S=r.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&d&&w&&y&&x){let e=a.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=x.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,s=e.width+a,u=Math.max(s,t.width),f=i(l,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.left=f+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,s=e.width+a,u=Math.max(s,t.width),f=i(l,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.right=f+"px"}let l=m(),s=window.innerHeight-20,u=w.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),h=parseInt(f.paddingTop,10),v=parseInt(f.borderBottomWidth,10),b=p+h+u+parseInt(f.paddingBottom,10)+v,S=Math.min(5*y.offsetHeight,b),C=window.getComputedStyle(w),R=parseInt(C.paddingTop,10),T=parseInt(C.paddingBottom,10),A=e.top+e.height/2-10,P=y.offsetHeight/2,j=p+h+(y.offsetTop+P);if(j<=A){let e=l.length>0&&y===l[l.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-A,P+(e?T:0)+(d.clientHeight-w.offsetTop-w.offsetHeight)+v);c.style.height=j+t+"px"}else{let e=l.length>0&&y===l[0].ref.current;c.style.top="0px";let t=Math.max(A,p+w.offsetTop+(e?R:0)+P);c.style.height=t+(b-j)+"px",w.scrollTop=j-A+w.offsetTop}c.style.margin="10px 0",c.style.minHeight=S+"px",c.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>g.current=!0)}},[m,a.trigger,a.valueNode,c,d,w,y,x,a.dir,o]);(0,eZ.N)(()=>S(),[S]);let[C,R]=r.useState();(0,eZ.N)(()=>{d&&R(window.getComputedStyle(d).zIndex)},[d]);let T=r.useCallback(e=>{e&&!0===v.current&&(S(),b?.(),v.current=!1)},[S,b]);return(0,eq.jsx)(tz,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:g,onScrollButtonChange:T,children:(0,eq.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,eq.jsx)(eU.sG.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tV.displayName="SelectItemAlignedPosition";var tW=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=tS(n);return(0,eq.jsx)(te,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tW.displayName="SelectPopperPosition";var[tz,t_]=tx(tM,{}),tG="SelectViewport",t$=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=tI(tG,n),u=t_(tG,n),c=(0,s.s)(t,a.onViewportChange),f=r.useRef(0);return(0,eq.jsxs)(eq.Fragment,{children:[(0,eq.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,eq.jsx)(tv.Slot,{scope:n,children:(0,eq.jsx)(eU.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if(r?.current&&n){let e=Math.abs(f.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}f.current=t.scrollTop})})})]})});t$.displayName=tG;var tK="SelectGroup",[tU,tq]=tx(tK),tX=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,eq.jsx)(tU,{scope:n,id:o,children:(0,eq.jsx)(eU.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});tX.displayName=tK;var tY="SelectLabel",tZ=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tq(tY,n);return(0,eq.jsx)(eU.sG.div,{id:o.id,...r,ref:t})});tZ.displayName=tY;var tJ="SelectItem",[tQ,t0]=tx(tJ),t1=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...u}=e,c=tR(tJ,n),f=tI(tJ,n),d=c.value===o,[p,m]=r.useState(a??""),[g,v]=r.useState(!1),w=(0,s.s)(t,e=>f.itemRefCallback?.(e,o,i)),y=(0,h.B)(),x=r.useRef("touch"),b=()=>{i||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,eq.jsx)(tQ,{scope:n,value:o,disabled:i,textId:y,isSelected:d,onItemTextChange:r.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,eq.jsx)(tv.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,eq.jsx)(eU.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":g?"":void 0,"aria-selected":d&&g,"data-state":d?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...u,ref:w,onFocus:(0,l.m)(u.onFocus,()=>v(!0)),onBlur:(0,l.m)(u.onBlur,()=>v(!1)),onClick:(0,l.m)(u.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:(0,l.m)(u.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:(0,l.m)(u.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,l.m)(u.onPointerMove,e=>{x.current=e.pointerType,i?f.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&f.onItemLeave?.()}),onKeyDown:(0,l.m)(u.onKeyDown,e=>{(f.searchRef?.current===""||" "!==e.key)&&(tm.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});t1.displayName=tJ;var t2="SelectItemText",t5=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,u=tR(t2,n),c=tI(t2,n),f=t0(t2,n),d=tA(t2,n),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e),f.onItemTextChange,e=>c.itemTextRefCallback?.(e,f.value,f.disabled)),g=p?.textContent,v=r.useMemo(()=>(0,eq.jsx)("option",{value:f.value,disabled:f.disabled,children:g},f.value),[f.disabled,f.value,g]),{onNativeOptionAdd:w,onNativeOptionRemove:y}=d;return(0,eZ.N)(()=>(w(v),()=>y(v)),[w,y,v]),(0,eq.jsxs)(eq.Fragment,{children:[(0,eq.jsx)(eU.sG.span,{id:f.textId,...a,ref:m}),f.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});t5.displayName=t2;var t9="SelectItemIndicator",t4=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return t0(t9,n).isSelected?(0,eq.jsx)(eU.sG.span,{"aria-hidden":!0,...r,ref:t}):null});t4.displayName=t9;var t3="SelectScrollUpButton",t6=r.forwardRef((e,t)=>{let n=tI(t3,e.__scopeSelect),o=t_(t3,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,eZ.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eq.jsx)(ne,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});t6.displayName=t3;var t7="SelectScrollDownButton",t8=r.forwardRef((e,t)=>{let n=tI(t7,e.__scopeSelect),o=t_(t7,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,eZ.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eq.jsx)(ne,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});t8.displayName=t7;var ne=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=tI("SelectScrollButton",n),s=r.useRef(null),u=tw(n),c=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,eZ.N)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,eq.jsx)(eU.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{c()})})}),nt=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,eq.jsx)(eU.sG.div,{"aria-hidden":!0,...r,ref:t})});nt.displayName="SelectSeparator";var nn="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tS(n),i=tR(nn,n),l=tI(nn,n);return i.open&&"popper"===l.position?(0,eq.jsx)(tr,{...o,...r,ref:t}):null}).displayName=nn;var nr=r.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{let i=r.useRef(null),l=(0,s.s)(o,i),a=(0,tc.Z)(t);return r.useEffect(()=>{let e=i.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[a,t]),(0,eq.jsx)(eU.sG.select,{...n,style:{...tf,...n.style},ref:l,defaultValue:t})});function no(e){return""===e||void 0===e}function ni(e){let t=(0,eY.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function nl(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==n?s:void 0}nr.displayName="SelectBubbleInput";var na=tP,ns=tE,nu=tk,nc=tN,nf=tD,nd=tH,np=t$,nh=tX,nm=tZ,ng=t1,nv=t5,nw=t4,ny=t6,nx=t8,nb=nt},39827:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(73356),o=n(7409);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},40741:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(99024).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},42291:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(73356);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},97586:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(99024).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}};