(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4265],{1588:(e,s,a)=>{Promise.resolve().then(a.bind(a,3271))},2790:(e,s,a)=>{"use strict";a.d(s,{E:()=>n});var t=a(9605);a(9585);var r=a(7276),l=a(6994);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:(0,l.cn)(i({variant:a}),s),...r})}},3089:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},3271:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>f});var t=a(9605);a(9585);var r=a(3005),l=a(5594),i=a(8063),n=a(2933),c=a(7971),d=a(5097),x=a(2790),o=a(7),m=a(360),h=a(9265),u=a(3089),p=a(5828);function f(){return(0,t.jsx)(r.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(l.Ay,{title:"Withdraw Money",description:"Withdraw funds from your wallet to your bank account",icon:o.A,gradient:!0,showBackButton:!0,breadcrumbs:[{label:"Wallet",href:"/wallet"},{label:"Withdraw"}],actions:(0,t.jsxs)(l.lX.Secondary,{children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Secure Transfer"]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,t.jsxs)(i.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(o.A,{className:"h-5 w-5 text-sky-600"}),(0,t.jsx)("span",{children:"Withdraw to Bank Account"})]})}),(0,t.jsxs)(i.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"withdraw-amount",className:"text-sm font-medium text-gray-700",children:"Withdrawal Amount"}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500",children:"₹"}),(0,t.jsx)(c.p,{id:"withdraw-amount",type:"number",placeholder:"0",className:"pl-8 text-lg font-medium h-12"})]})}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Available: ₹25,480 | Minimum: ₹100 | Maximum: ₹25,480"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{className:"text-sm font-medium text-gray-700 mb-3 block",children:"Quick Select"}),(0,t.jsx)("div",{className:"grid grid-cols-4 gap-3",children:[1e3,5e3,1e4,25e3].map(e=>(0,t.jsxs)(n.$,{variant:"outline",className:"h-12 text-sm font-medium hover:bg-sky-50 hover:border-sky-300",children:["₹",e.toLocaleString()]},e))}),(0,t.jsx)(n.$,{variant:"outline",className:"w-full mt-3 h-12 text-sm font-medium hover:bg-sky-50 hover:border-sky-300",children:"Withdraw All (₹25,480)"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{className:"text-sm font-medium text-gray-700 mb-4 block",children:"Select Bank Account"}),(0,t.jsx)("div",{className:"space-y-3",children:[{id:1,bankName:"HDFC Bank",accountNumber:"****1234",ifsc:"HDFC0001234",accountType:"Savings",isDefault:!0},{id:2,bankName:"ICICI Bank",accountNumber:"****5678",ifsc:"ICIC0005678",accountType:"Current",isDefault:!1}].map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-sky-300 hover:bg-sky-50 cursor-pointer transition-colors",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"p-2 bg-sky-100 rounded-lg",children:(0,t.jsx)(h.A,{className:"h-5 w-5 text-sky-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:e.bankName}),e.isDefault&&(0,t.jsx)(x.E,{variant:"secondary",className:"text-xs",children:"Default"})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.accountNumber," • ",e.accountType]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["IFSC: ",e.ifsc]})]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)(x.E,{variant:"outline",className:"text-xs",children:"Verified"})})]},e.id))}),(0,t.jsx)(n.$,{variant:"outline",className:"w-full mt-3",children:"+ Add New Bank Account"})]}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-blue-900",children:"Processing Time"}),(0,t.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Withdrawals are processed within 2-4 business hours during working days. Weekend withdrawals will be processed on the next working day."})]})]})}),(0,t.jsx)(n.$,{className:"w-full bg-sky-600 hover:bg-sky-700 h-12 text-lg font-medium",children:"Proceed to Withdraw"})]})]})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(i.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{className:"text-lg",children:"Wallet Balance"})}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-3xl font-bold text-sky-600",children:"₹25,480"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Available for Withdrawal"})]}),(0,t.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Invested Amount"}),(0,t.jsx)("span",{className:"font-medium",children:"₹8,75,000"})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm mt-2",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Pending Returns"}),(0,t.jsx)("span",{className:"font-medium",children:"₹12,500"})]})]})]})]}),(0,t.jsxs)(i.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{className:"text-lg",children:"Withdrawal Limits"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Daily Limit"}),(0,t.jsx)("span",{className:"font-medium",children:"₹1,00,000"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Used Today"}),(0,t.jsx)("span",{className:"font-medium",children:"₹0"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Remaining"}),(0,t.jsx)("span",{className:"font-medium text-green-600",children:"₹1,00,000"})]})]})})]}),(0,t.jsxs)(i.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center space-x-2 text-lg",children:[(0,t.jsx)(p.A,{className:"h-5 w-5 text-orange-600"}),(0,t.jsx)("span",{children:"Important Notes"})]})}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3 text-sm text-gray-600",children:[(0,t.jsx)("p",{children:"• Withdrawals are processed only to verified bank accounts"}),(0,t.jsx)("p",{children:"• No charges for withdrawals above ₹1,000"}),(0,t.jsx)("p",{children:"• ₹10 fee for withdrawals below ₹1,000"}),(0,t.jsx)("p",{children:"• Withdrawals cannot be cancelled once initiated"})]})})]}),(0,t.jsxs)(i.Zp,{className:"border-0 shadow-lg",children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{className:"text-lg",children:"Recent Withdrawals"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{amount:15e3,date:"Jan 12",status:"Completed"},{amount:8e3,date:"Jan 08",status:"Completed"},{amount:5e3,date:"Jan 03",status:"Completed"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"font-medium",children:["₹",e.amount.toLocaleString()]}),(0,t.jsx)("p",{className:"text-gray-600",children:e.date})]}),(0,t.jsx)(x.E,{variant:"secondary",className:"text-xs",children:e.status})]},s))})})]})]})]})]})})}},5097:(e,s,a)=>{"use strict";a.d(s,{J:()=>d});var t=a(9605),r=a(9585),l=a(8436),i=a(7276),n=a(6994);let c=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.b,{ref:s,className:(0,n.cn)(c(),a),...r})});d.displayName=l.b.displayName},5594:(e,s,a)=>{"use strict";a.d(s,{Ay:()=>d,T0:()=>x,lX:()=>o});var t=a(9605);a(9585);var r=a(2933),l=a(2790),i=a(6994),n=a(9644),c=a(5935);function d(e){let{title:s,description:a,icon:d,badge:x,actions:o,breadcrumbs:m,showBackButton:h=!1,className:u,gradient:p=!1}=e,f=(0,c.useRouter)();return(0,t.jsxs)("div",{className:(0,i.cn)("relative overflow-hidden",p&&"bg-gradient-to-r from-sky-50 via-blue-50 to-indigo-50",!p&&"bg-white",u),children:[p&&(0,t.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,t.jsx)("div",{className:"relative px-4 sm:px-6 lg:px-8 py-6 sm:py-8",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[m&&m.length>0&&(0,t.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,t.jsx)("ol",{className:"flex items-center space-x-2 text-sm",children:m.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center",children:[s>0&&(0,t.jsx)("span",{className:"mx-2 text-gray-400",children:"/"}),e.href?(0,t.jsx)("button",{onClick:()=>f.push(e.href),className:"text-gray-600 hover:text-sky-600 transition-colors",children:e.label}):(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:e.label})]},s))})}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[h&&(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>f.back(),className:"flex-shrink-0 mt-1",children:(0,t.jsx)(n.A,{className:"h-4 w-4"})}),d&&(0,t.jsx)("div",{className:(0,i.cn)("flex-shrink-0 p-3 rounded-xl",p?"bg-white/80 backdrop-blur-sm shadow-lg":"bg-sky-50","text-sky-600"),children:(0,t.jsx)(d,{className:"h-6 w-6"})}),(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center flex-wrap gap-3 mb-2",children:[(0,t.jsx)("h1",{className:(0,i.cn)("text-2xl sm:text-3xl font-bold text-gray-900 leading-tight",p&&"bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent"),children:s}),x&&(0,t.jsx)(l.E,{variant:x.variant||"default",className:(0,i.cn)("text-xs font-medium",x.className),children:x.text})]}),a&&(0,t.jsx)("p",{className:"text-gray-600 text-sm sm:text-base max-w-2xl leading-relaxed",children:a})]})]}),o&&(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"flex items-center space-x-3",children:o})})]})]})}),(0,t.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"})]})}function x(e){let{stats:s,className:a}=e;return(0,t.jsx)("div",{className:(0,i.cn)("grid grid-cols-2 sm:grid-cols-4 gap-4 mt-6",a),children:s.map((e,s)=>(0,t.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50",children:[(0,t.jsx)("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide",children:e.label}),(0,t.jsx)("p",{className:"text-lg font-bold text-gray-900 mt-1",children:e.value}),e.change&&(0,t.jsx)("p",{className:(0,i.cn)("text-xs font-medium mt-1","up"===e.trend&&"text-green-600","down"===e.trend&&"text-red-600","neutral"===e.trend&&"text-gray-600"),children:e.change})]},s))})}let o={Primary:e=>{let{children:s,...a}=e;return(0,t.jsx)(r.$,{className:"bg-sky-600 hover:bg-sky-700 text-white shadow-lg",...a,children:s})},Secondary:e=>{let{children:s,...a}=e;return(0,t.jsx)(r.$,{variant:"outline",className:"border-sky-200 text-sky-700 hover:bg-sky-50",...a,children:s})},Ghost:e=>{let{children:s,...a}=e;return(0,t.jsx)(r.$,{variant:"ghost",className:"text-gray-600 hover:text-sky-600 hover:bg-sky-50",...a,children:s})}}},5828:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7905:(e,s,a)=>{"use strict";a.d(s,{hO:()=>c,sG:()=>n});var t=a(9585),r=a(3220),l=a(8130),i=a(9605),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let a=(0,l.TL)(`Primitive.${s}`),r=t.forwardRef((e,t)=>{let{asChild:r,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(r?a:s,{...l,ref:t})});return r.displayName=`Primitive.${s}`,{...e,[s]:r}},{});function c(e,s){e&&r.flushSync(()=>e.dispatchEvent(s))}},7971:(e,s,a)=>{"use strict";a.d(s,{p:()=>i});var t=a(9605),r=a(9585),l=a(6994);let i=r.forwardRef((e,s)=>{let{className:a,type:r,...i}=e;return(0,t.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...i})});i.displayName="Input"},8436:(e,s,a)=>{"use strict";a.d(s,{b:()=>n});var t=a(9585),r=a(7905),l=a(9605),i=t.forwardRef((e,s)=>(0,l.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{var a;s.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var n=i},9265:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},9644:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(5050).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[2094,5315,7436,7693,1147,7627,3005,390,110,7358],()=>s(1588)),_N_E=e.O()}]);