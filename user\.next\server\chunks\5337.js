"use strict";exports.id=5337,exports.ids=[5337],exports.modules={12053:(e,t,a)=>{a.d(t,{J:()=>d});var o=a(40969),r=a(73356),n=a(2724),s=a(52774),l=a(21764);let i=(0,s.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...t},a)=>(0,o.jsx)(n.b,{ref:a,className:(0,l.cn)(i(),e),...t}));d.displayName=n.b.displayName},24689:(e,t,a)=>{a.d(t,{A:()=>o});let o=(0,a(99024).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},34882:(e,t,a)=>{a.d(t,{lG:()=>Y,Cf:()=>et,c7:()=>ea,L3:()=>eo});var o=a(40969),r=a(73356),n=a(60952),s=a(24629),l=a(64680),i=a(59705),d=a(26314),c=a(21431),u=a(25028),f=a(36146),p=a(22195),g=a(81861),m=a(43424),y=a(7111),x=a(87465),h=a(19334),b="Dialog",[v,N]=(0,l.A)(b),[j,w]=v(b),D=e=>{let{__scopeDialog:t,children:a,open:n,defaultOpen:s,onOpenChange:l,modal:c=!0}=e,u=r.useRef(null),f=r.useRef(null),[p,g]=(0,d.i)({prop:n,defaultProp:s??!1,onChange:l,caller:b});return(0,o.jsx)(j,{scope:t,triggerRef:u,contentRef:f,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:g,onOpenToggle:r.useCallback(()=>g(e=>!e),[g]),modal:c,children:a})};D.displayName=b;var R="DialogTrigger";r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,l=w(R,a),i=(0,s.s)(t,l.triggerRef);return(0,o.jsx)(g.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":L(l.open),...r,ref:i,onClick:(0,n.m)(e.onClick,l.onOpenToggle)})}).displayName=R;var k="DialogPortal",[C,I]=v(k,{forceMount:void 0}),A=e=>{let{__scopeDialog:t,forceMount:a,children:n,container:s}=e,l=w(k,t);return(0,o.jsx)(C,{scope:t,forceMount:a,children:r.Children.map(n,e=>(0,o.jsx)(p.C,{present:a||l.open,children:(0,o.jsx)(f.Z,{asChild:!0,container:s,children:e})}))})};A.displayName=k;var M="DialogOverlay",O=r.forwardRef((e,t)=>{let a=I(M,e.__scopeDialog),{forceMount:r=a.forceMount,...n}=e,s=w(M,e.__scopeDialog);return s.modal?(0,o.jsx)(p.C,{present:r||s.open,children:(0,o.jsx)(E,{...n,ref:t})}):null});O.displayName=M;var F=(0,h.TL)("DialogOverlay.RemoveScroll"),E=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,n=w(M,a);return(0,o.jsx)(y.A,{as:F,allowPinchZoom:!0,shards:[n.contentRef],children:(0,o.jsx)(g.sG.div,{"data-state":L(n.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),_="DialogContent",P=r.forwardRef((e,t)=>{let a=I(_,e.__scopeDialog),{forceMount:r=a.forceMount,...n}=e,s=w(_,e.__scopeDialog);return(0,o.jsx)(p.C,{present:r||s.open,children:s.modal?(0,o.jsx)(z,{...n,ref:t}):(0,o.jsx)(B,{...n,ref:t})})});P.displayName=_;var z=r.forwardRef((e,t)=>{let a=w(_,e.__scopeDialog),l=r.useRef(null),i=(0,s.s)(t,a.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,x.Eq)(e)},[]),(0,o.jsx)(G,{...e,ref:i,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=r.forwardRef((e,t)=>{let a=w(_,e.__scopeDialog),n=r.useRef(!1),s=r.useRef(!1);return(0,o.jsx)(G,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||a.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,s.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(s.current=!0));let o=t.target;a.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&s.current&&t.preventDefault()}})}),G=r.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:i,...d}=e,f=w(_,a),p=r.useRef(null),g=(0,s.s)(t,p);return(0,m.Oh)(),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(u.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,o.jsx)(c.qW,{role:"dialog",id:f.contentId,"aria-describedby":f.descriptionId,"aria-labelledby":f.titleId,"data-state":L(f.open),...d,ref:g,onDismiss:()=>f.onOpenChange(!1)})}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(U,{titleId:f.titleId}),(0,o.jsx)(V,{contentRef:p,descriptionId:f.descriptionId})]})]})}),T="DialogTitle",q=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,n=w(T,a);return(0,o.jsx)(g.sG.h2,{id:n.titleId,...r,ref:t})});q.displayName=T;var $="DialogDescription",W=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,n=w($,a);return(0,o.jsx)(g.sG.p,{id:n.descriptionId,...r,ref:t})});W.displayName=$;var S="DialogClose",H=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=w(S,a);return(0,o.jsx)(g.sG.button,{type:"button",...r,ref:t,onClick:(0,n.m)(e.onClick,()=>s.onOpenChange(!1))})});function L(e){return e?"open":"closed"}H.displayName=S;var Z="DialogTitleWarning",[J,K]=(0,l.q)(Z,{contentName:_,titleName:T,docsSlug:"dialog"}),U=({titleId:e})=>{let t=K(Z),a=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(a))},[a,e]),null},V=({contentRef:e,descriptionId:t})=>{let a=K("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return r.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");t&&a&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},Q=a(83241),X=a(21764);let Y=D,ee=r.forwardRef(({className:e,...t},a)=>(0,o.jsx)(O,{ref:a,className:(0,X.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));ee.displayName=O.displayName;let et=r.forwardRef(({className:e,children:t,...a},r)=>(0,o.jsxs)(A,{children:[(0,o.jsx)(ee,{}),(0,o.jsxs)(P,{ref:r,className:(0,X.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,o.jsxs)(H,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,o.jsx)(Q.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));et.displayName=P.displayName;let ea=({className:e,...t})=>(0,o.jsx)("div",{className:(0,X.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});ea.displayName="DialogHeader";let eo=r.forwardRef(({className:e,...t},a)=>(0,o.jsx)(q,{ref:a,className:(0,X.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));eo.displayName=q.displayName,r.forwardRef(({className:e,...t},a)=>(0,o.jsx)(W,{ref:a,className:(0,X.cn)("text-sm text-muted-foreground",e),...t})).displayName=W.displayName},44356:(e,t,a)=>{a.d(t,{A:()=>o});let o=(0,a(99024).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},57387:(e,t,a)=>{a.d(t,{p:()=>s});var o=a(40969),r=a(73356),n=a(21764);let s=r.forwardRef(({className:e,type:t,...a},r)=>(0,o.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...a}));s.displayName="Input"},60545:(e,t,a)=>{a.d(t,{A:()=>o});let o=(0,a(99024).A)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])}};