(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6174],{175:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(5050).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},1158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(5050).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},2006:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(5050).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2794:(e,t,s)=>{Promise.resolve().then(s.bind(s,8700))},3089:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(5050).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5828:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(5050).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7379:(e,t,s)=>{"use strict";s.d(t,{Rc:()=>d,g1:()=>r,iH:()=>h,ot:()=>a,u8:()=>i});let{useGetUserTicketsQuery:r,useGetTicketByIdQuery:a,useCreateSupportTicketMutation:i,useAddTicketResponseMutation:l,useCloseTicketMutation:c,useReopenTicketMutation:n,useAddTicketMessageMutation:d,useGetFAQCategoriesQuery:o,useGetFAQQuestionsQuery:u,useSearchFAQQuery:p,useRateFAQAnswerMutation:m,useGetSupportStatsQuery:x,useGetLiveChatStatusQuery:h,useStartLiveChatMutation:g,useSendChatMessageMutation:y,useEndLiveChatMutation:j,useGetChatHistoryQuery:b,useSubmitFeedbackMutation:v,useGetSupportContactsQuery:N}=s(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getUserTickets:e.query({query:e=>({url:"/support/tickets/my-tickets",params:{page:e.page||1,limit:e.limit||20,...e.status&&{status:e.status},...e.category&&{category:e.category},...e.priority&&{priority:e.priority},...e.sortBy&&{sortBy:e.sortBy},...e.sortOrder&&{sortOrder:e.sortOrder}}}),providesTags:e=>{var t;return(null==e||null==(t=e.data)?void 0:t.data)?[...e.data.data.map(e=>{let{_id:t}=e;return{type:"SupportTicket",id:t}}),{type:"SupportTicket",id:"LIST"}]:[{type:"SupportTicket",id:"LIST"}]},keepUnusedDataFor:120}),getTicketById:e.query({query:e=>"/support/tickets/".concat(e),providesTags:(e,t,s)=>[{type:"SupportTicket",id:s}],keepUnusedDataFor:300}),createSupportTicket:e.mutation({query:e=>({url:"/support/tickets",method:"POST",body:e}),invalidatesTags:[{type:"SupportTicket",id:"LIST"}]}),addTicketResponse:e.mutation({query:e=>{let{ticketId:t,...s}=e;return{url:"/support/tickets/".concat(t,"/responses"),method:"POST",body:s}},invalidatesTags:(e,t,s)=>{let{ticketId:r}=s;return[{type:"SupportTicket",id:r},{type:"SupportTicket",id:"LIST"}]}}),closeTicket:e.mutation({query:e=>{let{ticketId:t,...s}=e;return{url:"/support/tickets/".concat(t,"/close"),method:"PUT",body:s}},invalidatesTags:(e,t,s)=>{let{ticketId:r}=s;return[{type:"SupportTicket",id:r},{type:"SupportTicket",id:"LIST"}]}}),reopenTicket:e.mutation({query:e=>{let{ticketId:t,...s}=e;return{url:"/support/tickets/".concat(t,"/reopen"),method:"PUT",body:s}},invalidatesTags:(e,t,s)=>{let{ticketId:r}=s;return[{type:"SupportTicket",id:r},{type:"SupportTicket",id:"LIST"}]}}),addTicketMessage:e.mutation({query:e=>{let{ticketId:t,message:s,attachments:r}=e;return{url:"/support/tickets/".concat(t,"/messages"),method:"POST",body:{content:s,attachments:r}}},invalidatesTags:(e,t,s)=>{let{ticketId:r}=s;return[{type:"SupportTicket",id:r},{type:"SupportTicket",id:"LIST"}]}}),getFAQCategories:e.query({query:()=>"/support/faq/categories",providesTags:[{type:"SupportTicket",id:"FAQ_CATEGORIES"}],keepUnusedDataFor:3600}),getFAQQuestions:e.query({query:e=>({url:"/support/faq/questions",params:{page:e.page||1,limit:e.limit||20,...e.categoryId&&{categoryId:e.categoryId},...e.search&&{search:e.search}}}),providesTags:[{type:"SupportTicket",id:"FAQ_QUESTIONS"}],keepUnusedDataFor:1800}),searchFAQ:e.query({query:e=>{let{query:t,limit:s=10}=e;return{url:"/support/faq/search",params:{q:t,limit:s}}},keepUnusedDataFor:0}),rateFAQAnswer:e.mutation({query:e=>({url:"/support/faq/rate",method:"POST",body:e})}),getSupportStats:e.query({query:()=>"/support/stats",providesTags:[{type:"SupportTicket",id:"STATS"}],keepUnusedDataFor:600}),getLiveChatStatus:e.query({query:()=>"/support/live-chat/status",keepUnusedDataFor:60}),startLiveChat:e.mutation({query:e=>({url:"/support/live-chat/start",method:"POST",body:e})}),sendChatMessage:e.mutation({query:e=>{let{chatId:t,...s}=e;return{url:"/support/live-chat/".concat(t,"/messages"),method:"POST",body:s}}}),endLiveChat:e.mutation({query:e=>{let{chatId:t,...s}=e;return{url:"/support/live-chat/".concat(t,"/end"),method:"PUT",body:s}}}),getChatHistory:e.query({query:e=>({url:"/support/live-chat/history",params:{page:e.page||1,limit:e.limit||20,...e.fromDate&&{fromDate:e.fromDate},...e.toDate&&{toDate:e.toDate}}}),providesTags:[{type:"SupportTicket",id:"CHAT_HISTORY"}],keepUnusedDataFor:600}),submitFeedback:e.mutation({query:e=>({url:"/support/feedback",method:"POST",body:e})}),getSupportContacts:e.query({query:()=>"/support/contacts",providesTags:[{type:"SupportTicket",id:"CONTACTS"}],keepUnusedDataFor:86400})})})},8163:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(5050).A)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},8415:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(5050).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},8700:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var r=s(9605),a=s(9585),i=s(5935),l=s(3005),c=s(8063),n=s(2933),d=s(8415),o=s(2237),u=s(689),p=s(175),m=s(5828),x=s(3089),h=s(2006),g=s(5449),y=s(9405),j=s(1158);let b=(0,s(5050).A)("Paperclip",[["path",{d:"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48",key:"1u3ebp"}]]);var v=s(8163),N=s(6994),f=s(7379),k=s(9294),T=s(9837),w=s(6845);function A(){var e,t,s;let A=(0,i.useRouter)(),[q,S]=(0,a.useState)("overview"),[C,F]=(0,a.useState)(!1),[I,L]=(0,a.useState)(""),[P,D]=(0,a.useState)(""),[E,U]=(0,a.useState)({category:"",priority:T.LH.MEDIUM,subject:"",description:""}),{data:M,isLoading:O}=(0,f.g1)({limit:10}),{data:Q,isLoading:_}=(0,k.Ud)(),{data:R,isLoading:Z}=(0,k.vG)({category:P||void 0,search:I||void 0,limit:20}),{data:H}=(0,f.iH)(),[W,{isLoading:B}]=(0,f.u8)(),G=async e=>{if(e.preventDefault(),!E.category||!E.subject||!E.description)return void w.toast.error("Please fill in all required fields");try{await W({category:E.category,priority:E.priority,subject:E.subject,description:E.description}).unwrap(),w.toast.success("Support ticket created successfully!"),F(!1),U({category:"",priority:T.LH.MEDIUM,subject:"",description:""})}catch(e){console.error("Error creating ticket:",e),w.toast.error("Failed to create support ticket")}},Y=Array.isArray(null==M?void 0:M.data)?M.data:(null==M||null==(e=M.data)?void 0:e.data)||[],z=(null==Q||null==(t=Q.data)?void 0:t.categories)||[],$=Object.values((null==R||null==(s=R.data)?void 0:s.faqs)||{}).flat()||[],V=(null==H?void 0:H.data)||{available:!1,estimatedWaitTime:0,agentsOnline:0},J=[{title:"Live Chat",description:"Get instant help from our support team",icon:d.A,color:"text-blue-600",bgColor:"bg-blue-100",available:V.available,waitTime:V.estimatedWaitTime},{title:"Create Ticket",description:"Submit a detailed support request",icon:o.A,color:"text-green-600",bgColor:"bg-green-100",available:!0},{title:"FAQ",description:"Find answers to common questions",icon:u.A,color:"text-purple-600",bgColor:"bg-purple-100",available:!0},{title:"Contact Us",description:"Reach out via phone or email",icon:p.A,color:"text-orange-600",bgColor:"bg-orange-100",available:!0}],K=e=>{switch(e){case"open":return(0,r.jsx)(m.A,{className:"h-4 w-4 text-blue-600"});case"in_progress":return(0,r.jsx)(x.A,{className:"h-4 w-4 text-yellow-600"});case"resolved":return(0,r.jsx)(h.A,{className:"h-4 w-4 text-green-600"});case"closed":return(0,r.jsx)(h.A,{className:"h-4 w-4 text-gray-600"});default:return(0,r.jsx)(m.A,{className:"h-4 w-4 text-gray-600"})}},X=e=>{switch(e){case"open":return"bg-blue-100 text-blue-800";case"in_progress":return"bg-yellow-100 text-yellow-800";case"resolved":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},ee=e=>{switch(e){case"urgent":return"bg-red-100 text-red-800";case"high":return"bg-orange-100 text-orange-800";case"medium":return"bg-yellow-100 text-yellow-800";case"low":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}};return(0,r.jsxs)(l.A,{children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Support Center"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Get help and find answers to your questions"})]}),(0,r.jsxs)(n.$,{onClick:()=>F(!0),className:"flex items-center space-x-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Create Ticket"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:J.map((e,t)=>{let s=e.icon;return(0,r.jsx)(c.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow ".concat(e.available?"":"opacity-60"),onClick:()=>{"Create Ticket"===e.title?F(!0):"FAQ"===e.title?S("faq"):"Live Chat"===e.title&&e.available},children:(0,r.jsxs)(c.Wu,{className:"p-6 text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 ".concat(e.bgColor," rounded-full flex items-center justify-center mx-auto mb-4"),children:(0,r.jsx)(s,{className:"h-6 w-6 ".concat(e.color)})}),(0,r.jsx)("h3",{className:"font-semibold mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),"Live Chat"===e.title&&(0,r.jsx)("div",{className:"text-xs",children:e.available?(0,r.jsxs)("span",{className:"text-green-600",children:["Available • ",null==e?void 0:e.waitTime,"min wait"]}):(0,r.jsx)("span",{className:"text-red-600",children:"Currently offline"})})]})},t)})}),(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",label:"Overview"},{id:"tickets",label:"My Tickets"},{id:"faq",label:"FAQ"},{id:"contact",label:"Contact Info"}].map(e=>(0,r.jsx)("button",{onClick:()=>S(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(q===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:e.label},e.id))})}),"overview"===q&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsxs)(c.ZB,{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Recent Tickets"]})}),(0,r.jsx)(c.Wu,{children:Y.length>0?(0,r.jsx)("div",{className:"space-y-4",children:Y.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[K(e.status),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:e.subject}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:(0,N.Yq)(e.createdAt)})]})]}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(X(e.status)),children:e.status})]},e._id))}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("p",{className:"text-gray-600",children:"No support tickets yet"})]})})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsxs)(c.ZB,{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Quick Help"]})}),(0,r.jsx)(c.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Getting Started"}),(0,r.jsx)("p",{className:"text-sm text-blue-700",children:"Learn how to make your first investment and navigate the platform."})]}),(0,r.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"Investment Guide"}),(0,r.jsx)("p",{className:"text-sm text-green-700",children:"Understand how real estate stock investments work and their benefits."})]}),(0,r.jsxs)("div",{className:"p-4 bg-purple-50 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-purple-900 mb-2",children:"Account Security"}),(0,r.jsx)("p",{className:"text-sm text-purple-700",children:"Tips to keep your account secure and protect your investments."})]})]})})]})]}),"tickets"===q&&(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(c.ZB,{children:"My Support Tickets"}),(0,r.jsxs)(n.$,{onClick:()=>F(!0),size:"sm",className:"flex items-center space-x-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"New Ticket"})]})]})}),(0,r.jsx)(c.Wu,{children:O?(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"animate-pulse flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gray-200 rounded-full"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},e))}):Y.length>0?(0,r.jsx)("div",{className:"space-y-4",children:Y.map(e=>{var t;return(0,r.jsx)("div",{className:"border rounded-lg p-4 hover:bg-gray-50 cursor-pointer",onClick:()=>A.push("/support/tickets/".concat(e._id)),children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[K(e.status),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:e.subject}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[e.description.substring(0,100),"..."]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:["Created: ",(0,N.Yq)(e.createdAt)]}),(0,r.jsxs)("span",{children:["Category: ",e.category]}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(ee(e.priority)),children:e.priority})]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat(X(e.status)),children:e.status}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 mt-2",children:[(null==(t=e.responses)?void 0:t.length)||0," responses"]})]})]})},e._id)})}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(o.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No support tickets"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"You haven't created any support tickets yet. Create one if you need help."}),(0,r.jsx)(n.$,{onClick:()=>F(!0),children:"Create Your First Ticket"})]})})]}),"faq"===q&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(c.Zp,{children:(0,r.jsx)(c.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search frequently asked questions...",value:I,onChange:e=>L(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:z.map(e=>(0,r.jsx)(c.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>D(e),children:(0,r.jsxs)(c.Wu,{className:"p-6 text-center",children:[(0,r.jsx)("div",{className:"text-4xl mb-4",children:"❓"}),(0,r.jsx)("h3",{className:"font-semibold mb-2",children:e}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-3",children:["Find answers to ",e.toLowerCase()," related questions"]}),(0,r.jsx)("p",{className:"text-xs text-blue-600",children:"View questions"})]})},e))}),P&&(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Frequently Asked Questions"})}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:$.map((e,t)=>(0,r.jsxs)("div",{className:"border-b pb-4 last:border-b-0",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:e.question}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:e.answer})]},t))})})]})]}),"contact"===q&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Contact Information"})}),(0,r.jsxs)(c.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"h-5 w-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Phone Support"}),(0,r.jsx)("p",{className:"text-gray-600",children:"+91 1800-123-4567"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Mon-Fri, 9 AM - 6 PM IST"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(j.A,{className:"h-5 w-5 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Email Support"}),(0,r.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Response within 24 hours"})]})]})]})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Office Hours"})}),(0,r.jsx)(c.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Monday - Friday"}),(0,r.jsx)("span",{className:"text-gray-600",children:"9:00 AM - 6:00 PM IST"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Saturday"}),(0,r.jsx)("span",{className:"text-gray-600",children:"10:00 AM - 4:00 PM IST"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Sunday"}),(0,r.jsx)("span",{className:"text-gray-600",children:"Closed"})]}),(0,r.jsx)("div",{className:"pt-4 border-t",children:(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"For urgent issues outside office hours, please use our create a support ticket."})})]})})]})]})]}),C&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Create Support Ticket"}),(0,r.jsxs)("form",{onSubmit:G,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),(0,r.jsxs)("select",{value:E.category,onChange:e=>U({...E,category:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg",required:!0,children:[(0,r.jsx)("option",{value:"",children:"Select Category"}),(0,r.jsx)("option",{value:"technical_issues",children:"Technical Issues"}),(0,r.jsx)("option",{value:"account_issues",children:"Account Issues"}),(0,r.jsx)("option",{value:"kyc_problems",children:"KYC Problems"}),(0,r.jsx)("option",{value:"payment_issues",children:"Payment Issues"}),(0,r.jsx)("option",{value:"property_queries",children:"Property Queries"}),(0,r.jsx)("option",{value:"general",children:"General"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),(0,r.jsxs)("select",{value:E.priority,onChange:e=>U({...E,priority:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg",children:[(0,r.jsx)("option",{value:"low",children:"Low"}),(0,r.jsx)("option",{value:"medium",children:"Medium"}),(0,r.jsx)("option",{value:"high",children:"High"}),(0,r.jsx)("option",{value:"urgent",children:"Urgent"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject"}),(0,r.jsx)("input",{type:"text",value:E.subject,onChange:e=>U({...E,subject:e.target.value}),placeholder:"Brief description of your issue",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,r.jsx)("textarea",{rows:6,value:E.description,onChange:e=>U({...E,description:e.target.value}),placeholder:"Please provide detailed information about your issue...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Attachments (Optional)"}),(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[(0,r.jsx)(b,{className:"h-8 w-8 mx-auto mb-2 text-gray-400"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Drag and drop files here or click to browse"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>F(!1),className:"flex-1",children:"Cancel"}),(0,r.jsx)(n.$,{type:"submit",className:"flex-1",disabled:B,children:B?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Create Ticket"]})})]})]})]})})]})}},9294:(e,t,s)=>{"use strict";s.d(t,{Ud:()=>a,ec:()=>c,vG:()=>r});let{useGetFAQsQuery:r,useGetFAQCategoriesQuery:a,useGetFAQByIdQuery:i,useSearchFAQsQuery:l,useSubmitFAQFeedbackMutation:c,useLazyGetFAQsQuery:n,useLazySearchFAQsQuery:d}=s(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getFAQs:e.query({query:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return e.category&&"all"!==e.category&&t.append("category",e.category),e.search&&t.append("search",e.search),e.limit&&t.append("limit",e.limit.toString()),{url:"/faq?".concat(t.toString()),method:"GET"}},providesTags:["FAQ"]}),getFAQCategories:e.query({query:()=>"/faq/categories",providesTags:["FAQCategories"]}),getFAQById:e.query({query:e=>"/faq/".concat(e),providesTags:(e,t,s)=>[{type:"FAQ",id:s}]}),searchFAQs:e.query({query:e=>{let{query:t,limit:s=20}=e;return"/faq/search/".concat(encodeURIComponent(t),"?limit=").concat(s)},providesTags:["FAQ"]}),submitFAQFeedback:e.mutation({query:e=>{let{id:t,helpful:s}=e;return{url:"/faq/".concat(t,"/helpful"),method:"POST",body:{helpful:s}}},invalidatesTags:(e,t,s)=>{let{id:r}=s;return[{type:"FAQ",id:r}]}})})})},9837:(e,t,s)=>{"use strict";s.d(t,{LH:()=>a,iC:()=>r});var r=function(e){return e.NOT_STARTED="not_started",e.PENDING="pending",e.UNDER_REVIEW="under_review",e.APPROVED="approved",e.REJECTED="rejected",e}({}),a=function(e){return e.LOW="low",e.MEDIUM="medium",e.HIGH="high",e.URGENT="urgent",e}({})}},e=>{var t=t=>e(e.s=t);e.O(0,[2094,5315,7436,7693,1147,7627,3005,390,110,7358],()=>t(2794)),_N_E=e.O()}]);