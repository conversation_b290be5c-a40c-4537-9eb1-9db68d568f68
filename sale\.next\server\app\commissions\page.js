(()=>{var e={};e.id=783,e.ids=[783],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6637:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(60605).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\web-projects\\\\builder\\\\sale\\\\src\\\\app\\\\commissions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\commissions\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36521:(e,s,a)=>{Promise.resolve().then(a.bind(a,79539))},45651:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=a(10557),r=a(68490),i=a(13172),l=a.n(i),n=a(68835),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(s,d);let o={children:["",{children:["commissions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,6637)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\commissions\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,92603)),"C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51104,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,55993,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,95846,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4942))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\web-projects\\builder\\sale\\src\\app\\commissions\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/commissions/page",pathname:"/commissions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},46249:(e,s,a)=>{Promise.resolve().then(a.bind(a,6637))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77060:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},79201:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(98085).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},79539:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>w});var t=a(40969),r=a(73356),i=a(88251),l=a(89073),n=a(66949),d=a(46411),o=a(76650),c=a(2223),m=a(83427),x=a(79201),u=a(21857),p=a(77060),h=a(91798),g=a(54076),j=a(77272),y=a(8713),v=a(99206),b=a(48567),f=a(61631),N=a(21764);function w(){let[e,s]=(0,r.useState)(""),[a,w]=(0,r.useState)("all"),[T,A]=(0,r.useState)(1),q=(0,b.G)(f.mB);if(q?.role!=="sales"&&q?.role!=="sales_manager"&&q?.role!=="admin")return(0,t.jsx)(i.A,{title:"Access Denied",children:(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(m.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access commission tracking."})]})})});let{data:P,isLoading:C,error:k}=(0,v.OT)({page:T,limit:20,status:"all"!==a?a:void 0}),D=e=>{switch(e){case"paid":return(0,t.jsx)(x.A,{className:"w-4 h-4 text-green-600"});case"approved":return(0,t.jsx)(x.A,{className:"w-4 h-4 text-blue-600"});case"pending":return(0,t.jsx)(u.A,{className:"w-4 h-4 text-yellow-600"});case"rejected":return(0,t.jsx)(p.A,{className:"w-4 h-4 text-red-600"});default:return(0,t.jsx)(u.A,{className:"w-4 h-4 text-gray-600"})}},_=e=>{switch(e){case"paid":return"success";case"approved":return"default";case"pending":return"warning";case"rejected":return"destructive";default:return"secondary"}},L=P?.data?.commissions?.reduce((e,s)=>e+s.commissionAmount,0)||0,S=P?.data?.commissions?.filter(e=>"paid"===e.status).reduce((e,s)=>e+s.commissionAmount,0)||0,E=P?.data?.commissions?.filter(e=>"pending"===e.status).reduce((e,s)=>e+s.commissionAmount,0)||0;return(0,t.jsxs)(i.A,{title:"Commissions",children:[(0,t.jsx)(l.A,{title:"Commission Tracking",subtitle:"Track your earnings and commission payments",icon:m.A}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.A,{className:"w-5 h-5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Total Earned"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:(0,N.vv)(L)})]})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.A,{className:"w-5 h-5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Paid"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:(0,N.vv)(S)})]})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"w-5 h-5 text-yellow-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Pending"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:(0,N.vv)(E)})]})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"w-5 h-5 text-purple-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"This Month"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:"₹0"})]})]})})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,t.jsx)(n.ZB,{children:"Commission History"}),(0,t.jsx)("div",{className:"flex space-x-4",children:(0,t.jsxs)(c.l6,{value:a,onValueChange:w,children:[(0,t.jsx)(c.bq,{className:"w-40",children:(0,t.jsx)(c.yv,{placeholder:"Status"})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"all",children:"All Status"}),(0,t.jsx)(c.eb,{value:"pending",children:"Pending"}),(0,t.jsx)(c.eb,{value:"approved",children:"Approved"}),(0,t.jsx)(c.eb,{value:"paid",children:"Paid"}),(0,t.jsx)(c.eb,{value:"rejected",children:"Rejected"})]})]})})]})}),(0,t.jsxs)(n.Wu,{children:[C?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-sky-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading commissions..."})]}):k?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(m.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Failed to load commissions"})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[P?.data?.commissions?.map(e=>(0,t.jsx)("div",{className:"border rounded-lg p-4 hover:bg-gray-50 transition-colors",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[D(e.status),(0,t.jsxs)("h3",{className:"font-medium text-gray-900",children:["Commission #",e._id.slice(-6)]}),(0,t.jsx)(o.E,{variant:_(e.status),children:e.status})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:["Amount: ",(0,N.vv)(e.commissionAmount)]})]}),e.propertyId&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:["Property: ",e.propertyId.name||"Property"]})]}),e.customerId&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:["Customer: ",e.customerId.firstName," ",e.customerId.lastName]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-sm text-gray-500",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(y.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:["Created: ",(0,N.Yq)(e.createdAt)]})]}),e.paidAt&&(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(x.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:["Paid: ",(0,N.Yq)(e.paidAt)]})]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("p",{className:"text-lg font-bold text-green-600",children:(0,N.vv)(e.commissionAmount)}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[e.commissionRate,"% rate"]})]})]})},e._id)),(!P?.data?.commissions||0===P.data.commissions.length)&&(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(m.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"No commissions found"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Commissions will appear here when you make sales"})]})]}),P?.data?.pagination&&P.data.pagination.totalPages>1&&(0,t.jsxs)("div",{className:"flex justify-center mt-6 space-x-2",children:[(0,t.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>A(e=>Math.max(1,e-1)),disabled:1===T,children:"Previous"}),(0,t.jsxs)("span",{className:"flex items-center px-4 text-sm text-gray-600",children:["Page ",T," of ",P.data.pagination.totalPages]}),(0,t.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>A(e=>e+1),disabled:T===P.data.pagination.totalPages,children:"Next"})]})]})]})]})]})}},79551:e=>{"use strict";e.exports=require("url")},89073:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var t=a(40969);function r({title:e,subtitle:s,icon:a,greeting:r,userName:i,actions:l,children:n}){return(0,t.jsx)("div",{className:"bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg p-6 text-white shadow-lg",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center gap-3",children:[a&&(0,t.jsx)(a,{className:"h-8 w-8"}),r&&i?`${r}, ${i}! 👋`:e]}),(0,t.jsx)("p",{className:"text-sky-100",children:s||"Manage and track your SGM sales activities with real-time insights"})]}),(l||n)&&(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[l,n]})]})})}a(73356)},99206:(e,s,a)=>{"use strict";a.d(s,{$V:()=>n,FO:()=>o,Hu:()=>i,OT:()=>q,Pb:()=>r,WD:()=>y,Zu:()=>b,aT:()=>j,cm:()=>v,nK:()=>u,pv:()=>A,ro:()=>f});let t=a(53412).q.injectEndpoints({endpoints:e=>({getDashboardStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getDashboardActivities:e.query({query:e=>({url:"/dashboard/activities",params:e}),providesTags:["Dashboard"]}),getSalesStats:e.query({query:()=>"/sales/stats",providesTags:["Dashboard"]}),getLeads:e.query({query:e=>({url:"/sales/leads",params:e}),providesTags:["Lead"]}),getLeadById:e.query({query:e=>`/sales/leads/${e}`,providesTags:(e,s,a)=>[{type:"Lead",id:a}]}),createLead:e.mutation({query:e=>({url:"/sales/leads",method:"POST",body:e}),invalidatesTags:["Lead","Dashboard"]}),updateLead:e.mutation({query:({id:e,data:s})=>({url:`/sales/leads/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:a})=>[{type:"Lead",id:a},"Lead","Dashboard"]}),deleteLead:e.mutation({query:e=>({url:`/sales/leads/${e}`,method:"DELETE"}),invalidatesTags:["Lead","Dashboard"]}),assignLead:e.mutation({query:({leadId:e,salesRepId:s})=>({url:`/sales/leads/${e}/assign`,method:"POST",body:{salesRepId:s}}),invalidatesTags:["Lead"]}),getCustomers:e.query({query:e=>({url:"/sales/customers",params:e}),providesTags:["Customer"]}),getCustomerById:e.query({query:e=>`/sales/customers/${e}`,providesTags:(e,s,a)=>[{type:"Customer",id:a}]}),createCustomer:e.mutation({query:e=>({url:"/sales/customers",method:"POST",body:e}),invalidatesTags:["Customer","Dashboard"]}),updateCustomer:e.mutation({query:({id:e,data:s})=>({url:`/sales/customers/${e}`,method:"PUT",body:s}),invalidatesTags:(e,s,{id:a})=>[{type:"Customer",id:a},"Customer"]}),getCommissions:e.query({query:e=>({url:"/sales/commissions",params:e}),providesTags:["Commission"]}),getSalesTargets:e.query({query:()=>"/sales/targets",providesTags:["Report"]}),createSalesTarget:e.mutation({query:e=>({url:"/sales/targets",method:"POST",body:e}),invalidatesTags:["Report","Dashboard"]}),updateSalesTarget:e.mutation({query:({id:e,data:s})=>({url:`/sales/targets/${e}`,method:"PUT",body:s}),invalidatesTags:["Report","Dashboard"]}),getFollowUps:e.query({query:e=>({url:"/follow-ups",params:e}),providesTags:["Task"]}),createFollowUp:e.mutation({query:e=>({url:"/follow-ups",method:"POST",body:e}),invalidatesTags:["Task","Dashboard"]}),updateFollowUp:e.mutation({query:({id:e,data:s})=>({url:`/follow-ups/${e}`,method:"PUT",body:s}),invalidatesTags:["Task","Dashboard"]}),deleteFollowUp:e.mutation({query:e=>({url:`/follow-ups/${e}`,method:"DELETE"}),invalidatesTags:["Task","Dashboard"]})})}),{useGetDashboardStatsQuery:r,useGetDashboardActivitiesQuery:i,useGetSalesStatsQuery:l,useGetLeadsQuery:n,useGetLeadByIdQuery:d,useCreateLeadMutation:o,useUpdateLeadMutation:c,useDeleteLeadMutation:m,useAssignLeadMutation:x,useGetCustomersQuery:u,useGetCustomerByIdQuery:p,useCreateCustomerMutation:h,useUpdateCustomerMutation:g,useGetCommissionsQuery:j,useGetSalesTargetsQuery:y,useCreateSalesTargetMutation:v,useUpdateSalesTargetMutation:b,useGetFollowUpsQuery:f,useCreateFollowUpMutation:N,useUpdateFollowUpMutation:w,useDeleteFollowUpMutation:T}=t,{useGetCustomersQuery:A,useGetCommissionsQuery:q}=t}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[755,598,544,29,796,286,447,512],()=>a(45651));module.exports=t})();