(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3345],{839:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var a=r(9605),u=r(9585),t=r(5935);function n(){let e=(0,t.useRouter)();return(0,u.useEffect)(()=>{e.replace("/faq")},[e]),(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Redirecting to FAQ page..."})]})})}},3753:(e,s,r)=>{Promise.resolve().then(r.bind(r,839))},5935:(e,s,r)=>{"use strict";var a=r(5383);r.o(a,"useParams")&&r.d(s,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(s,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(s,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(s,{useSearchParams:function(){return a.useSearchParams}})}},e=>{var s=s=>e(e.s=s);e.O(0,[390,110,7358],()=>s(3753)),_N_E=e.O()}]);