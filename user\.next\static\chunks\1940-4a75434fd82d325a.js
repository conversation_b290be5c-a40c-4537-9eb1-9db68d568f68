(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1940],{1432:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},1645:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},2403:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},2848:(e,t,n)=>{"use strict";n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ei,hE:()=>er,hJ:()=>et,l9:()=>X});var r=n(9585),o=n(4761),i=n(4455),c=n(8972),a=n(7421),u=n(9728),s=n(7271),l=n(6452),p=n(8036),d=n(4853),f=n(7905),h=n(5790),m=n(4229),y=n(3383),g=n(8130),v=n(9605),k="Dialog",[C,b]=(0,c.A)(k),[E,w]=C(k),x=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:c,modal:s=!0}=e,l=r.useRef(null),p=r.useRef(null),[d,f]=(0,u.i)({prop:o,defaultProp:null!=i&&i,onChange:c,caller:k});return(0,v.jsx)(E,{scope:t,triggerRef:l,contentRef:p,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:d,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:s,children:n})};x.displayName=k;var S="DialogTrigger",j=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,c=w(S,n),a=(0,i.s)(t,c.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":c.open,"aria-controls":c.contentId,"data-state":V(c.open),...r,ref:a,onClick:(0,o.m)(e.onClick,c.onOpenToggle)})});j.displayName=S;var A="DialogPortal",[P,O]=C(A,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,c=w(A,t);return(0,v.jsx)(P,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,v.jsx)(d.C,{present:n||c.open,children:(0,v.jsx)(p.Z,{asChild:!0,container:i,children:e})}))})};M.displayName=A;var R="DialogOverlay",I=r.forwardRef((e,t)=>{let n=O(R,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=w(R,e.__scopeDialog);return i.modal?(0,v.jsx)(d.C,{present:r||i.open,children:(0,v.jsx)(N,{...o,ref:t})}):null});I.displayName=R;var D=(0,g.TL)("DialogOverlay.RemoveScroll"),N=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=w(R,n);return(0,v.jsx)(m.A,{as:D,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":V(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),_="DialogContent",T=r.forwardRef((e,t)=>{let n=O(_,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=w(_,e.__scopeDialog);return(0,v.jsx)(d.C,{present:r||i.open,children:i.modal?(0,v.jsx)(L,{...o,ref:t}):(0,v.jsx)(B,{...o,ref:t})})});T.displayName=_;var L=r.forwardRef((e,t)=>{let n=w(_,e.__scopeDialog),c=r.useRef(null),a=(0,i.s)(t,n.contentRef,c);return r.useEffect(()=>{let e=c.current;if(e)return(0,y.Eq)(e)},[]),(0,v.jsx)(F,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=r.forwardRef((e,t)=>{let n=w(_,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,v.jsx)(F,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,c;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(c=n.triggerRef.current)||c.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var r,c;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let a=t.target;(null==(c=n.triggerRef.current)?void 0:c.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),F=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:c,onCloseAutoFocus:a,...u}=e,p=w(_,n),d=r.useRef(null),f=(0,i.s)(t,d);return(0,h.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(l.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:c,onUnmountAutoFocus:a,children:(0,v.jsx)(s.qW,{role:"dialog",id:p.contentId,"aria-describedby":p.descriptionId,"aria-labelledby":p.titleId,"data-state":V(p.open),...u,ref:f,onDismiss:()=>p.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(J,{titleId:p.titleId}),(0,v.jsx)(K,{contentRef:d,descriptionId:p.descriptionId})]})]})}),q="DialogTitle",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=w(q,n);return(0,v.jsx)(f.sG.h2,{id:o.titleId,...r,ref:t})});U.displayName=q;var Y="DialogDescription",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=w(Y,n);return(0,v.jsx)(f.sG.p,{id:o.descriptionId,...r,ref:t})});W.displayName=Y;var z="DialogClose",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=w(z,n);return(0,v.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function V(e){return e?"open":"closed"}H.displayName=z;var G="DialogTitleWarning",[Z,$]=(0,c.q)(G,{contentName:_,titleName:q,docsSlug:"dialog"}),J=e=>{let{titleId:t}=e,n=$(G),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},K=e=>{let{contentRef:t,descriptionId:n}=e,o=$("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},Q=x,X=j,ee=M,et=I,en=T,er=U,eo=W,ei=H},3119:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},4454:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4472:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4707:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},5554:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("ArrowDownRight",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]])},5828:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6793:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8049:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},8999:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},9033:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("Bitcoin",[["path",{d:"M11.767 19.089c4.924.868 6.14-6.025 1.216-6.894m-1.216 6.894L5.86 18.047m5.908 1.042-.347 1.97m1.563-8.864c4.924.869 6.14-6.025 1.215-6.893m-1.215 6.893-3.94-.694m5.155-6.2L8.29 4.26m5.908 1.042.348-1.97M7.48 20.364l3.126-17.727",key:"yr8idg"}]])},9265:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},9625:function(e,t,n){(function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){i(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function a(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,r,o=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null!=o){var i=[],c=!0,a=!1;try{for(o=o.call(e);!(c=(n=o.next()).done)&&(i.push(n.value),!t||i.length!==t);c=!0);}catch(e){a=!0,r=e}finally{try{c||null==o.return||o.return()}finally{if(a)throw r}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return u(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return u(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var s,l,p,d,f,h={exports:{}};h.exports=(function(){if(f)return d;f=1;var e=p?l:(p=1,l="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");function t(){}function n(){}return n.resetWarningCache=t,d=function(){function r(t,n,r,o,i,c){if(c!==e){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function o(){return r}r.isRequired=r;var i={array:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:o,element:r,elementType:r,instanceOf:o,node:r,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:n,resetWarningCache:t};return i.PropTypes=i,i}})()();var m=(s=h.exports)&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s,y=function(e,n,r){var o=!!r,i=t.useRef(r);t.useEffect(function(){i.current=r},[r]),t.useEffect(function(){if(!o||!e)return function(){};var t=function(){i.current&&i.current.apply(i,arguments)};return e.on(n,t),function(){e.off(n,t)}},[o,n,e,i])},g=function(e){var n=t.useRef(e);return t.useEffect(function(){n.current=e},[e]),n.current},v=function(e){return null!==e&&"object"===o(e)},k="[object Object]",C=function e(t,n){if(!v(t)||!v(n))return t===n;var r=Array.isArray(t);if(r!==Array.isArray(n))return!1;var o=Object.prototype.toString.call(t)===k;if(o!==(Object.prototype.toString.call(n)===k))return!1;if(!o&&!r)return t===n;var i=Object.keys(t),c=Object.keys(n);if(i.length!==c.length)return!1;for(var a={},u=0;u<i.length;u+=1)a[i[u]]=!0;for(var s=0;s<c.length;s+=1)a[c[s]]=!0;var l=Object.keys(a);return l.length===i.length&&l.every(function(r){return e(t[r],n[r])})},b=function(e,t,n){return v(e)?Object.keys(e).reduce(function(o,c){var a=!v(t)||!C(e[c],t[c]);return n.includes(c)?(a&&console.warn("Unsupported prop change: options.".concat(c," is not a mutable property.")),o):a?r(r({},o||{}),{},i({},c,e[c])):o},null):null},E="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:E;if(null===e||v(e)&&"function"==typeof e.elements&&"function"==typeof e.createToken&&"function"==typeof e.createPaymentMethod&&"function"==typeof e.confirmCardPayment)return e;throw Error(t)},x=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:E;if(v(e)&&"function"==typeof e.then)return{tag:"async",stripePromise:Promise.resolve(e).then(function(e){return w(e,t)})};var n=w(e,t);return null===n?{tag:"empty"}:{tag:"sync",stripe:n}},S=function(e){e&&e._registerWrapper&&e.registerAppInfo&&(e._registerWrapper({name:"react-stripe-js",version:"3.8.0"}),e.registerAppInfo({name:"react-stripe-js",version:"3.8.0",url:"https://stripe.com/docs/stripe-js/react"}))},j=t.createContext(null);j.displayName="ElementsContext";var A=function(e,t){if(!e)throw Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},P=function(e){var n=e.stripe,r=e.options,o=e.children,i=t.useMemo(function(){return x(n)},[n]),c=a(t.useState(function(){return{stripe:"sync"===i.tag?i.stripe:null,elements:"sync"===i.tag?i.stripe.elements(r):null}}),2),u=c[0],s=c[1];t.useEffect(function(){var e=!0,t=function(e){s(function(t){return t.stripe?t:{stripe:e,elements:e.elements(r)}})};return"async"!==i.tag||u.stripe?"sync"!==i.tag||u.stripe||t(i.stripe):i.stripePromise.then(function(n){n&&e&&t(n)}),function(){e=!1}},[i,u,r]);var l=g(n);t.useEffect(function(){null!==l&&l!==n&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[l,n]);var p=g(r);return t.useEffect(function(){if(u.elements){var e=b(r,p,["clientSecret","fonts"]);e&&u.elements.update(e)}},[r,p,u.elements]),t.useEffect(function(){S(u.stripe)},[u.stripe]),t.createElement(j.Provider,{value:u},o)};P.propTypes={stripe:m.any,options:m.object};var O=function(e){return A(t.useContext(j),e)},M=function(e){return(0,e.children)(O("mounts <ElementsConsumer>"))};M.propTypes={children:m.func.isRequired};var R=["on","session"],I=t.createContext(null);I.displayName="CheckoutSdkContext";var D=function(e,t){if(!e)throw Error("Could not find CheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CheckoutProvider> provider."));return e},N=t.createContext(null);N.displayName="CheckoutContext";var _=function(e,t){if(!e)return null;e.on,e.session;var n=c(e,R);return t?Object.assign(t,n):Object.assign(e.session(),n)},T=function(e){var n=e.stripe,r=e.options,o=e.children,i=t.useMemo(function(){return x(n,"Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),c=a(t.useState(null),2),u=c[0],s=c[1],l=a(t.useState(function(){return{stripe:"sync"===i.tag?i.stripe:null,checkoutSdk:null}}),2),p=l[0],d=l[1],f=function(e,t){d(function(n){return n.stripe&&n.checkoutSdk?n:{stripe:e,checkoutSdk:t}})},h=t.useRef(!1);t.useEffect(function(){var e=!0;return"async"!==i.tag||p.stripe?"sync"===i.tag&&i.stripe&&!h.current&&(h.current=!0,i.stripe.initCheckout(r).then(function(e){e&&(f(i.stripe,e),e.on("change",s))})):i.stripePromise.then(function(t){t&&e&&!h.current&&(h.current=!0,t.initCheckout(r).then(function(e){e&&(f(t,e),e.on("change",s))}))}),function(){e=!1}},[i,p,r,s]);var m=g(n);t.useEffect(function(){null!==m&&m!==n&&console.warn("Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.")},[m,n]);var y=g(r),v=g(p.checkoutSdk);t.useEffect(function(){if(p.checkoutSdk){var e,t,n=null==y||null==(e=y.elementsOptions)?void 0:e.appearance,o=null==r||null==(t=r.elementsOptions)?void 0:t.appearance,i=!C(o,n),c=!v&&p.checkoutSdk;o&&(i||c)&&p.checkoutSdk.changeAppearance(o)}},[r,y,p.checkoutSdk,v]),t.useEffect(function(){S(p.stripe)},[p.stripe]);var k=t.useMemo(function(){return _(p.checkoutSdk,u)},[p.checkoutSdk,u]);return p.checkoutSdk?t.createElement(I.Provider,{value:p},t.createElement(N.Provider,{value:k},o)):null};T.propTypes={stripe:m.any,options:m.shape({fetchClientSecret:m.func.isRequired,elementsOptions:m.object}).isRequired};var L=function(e){var n=t.useContext(I),r=t.useContext(j);if(n&&r)throw Error("You cannot wrap the part of your app that ".concat(e," in both <CheckoutProvider> and <Elements> providers."));return n?D(n,e):A(r,e)},B=["mode"],F=function(e,n){var r="".concat(e.charAt(0).toUpperCase()+e.slice(1),"Element"),o=n?function(e){L("mounts <".concat(r,">"));var n=e.id,o=e.className;return t.createElement("div",{id:n,className:o})}:function(n){var o,i=n.id,u=n.className,s=n.options,l=void 0===s?{}:s,p=n.onBlur,d=n.onFocus,f=n.onReady,h=n.onChange,m=n.onEscape,v=n.onClick,k=n.onLoadError,C=n.onLoaderStart,E=n.onNetworksChange,w=n.onConfirm,x=n.onCancel,S=n.onShippingAddressChange,j=n.onShippingRateChange,A=L("mounts <".concat(r,">")),P="elements"in A?A.elements:null,O="checkoutSdk"in A?A.checkoutSdk:null,M=a(t.useState(null),2),R=M[0],I=M[1],D=t.useRef(null),N=t.useRef(null);y(R,"blur",p),y(R,"focus",d),y(R,"escape",m),y(R,"click",v),y(R,"loaderror",k),y(R,"loaderstart",C),y(R,"networkschange",E),y(R,"confirm",w),y(R,"cancel",x),y(R,"shippingaddresschange",S),y(R,"shippingratechange",j),y(R,"change",h),f&&(o="expressCheckout"===e?f:function(){f(R)}),y(R,"ready",o),t.useLayoutEffect(function(){if(null===D.current&&null!==N.current&&(P||O)){var t=null;if(O)switch(e){case"payment":t=O.createPaymentElement(l);break;case"address":if("mode"in l){var n=l.mode,o=c(l,B);if("shipping"===n)t=O.createShippingAddressElement(o);else if("billing"===n)t=O.createBillingAddressElement(o);else throw Error("Invalid options.mode. mode must be 'billing' or 'shipping'.")}else throw Error("You must supply options.mode. mode must be 'billing' or 'shipping'.");break;case"expressCheckout":t=O.createExpressCheckoutElement(l);break;case"currencySelector":t=O.createCurrencySelectorElement();break;case"taxId":t=O.createTaxIdElement(l);break;default:throw Error("Invalid Element type ".concat(r,". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />."))}else P&&(t=P.create(e,l));D.current=t,I(t),t&&t.mount(N.current)}},[P,O,l]);var _=g(l);return t.useEffect(function(){if(D.current){var e=b(l,_,["paymentRequest"]);e&&"update"in D.current&&D.current.update(e)}},[l,_]),t.useLayoutEffect(function(){return function(){if(D.current&&"function"==typeof D.current.destroy)try{D.current.destroy(),D.current=null}catch(e){}}},[]),t.createElement("div",{id:i,className:u,ref:N})};return o.propTypes={id:m.string,className:m.string,onChange:m.func,onBlur:m.func,onFocus:m.func,onReady:m.func,onEscape:m.func,onClick:m.func,onLoadError:m.func,onLoaderStart:m.func,onNetworksChange:m.func,onConfirm:m.func,onCancel:m.func,onShippingAddressChange:m.func,onShippingRateChange:m.func,options:m.object},o.displayName=r,o.__elementType=e,o},q="undefined"==typeof window,U=t.createContext(null);U.displayName="EmbeddedCheckoutProviderContext";var Y=function(){var e=t.useContext(U);if(!e)throw Error("<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>");return e},W=q?function(e){var n=e.id,r=e.className;return Y(),t.createElement("div",{id:n,className:r})}:function(e){var n=e.id,r=e.className,o=Y().embeddedCheckout,i=t.useRef(!1),c=t.useRef(null);return t.useLayoutEffect(function(){return!i.current&&o&&null!==c.current&&(o.mount(c.current),i.current=!0),function(){if(i.current&&o)try{o.unmount(),i.current=!1}catch(e){}}},[o]),t.createElement("div",{ref:c,id:n,className:r})},z=F("auBankAccount",q),H=F("card",q),V=F("cardNumber",q),G=F("cardExpiry",q),Z=F("cardCvc",q),$=F("fpxBank",q),J=F("iban",q),K=F("idealBank",q),Q=F("p24Bank",q),X=F("epsBank",q),ee=F("payment",q),et=F("expressCheckout",q),en=F("currencySelector",q),er=F("paymentRequestButton",q),eo=F("linkAuthentication",q),ei=F("address",q),ec=F("shippingAddress",q),ea=F("paymentMethodMessaging",q),eu=F("affirmMessage",q),es=F("afterpayClearpayMessage",q),el=F("taxId",q);e.AddressElement=ei,e.AffirmMessageElement=eu,e.AfterpayClearpayMessageElement=es,e.AuBankAccountElement=z,e.CardCvcElement=Z,e.CardElement=H,e.CardExpiryElement=G,e.CardNumberElement=V,e.CheckoutProvider=T,e.CurrencySelectorElement=en,e.Elements=P,e.ElementsConsumer=M,e.EmbeddedCheckout=W,e.EmbeddedCheckoutProvider=function(e){var n=e.stripe,r=e.options,o=e.children,i=t.useMemo(function(){return x(n,"Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),c=t.useRef(null),u=t.useRef(null),s=a(t.useState({embeddedCheckout:null}),2),l=s[0],p=s[1];t.useEffect(function(){if(!u.current&&!c.current){var e=function(e){u.current||c.current||(u.current=e,c.current=u.current.initEmbeddedCheckout(r).then(function(e){p({embeddedCheckout:e})}))};"async"===i.tag&&!u.current&&(r.clientSecret||r.fetchClientSecret)?i.stripePromise.then(function(t){t&&e(t)}):"sync"===i.tag&&!u.current&&(r.clientSecret||r.fetchClientSecret)&&e(i.stripe)}},[i,r,l,u]),t.useEffect(function(){return function(){l.embeddedCheckout?(c.current=null,l.embeddedCheckout.destroy()):c.current&&c.current.then(function(){c.current=null,l.embeddedCheckout&&l.embeddedCheckout.destroy()})}},[l.embeddedCheckout]),t.useEffect(function(){S(u)},[u]);var d=g(n);t.useEffect(function(){null!==d&&d!==n&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.")},[d,n]);var f=g(r);return t.useEffect(function(){if(null!=f){if(null==r)return void console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.");void 0===r.clientSecret&&void 0===r.fetchClientSecret&&console.warn("Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`."),null!=f.clientSecret&&r.clientSecret!==f.clientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=f.fetchClientSecret&&r.fetchClientSecret!==f.fetchClientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=f.onComplete&&r.onComplete!==f.onComplete&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it."),null!=f.onShippingDetailsChange&&r.onShippingDetailsChange!==f.onShippingDetailsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it."),null!=f.onLineItemsChange&&r.onLineItemsChange!==f.onLineItemsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.")}},[f,r]),t.createElement(U.Provider,{value:l},o)},e.EpsBankElement=X,e.ExpressCheckoutElement=et,e.FpxBankElement=$,e.IbanElement=J,e.IdealBankElement=K,e.LinkAuthenticationElement=eo,e.P24BankElement=Q,e.PaymentElement=ee,e.PaymentMethodMessagingElement=ea,e.PaymentRequestButtonElement=er,e.ShippingAddressElement=ec,e.TaxIdElement=el,e.useCheckout=function(){D(t.useContext(I),"calls useCheckout()");var e=t.useContext(N);if(!e)throw Error("Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.");return e},e.useElements=function(){return O("calls useElements()").elements},e.useStripe=function(){return L("calls useStripe()").stripe}})(t,n(9585))},9858:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},9864:(e,t,n)=>{"use strict";n.d(t,{c:()=>v});var r,o="basil",i="https://js.stripe.com",c="".concat(i,"/").concat(o,"/stripe.js"),a=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,u=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,s=function(){for(var e=document.querySelectorAll('script[src^="'.concat(i,'"]')),t=0;t<e.length;t++){var n,r=e[t];if(n=r.src,a.test(n)||u.test(n))return r}return null},l=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",n=document.createElement("script");n.src="".concat(c).concat(t);var r=document.head||document.body;if(!r)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return r.appendChild(n),n},p=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"7.6.1",startTime:t})},d=null,f=null,h=null,m=function(e,t,n){if(null===e)return null;var r,i=t[0].match(/^pk_test/),c=3===(r=e.version)?"v3":r;i&&c!==o&&console.warn("Stripe.js@".concat(c," was loaded on the page, but @stripe/stripe-js@").concat("7.6.1"," expected Stripe.js@").concat(o,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var a=e.apply(void 0,t);return p(a,n),a},y=!1,g=function(){return r?r:r=(null!==d?d:(d=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document)return void e(null);if(window.Stripe,window.Stripe)return void e(window.Stripe);try{var n,r=s();r?r&&null!==h&&null!==f&&(r.removeEventListener("load",h),r.removeEventListener("error",f),null==(n=r.parentNode)||n.removeChild(r),r=l(null)):r=l(null),h=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},f=function(e){t(Error("Failed to load Stripe.js",{cause:e}))},r.addEventListener("load",h),r.addEventListener("error",f)}catch(e){t(e);return}})).catch(function(e){return d=null,Promise.reject(e)})).catch(function(e){return r=null,Promise.reject(e)})};Promise.resolve().then(function(){return g()}).catch(function(e){y||console.warn(e)});var v=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];y=!0;var r=Date.now();return g().then(function(e){return m(e,t,r)})}},9922:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(5050).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])}}]);