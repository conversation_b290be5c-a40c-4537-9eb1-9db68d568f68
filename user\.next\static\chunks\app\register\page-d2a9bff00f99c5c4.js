(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2454],{175:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(5050).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},498:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(5050).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1158:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(5050).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},1470:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(5050).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1964:(e,s,t)=>{Promise.resolve().then(t.bind(t,9743))},2363:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(5050).A)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},2855:(e,s,t)=>{"use strict";t.d(s,{SettingsProvider:()=>n,YK:()=>o});var a=t(9605),r=t(9585);let{useGetPublicSettingsQuery:l}=t(6965).q.injectEndpoints({overrideExisting:!0,endpoints:e=>({getPublicSettings:e.query({query:()=>"/settings/public",providesTags:["Settings"]})})}),i=(0,r.createContext)(void 0),n=e=>{let{children:s}=e,[t,n]=(0,r.useState)({}),{data:c,isLoading:o,error:d,refetch:m}=l(void 0,{skip:!1,refetchOnMountOrArgChange:!0,refetchOnFocus:!1,refetchOnReconnect:!0});return(0,r.useEffect)(()=>{var e;(null==c?void 0:c.success)&&(null==(e=c.data)?void 0:e.settings)&&n(e=>{var s;return{...e,...null==(s=c.data)?void 0:s.settings}})},[c]),(0,r.useEffect)(()=>{d&&console.warn("Failed to load settings from server, using defaults:",d)},[d]),(0,r.useEffect)(()=>{let e=setInterval(()=>{m()},3e5);return()=>clearInterval(e)},[m]),(0,a.jsx)(i.Provider,{value:{settings:t,isLoading:o,error:d,isEmailVerificationEnabled:()=>{var e;return null==(e=t.EMAIL_VERIFICATION_ENABLED)||e},isPhoneVerificationEnabled:()=>{var e;return null!=(e=t.PHONE_VERIFICATION_ENABLED)&&e},isKYCRequired:()=>{var e;return null==(e=t.KYC_REQUIRED_FOR_ACTIVATION)||e},isKYCPriorityOverEmail:()=>{var e;return null==(e=t.KYC_PRIORITY_OVER_EMAIL)||e},allowKYCForInactiveUsers:()=>{var e;return null==(e=t.ALLOW_KYC_FOR_INACTIVE_USERS)||e},getMinInvestmentAmount:()=>{var e;return null!=(e=t.MIN_INVESTMENT_AMOUNT)?e:1e3},getMaxInvestmentAmount:()=>{var e;return null!=(e=t.MAX_INVESTMENT_AMOUNT)?e:1e6},isFeatureEnabled:e=>{var s;return null==(s=t["FEATURE_".concat(e.toUpperCase(),"_ENABLED")])||s}},children:s})},c=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e},o=()=>{let{isEmailVerificationEnabled:e,isKYCRequired:s,isKYCPriorityOverEmail:t,allowKYCForInactiveUsers:a}=c();return{emailVerificationEnabled:e(),kycRequired:s(),kycPriorityOverEmail:t(),allowKYCForInactive:a(),shouldShowEmailFirst:()=>e()&&!t(),shouldShowKYCFirst:()=>s()&&t(),getNextStepForUser:a=>{if(!a)return null;let r=s()&&["not_started","not_submitted","incomplete","rejected"].includes(a.kycStatus||"not_started"),l=e()&&!a.emailVerified;return"pending"===a.kycStatus||"under_review"===a.kycStatus?l?"email_verification":null:r&&t()?"kyc":l&&!t()?"email_verification":r?"kyc":l?"email_verification":null}}}},4454:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(5050).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},7530:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(5050).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},8049:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(5050).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},9249:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(5050).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},9743:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>M});var a=t(9605),r=t(9585),l=t(5935),i=t(6762),n=t.n(i),c=t(2407),o=t(7730),d=t(5706),m=t(8049),u=t(6793),h=t(1158),x=t(498),f=t(175),p=t(4454),y=t(7530),g=t(1470),b=t(9249),v=t(2363),N=t(8120),j=t(2933),w=t(8063),k=t(1128),A=t(9311),P=t(3815),C=t(2855),E=t(6845);let T=e=>{let{content:s,children:t}=e,[l,i]=(0,r.useState)(!1),n=(0,r.useRef)(null),c=()=>{n.current=setTimeout(()=>i(!0),100)},o=()=>{n.current&&clearTimeout(n.current),i(!1)};return(0,a.jsxs)("span",{className:"relative inline-block",onMouseEnter:c,onMouseLeave:o,onFocus:c,onBlur:o,tabIndex:0,"aria-describedby":"tooltip",children:[t,l&&(0,a.jsx)("span",{id:"tooltip",role:"tooltip",className:"absolute z-50 left-1/2 -translate-x-1/2 mt-2 px-3 py-1 rounded bg-gray-900 text-white text-xs shadow-lg whitespace-nowrap",style:{top:"100%"},children:s})]})},S=d.Ik({firstName:d.Yj().min(2,"First name must be at least 2 characters"),lastName:d.Yj().min(2,"Last name must be at least 2 characters"),email:d.Yj().email("Please enter a valid email address"),phone:d.Yj().min(10,"Please enter a valid phone number"),password:d.Yj().min(8,"Password must be at least 8 characters"),confirmPassword:d.Yj(),referralCode:d.Yj().optional(),agreeToTerms:d.zM().refine(e=>!0===e,{message:"You must agree to the terms and conditions"})}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function M(){let e=(0,l.useRouter)(),s=(0,l.useSearchParams)(),t=(0,A.jL)(),i=(0,A.GV)(P.Kc),[d,M]=(0,r.useState)(!1),[O,I]=(0,r.useState)(!1),[_,{isLoading:F}]=(0,k.ge)(),[R]=(0,k.ac)(),[Y]=(0,k.pv)(),[L,V]=(0,r.useState)(0),[B,K]=(0,r.useState)(!1),[z,q]=(0,r.useState)(!1),[D,U]=(0,r.useState)(""),[Z,W]=(0,r.useState)(""),[$,H]=(0,r.useState)(!1),[G,J]=(0,r.useState)(!1),[Q,X]=(0,r.useState)(0),{getNextStepForUser:ee}=(0,C.YK)(),{register:es,handleSubmit:et,formState:{errors:ea},setError:er,watch:el,setValue:ei}=(0,c.mN)({resolver:(0,o.u)(S),defaultValues:{firstName:"",lastName:"",email:"",phone:"",password:"",confirmPassword:"",referralCode:"",agreeToTerms:!1}}),en=el("password"),ec=el("referralCode");(0,r.useEffect)(()=>{let e=s.get("ref")||s.get("referral");e&&ei("referralCode",e)},[s,ei]),(0,r.useEffect)(()=>{i&&e.push("/dashboard")},[i,e]),(0,r.useEffect)(()=>{let e;return Q>0&&(e=setInterval(()=>{X(e=>e-1)},1e3)),()=>clearInterval(e)},[Q]),(0,r.useEffect)(()=>{if(!en||0===en.length)return void V(0);let e=0;en.length>=8&&e++,/[A-Z]/.test(en)&&e++,/[a-z]/.test(en)&&e++,/[0-9]/.test(en)&&e++,/[^A-Za-z0-9]/.test(en)&&e++,V(e)},[en]);let eo=async s=>{try{let a=await _(s).unwrap();if(a.success&&a.data){t((0,P.LA)({user:a.data.user,token:a.data.token,refreshToken:a.data.refreshToken}));let r=ee(a.data.user);"kyc"===r?(E.toast.success("Account created successfully! Please complete your KYC verification."),e.push("/kyc")):a.data.otpSent&&a.data.otpRequired?(U(s.email),q(!0),X(60),E.toast.success("Account created successfully! Please check your email for the OTP code.")):"email_verification"===r||a.data.emailVerificationRequired?(U(s.email),K(!0),E.toast.success("Account created successfully! Please check your email to verify your account.")):(E.toast.success("Account created successfully! Please login with your credentials."),localStorage.setItem("loginCredentials",JSON.stringify({email:s.email,referralCode:s.referralCode||""})),e.push("/login?registered=true"))}}catch(e){var a;console.error("Registration error:",e),409===e.status?er("email",{message:"Email already exists"}):(null==(a=e.data)?void 0:a.message)?E.toast.error(e.data.message):E.toast.error("Registration failed. Please try again.")}},ed=async()=>{if(!Z||6!==Z.length)return void E.toast.error("Please enter a valid 6-digit OTP");H(!0);try{if((await Y({email:D,otp:Z}).unwrap()).success){E.toast.success("Email verified successfully! Your account is now active.");let s=el();localStorage.setItem("loginCredentials",JSON.stringify({email:D,referralCode:s.referralCode||""})),e.push("/login?verified=true")}}catch(e){var s;console.error("OTP verification error:",e),(null==(s=e.data)?void 0:s.message)?E.toast.error(e.data.message):E.toast.error("Invalid OTP. Please try again.")}finally{H(!1)}},em=async()=>{if(!D)return void E.toast.error("Email not found. Please try registering again.");if(Q>0)return void E.toast.error("Please wait ".concat(Q," seconds before requesting another OTP."));J(!0);try{(await R({email:D}).unwrap()).success&&(E.toast.success("OTP sent successfully! Please check your email."),W(""),X(60))}catch(s){var e;console.error("Resend OTP error:",s),(null==(e=s.data)?void 0:e.message)?E.toast.error(s.data.message):E.toast.error("Failed to resend OTP. Please try again.")}finally{J(!1)}};return z?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,a.jsx)(m.A,{className:"h-12 w-12 text-blue-600"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"PropertyInvest"})]}),(0,a.jsxs)(w.Zp,{className:"shadow-2xl border-0 p-2",children:[(0,a.jsxs)(w.aR,{className:"space-y-1",children:[(0,a.jsx)(w.ZB,{className:"text-2xl font-bold text-center text-green-600",children:"Verify Your Email"}),(0,a.jsxs)(w.BT,{className:"text-center",children:["We've sent a 6-digit OTP to ",(0,a.jsx)("strong",{children:D})]})]}),(0,a.jsxs)(w.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"otp",className:"text-sm font-medium text-gray-700",children:"Enter OTP"}),(0,a.jsx)("input",{id:"otp",type:"text",maxLength:6,value:Z,onChange:e=>W(e.target.value.replace(/\D/g,"")),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-center text-lg font-mono tracking-widest focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"000000"})]}),(0,a.jsx)(j.$,{onClick:ed,disabled:$||6!==Z.length,className:"w-full",children:$?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Verifying..."]}):"Verify OTP"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Didn't receive the OTP?"}),(0,a.jsx)(j.$,{variant:"outline",onClick:em,disabled:G||Q>0,className:"text-blue-600 hover:text-blue-700",children:G?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending..."]}):Q>0?"Resend OTP (".concat(Q,"s)"):"Resend OTP"})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(j.$,{variant:"ghost",onClick:()=>{q(!1),W("")},className:"text-gray-600 hover:text-gray-700",children:"Back to Registration"})})]})]})]})}):B?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,a.jsx)(m.A,{className:"h-12 w-12 text-blue-600"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"SGM "})]}),(0,a.jsxs)(w.Zp,{className:"shadow-2xl border-0 p-2",children:[(0,a.jsxs)(w.aR,{className:"space-y-1",children:[(0,a.jsx)(w.ZB,{className:"text-2xl font-bold text-center text-green-600",children:"Account Created Successfully!"}),(0,a.jsx)(w.BT,{className:"text-center",children:"Please verify your email to activate your account"})]}),(0,a.jsxs)(w.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(h.A,{className:"h-16 w-16 text-blue-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-700 mb-2",children:"We've sent a verification email to:"}),(0,a.jsx)("p",{className:"font-semibold text-blue-600 mb-4",children:D}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Click the verification link in your email to activate your account and start investing."})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(j.$,{onClick:()=>e.push("/login"),className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:"Go to Login"}),(0,a.jsx)(j.$,{onClick:()=>K(!1),variant:"outline",className:"w-full",children:"Back to Registration"})]}),(0,a.jsxs)("div",{className:"text-center text-sm text-gray-600",children:[(0,a.jsx)("p",{children:"Didn't receive the email? Check your spam folder or"}),(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-500 font-medium",onClick:()=>{E.toast.info("Resend verification feature coming soon!")},children:"resend verification email"})]})]})]})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8 animate-fade-in",children:[(0,a.jsx)("div",{className:"flex justify-center mb-6",children:(0,a.jsx)(N.A,{size:"lg",variant:"full"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-black mb-2",children:"Create Your Account"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Start your real estate investment journey"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-sky-500 mx-auto mt-4 rounded-full"})]}),(0,a.jsxs)(w.Zp,{className:"sgm-card sgm-card-hover border-t-4 border-t-sky-500",children:[(0,a.jsxs)(w.aR,{className:"space-y-1",children:[(0,a.jsx)(w.ZB,{className:"text-2xl font-bold text-center text-black",children:"Create Account"}),(0,a.jsx)(w.BT,{className:"text-center text-gray-600",children:"Join thousands of investors building wealth through real estate"})]}),(0,a.jsxs)(w.Wu,{children:[(0,a.jsxs)("form",{onSubmit:et(eo),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"firstName",className:"text-sm font-medium text-gray-700",children:"First Name"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{...es("firstName"),type:"text",id:"firstName","aria-label":"First Name",placeholder:"First name",className:"w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all duration-200 ".concat(ea.firstName?"border-red-500":"border-gray-300")})]}),ea.firstName&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:ea.firstName.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"lastName",className:"text-sm font-medium text-gray-700",children:"Last Name"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{...es("lastName"),type:"text",id:"lastName","aria-label":"Last Name",placeholder:"Last name",className:"w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(ea.lastName?"border-red-500":"border-gray-300")})]}),ea.lastName&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:ea.lastName.message})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{...es("email"),type:"email",id:"email","aria-label":"Email Address",placeholder:"Enter your email",className:"w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(ea.email?"border-red-500":"border-gray-300")})]}),ea.email&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:ea.email.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"phone",className:"text-sm font-medium text-gray-700",children:"Phone Number"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{...es("phone"),type:"tel",id:"phone","aria-label":"Phone Number",placeholder:"Enter your phone number",className:"w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(ea.phone?"border-red-500":"border-gray-300")})]}),ea.phone&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:ea.phone.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{...es("password"),type:d?"text":"password",id:"password","aria-label":"Password",placeholder:"Create a password",className:"w-full pl-10 pr-12 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(ea.password?"border-red-500":"border-gray-300")}),(0,a.jsx)("button",{type:"button",onClick:()=>M(!d),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600","aria-label":d?"Hide password":"Show password",children:d?(0,a.jsx)(y.A,{className:"h-4 w-4"}):(0,a.jsx)(g.A,{className:"h-4 w-4"})})]}),en&&en.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"h-2 w-full bg-gray-200 rounded-full mt-1",children:(0,a.jsx)("div",{className:"h-2 rounded-full transition-all ".concat(L<=2?"bg-red-400 w-1/5":3===L?"bg-yellow-400 w-3/5":L>=4?"bg-green-500 w-full":"")})}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[L<=2&&"Weak password",3===L&&"Medium strength",L>=4&&"Strong password"]})]}),ea.password&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:ea.password.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{...es("confirmPassword"),type:O?"text":"password",id:"confirmPassword","aria-label":"Confirm Password",placeholder:"Confirm your password",className:"w-full pl-10 pr-12 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(ea.confirmPassword?"border-red-500":"border-gray-300")}),(0,a.jsx)("button",{type:"button",onClick:()=>I(!O),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600","aria-label":O?"Hide confirm password":"Show confirm password",children:O?(0,a.jsx)(y.A,{className:"h-4 w-4"}):(0,a.jsx)(g.A,{className:"h-4 w-4"})})]}),ea.confirmPassword&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:ea.confirmPassword.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"referralCode",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["Referral Code (Optional)",(0,a.jsx)(T,{content:"If you have a referral code, enter it to earn bonus rewards!",children:(0,a.jsx)(b.A,{className:"h-4 w-4 text-blue-400 cursor-pointer"})})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{...es("referralCode"),type:"text",id:"referralCode","aria-label":"Referral Code",placeholder:"Enter referral code",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),ec&&(0,a.jsx)("p",{className:"text-sm text-green-600",children:"\uD83C\uDF89 You'll earn bonus rewards with this referral!"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("input",{...es("agreeToTerms"),type:"checkbox",id:"agreeToTerms","aria-label":"Agree to Terms",className:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsxs)("label",{htmlFor:"agreeToTerms",className:"text-sm text-gray-700",children:["I agree to the"," ",(0,a.jsx)(n(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",(0,a.jsx)(n(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})]}),ea.agreeToTerms&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:ea.agreeToTerms.message}),(0,a.jsx)(j.$,{type:"submit",disabled:F,className:"w-full bg-sky-500 hover:bg-sky-600 text-white py-3 px-4 rounded-lg font-medium transition-colors","aria-label":"Create Account",children:F?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating account..."]}):"Create Account"})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,a.jsx)(n(),{href:"/login",className:"text-blue-600 hover:text-blue-500 font-medium",children:"Sign in here"})]})})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2094,5315,7436,3403,1147,7627,390,110,7358],()=>s(1964)),_N_E=e.O()}]);